import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { putMethod, postMethod } from "utils/requests"

import { store } from "App"

export const fieldVariants = {
  GROUP: "group",
  MIN_MAX_PRICE: "minMax",
  USE_SALE_PRICE: "useSalePrice",
  OPTIMIZATION_ACTIVE: "optimizationActive",
  VAT: "taxAmount",
}

export const sideVariants = {
  SELECTED: "selected",
  ALL: "all",
}

/**
 * Update optimization
 * @param payload
 * @param params
 * @return {Promise<{fileName: *, blob}|{data}|undefined>}
 */
export const updateOptimization = (payload, params = {}) =>
  putMethod({
    requestUrl: `/amazon-product/group-action${getUrlSearchParamsString({
      params,
    })}`,
    payload,
  })

export const setBulkEdit = (payload, params = {}) => {
  const {
    customer: { customer: { id: customerId } = {} },
  } = store.getState()
  return postMethod({
    requestUrl: `/amazon-product/bulk-edit/c-${customerId}${getUrlSearchParamsString(
      { params }
    )}`,
    payload,
  })
}

/**
 *  Update Price
 * @param payload
 * @param params
 * @return {Promise<{fileName: *, blob}|{data}|undefined>}
 */
export const updatePrice = (payload, params) =>
  updateOptimization(payload, params)

/**
 *  Update Group
 * @param payload
 * @param params
 * @return {Promise<{fileName: *, blob}|{data}|undefined>}
 */
export const updateProductGroup = (payload, params) =>
  updateOptimization(payload, params)

/**
 * Update Sale Price
 * @param payload
 * @param params
 * @return {Promise<{fileName: *, blob}|{data}|undefined>}
 */
export const updateSalePrice = (payload, params) =>
  updateOptimization(payload, params)

export default {
  updateOptimization,
  updatePrice,
  updateProductGroup,
  updateSalePrice,
  setBulkEdit,
}
