import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { getMethod, postMethod } from "utils/requests"

export const getCustomerInvoiceTransactions = (params = {}) =>
  getMethod({
    requestUrl: `/customer-invoice-transaction${getUrlSearchParamsString({
      params,
    })}`,
  })

export const getCustomerInvoiceImportStatus = (id) =>
  getMethod({
    requestUrl: `/customer-invoice-transaction/import-status?id=${id}`,
  })

export const importCustomerInvoiceTransactions = (payload) =>
  postMethod({
    requestUrl: `/customer-invoice-transaction/import`,
    payload,
    shouldAddAuthHeader: true,
    isFormData: true,
  })

export default {
  getCustomerInvoiceTransactions,
  importCustomerInvoiceTransactions,
  getCustomerInvoiceImportStatus,
}
