import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import { getMethod, postMethod, putMethod } from "utils/requests"

import type { CreatePromoCodeRequestParams } from "types/RequestParams/PromoCode"

import type { UpdatePromoCodeArgs } from "./promoCodeServiceTypes"

const getPromoCodes = (params = {}) =>
  getMethod({
    requestUrl: `/promo-code${getUrlSearchParamsString({ params })}`,
  })

const updatePromoCode = ({ id, payload }: UpdatePromoCodeArgs) =>
  putMethod({
    requestUrl: `/promo-code${getUrlSearchParamsString({ params: { id } })}`,
    payload,
  })

const createPromoCode = (payload: CreatePromoCodeRequestParams) =>
  postMethod({
    requestUrl: "/promo-code",
    payload,
  })

export default {
  getPromoCodes,
  updatePromoCode,
  createPromoCode,
}
