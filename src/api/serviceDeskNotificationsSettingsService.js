import config from "config"

import { getMethod, putMethod } from "utils/requests"

import { API_VERSION } from "consts"

const { serviceDeskHostUrl } = config
const PATH = "/notification-settings"

export const fetchAllSettings = () => {
  return getMethod({
    requestUrl: PATH,
    hostUrl: serviceDeskHostUrl,
    fixedApiVersion: API_VERSION.WITHOUT,
  })
}

export const patchAllSettings = (payload) =>
  putMethod({
    requestUrl: PATH,
    payload,
    hostUrl: serviceDeskHostUrl,
    fixedApiVersion: API_VERSION.WITHOUT,
  })

export default {
  fetchAllSettings,
  patchAllSettings,
}
