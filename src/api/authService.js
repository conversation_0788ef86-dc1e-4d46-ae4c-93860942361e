import { postMethod, put<PERSON>eth<PERSON> } from "utils/requests"

import { API_VERSION } from "consts"

export const redirect = ({
  clientId,
  codeChallenge,
  redirectUri,
  authHostUrl,
}) => {
  window.location.href = `${authHostUrl}/?client_id=${clientId}&grant_type=authorization_code&response_type=code&redirect_uri=${redirectUri}&code_challenge=${codeChallenge}`
}

export const logoutRedirect = ({ redirectUri, authHostUrl }) => {
  window.location.href = `${authHostUrl}/site/auto-logout?returnUrl=${redirectUri}`
}

export const authenticate = ({
  authCode,
  clientId,
  clientSecret,
  codeChallenge,
  redirectUri,
  authHostUrl,
}) => {
  const endpointUri = `/oauth2/token?response_type=token&client_id=${clientId}`

  return postMethod({
    requestUrl: endpointUri,
    payload: {
      code: authCode,
      code_verifier: codeChallenge,
      grant_type: "authorization_code",
      redirect_uri: redirectUri,
    },
    hostUrl: authHostUrl,
    headers: {
      Authorization: `Basic ${btoa(`${clientId}:${clientSecret}`)}`,
    },
    shouldAddAuthHeader: false,
    fixedApiVersion: API_VERSION.WITHOUT,
  })
}

export const refreshToken = ({
  clientId,
  clientSecret,
  token,
  authHostUrl,
}) => {
  const endpointUri = `/oauth2/token`

  return postMethod({
    requestUrl: endpointUri,
    payload: {
      grant_type: "refresh_token",
      refresh_token: token,
    },
    hostUrl: authHostUrl,
    headers: {
      Authorization: `Basic ${btoa(`${clientId}:${clientSecret}`)}`,
    },
    shouldAddAuthHeader: false,
    fixedApiVersion: API_VERSION.WITHOUT,
  })
}

export const authorize = (id) =>
  putMethod({ requestUrl: `/token/authorize`, payload: { id } })

export const unauthorize = () =>
  putMethod({ requestUrl: `/token/un-authorize` })

export default {
  authenticate,
  logoutRedirect,
  redirect,
  refreshToken,
  authorize,
  unauthorize,
}
