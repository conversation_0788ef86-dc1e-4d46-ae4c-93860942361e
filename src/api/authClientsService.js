import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { deleteMethod, getMethod, postMethod, putMethod } from "utils/requests"

export const addAuthClient = (payload) =>
  postMethod({ requestUrl: `/auth-client`, payload })

export const deleteAuthClient = (id) =>
  deleteMethod({ requestUrl: `/auth-client?id=${id}` })

export const getAuthClients = (params = {}) =>
  getMethod({
    requestUrl: `/auth-client${getUrlSearchParamsString({
      params,
    })}`,
  })

export const updateAuthClient = (id, payload) =>
  putMethod({ requestUrl: `/auth-client?id=${id}`, payload })

export default {
  addAuthClient,
  deleteAuthClient,
  getAuthClients,
  updateAuthClient,
}
