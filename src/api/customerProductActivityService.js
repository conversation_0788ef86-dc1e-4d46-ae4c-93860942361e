import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { getMethod, putMethod } from "utils/requests"

export const getCustomerProductActivities = (params = {}) =>
  getMethod({
    requestUrl: `/amazon-marketplace-product-activity${getUrlSearchParamsString(
      { params }
    )}`,
  })

export const updateCustomerProductActivity = (id, payload) =>
  putMethod({
    requestUrl: `/amazon-marketplace-product-activity${getUrlSearchParamsString(
      { params: { id } }
    )}`,
    payload,
  })

export default {
  getCustomerProductActivities,
  updateCustomerProductActivity,
}
