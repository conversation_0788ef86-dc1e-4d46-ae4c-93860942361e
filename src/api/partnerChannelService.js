import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { getMethod, postMethod, putMethod } from "utils/requests"

export const addPartnerChannel = (payload) =>
  postMethod({ requestUrl: `/partner-channel`, payload })

export const getPartnerChannelsForSpecificPartner = (partnerId) =>
  getMethod({
    requestUrl: `/partner-channel?partner_id=${partnerId}`,
  })

export const getPartnerChannels = (params = {}) =>
  getMethod({
    requestUrl: `/partner-channel${getUrlSearchParamsString({
      params,
    })}`,
  })

export const updatePartnerChannel = (id, payload) =>
  putMethod({ requestUrl: `/partner-channel?id=${id}`, payload })

export default {
  addPartnerChannel,
  getPartnerChannels,
  updatePartnerChannel,
  getPartnerChannelsForSpecificPartner,
}
