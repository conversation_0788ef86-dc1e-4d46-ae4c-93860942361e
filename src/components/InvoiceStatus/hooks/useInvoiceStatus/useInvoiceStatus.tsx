import { useDispatch, useSelector } from "react-redux"

import invoiceStatusActions from "actions/invoiceStatusActions"

import {
  invoiceStatusDataItemsSelector,
  invoiceStatusSearchOptionsSelector,
  invoiceStatusTotalCountSelector,
} from "selectors/invoiceStatusSelectors"

// @ts-expect-error
const { get: getInvoiceStatusData } = invoiceStatusActions

export const useInvoiceStatus = () => {
  const dataSource = useSelector(invoiceStatusDataItemsSelector)
  const searchOptions = useSelector(invoiceStatusSearchOptionsSelector)
  const totalCount = useSelector(invoiceStatusTotalCountSelector)

  const dispatch = useDispatch()

  const getInvoiceStatus = <Type,>(searchOptions: Type) => {
    return dispatch(getInvoiceStatusData(searchOptions))
  }

  return {
    dataSource,
    searchOptions,
    totalCount,
    getInvoiceStatus,
  }
}
