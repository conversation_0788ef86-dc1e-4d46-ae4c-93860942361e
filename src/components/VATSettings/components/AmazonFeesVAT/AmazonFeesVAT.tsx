import React from "react"
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  IconPopover,
  SimpleTable,
  Switch,
  Typography,
} from "@develop/fe-library"

import { RestrictedButtonPopover } from "components/shared/Buttons"
import { ConnectedField } from "components/shared/ConnectedField"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"
import { AMAZON_FEES_VAT_FIELDS } from "consts/amazonFeesVat"

import { useAmazonFeesVat, useAmazonFeesVatColumns } from "./hooks"

import type { AmazonFeesVATProps } from "./AmazonFeesVATTypes"

import styles from "./amazonFeesVAT.module.scss"

export const AmazonFeesVAT = ({
  setIsTabChangeAllowed,
}: AmazonFeesVATProps) => {
  const {
    accountAmazonFeesVatList,
    control,
    countriesOptions,
    enabled,
    enabledByAccount,
    isSaveButtonDisabled,
    buildHandleChangeAccountSwitch,
    buildHandleChangeAccountCountry,
    handleChangeEnabledByAccountSwitch,
    handleChangeGlobalCountry,
    handleChangeMainSwitch,
    handleClickCancel,
    handleClickSaveButton,
  } = useAmazonFeesVat({ setIsTabChangeAllowed })

  const columns = useAmazonFeesVatColumns({
    control,
    countriesOptions,
    enabled,
    enabledByAccount,
    buildHandleChangeAccountSwitch,
    buildHandleChangeAccountCountry,
  })

  return (
    <Box
      flexDirection="column"
      gap="m"
      mXL={{ gap: "l", paddingTop: "l" }}
      paddingTop="m"
    >
      <Box
        flexDirection="column"
        gap="m"
        mXL={{ gap: "l", padding: "0 l" }}
        padding="0 m"
      >
        <Typography variant="--font-headline-5">
          {l("VAT on Amazon fees in European marketplaces")}
        </Typography>

        <Box align="center" gap="m">
          <Switch
            hasEllipsis={false}
            isChecked={enabled}
            label={l(
              "Entitled to deduct input tax in the EU for Amazon fees (starting August 2024)"
            )}
            onChange={handleChangeMainSwitch}
          />

          <IconPopover
            color="--color-icon-active"
            maxWidth={500}
            name="icnInfoCircle"
            placement="topLeft"
            size="--icon-size-3"
            content={l(
              "By selecting this option you confirm that you will be able to recover VAT applied to Amazon fees starting August 2024. Amazon fees' transactions displayed in Business Analytics will be reduced by a VAT rate confirmed in this form."
            )}
          />
        </Box>

        <Alert
          alertType="info"
          message={l(
            "Please, note that data displayed on Business Analytics pages will require time to be recalculated after the VAT recovery option selection. If the selection is reversed, the data starting August 2024 will be recalculated again."
          )}
        />

        <Box
          display="grid"
          gap="m"
          gridTemplateColumns="auto"
          mXL={{ gridTemplateColumns: "251px 160px" }}
        >
          <ConnectedField
            control={control}
            name={AMAZON_FEES_VAT_FIELDS.countryId}
            type="select"
            inputProps={{
              label: l("Company’s registration country"),
              isFullWidth: true,
              isDisabled: enabledByAccount || !enabled,
              isGlobal: true,
              hasSearch: true,
              options: countriesOptions,
              onChange: handleChangeGlobalCountry,
            }}
          />

          <ConnectedField
            control={control}
            name={AMAZON_FEES_VAT_FIELDS.value}
            type="numeric"
            inputProps={{
              label: l("VAT"),
              isFullWidth: true,
              isDecimalAllowed: false,
              isZeroAllowed: false,
              isDisabled: enabledByAccount || !enabled,
              isNegativeAllowed: false,
              suffixIcons: [
                { name: "icnPercentage", color: "--color-icon-static" },
              ],
            }}
          />
        </Box>

        <Switch
          isChecked={enabledByAccount}
          isDisabled={!enabled}
          label={l("Apply by Amazon seller account")}
          onChange={handleChangeEnabledByAccountSwitch}
        />
      </Box>

      <Box
        display="block"
        hasBorder={{ top: true, bottom: true }}
        marginTop="m"
        mXL={{ marginTop: "0" }}
        overflowX="scroll"
        tb={{ overflow: "unset" }}
      >
        <Box className={styles.tableWrapper} display="block" minWidth={660}>
          <SimpleTable
            columns={columns}
            data={accountAmazonFeesVatList}
            hasOuterBorder={false}
          />
        </Box>
      </Box>

      <Box
        backgroundColor="--color-main-background"
        bottom="0"
        display="grid"
        gap="m"
        gridTemplateColumns="1fr 1fr"
        hasBorder={{ top: true }}
        padding="m"
        position="sticky"
        zIndex="10"
        mXL={{
          display: "flex",
          padding: "0 l l",
          position: "unset",
          hasBorder: { top: false },
        }}
      >
        <Button variant="secondary" onClick={handleClickCancel}>
          {l("Cancel")}
        </Button>

        <RestrictedButtonPopover
          disabled={isSaveButtonDisabled}
          managePermission={permissionKeys.customerVatSettingManage}
          popoverMessage={restrictPopoverMessages.alter}
          onClick={handleClickSaveButton}
        >
          {l("Save")}
        </RestrictedButtonPopover>
      </Box>
    </Box>
  )
}
