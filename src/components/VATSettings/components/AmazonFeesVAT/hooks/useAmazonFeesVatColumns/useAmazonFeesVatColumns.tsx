import React, { useMemo } from "react"
import { Box, Switch } from "@develop/fe-library"

import { ConnectedField } from "components/shared/ConnectedField"
import type { FieldName } from "components/VATSettings/components/AmazonFeesVAT/AmazonFeesVATTypes"

import l from "utils/intl"

import { AMAZON_FEES_VAT_FIELDS } from "consts/amazonFeesVat"

import type { UseAmazonFeesVatColumns } from "./UseAmazonFeesVatColumnsTypes"

export const useAmazonFeesVatColumns: UseAmazonFeesVatColumns = ({
  control,
  countriesOptions,
  enabled: isRecoverEnabled,
  enabledByAccount,
  buildHandleChangeAccountSwitch,
  buildHandleChangeAccountCountry,
}) => {
  return useMemo(() => {
    const isAllDisabled: boolean = !enabledByAccount || !isRecoverEnabled

    return [
      {
        title: (
          <Box marginLeft="s" paddingLeft="xl" width="100%">
            {l("Amazon seller account")}
          </Box>
        ),
        dataIndex: "amazonCustomerAccountLabel",
        key: "amazonCustomerAccountLabel",
        width: 280,
        renderCell: ({ value, index, item: { enabled: isAccountEnabled } }) => {
          return (
            <Box width="100%">
              <Switch
                isChecked={!!isAccountEnabled}
                isDisabled={isAllDisabled}
                label={value}
                onChange={buildHandleChangeAccountSwitch(index)}
              />
            </Box>
          )
        },
      },
      {
        title: l("Country of registration"),
        dataIndex: "countryId",
        key: "countryId",
        width: 180,
        renderCell: ({ index, item: { enabled: isAccountEnabled } }) => {
          const name: FieldName = `${AMAZON_FEES_VAT_FIELDS.accountAmazonFeesVatList}.${index}.countryId`

          const isDisabled: boolean = isAllDisabled || !isAccountEnabled

          return (
            <ConnectedField
              control={control}
              name={name}
              type="select"
              inputProps={{
                isDisabled,
                hasSearch: true,
                options: countriesOptions,
                isGlobal: true,
                onChange: buildHandleChangeAccountCountry(index),
              }}
            />
          )
        },
      },
      {
        title: (
          <Box display="block" width="100%">
            <Box justify="center" width="160px">
              {l("VAT value")}
            </Box>
          </Box>
        ),
        dataIndex: "value",
        key: "value",
        width: "auto",
        renderCell: ({ index, item: { enabled: isAccountEnabled } }) => {
          const name: FieldName = `${AMAZON_FEES_VAT_FIELDS.accountAmazonFeesVatList}.${index}.value`

          const isDisabled: boolean = isAllDisabled || !isAccountEnabled

          return (
            <Box display="block" width="100%">
              <Box width="170px">
                <ConnectedField
                  control={control}
                  name={name}
                  type="numeric"
                  inputProps={{
                    isFullWidth: true,
                    isDecimalAllowed: false,
                    isDisabled,
                    isNegativeAllowed: false,
                    isZeroAllowed: false,
                    suffixIcons: [
                      {
                        name: "icnPercentage",
                        color: "--color-icon-static",
                      },
                    ],
                  }}
                />
              </Box>
            </Box>
          )
        },
      },
    ]
  }, [
    control,
    countriesOptions,
    isRecoverEnabled,
    enabledByAccount,
    buildHandleChangeAccountSwitch,
    buildHandleChangeAccountCountry,
  ])
}
