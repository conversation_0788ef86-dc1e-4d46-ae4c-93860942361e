import React from "react"

import TabsComponent from "components/TabsComponent/TabsComponent"

import { useVATSettings } from "./hooks"

export const VATSettings = () => {
  const {
    activeTab,
    isTabChangeAllowed,
    tabs,
    handleChangeTab,
    handlePreventedChangeTab,
  } = useVATSettings()

  return (
    <TabsComponent
      // @ts-expect-error
      hideIconsOnMobile
      activeTab={activeTab}
      isTabChangeAllowed={isTabChangeAllowed}
      tabs={tabs}
      onChangeTab={handleChangeTab}
      onPreventedChangeTab={handlePreventedChangeTab}
    />
  )
}
