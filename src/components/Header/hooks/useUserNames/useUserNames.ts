import { useSelector } from "react-redux"

import { currentUserSelector } from "selectors/userSelectors"

import l from "utils/intl"

export const useUserNames = (): {
  userName: string
  anotherUserName: string | undefined
} => {
  const currentUser = useSelector(currentUserSelector)

  const userName = `${currentUser.firstname} ${currentUser.lastname}`

  const anotherUserName = currentUser.mainStaff
    ? l("Logged in as {name}", { name: userName })
    : undefined

  return {
    userName,
    anotherUserName,
  }
}
