import { useDispatch, useSelector } from "react-redux"
import { HeaderProps } from "@develop/fe-library"

import viewModeActions from "actions/viewModeActions"

import { viewModeSelector } from "selectors/userSelectors"

import l from "utils/intl"

// @ts-ignore
const { change } = viewModeActions

export const useUserModeProps = (
  isVisible: boolean
): HeaderProps["userModeProps"] => {
  const dispatch = useDispatch()
  const mode = useSelector(viewModeSelector)

  const isAdminMode = mode === "admin"

  const userLabel = l("User")
  const adminLabel = l("Admin")

  const toggleViewMode = () => {
    const newMode = isAdminMode ? "user" : "admin"

    dispatch(change(newMode))
  }

  return {
    userLabel,
    adminLabel,
    isAdminMode,
    onToggle: toggleViewMode,
    isVisible,
  }
}
