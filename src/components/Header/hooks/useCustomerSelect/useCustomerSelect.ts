import { useCallback, useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import customerActions from "actions/customerActions"

import {
  customerOptionsSelector,
  customerStateSelector,
} from "selectors/customerSelectors"

import { SelectItemWithKey } from "@develop/fe-library/dist/lib/components/Header/HeaderTypes"
// @ts-ignore
const { change: changeCustomer, getAll: getCustomers } = customerActions

export const useCustomerSelect = (hasAccess: boolean): SelectItemWithKey => {
  const dispatch = useDispatch()
  const [page, setPage] = useState(1)
  const [searchValue, setSearchValue] = useState("")
  const [isEnabled, setIsEnabled] = useState(true)

  const { customer, pageOptions: { pageCount = 1 } = {} } = useSelector(
    customerStateSelector
  )

  const { id, title } = customer || {}
  const customerName = !!id && !!title ? `(${id}) ${title}` : ""

  const customers = useSelector(customerOptionsSelector)

  // @ts-ignore
  const customersOptions = customers.map(({ id, title }) => ({
    value: id,
    label: `(${id}) ${title}`,
  }))

  const handleSelect = useCallback((value: string): void => {
    dispatch(changeCustomer(value, false))
  }, [])

  const handleClose = useCallback(() => {
    setIsEnabled(false)
    setSearchValue("")
    setPage(1)

    setTimeout(() => {
      setIsEnabled(true)
    }, 500)
  }, [])

  // Search request is sent when searchValue or page changes.
  // This is needed for optimistic rendering of the searchValue.
  // The search is put in a timeout to avoid sending a request on every key press.
  // The timeout clear allows to cancel the previous request if the user types quickly.
  useEffect(() => {
    const isSkipped = !isEnabled || !hasAccess

    if (isSkipped) {
      return
    }

    const timeout = setTimeout(() => {
      dispatch(getCustomers(searchValue, page))
    }, 500)

    return () => {
      clearTimeout(timeout)
    }
  }, [hasAccess, searchValue, page, isEnabled])

  return {
    key: "customerSelect",
    type: "selectWithFilter",
    value: id,
    displayedValue: customerName,
    options: customersOptions,
    page,
    pageCount,
    onSearch: setSearchValue,
    onPageChange: setPage,
    onSelect: handleSelect,
    searchValue,
    onClose: handleClose,
  }
}
