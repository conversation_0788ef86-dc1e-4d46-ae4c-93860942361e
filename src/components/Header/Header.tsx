import React from "react"
import { Head<PERSON> as HeaderComponent } from "@develop/fe-library"

import LostNotificationsModal from "components/LostNotificationsModal"
import NotesModal from "components/NotesModal"
import { Notifications as ServiceDeskNotifications } from "components/ServiceDeskNotifications"

import l from "utils/intl"

import { HomePage, SubHeader } from "./components"

import { useHeader } from "./hooks"

export const Header = () => {
  const {
    profileTitle,
    profileSubTitle,
    logoType,
    homepageIconProps,
    navigation,
    anotherUserLabel,
    userModeProps,
    selectItems,
    icons,
    profile,
    settings,
    isNotesModalOpen,
    isNotificationsModalOpen,
    handleAction,
    isItemSelected,
    navigateTo,
    hasSubHeader,
    languages,
  } = useHeader()

  const iconsProcessed = icons.map((iconItem) => {
    if (iconItem.key === "serviceDeskNotifications") {
      return {
        ...iconItem,
        menu: <ServiceDeskNotifications onClose={() => {}} />,
      }
    }

    return iconItem
  })

  return (
    <>
      <HeaderComponent
        anotherUserLabel={anotherUserLabel}
        homepage={homepageIconProps.isOpen ? <HomePage /> : null}
        homepageIconProps={homepageIconProps}
        icons={iconsProcessed}
        isItemSelected={isItemSelected}
        languages={languages}
        languagesDrawerTitle={l("Language")}
        logoType={logoType}
        navigateTo={navigateTo}
        navigation={navigation}
        profile={profile}
        profileSubTitle={profileSubTitle}
        profileTitle={profileTitle}
        selectItems={selectItems}
        settings={settings}
        userModeProps={userModeProps}
        nestedMenuSettings={{
          titleKey: "id",
          urlKey: "url",
          childrenKey: "items",
          translateFunc: l,
        }}
        subHeader={
          hasSubHeader ? (
            <SubHeader homepageIconProps={homepageIconProps} />
          ) : null
        }
        onAction={handleAction}
      />
      {isNotesModalOpen ? <NotesModal /> : null}
      {isNotificationsModalOpen ? <LostNotificationsModal /> : null}
    </>
  )
}
