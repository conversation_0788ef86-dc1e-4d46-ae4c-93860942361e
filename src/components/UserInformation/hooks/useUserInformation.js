import { useEffect, useState, useRef } from "react"
import { useSelector, useDispatch } from "react-redux"
import { useParams } from "react-router-dom"

import { showPhoneConfirm } from "components/PhoneConfirmation"
import { showEmailConfirm } from "components/EmailConfirmation"
import { fieldTypes } from "components/ProfileInformation/ProfileInformationForm"
import UserLanguages from "../components/UserLanguages"

import userSettingActions from "actions/userSettingActions"
import staffActions from "actions/staffActions"
import timezoneActions from "actions/timezoneActions"
import selectedRoleActions from "actions/selectedRoleActions"
import translationsActions from "actions/translationsActions"
import languagesActions from "actions/languagesActions"
import cacheAPIActions from "actions/cacheAPIActions"
import serviceDeskActions from "actions/serviceDeskProfileActions"
import { SIGNATURE_PREFIX } from "./constants"

import {
  informationEditDataSelector,
  currentUserInformationSelector,
} from "selectors/informationSelector"
import {
  userInformationSelector,
  formInitialValueSelector,
} from "selectors/UserProfileSelectors"
import {
  activeLanguagesSelector,
  localesSelector,
} from "selectors/languagesSelectors"
import {
  serviceDeskProfileIdSelector,
  serviceDeskViewSignaturesSelector,
  serviceDeskProfileDepartmentsSelector,
  hasServiceDeskAccessSelector,
  selectedUserHasSDAccessSelector,
} from "selectors/serviceDeskProfileSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import useTimeZoneOptions from "hooks/useTimeZoneOptions"

import { setItem } from "utils/storage"
import { userLocaleKey } from "utils/currentUserInfo"
import {
  ValidationErrorToFormik,
  removeNullAndUndefined,
} from "utils/objectHelpers"
import l from "utils/intl"
import { USER_ROLE_SIDES, SALUTATIONS_MAP } from "consts/user"
import { permissionKeys, restrictPopoverMessages } from "consts"

import { entityTypes } from "../constants"

const { get: getUserSettings, updateInfo: updateUserSettingInfo } =
  userSettingActions
const { updateSelectedUser } = staffActions
const { getAll: getAllTimezones } = timezoneActions
const { getLocalesList: getLocales } = languagesActions
const { change: changeLanguage } = translationsActions
const { getAll: getRoles } = selectedRoleActions
const { getServiceDeskUser, getServiceDeskProfile, getSignatureTemplates } =
  serviceDeskActions

const sync = () =>
  cacheAPIActions.purgeCache(true, () => window.location.reload())

const getSelectors = (entityType) => {
  switch (entityType) {
    case entityTypes.userSetting: {
      return {
        valuesSelector: currentUserInformationSelector,
        initialValuesSelector: informationEditDataSelector,
      }
    }
    case entityTypes.user:
    case entityTypes.staff: {
      return {
        valuesSelector: userInformationSelector,
        initialValuesSelector: formInitialValueSelector,
      }
    }
    default: {
      return {}
    }
  }
}

const buildFailureCallback =
  ({ setErrors, setSubmitting }) =>
  (errors) => {
    if (errors?.length > 0) {
      const mappedErrors = ValidationErrorToFormik(
        errors.map((error) => {
          if (error.field === "password" || error.field === "passwordConfirm") {
            return { ...error, field: `_${error.field}` }
          }
          return error
        })
      )
      setErrors(mappedErrors)
    }
    setSubmitting(false)
  }

const managePermissions = {
  [entityTypes.userSetting]: true,
  [entityTypes.user]: permissionKeys.userManage,
  [entityTypes.staff]: permissionKeys.staffManage,
}

const formTitle = "Personal information"

const formFieldsTitles = {
  salutation: "Salutation",
  firstname: "First name",
  lastname: "Last name",
  timezoneId: "Time zone",
  languageId: "Language",
  locale: "Region format",
  translationAccessOperations: "Supported languages",
  password: "Password",
  confirmPassword: "Confirm password",
}

const useUserInformation = ({ entityType }) => {
  const { id: userId } = useParams()
  const { valuesSelector, initialValuesSelector } = getSelectors(entityType)

  const dispatch = useDispatch()

  const { rbacRoleList, staffList, userList } = useSelector(permissionsSelector)
  const canRequestAccessRoles = rbacRoleList || staffList || userList

  const values = useSelector(valuesSelector)
  const initialValues = useSelector(initialValuesSelector)
  const languagesOptions = useSelector(activeLanguagesSelector)
  const localesOptions = useSelector(localesSelector)
  const serviceDeskProfileId = useSelector(serviceDeskProfileIdSelector)
  const hasCurrentUserSDAccess = useSelector(hasServiceDeskAccessSelector)
  const hasSelectedUserSDAccess = useSelector(selectedUserHasSDAccessSelector)
  const [defaultSignature, otherSignatures] = useSelector(
    serviceDeskViewSignaturesSelector
  )
  const serviceDeskDepartments = useSelector(
    serviceDeskProfileDepartmentsSelector
  )

  const [isEditMode, setIsEditMode] = useState(false)
  const timeZoneOptions = useTimeZoneOptions()
  const signatureIdFromPath = useRef("")
  const [path, setPath] = useState("")
  const [isServiceDeskEdit, setIsServiceDeskEdit] = useState(false)

  const hasServiceDeskAccess = userId
    ? hasSelectedUserSDAccess
    : hasCurrentUserSDAccess
  const hasServiceDeskAccessAndId =
    hasServiceDeskAccess && !!serviceDeskProfileId
  const serviceDeskDepartmentsNames = serviceDeskDepartments.length
    ? serviceDeskDepartments.map((department) => department.name).join(", ")
    : l("None")
  const areTranslationOperationsVisible =
    entityType !== entityTypes.userSetting &&
    [USER_ROLE_SIDES.sellerLogic, USER_ROLE_SIDES.translator].includes(
      initialValues?.side
    )

  const {
    id,
    contactInformation = [],
    personalInformation,
    activityInformation,
  } = values || {}

  const [emailInformation, phoneInformation] = contactInformation

  useEffect(() => {
    const isSignatureInPath =
      window.location.href.indexOf(SIGNATURE_PREFIX) >= 0
    if (isSignatureInPath) {
      setPath(window.location.href)
      setIsServiceDeskEdit(true)
    }
  }, [])

  if (hasServiceDeskAccessAndId) {
    const index = path.indexOf(SIGNATURE_PREFIX)
    if (path) {
      signatureIdFromPath.current = path.substring(
        index + SIGNATURE_PREFIX.length
      )
    }
  }

  useEffect(() => {
    if (entityType === entityTypes.userSetting) {
      dispatch(getUserSettings())
      if (canRequestAccessRoles) {
        dispatch(getRoles())
      }
    }
    dispatch(getLocales())
    dispatch(getAllTimezones())
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entityType, canRequestAccessRoles])

  useEffect(() => {
    const initializeSDProfile = async () => {
      const payload = userId ? { userId } : {}

      await dispatch(getServiceDeskProfile(payload))
    }

    if (hasServiceDeskAccess) {
      initializeSDProfile()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasServiceDeskAccess, userId])

  useEffect(() => {
    if (serviceDeskProfileId) {
      dispatch(getSignatureTemplates())
      dispatch(getServiceDeskUser(serviceDeskProfileId))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [serviceDeskProfileId])

  const salutationsOptions = SALUTATIONS_MAP.map(({ id, title }) => ({
    id,
    title: l(title),
  }))

  const formFields = [
    {
      title: formFieldsTitles.salutation,
      name: "salutation",
      inputType: fieldTypes.SelectType,
      placeholder: formFieldsTitles.salutation,
      options: salutationsOptions,
    },
    {
      title: formFieldsTitles.firstname,
      name: "firstname",
      inputType: fieldTypes.TextType,
      placeholder: formFieldsTitles.firstname,
      required: true,
    },
    {
      title: formFieldsTitles.lastname,
      name: "lastname",
      inputType: fieldTypes.TextType,
      placeholder: formFieldsTitles.lastname,
      required: true,
      isLast: true,
    },
    {
      title: "Local settings",
      inputType: fieldTypes.Title,
    },
    {
      title: formFieldsTitles.timezoneId,
      name: "timezone_id",
      inputType: fieldTypes.SelectSearchType,
      placeholder: formFieldsTitles.timezoneId,
      options: timeZoneOptions.map(({ label: title, value: id }) => ({
        id,
        title,
      })),
      required: true,
    },
    {
      title: formFieldsTitles.languageId,
      name: "language_id",
      inputType: fieldTypes.SelectType,
      placeholder: formFieldsTitles.languageId,
      options: languagesOptions,
      required: true,
    },
    {
      title: formFieldsTitles.locale,
      name: "locale",
      inputType: fieldTypes.SelectType,
      placeholder: formFieldsTitles.locale,
      options: localesOptions.map(({ code, value }) => ({
        id: code,
        title: value,
      })),
      isLast: !areTranslationOperationsVisible,
    },
    ...(areTranslationOperationsVisible
      ? [
          {
            title: formFieldsTitles.translationAccessOperations,
            name: "translationAccessOperations",
            fieldComponent: UserLanguages,
            inputType: fieldTypes.CheckListType,
            placeholder: formFieldsTitles.translationAccessOperations,
            fieldProps: {
              languagesOptions: languagesOptions.map((languagesOptionItem) => ({
                label: languagesOptionItem.title,
                value: languagesOptionItem.id,
                code: languagesOptionItem.code,
              })),
            },
            required: true,
            isLast: areTranslationOperationsVisible,
          },
        ]
      : []),
    {
      title: "Change password",
      inputType: fieldTypes.Title,
    },
    {
      title: formFieldsTitles.password,
      name: "_password",
      confirmName: "passwordConfirm",
      inputType: fieldTypes.TextTypePassword,
      placeholder: formFieldsTitles.password,
    },
    {
      title: formFieldsTitles.confirmPassword,
      name: "_passwordConfirm",
      inputType: fieldTypes.TextTypePassword,
      placeholder: formFieldsTitles.confirmPassword,
      withGenerate: false,
      isLast: true,
    },
  ]

  const handleEditEmail = () =>
    showEmailConfirm({
      visible: true,
      isSelectedUser: !!id,
    })

  const handleEditPhone = () =>
    showPhoneConfirm({
      visible: true,
      isSelectedUser: !!id,
      withVerification: entityType === entityTypes.userSetting,
    })

  const handleEdit = () => setIsEditMode(true)

  const handleCancel = () => setIsEditMode(false)

  const handleSaveUserSettingInfo = (values, { setErrors, setSubmitting }) => {
    const langCode = languagesOptions
      .filter((lang) => lang.id === values.language_id)
      .map((item) => item.code)[0]

    const payload = removeNullAndUndefined({
      ...values,
      password: values._password,
      passwordConfirm: values._passwordConfirm,
      _password: null,
      _passwordConfirm: null,
      email: null,
      new_email: null,
    })

    const successCallback = () => {
      setSubmitting(false)
      setIsEditMode(false)
      setItem(userLocaleKey, values.locale.replaceAll("_", "-"))

      dispatch(changeLanguage(values.language_id, langCode))
      if (!id) {
        dispatch(sync())
      }
    }

    const failureCallback = buildFailureCallback({ setErrors, setSubmitting })

    dispatch(updateUserSettingInfo(payload, successCallback, failureCallback))
  }

  const handleSaveUserProfile = (values, { setErrors, setSubmitting }) => {
    const {
      email,
      firstname,
      lastname,
      phone,
      _password,
      _passwordConfirm,
      timezone_id,
      language_id,
      locale,
      translationAccessOperations,
      salutation,
    } = values

    const payload = {
      params: { id },
      payload: {
        email,
        firstname,
        lastname,
        phone,
        timezone_id,
        language_id,
        locale,
        password: _password,
        passwordConfirm: _passwordConfirm,
        translationAccessOperations: translationAccessOperations.map(
          (translation) => {
            if (typeof translation === "string") {
              return {
                language_code: translation,
                order: 0,
              }
            }
            return {
              language_code: translation.code,
              order: translation.order,
            }
          }
        ),
        salutation,
      },
    }

    const successCallback = () => {
      setSubmitting(false)
      setIsEditMode(false)
    }

    const failureCallback = buildFailureCallback({ setErrors, setSubmitting })

    dispatch(updateSelectedUser(payload, successCallback, failureCallback))
  }

  const handleSubmit = (values, { setErrors, setSubmitting }) => {
    switch (entityType) {
      case entityTypes.userSetting: {
        return handleSaveUserSettingInfo(values, { setErrors, setSubmitting })
      }
      case entityTypes.user:
      case entityTypes.staff: {
        return handleSaveUserProfile(values, { setErrors, setSubmitting })
      }
      default:
        return () => {}
    }
  }

  const handleServiceDeskEdit = () => {
    setIsServiceDeskEdit(!isServiceDeskEdit)
    if (isServiceDeskEdit) {
      setPath("")
      signatureIdFromPath.current = ""
    }
  }

  return {
    formTitle: l(formTitle),
    isEditMode,
    isServiceDeskEdit,
    signatureIdFromPath: signatureIdFromPath.current,
    handleServiceDeskEdit,
    personalInformation: [
      {
        type: "title",
        label: l(formTitle),
        managePermission: managePermissions[entityType],
        popoverMessage: restrictPopoverMessages.alter,
        onEdit: handleEdit,
      },
      ...personalInformation,
    ],
    contactInformation: [
      {
        type: "title",
        label: l("Contact information"),
      },
      {
        ...emailInformation,
        managePermission: managePermissions[entityType],
        popoverMessage: restrictPopoverMessages.alter,
        onEdit: handleEditEmail,
      },
      {
        ...phoneInformation,
        managePermission: managePermissions[entityType],
        popoverMessage: restrictPopoverMessages.alter,
        onEdit: handleEditPhone,
      },
    ],
    activityInformation: [
      {
        type: "title",
        label: l("Activity information"),
      },
      ...activityInformation,
    ],
    ...(hasServiceDeskAccessAndId && {
      serviceDeskInformation: [
        {
          type: "title",
          label: l("Service desk"),
          onEdit: handleServiceDeskEdit,
        },
        {
          label: l("Departments"),
          value: serviceDeskDepartmentsNames,
        },
        {
          type: "subTitle",
          label: l("Signature"),
        },
        {
          label: l("Default"),
          value: defaultSignature,
        },
        {
          label: l("Others"),
          value: otherSignatures,
        },
      ],
    }),
    formFields,
    initialValues,
    handleSubmit,
    handleCancel,
  }
}

export { useUserInformation }
