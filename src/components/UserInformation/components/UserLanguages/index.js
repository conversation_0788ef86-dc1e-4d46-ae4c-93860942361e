import React from "react"
import UserLanguagesView from "./UserLanguagesView"
import { findIndex } from "lodash"

const mapToTranslationOperations = (options) => {
  return options
    .map((option, index) => ({
      ...option,
      order: index,
    }))
    .filter((option) => option.active)
    .map((option) => ({
      code: option.value,
      order: option.order,
    }))
}

const getDefaultOptions = (initialValues, languagesOptions) => {
  const options = languagesOptions.map(({ label, code }) => {
    return {
      label,
      value: code,
      active: false,
      order: undefined,
    }
  })
  if (initialValues.length) {
    const reorderedOptions = [...options]
    initialValues.forEach((data) => {
      const fromIndex = findIndex(
        reorderedOptions,
        (o) => o.value === data.code
      )
      if (fromIndex > -1) {
        reorderedOptions[fromIndex].active = true
        reorderedOptions.splice(
          data.order,
          0,
          reorderedOptions.splice(fromIndex, 1)[0]
        )
      }
    })

    return reorderedOptions
  }

  return options
}

const UserLanguages = (props) => (
  <UserLanguagesView
    {...props}
    mapToTranslationOperations={mapToTranslationOperations}
    getDefaultOptions={getDefaultOptions}
  />
)

export default UserLanguages
