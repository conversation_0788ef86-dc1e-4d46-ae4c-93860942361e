@import "assets/styles/variables.scss";

.title {
  font-size: 12px;
  color: $text_second;
}

.languagesList {
  list-style-type: none;
  padding-left: 0;
  margin-top: 10px;
  margin-bottom: 0;
}

.languagesItem {
  height: 40px;
  border: 1px solid #d5dce0;
  display: flex;
  align-items: center;
  padding-left: 8px;
  cursor: move;
  font-size: 13px;

  &:not(:first-child) {
    margin-top: -1px;
  }

  .languagesCheckbox {
    border-left: 1px solid #d5dce0;
    height: 100%;
    display: flex;
    align-items: center;
    margin-left: 8px;
    padding-left: 8px;
  }
  :global(.ant-checkbox-wrapper) {
    span {
      font-size: 13px;
    }
  }
}
