@import "assets/styles/variables.scss";

.notifications-modal {
  .ant-input-affix-wrapper {
    .ant-input-prefix {
      font-size: 16px;
      left: 8px;
    }
  }

  animation-duration: 0s;
  user-select: none;

  .ant-modal-body.ant-modal-body {
    padding: 0 20px;
  }

  button.ant-modal-close {
    top: 16px;
    right: 10px;
  }

  .ant-modal-close-x {
    font-size: 20px;
    height: unset;
    line-height: unset;
    width: unset;
  }
  .ant-select,
  .ant-input-affix-wrapper {
    width: 165px;
    color: $text_second;
    @media all and (min-width: 320px) and (max-width: 375px) {
      width: 130px;
    }

    @media all and (min-width: 375px) and (max-width: 425px) {
      width: 158px;
    }

    @media all and (min-width: 425px) and (max-width: 768px) {
      width: 183px;
    }

    @media all and (min-width: 768px) and (max-width: 1024px) {
      width: 165px;
    }
    .ant-select-selection,
    .ant-input {
      font-size: 12px;
    }
  }

  &.no-data {
    .ant-modal-footer {
      border: none;
    }
  }
}

@media (max-width: $xs) {
  .notifications-modal {
    padding-bottom: 0;
    top: 0;

    .ant-modal-body.ant-modal-body {
      display: flex;
      flex-direction: column;
      padding-top: 16px;
      @media (max-width: 768px) {
        padding: 0 10px;
      }
    }

    .ant-modal-header.ant-modal-header {
      padding: 15px 10px;
    }

    .ant-modal-content.ant-modal-content {
      display: flex !important;
      flex-direction: column;
    }
  }
}
