import { connect } from "react-redux"

import staffActions from "actions/staffActions"

import {
  userInformationSelector,
  formInitialValueSelector,
} from "selectors/UserProfileSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import UserProfileTabs from "./UserProfileTabs"

const { getUserById } = staffActions

const mapStateToProps = (state) => {
  const { personalInformation, activityInformation } =
    userInformationSelector(state)
  return {
    fromInitialValue: formInitialValueSelector(state),
    pagePermissions: permissionsSelector(state),
    userInfo: [...personalInformation, ...activityInformation],
  }
}

const mapDispatchToProps = (dispatch) => {
  return {
    getUser: (params, isEditMode) =>
      dispatch(getUserById({ params, isEditMode })),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(UserProfileTabs)
