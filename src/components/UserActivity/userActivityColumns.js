import React from "react"
import { Icon, Typography } from "@develop/fe-library"

import FormattedMessage from "components/FormattedMessage"
import classes from "components/UserActivity/userActivity.module.scss"

import { convertToLocalDateTime } from "utils/dateConverter"
import l from "utils/intl"

const userActivityColumns = [
  {
    title: "User",
    dataIndex: "userName",
    key: "user",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
    onHeaderCell: () => ({
      style: { textAlign: "left" },
    }),
    width: 200,
  },
  {
    title: "Entity",
    dataIndex: ["source", "entityId"],
    key: "entity",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
    onHeaderCell: () => ({
      style: { textAlign: "left" },
    }),
    render: (text, { entityLabel }) => entityLabel,
    width: 200,
  },
  {
    title: "Entity ID",
    dataIndex: "entityId",
    key: "entityId",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
    onHeaderCell: () => ({
      style: { textAlign: "left" },
    }),
    width: 120,
    render: (entityId) => entityId ?? l("N/A"),
  },
  {
    title: "Action type",
    dataIndex: "actionLabel",
    key: "type",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
    onHeaderCell: () => ({
      style: { textAlign: "left" },
    }),
    width: 200,
    render: (text) => {
      return <FormattedMessage id={text} />
    },
  },
  {
    title: "Date",
    dataIndex: ["source", "created_at"],
    key: "date",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
    onHeaderCell: () => ({
      style: { textAlign: "left" },
    }),
    render: (text, { source: { created_at } }) => (
      <div className={classes.dateColumns}>
        <Icon
          className={classes.calendarIcon}
          name="icnCalendar"
          size="--icon-size-3"
        />
        {convertToLocalDateTime(created_at, true)}
      </div>
    ),
    width: 200,
  },
  {
    title: "Object group",
    dataIndex: ["source", "object_group"],
    key: "objectGroup",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
    onHeaderCell: () => ({
      style: { textAlign: "left" },
    }),
    render: (text) => {
      const capitalizedText = text.charAt(0).toUpperCase() + text.slice(1)

      return <FormattedMessage id={capitalizedText} />
    },
    width: 200,
  },
  {
    title: "Current value",
    dataIndex: ["source", "created_at"],
    key: "current",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
    onHeaderCell: () => ({
      style: { textAlign: "left" },
    }),
    render: (_, { changeList, expanded, fieldsCount }) => (
      <>
        {expanded ? (
          <div className={classes.changesContainer}>
            <div className={classes.listContainer}>
              <Typography
                className={classes.listTitle}
                color="--color-text-placeholders"
                variant="--font-body-text-9"
              >
                <FormattedMessage id="Field" />
              </Typography>
              <ul className={classes.list}>
                {changeList.map(({ label }) => (
                  <li key={label} className={classes.listItem}>
                    <Typography variant="--font-body-text-9">
                      {label}
                    </Typography>
                  </li>
                ))}
              </ul>
            </div>
            <div className={classes.listContainer}>
              <Typography
                className={classes.listTitle}
                color="--color-text-placeholders"
                variant="--font-body-text-9"
              >
                <FormattedMessage id="Previous value" />
              </Typography>
              <ul className={classes.list}>
                {changeList.map(({ prev, label }) => (
                  <li key={label} className={classes.listItem}>
                    <Typography variant="--font-body-text-9">{prev}</Typography>
                  </li>
                ))}
              </ul>
            </div>
            <div className={classes.listContainer}>
              <Typography
                className={classes.listTitle}
                color="--color-text-placeholders"
                variant="--font-body-text-9"
              >
                <FormattedMessage id="Current value" />
              </Typography>
              <ul className={classes.list}>
                {changeList.map(({ curr, label }) => (
                  <li key={label} className={classes.listItem}>
                    <Typography variant="--font-body-text-9">{curr}</Typography>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ) : (
          <FormattedMessage
            defaultMessage="{count, plural, one {Changed # field} other {Changed # fields}}"
            id="{count, plural, one {Changed # field} other {Changed # fields}}"
            values={{ count: fieldsCount }}
          />
        )}
      </>
    ),
    width: 1000,
  },
]

export default userActivityColumns
