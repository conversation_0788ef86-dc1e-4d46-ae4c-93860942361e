import { useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import debounce from "lodash/debounce"

import gridActions from "actions/gridActions"
import staffActions from "actions/staffActions"
import userActivityActions from "actions/userActivityActions"

import {
  userActivityItemsSelector,
  userActivitySearchOptionsSelector,
  userActivityTotalCountSelector,
} from "selectors/userActivitySelectors"

// @ts-ignore
const { get: getUserActivityData, getEntityList } = userActivityActions
// @ts-ignore
const { getAll: getStaff } = staffActions
// @ts-ignore
const { pushUrl } = gridActions

export const useUserActivity = () => {
  const [expandedItems, setExpandedItems] = useState([])

  const dataSource = useSelector(userActivityItemsSelector)
  const searchOptions = useSelector(userActivitySearchOptionsSelector)
  const totalCount = useSelector(userActivityTotalCountSelector)

  const transformedDataSource = dataSource.map((item: any) => ({
    ...item,
    // @ts-ignore
    expanded: expandedItems.includes(item.id),
  }))

  const dispatch = useDispatch()

  const getUserActivity = <Type,>(searchOptions: Type): void => {
    dispatch(
      getUserActivityData({
        ...searchOptions,
      })
    )
  }

  const getAdditionalData = (): void => {
    dispatch(getStaff(false, 1))
    dispatch(getEntityList())
  }

  const clearFiltersHandler = () => {
    return dispatch(pushUrl({ page: 1, pageSize: searchOptions?.pageSize }))
  }

  const toggleItemHandler = ({ id }: { id: number | string }): void => {
    const newExpandedItems = [...expandedItems]
    const itemIndex = expandedItems.findIndex((item) => item === id)

    if (itemIndex === -1) {
      // @ts-ignore
      newExpandedItems.push(id)
    } else {
      newExpandedItems.splice(itemIndex, 1)
    }

    setExpandedItems(newExpandedItems)
  }

  const changeFilterHandler = debounce(<Type,>(params: Type) => {
    const newSearchOptions = {
      ...searchOptions,
      page: 1,
    }

    // @ts-ignore
    params.forEach(({ key, value }) => {
      if (key) {
        newSearchOptions[key] = value
        if (!newSearchOptions[key]) {
          delete newSearchOptions[key]
        }
      }
    })
    dispatch(pushUrl(newSearchOptions))
  }, 500)

  return {
    transformedDataSource,
    searchOptions,
    totalCount,
    expandedItems,
    getUserActivity,
    getAdditionalData,
    changeFilterHandler,
    toggleItemHandler,
    clearFiltersHandler,
  }
}
