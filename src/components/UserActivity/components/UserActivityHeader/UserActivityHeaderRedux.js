import { connect } from "react-redux"

import UserActivityHeaderView from "./UserActivityHeaderView"
import selectedRoleActions from "actions/selectedRoleActions"
import { customerOptionsSelector } from "selectors/customerSelectors"
import customerActions from "actions/customerActions"
import productGroupsActions from "actions/productGroupsActions"
import optimizationTemplatesActions from "actions/optimizationTemplatesActions"
import translationsActions from "actions/translationsActions"

import { userActivityEntitiesSelector } from "selectors/userActivitySelectors"

const { getAll: getCustomers } = customerActions
const { getAll: getOptimizationTemplates } = optimizationTemplatesActions
const { getAll: getProductGroups } = productGroupsActions
const { getAll: getAccessRoles } = selectedRoleActions
const { getForFilter: getTranslations } = translationsActions

const mapStateToProps = (state) => {
  const {
    customer: { pageOptions },
    optimizationTemplates: { optimizationTemplatesOptions },
    productGroups: { productGroupsOptions },
    selectedRole: { list },
    translations: { itemsForFilter },
  } = state

  return {
    accessRoles: list.data || list,
    customerPageOptions: pageOptions,
    customers: customerOptionsSelector(state),
    entitiesOptions: userActivityEntitiesSelector(state),
    messageOptions: itemsForFilter,
    optimizationTemplatesOptions,
    productGroupsOptions,
  }
}

export default connect(mapStateToProps, {
  getAccessRoles,
  getCustomers,
  getOptimizationTemplates,
  getProductGroups,
  getTranslations,
})(UserActivityHeaderView)
