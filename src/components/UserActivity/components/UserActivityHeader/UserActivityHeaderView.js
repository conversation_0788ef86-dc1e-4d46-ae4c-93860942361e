import React, { useCallback, useEffect, useMemo, useState } from "react"
import {
  Box,
  Icon,
  Select,
  Tag,
  TextInput,
  Typography,
} from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import debounce from "lodash.debounce"
import moment from "moment"
import PropTypes from "prop-types"

import DatePicker from "components/shared/DatePicker"
import DropdownWithFilter from "components/shared/DropdownWithFilter"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import ProductFilterModal from "../ProductFilterModal"
import UserFilterModal from "../UserFilterModal"

import styles from "./userActivityHeader.module.scss"

const inputWidth = "200px"

const commonIconBoxProps = {
  align: "center",
  gap: "m",
  cursor: "pointer",
}

const commonIconProps = {
  size: "--icon-size-6",
  color: "--color-icon-static",
  isHovered: true,
}

const clearLabels = {
  amazon_product: { plural: "products selected", single: "product selected" },
  staff: { plural: "user entities", single: "user entity" },
  user: { plural: "user entities", single: "user entity" },
}

const UserActivityHeaderView = ({
  accessRoles,
  customerPageOptions: { currentPage, totalCount },
  customers,
  defaultFilterValues,
  entitiesOptions,
  getAccessRoles,
  getCustomers,
  getOptimizationTemplates,
  getProductGroups,
  getTranslations,
  messageOptions: {
    currentPage: messageCurrentPage,
    data: messageItems,
    totalCount: messageTotalCount,
  },
  optimizationTemplatesOptions,
  onChangeFilter,
  onClearFilters,
  productGroupsOptions,
}) => {
  const [productFilterVisible, setProductFilterVisible] = useState(false)
  const [userFilterVisible, setUserFilterVisible] = useState(false)
  const [userEntityFilterVisible, setUserEntityFilterVisible] = useState(false)
  const [customerFilter, setCustomerFilter] = useState("")
  const [messageFilter, setMessageFilter] = useState("")
  const {
    dateFrom,
    dateTo,
    entityId = "",
    userId = "",
    entity = null,
    objectGroup = null,
  } = defaultFilterValues

  const onToggleUserFilter = useCallback(
    () => setUserFilterVisible(!userFilterVisible),
    [setUserFilterVisible, userFilterVisible]
  )
  const onToggleUserEntityFilter = useCallback(
    () => setUserEntityFilterVisible(!userEntityFilterVisible),
    [setUserEntityFilterVisible, userEntityFilterVisible]
  )
  const onToggleProductFilter = useCallback(
    () => setProductFilterVisible(!productFilterVisible),
    [productFilterVisible, setProductFilterVisible]
  )
  const searchCustomers = useMemo(
    () => debounce(getCustomers, 500),
    [getCustomers]
  )
  const searchTranslations = useMemo(
    () => debounce(getTranslations, 500),
    [getTranslations]
  )
  const hasClearIcon = useMemo(
    () =>
      defaultFilterValues.page
        ? getObjectKeys(defaultFilterValues).length > 3
        : getObjectKeys(defaultFilterValues).length > 2,
    [defaultFilterValues]
  )

  const sortedEntitiesOptions = entitiesOptions.sort(
    ({ label }, { label: labelB }) => label.localeCompare(labelB)
  )
  const handleChangeMultiSelectEntityId = (selectedOptions) => {
    const value = selectedOptions.map(({ value }) => value).join(",")

    onChangeFilter([{ key: "entityId", value }])
  }

  const handleClearEntityId = () => {
    onChangeFilter([{ key: "entityId", value: undefined }])
  }

  const handleChangeTextEntityId = (value) => {
    onChangeFilter([{ key: "entityId", value: value }])
  }

  const handleChangeEntity = (selectedOption) => {
    if (!selectedOption) {
      onChangeFilter([
        { key: "entity", value: undefined },
        { key: "entityId", value: undefined },
      ])

      return
    }
    const { value } = selectedOption

    onChangeFilter([
      { key: "entity", value },
      { key: "entityId", value: undefined },
    ])
  }

  const handleChangeObjectGroup = (selectedOption) => {
    if (!selectedOption) {
      onChangeFilter([{ key: "objectGroup", value: undefined }])

      return
    }
    const { value } = selectedOption

    onChangeFilter([{ key: "objectGroup", value }])
  }

  const multiSelectEntityId = (() => {
    const entityIdArray = entityId.split(",")

    if (checkIsArray(entityIdArray)) {
      return entityIdArray
    }

    if (entityId) {
      return [entityId]
    }

    return []
  })()

  const clearLabel = entity ? clearLabels[entity] : null

  const hasClearLabel = entityId && clearLabel

  const customFilters = {
    access_role: (
      <Box align="center" width={inputWidth}>
        <Select
          hasClearIcon
          isFullWidth
          isMultiSelect
          label={l("Select access role")}
          value={multiSelectEntityId}
          options={accessRoles?.map(({ id, title }) => ({
            label: title,
            value: id,
          }))}
          onChange={handleChangeMultiSelectEntityId}
          onClear={handleClearEntityId}
        />
      </Box>
    ),
    amazon_product: (
      <Box {...commonIconBoxProps} onClick={onToggleProductFilter}>
        <Icon name="icnProfile" {...commonIconProps} />
        <Typography variant="--font-body-text-7">
          {l("Product list")}
        </Typography>
      </Box>
    ),
    customer: (
      <DropdownWithFilter
        allowClear
        className={styles.viewSelect}
        currentPage={currentPage}
        filterField="name"
        filterValue={customerFilter}
        placeholder={l("Select customer")}
        selectedValue={entityId}
        totalCount={totalCount}
        value={entityId}
        items={customers.map(({ id, title }) => ({
          id,
          name: `(${id}) ${title}`,
        }))}
        optionComponent={({ name }) => (
          <Typography className={styles.label} variant="menuItem">
            {name}
          </Typography>
        )}
        onClear={handleClearEntityId}
        onChange={({ id }) => {
          onChangeFilter([{ key: "entityId", value: `${id}` }])

          if (customerFilter || currentPage !== 1) {
            setCustomerFilter("")
            searchCustomers("", 1)
          }
        }}
        onClose={() => {
          if (customerFilter || currentPage !== 1) {
            setCustomerFilter("")
            searchCustomers("", 1)
          }
        }}
        onPageChange={(page) => {
          searchCustomers(customerFilter, page)
        }}
        onSearch={(value) => {
          setCustomerFilter(value)
          searchCustomers(value, 1)
        }}
      />
    ),
    message_translation: (
      <DropdownWithFilter
        allowClear
        className={styles.viewSelect}
        currentPage={messageCurrentPage}
        filterClassName={styles.filterClassName}
        filterField="message"
        filterValue={messageFilter}
        placeholder={l("Select message")}
        selectedValue={entityId}
        totalCount={messageTotalCount}
        value={entityId}
        items={messageItems.map(({ id, message }) => ({
          id,
          message,
        }))}
        optionComponent={({ message }) => (
          <Typography className={styles.label} variant="menuItem">
            {message}
          </Typography>
        )}
        onClear={handleClearEntityId}
        onChange={({ id }) => {
          onChangeFilter([{ key: "entityId", value: `${id}` }])

          if (messageFilter || messageCurrentPage !== 1) {
            setMessageFilter("")
            searchTranslations({ message: "", page: 1 })
          }
        }}
        onClose={() => {
          if (messageFilter || messageCurrentPage !== 1) {
            setMessageFilter("")
            searchTranslations({ message: "", page: 1 })
          }
        }}
        onPageChange={(page) => {
          searchTranslations({ message: messageFilter, page })
        }}
        onSearch={(value) => {
          setMessageFilter(value)
          searchTranslations({ message: value, page: 1 })
        }}
      />
    ),
    lost_case: (
      <Box align="center" width={inputWidth}>
        <TextInput
          isFullWidth
          defaultValue={entityId}
          label={l("Lost case ID")}
          onChange={handleChangeTextEntityId}
        />
      </Box>
    ),
    lost_case_template: (
      <Box align="center" width={inputWidth}>
        <TextInput
          isFullWidth
          defaultValue={entityId}
          label={l("Lost case template ID")}
          onChange={handleChangeTextEntityId}
        />
      </Box>
    ),
    optimization_template: (
      <Box align="center" width={inputWidth}>
        <Select
          hasClearIcon
          isFullWidth
          isMultiSelect
          label={l("Select optimization template")}
          value={multiSelectEntityId}
          options={optimizationTemplatesOptions?.map(({ id, title }) => ({
            label: title,
            value: id,
          }))}
          onChange={handleChangeMultiSelectEntityId}
          onClear={handleClearEntityId}
        />
      </Box>
    ),
    product_group: (
      <Box align="center" width={inputWidth}>
        <Select
          hasClearIcon
          isFullWidth
          isMultiSelect
          label={l("Select product group")}
          value={multiSelectEntityId}
          options={productGroupsOptions?.map(({ id, title }) => ({
            label: title,
            value: id,
          }))}
          onChange={handleChangeMultiSelectEntityId}
          onClear={handleClearEntityId}
        />
      </Box>
    ),
    product_import: (
      <Box align="center" width={inputWidth}>
        <TextInput
          isFullWidth
          defaultValue={entityId}
          label={l("Product import ID")}
          onChange={handleChangeTextEntityId}
        />
      </Box>
    ),
    staff: (
      <Box {...commonIconBoxProps} onClick={onToggleUserEntityFilter}>
        <Icon name="icnUser" {...commonIconProps} />
        <Typography variant="--font-body-text-7">
          {l("User entities")}
        </Typography>
      </Box>
    ),
    user: (
      <Box {...commonIconBoxProps} onClick={onToggleUserEntityFilter}>
        <Icon name="icnUser" {...commonIconProps} />
        <Typography variant="--font-body-text-7">
          {l("User entities")}
        </Typography>
      </Box>
    ),
  }

  useEffect(() => {
    getAccessRoles()
  }, [getAccessRoles])

  useEffect(() => {
    getCustomers(undefined, undefined)
  }, [getCustomers])

  useEffect(() => {
    getProductGroups()
  }, [getProductGroups])

  useEffect(() => {
    getOptimizationTemplates()
  }, [getOptimizationTemplates])

  useEffect(() => {
    getTranslations()
  }, [getTranslations])

  return (
    <Box
      backgroundColor="--color-background-second"
      gap="l"
      hasBorder={{ top: true }}
      padding="m l"
      width="100%"
    >
      {hasClearIcon ? (
        <Box align="center" height="32px" justify="center">
          <Icon
            isHovered
            color="--color-icon-static"
            name="icnFilterClean"
            size="--icon-size-6"
            onClick={onClearFilters}
          />
        </Box>
      ) : null}

      <Box flexWrap="wrap" gap="l">
        <Box align="center" gap="m">
          <Box width={inputWidth}>
            <Select
              hasClearIcon
              hasSearch
              isContentWidthMatchedWithTrigger
              isFullWidth
              label={l("Log type")}
              options={sortedEntitiesOptions}
              value={entity}
              onChange={handleChangeEntity}
            />
          </Box>
        </Box>

        {entity ? customFilters[entity] : null}

        <Box {...commonIconBoxProps} onClick={onToggleUserFilter}>
          <Icon name="icnUser" {...commonIconProps} />
          <Typography variant="--font-body-text-7">{l("Users")}</Typography>
        </Box>

        <Box align="center" width={inputWidth}>
          <DatePicker
            isRange
            filterType="between"
            placeholder={l("Date range")}
            value={
              dateFrom && dateTo
                ? [
                    moment(dateFrom, "YYYY-MM-DD HH:mm:ss"),
                    moment(dateTo, "YYYY-MM-DD HH:mm:ss"),
                  ]
                : undefined
            }
            onChange={(momentDates, dates) => {
              const [dateFrom, dateTo] = dates

              onChangeFilter([
                { key: "dateFrom", value: dateFrom ? dateFrom : undefined },
                { key: "dateTo", value: dateTo ? dateTo : undefined },
              ])
            }}
          />
        </Box>

        <Box align="center" width={inputWidth}>
          <Select
            hasClearIcon
            isFullWidth
            label={l("Object group")}
            value={objectGroup}
            options={[
              { label: l("Default"), value: "default" },
              { label: l("User API"), value: "userAPI" },
            ]}
            onChange={handleChangeObjectGroup}
          />
        </Box>

        {userId ? (
          <Tag
            isDefault
            closeIconName="icnClose"
            size="m"
            name={l(
              "{count, plural, one {# user selected} other {# users selected}}",
              {
                count: userId.split(",").length,
              }
            )}
            onCloseTag={() => {
              onChangeFilter([{ key: "userId", value: undefined }])
            }}
          />
        ) : null}

        {hasClearLabel ? (
          <Tag
            isDefault
            closeIconName="icnClose"
            margin="s"
            size="m"
            name={l(
              `{count, plural, one {# ${clearLabel.single}} other {# ${clearLabel.plural}}}`,
              {
                count: entityId.split(",").length,
              }
            )}
            onCloseTag={handleClearEntityId}
          />
        ) : null}

        {userFilterVisible ? (
          <UserFilterModal
            defaultValue={userId}
            onCancel={onToggleUserFilter}
            onSelect={onChangeFilter}
          />
        ) : null}
        {userEntityFilterVisible ? (
          <UserFilterModal
            defaultValue={entityId}
            fieldKey="entityId"
            onCancel={onToggleUserEntityFilter}
            onSelect={onChangeFilter}
          />
        ) : null}
        {productFilterVisible ? (
          <ProductFilterModal
            defaultValue={entityId}
            onCancel={onToggleProductFilter}
            onSelect={onChangeFilter}
          />
        ) : null}
      </Box>
    </Box>
  )
}

UserActivityHeaderView.propTypes = {
  accessRoles: PropTypes.array.isRequired,
  customerPageOptions: PropTypes.object.isRequired,
  customers: PropTypes.array.isRequired,
  defaultFilterValues: PropTypes.object.isRequired,
  entitiesOptions: PropTypes.object.isRequired,
  getAccessRoles: PropTypes.func.isRequired,
  getCustomers: PropTypes.func.isRequired,
  getOptimizationTemplates: PropTypes.func.isRequired,
  getProductGroups: PropTypes.func.isRequired,
  getTranslations: PropTypes.func.isRequired,
  messageOptions: PropTypes.object.isRequired,
  onChangeFilter: PropTypes.func.isRequired,
  onClearFilters: PropTypes.func.isRequired,
  productGroupsOptions: PropTypes.array.isRequired,
}

export default UserActivityHeaderView
