import React from "react"
import cn from "classnames"

import { UsersTableSettings } from "initialState/tableSettings"

import { InviteUserModal } from "components/InviteUserModal"
import { ConfirmModal } from "components/shared/Modal"
import { TableWrapper } from "components/shared/TableWrapper"
import ExportValue from "components/TableGridLayout/components/ExportValue"

import { DELETE_ITEM } from "utils/confirm"
import l from "utils/intl"

import { permissionKeys } from "consts"
import { USER_ROLE_SIDES, USER_ROLES } from "consts/user"

import { useUsersList } from "./hooks"

import getTableColumns from "./columnsConfig"
import TwoFactorIcon from "./components/TwoFactorIcon"

import styles from "./usersList.module.scss"

const PAGE_NAME = "Users"

export const UsersList = () => {
  const {
    dataSource,
    searchOptions,
    filtersOptions,
    totalCount,
    visible,
    userID,
    role,
    restSearchOptions,
    permissionManage,
    permissionView,
    canEditRole,
    canAuthorize,
    canManage2FA,
    confirmDisable2FA,
    getAllStaffHandler,
    getAdditionalData,
    setVisible,
    saveCellHandler,
    buildDisableTwoFactorIconHandler,
    deleteIconHandler,
    updateIconHandler,
    loginHandler,
    loadDataHandler,
    resentInviteToEmailHandler,
    cancelConfirmModalHandler,
    createButtonHandler,
    okConfirmModalHandler,
  } = useUsersList()

  const createButton = {
    checkAvailability: () => permissionManage,
    icon: "icnPlus",
    onClick: createButtonHandler,
    title: "Create",
    type: "primary",
  }

  const rowIcons = [
    {
      name: "icnEdit",
      checkAvailability: () => permissionView || permissionManage,
      onClick: updateIconHandler,
      title: "Update",
    },
    {
      name: "icnDeleteOutlined",
      confirm: {
        template: DELETE_ITEM,
      },
      checkAvailability: () => permissionManage,
      onClick: deleteIconHandler,
      title: "Delete",
    },
    {
      // @ts-ignore
      checkAvailability: ({ id, user: { role: userRole = "", roleSide } }) =>
        canAuthorize &&
        id !== userID &&
        userRole?.toLowerCase() !== USER_ROLES.superAdmin &&
        (role === USER_ROLES.superAdmin ||
          roleSide !== USER_ROLE_SIDES.sellerLogic),
      confirm: {
        title: l("Login as another user"),
      },
      name: "icnLogin",
      title: l("Login"),
      onClick: loginHandler,
    },
    {
      // @ts-ignore
      checkAvailability: ({ user }) => {
        const { roleCode, email_confirmed } = user
        const isInvited =
          roleCode === USER_ROLES.invitedUser ||
          roleCode === USER_ROLES.invitedSlUser ||
          roleCode === USER_ROLES.invitedTranslatorUser

        return isInvited && email_confirmed === 0
      },
      name: "icnSync",
      viewPermission: permissionKeys.userManage,
      confirm: {
        title: l("Re-send invitation"),
        message: l(
          "You are about to re-send an invitation to this user. The previous invitation link will remain valid as well. Are you sure you want to proceed?"
        ),
        okText: l("Confirm"),
      },
      onClick: resentInviteToEmailHandler,
      title: l("Re-send invitation"),
      placement: "left",
    },
  ]

  return (
    <>
      <TableWrapper
        // @ts-ignore
        isNeedSort
        actionsColumn={canEditRole}
        actionsColumnWidth={160}
        className={cn("gridView", styles.table)}
        componentTableSettings={UsersTableSettings}
        customDefaultFilterValues={restSearchOptions}
        dataSource={dataSource}
        getAdditionalData={getAdditionalData}
        getData={getAllStaffHandler}
        pageTableSettings={UsersTableSettings}
        searchOptions={searchOptions}
        selectFiltersOptions={filtersOptions}
        tableButtons={[createButton]}
        tableGridTitle={PAGE_NAME}
        tableHeaderTitle={PAGE_NAME}
        tableIcons={rowIcons}
        totalCount={totalCount}
        tableColumns={[
          ...getTableColumns({
            canEditRole,
          }),
          {
            title: "Two-factor authentication",
            dataIndex: ["user", "is_enabled_2fa"],
            key: "is_enabled_2fa",
            sorter: true,
            type: "selectWithFilter",
            width: 200,
            // @ts-ignore
            render: (text, { user }) => (
              <>
                <TwoFactorIcon
                  canManage={canManage2FA}
                  isActive={user.is_enabled_2fa}
                  onDisable={buildDisableTwoFactorIconHandler(user)}
                />
                <ExportValue hiden>{user.is_enabled_2fa + ""}</ExportValue>
              </>
            ),
          },
        ]}
        onSaveCell={saveCellHandler}
      />
      {/* @ts-ignore */}
      <InviteUserModal
        isNotCustomer
        okText={l("Invite")}
        setVisible={setVisible}
        visible={visible}
        onSubmit={loadDataHandler}
      />
      {/* @ts-ignore */}
      <ConfirmModal
        className={styles.confirm2faModale}
        message={l(
          "The 2 Factor Authentication will be turned off for this user. Are you sure you want to proceed?"
        )}
        onCancel={cancelConfirmModalHandler}
        onOk={okConfirmModalHandler}
        // @ts-ignore
        visible={confirmDisable2FA.visible}
      />
    </>
  )
}
