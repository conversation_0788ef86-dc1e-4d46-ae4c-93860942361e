import { connect } from "react-redux"
import staffActions from "actions/staffActions"
import { AllowLoginColumnView } from "./AllowLoginColumnView"

const { update } = staffActions

const mapDispatchToProps = (dispatch) => ({
  updateStaff: ({ queryParams, payload }, successCallback, failureCallback) =>
    dispatch(
      update({ queryParams, payload }, successCallback, failureCallback)
    ),
})

export const AllowLoginColumn = connect(
  null,
  mapDispatchToProps
)(AllowLoginColumnView)
