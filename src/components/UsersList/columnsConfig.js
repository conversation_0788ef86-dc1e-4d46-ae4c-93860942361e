import React from "react"
import { ROUTES } from "@develop/fe-library/dist/routes"

import Typography from "components/Typography"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import Link from "components/shared/Link"
import { AllowLoginColumn } from "./components/AllowLoginColumn"

import {
  COLUMN_INPUT_TYPE_DATE_RANGE,
  COLUMN_INPUT_TYPE_SELECT_WITH_FILTER,
  COLUMN_INPUT_TYPE_SELECT,
} from "components/TableGridLayout/TableGridLayoutView"
import { USER_ROLES, SALUTATIONS } from "consts/user"

import l from "utils/intl"
import { convertToLocalDate } from "utils/dateConverter"

export default ({ canEditRole }) => [
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
    sorter: true,
    type: "input",
    width: 50,
    align: "left",
  },
  {
    title: "Salutation",
    dataIndex: "salutation",
    key: "salutation",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT,
    width: 120,
    min: 120,
    align: "left",
    render: (value) => (
      <ExportValue>{l(SALUTATIONS?.[value]) || ""}</ExportValue>
    ),
  },
  {
    title: "First Name",
    dataIndex: "firstname",
    key: "firstname",
    sorter: true,
    type: "input",
    width: 200,
    align: "left",
  },
  {
    title: "Last name",
    dataIndex: "lastname",
    key: "lastname",
    sorter: true,
    type: "input",
    width: 200,
    align: "left",
  },
  {
    title: "Phone",
    dataIndex: "phone",
    key: "phone",
    sorter: true,
    type: "input",
    width: 200,
    align: "left",
    render: (text) => (
      <Typography type="span" variant="textSmall">
        <ExportValue>
          <Link
            internal={false}
            url={`tel:${text}`}
            text={text}
            variant="textSmall"
            styleType="primary"
          />
        </ExportValue>
      </Typography>
    ),
  },
  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    sorter: true,
    type: "input",
    align: "left",
    width: 200,
  },
  {
    title: "Role",
    dataIndex: ["user", "role"],
    key: "role",
    sorter: true,
    type: "selectWithFilter",
    width: 200,
    render: (text, item) => {
      const { id, user: { email_confirmed, roleCode } = {} } = item || {}
      const isInvitedUser =
        roleCode === USER_ROLES.invitedUser ||
        roleCode === USER_ROLES.invitedSlUser ||
        roleCode === USER_ROLES.invitedTranslatorUser

      const roleContent = canEditRole ? (
        <Link
          type="span"
          internal
          url={`${ROUTES.ADMIN_ROUTES.PATH_USERS}/${id}#roleAdministration`}
          text={text}
          variant="textSmall"
          styleType="primary"
        />
      ) : (
        text
      )

      if (isInvitedUser) {
        return (
          <>
            <ExportValue>{roleContent}</ExportValue>
            <br />
            <ExportValue>
              {l(!email_confirmed ? "Invitation sent" : "Accepted")}
            </ExportValue>
          </>
        )
      }

      return <ExportValue>{roleContent}</ExportValue>
    },
  },
  {
    title: "Customer",
    dataIndex: "customer_id",
    key: "customer_id",
    sorter: true,
    type: "input",
    width: 200,
    render: (text) =>
      text && (
        <Typography type="span" variant="textSmall">
          <ExportValue>
            <Link
              internal={false}
              url={`${ROUTES.GENERAL_ROUTES.PATH_AMAZON_ACCOUNTS}?customerID=${text}`}
              text={text}
              variant="textSmall"
              styleType="primary"
              target="_blank"
            />
          </ExportValue>
        </Typography>
      ),
  },
  {
    title: "Allow login",
    dataIndex: "allowAppLoginLable",
    key: "allow_app_login",
    width: 200,
    sorter: true,
    type: "selectWithFilter",
    render: (allow_app_login, user) => {
      return <AllowLoginColumn allowAppLogin={allow_app_login} user={user} />
    },
    permission: "userAllowLogin",
  },
  {
    title: "Supported languages",
    dataIndex: "translationAccessOperations",
    key: "translationAccessOperations",
    sorter: true,
    type: "multySelect",
    width: 200,
    render: (translationAccessOperations) => (
      <Typography type="span" variant="textSmall">
        <ExportValue>
          {translationAccessOperations
            .map((obj) => obj.language_code.toUpperCase())
            .join(", ")}
        </ExportValue>
      </Typography>
    ),
  },
  {
    title: "Profile language",
    dataIndex: "language_id",
    key: "language_id",
    sorter: true,
    type: "select",
    width: 200,
    render: (language_id, customer) => {
      const { language } = customer
      const title = language?.code.toUpperCase()
      return (
        <Typography type="span" variant="textSmall">
          <ExportValue>{title}</ExportValue>
        </Typography>
      )
    },
  },
  {
    title: "Date created",
    dataIndex: ["user", "registration_time"],
    key: "registration_time",
    width: 90,
    min: 80,
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE_RANGE,
    withDayTime: true,
    ellipsis: true,
    render: (date_created) => (
      <ExportValue>
        {date_created && convertToLocalDate(date_created)}
      </ExportValue>
    ),
  },
  {
    title: "Email confirmed",
    dataIndex: "emailConfimedLabel",
    key: "email_confirmed",
    sorter: true,
    type: "selectWithFilter",
    editable: {
      checkIfEditable: ({ email_confirmed }) => email_confirmed === 1,
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Update email confirmed",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "userManage",
  },
  {
    title: "Demo registration",
    dataIndex: ["user", "is_demo"],
    key: "is_demo",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT_WITH_FILTER,
    render: (isDemo) => {
      const label = !!isDemo ? "Yes" : "No"

      return l(label)
    },
  },
]
