import React from "react"
import FormattedMessage from "components/FormattedMessage"
import { Select } from "antd"
import PropTypes from "prop-types"
import cn from "classnames"

import { Typography } from "@develop/fe-library"
import Search from "./components/Search"
import {
  PrimaryButton,
  RestrictedButtonPopover,
} from "components/shared/Buttons"
import SortableSelect from "components/shared/SortableSelect"
import l from "utils/intl"

import styles from "components/CardsLayoutTitle/cardsLayoutTitle.module.scss"

const { Option } = Select

const CardsLayoutTitleView = ({
  buttons,
  defaultSortOption,
  initialValues,
  onAdd,
  isAdditionalAddButtonStyle = false,
  addTitle,
  onSearch,
  onSort,
  searchFields,
  disableSortTranslation,
  sortOptions,
  sortTip,
  title,
  isRightToolsAlign,
  managePermission,
  popoverMessage,
}) => {
  const onSortByDirection = (sort, direction) => {
    onSort(sort && direction === "down" ? `-${sort}` : sort)
  }
  return (
    <div className={styles.container}>
      <Typography className={styles.title} variant="--font-headline-1">
        <FormattedMessage id={title} />
      </Typography>
      <div className={styles.toolbarContainer}>
        <Search
          controls={searchFields}
          initialValues={initialValues}
          onSubmit={onSearch}
        />
        <div
          className={cn(styles.rightTools, {
            [styles.positionRight]: isRightToolsAlign,
          })}
        >
          {sortOptions && (
            <SortableSelect
              allowClear
              className={styles.sortSelect}
              defaultValue={defaultSortOption}
              value={defaultSortOption}
              onChange={onSortByDirection}
              placeholder={l("Sort by")}
            >
              {sortOptions.map(({ value, label }) => (
                <Option key={value} value={value}>
                  {disableSortTranslation ? (
                    label
                  ) : (
                    <FormattedMessage id={label} />
                  )}
                </Option>
              ))}
            </SortableSelect>
          )}
          {buttons.map(({ icon, onClick, title }) => (
            <PrimaryButton
              className={styles.button}
              icon={icon}
              key={title}
              onClick={onClick}
            >
              <FormattedMessage id={title} />
            </PrimaryButton>
          ))}
          {onAdd && (
            <RestrictedButtonPopover
              managePermission={managePermission}
              popoverMessage={popoverMessage}
              variant="primary"
              content={addTitle && l(addTitle)}
              className={cn(styles.addButton, {
                [styles.additionalButtonAdd]: isAdditionalAddButtonStyle,
              })}
              icon="icnPlus"
              iconOnly
              onClick={onAdd}
            />
          )}
        </div>
      </div>
      {sortTip && (
        <Typography variant="--font-body-text-7" color="--color-text-second">
          <FormattedMessage id="You may optionally enter a comparison operator (<, <=, >, >=, <> or =) at the beginning of each of your search values to specify how the comparison should be done" />
        </Typography>
      )}
    </div>
  )
}

CardsLayoutTitleView.propTypes = {
  buttons: PropTypes.array,
  defaultSortOption: PropTypes.string,
  initialValues: PropTypes.object.isRequired,
  onAdd: PropTypes.func,
  onSearch: PropTypes.func.isRequired,
  onSort: PropTypes.func,
  searchFields: PropTypes.array.isRequired,
  sortOptions: PropTypes.array,
  sortTip: PropTypes.bool,
  title: PropTypes.string.isRequired,
}

CardsLayoutTitleView.defaultProps = {
  buttons: [],
  sortTip: true,
}

export default CardsLayoutTitleView
