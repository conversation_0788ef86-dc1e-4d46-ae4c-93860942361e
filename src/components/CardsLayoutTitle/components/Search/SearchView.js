import React from "react"
import PropTypes from "prop-types"
import { Select } from "antd"
import cn from "classnames"
import FormattedMessage from "components/FormattedMessage"
import { Field, Form } from "formik"

import {
  SelectField,
  TextField,
  DateInput,
  RangePickerField,
} from "components/shared/FormFields"

import l from "utils/intl"

import styles from "./search.module.scss"
import { PrimaryButton } from "components/shared/Buttons"

const { Option } = Select

const SearchView = ({ controls }) => (
  <Form className={styles.form} noValidate>
    <div className={styles.container}>
      {controls.map(
        ({ name, onSearch, options, placeholder, type, ...otheProps }) => {
          switch (type) {
            case "text": {
              return (
                <Field
                  className={styles.textField}
                  component={TextField}
                  key={name}
                  name={name}
                  placeholder={l(placeholder)}
                  autoComplete="off"
                  allowClear
                  {...otheProps}
                />
              )
            }
            case "select": {
              return (
                <Field
                  allowClear
                  className={styles.selectField}
                  component={SelectField}
                  key={name}
                  name={name}
                  placeholder={l(placeholder)}
                  {...otheProps}
                >
                  {options.map(({ value, label }) => (
                    <Option className={styles.option} key={value} value={value}>
                      {l(label)}
                    </Option>
                  ))}
                </Field>
              )
            }
            case "selectWithSearch": {
              return (
                <Field
                  allowClear
                  className={cn(
                    styles.selectField,
                    styles.selectFieldWithSearch
                  )}
                  component={SelectField}
                  defaultActiveFirstOption={false}
                  filterOption={false}
                  key={name}
                  name={name}
                  notFoundContent={null}
                  placeholder={l(placeholder)}
                  onSearch={onSearch}
                  showArrow={false}
                  showSearch
                  {...otheProps}
                >
                  {options.map(({ value, label }) => (
                    <Option className={styles.option} key={value} value={value}>
                      {label}
                    </Option>
                  ))}
                </Field>
              )
            }
            case "date": {
              return (
                <Field
                  isFilter={true}
                  withDayTime={true}
                  className={styles.textField}
                  component={DateInput}
                  key={name}
                  name={name}
                  placeholder={l(placeholder)}
                  {...otheProps}
                />
              )
            }
            case "dateRange": {
              return (
                <Field
                  isFilter={true}
                  withDayTime={true}
                  className={cn(styles.textField, styles.rangePicker)}
                  component={RangePickerField}
                  convertUtcToUserTimeZone={true}
                  showTimeZoneToggle={true}
                  filterType="equal_to"
                  key={name}
                  name={name}
                  placeholder={l(placeholder)}
                  {...otheProps}
                />
              )
            }
            default:
              return null
          }
        }
      )}
      <PrimaryButton
        className={styles.button}
        htmlType="submit"
        icon="icnSearch"
      >
        <FormattedMessage id="Search" />
      </PrimaryButton>
    </div>
  </Form>
)

SearchView.propTypes = {
  controls: PropTypes.array.isRequired,
}

export default SearchView
