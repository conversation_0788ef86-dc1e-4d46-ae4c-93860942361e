@import "assets/styles/variables.scss";

.container {
  padding: 20px 20px 0;
}

.title.title {
  margin-bottom: 10px;
  font-size: 18px;
  line-height: 26px;
}

.toolbarContainer {
  margin-bottom: 15px;
  position: relative;
}

.toolbarContainer {
  align-items: flex-start;
  display: flex;
  justify-content: space-between;
}

.sortSelect {
  width: 170px;
  margin-bottom: 10px;
}

.addButton {
  margin-left: 20px;
}

.rightTools {
  margin-left: 20px;
  display: flex;
}

.button {
  align-items: center;
  display: flex;
  flex-shrink: 0;
  justify-content: space-between;

  :global(.anticon) {
    margin-right: 10px;
    color: inherit;
  }
}

@media (max-width: $lg) {
  .sortSelect {
    width: 150px;
  }

  .addButton {
    margin-left: 15px;
  }
}

@media (max-width: ($md - 1)) {
  .toolbarContainer {
    align-items: flex-start;
    flex-direction: column;
  }

  .rightTools {
    margin-left: 0;
    margin-top: 20px;
  }

  .sortSelect {
    width: 165px;
  }

  .addButton {
    margin-left: 10px;
  }

  .additionalButtonAdd.additionalButtonAdd {
    margin-left: 0;
  }
}

@media (max-width: $xs) {
  .rightTools {
    display: flex;
    width: 100%;
  }

  .sortSelect {
    flex-grow: 1;
    margin-right: 0;
  }

  .addButton {
    margin-left: 10px;
  }

  .rightTools {
    margin-top: 5px;
  }
}

@media (max-width: 385px) {
  .positionRight {
    width: auto;
    position: absolute;
    right: 0;
    bottom: 10px;
  }
}
