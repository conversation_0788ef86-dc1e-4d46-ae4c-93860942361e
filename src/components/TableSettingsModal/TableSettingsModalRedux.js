import { connect } from "react-redux"

import TableSettingsModalContainer from "components/TableSettingsModal/TableSettingsModalContainer"

import tableSettingsActions from "actions/tableSettingsActions"
import {
  defaultSettingsSelector,
  settingsSelector,
} from "selectors/tableSettingsSelectors"

const { display, save } = tableSettingsActions

const mapStateToProps = (state) => {
  const {
    tableSettings: { visible },
  } = state

  return {
    settings: settingsSelector(state),
    defaultSettings: defaultSettingsSelector(state),
    visible,
  }
}

const mapDispatchToProps = (dispatch) => ({
  onClose: () => dispatch(display(undefined, false)),
  saveSettings: (settings, callback) => dispatch(save(settings, callback)),
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(TableSettingsModalContainer)
