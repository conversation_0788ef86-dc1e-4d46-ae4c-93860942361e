import React, { useMemo } from "react"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import Modal from "components/shared/Modal"
import TableSettings from "./components/TableSettings"

import { buildTableContentColumns } from "./utils"

import "components/TableSettingsModal/tableSettingsModal.scss"

const TableSettingsModalView = ({
  onClose,
  saveSettings,
  settings,
  defaultSettings,
  visible,
  displayBlockedTableColumns,
}) => {
  const tableColumnsContent = useMemo(() => {
    return buildTableContentColumns({
      userSettingTableColumns: settings,
      availableTableColumns: displayBlockedTableColumns,
    })
  }, [settings, displayBlockedTableColumns.length])

  const defaultTableColumnsContent = useMemo(() => {
    return buildTableContentColumns({
      userSettingTableColumns: defaultSettings,
      availableTableColumns: displayBlockedTableColumns,
    })
  }, [defaultSettings, displayBlockedTableColumns.length])

  if (!visible) {
    return null
  }

  return (
    <Modal
      title={<FormattedMessage id="Table content" />}
      visible
      footer={null}
      className="table-settings-modal"
      width={360}
      onCancel={onClose}
    >
      <TableSettings
        onClose={onClose}
        save={saveSettings}
        settings={tableColumnsContent}
        defaultSettings={defaultTableColumnsContent}
      />
    </Modal>
  )
}

TableSettingsModalView.propTypes = {
  onClose: PropTypes.func.isRequired,
  saveSettings: PropTypes.func.isRequired,
  settings: PropTypes.array,
  defaultSettings: PropTypes.array,
  visible: PropTypes.bool,
  displayBlockedTableColumns: PropTypes.array,
}

TableSettingsModalView.defaultProps = {
  visible: false,
}

export default TableSettingsModalView
