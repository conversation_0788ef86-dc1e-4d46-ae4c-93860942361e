import React from "react"
import { Formik } from "formik"
import PropTypes from "prop-types"

import TableSettingsView from "./TableSettingsView"

const TableSettingsFormik = ({ onClose, save, settings, defaultSettings }) => (
  <Formik
    initialValues={{ settings: settings }}
    onSubmit={(payload, actions) => {
      save(payload, () => {
        actions.setSubmitting(false)

        onClose()
      })
    }}
  >
    {(props) => (
      <TableSettingsView {...props} defaultSettings={defaultSettings} />
    )}
  </Formik>
)

TableSettingsFormik.propTypes = {
  onClose: PropTypes.func.isRequired,
  save: PropTypes.func.isRequired,
  settings: PropTypes.array,
  defaultSettings: PropTypes.array,
}

TableSettingsFormik.defaultProps = {
  settings: [],
}

export default TableSettingsFormik
