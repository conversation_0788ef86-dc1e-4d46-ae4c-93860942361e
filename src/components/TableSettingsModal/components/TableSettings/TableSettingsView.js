import React from "react"
import PropTypes from "prop-types"
import FormattedMessage from "components/FormattedMessage"
import { Form } from "formik"

import SettingsList from "../SettingsList"
import { screenHaveTouch } from "utils/touchHelper"
import { FooterModal } from "components/shared/Modal"

import styles from "./tableSettings.module.scss"

const TableSettingsView = ({
  initialValues,
  isSubmitting,
  resetForm,
  setFieldValue,
  values: { settings },
  defaultSettings,
}) => {
  return (
    <Form noValidate>
      <div className={styles.container}>
        <SettingsList
          pressDelay={screenHaveTouch() ? 200 : 0}
          items={settings}
          isSubmitting={isSubmitting}
          onSortEnd={({ oldIndex: from, newIndex: to }) => {
            const updatedSettings = [...settings]

            updatedSettings.splice(to, 0, updatedSettings.splice(from, 1)[0])

            setFieldValue("settings", updatedSettings)
          }}
          onChange={(name, value) => {
            const updatedSettings = [...settings]
            const itemIndex = updatedSettings.findIndex(
              ({ name: itemName }) => itemName === name
            )

            updatedSettings[itemIndex] = {
              ...updatedSettings[itemIndex],
              value,
            }

            setFieldValue("settings", updatedSettings)
          }}
        />
        <FooterModal
          withLayout
          okText={<FormattedMessage id="Apply" />}
          style={{ marginTop: 0 }}
          okButtonProps={{
            disabled: isSubmitting,
            htmlType: "submit",
          }}
          cancelText={<FormattedMessage id="Reset" />}
          cancelButtonProps={{
            disabled: isSubmitting,
          }}
          onCancel={() => setFieldValue("settings", defaultSettings)}
        />
      </div>
    </Form>
  )
}

TableSettingsView.propTypes = {
  isSubmitting: PropTypes.bool,
  resetForm: PropTypes.func.isRequired,
  setFieldValue: PropTypes.func.isRequired,
  values: PropTypes.object.isRequired,
  defaultSettings: PropTypes.array,
}

export default TableSettingsView
