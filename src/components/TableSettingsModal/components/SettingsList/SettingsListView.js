import React from "react"
import PropTypes from "prop-types"

import SettingsItem from "../SettingsItem"

import styles from "./settingsList.module.scss"

const SettingsListView = ({ isSubmitting, items, onChange }) => {
  return (
    <ul className={styles.list}>
      {items.map((item, index) => (
        <SettingsItem
          {...item}
          key={item.name}
          index={index}
          isSubmitting={isSubmitting}
          label={item.label}
          onChange={onChange}
        />
      ))}
    </ul>
  )
}

SettingsListView.propTypes = {
  isSubmitting: PropTypes.bool.isRequired,
  items: PropTypes.array.isRequired,
  onChange: PropTypes.func.isRequired,
}

export default SettingsListView
