@import "assets/styles/variables.scss";

.SettingItem {
  &__container {
    align-items: center;
    border-bottom: 1px solid $border_main;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    width: 100%;
    &:hover {
      background-color: $hover_grid;
      border-radius: 4px;
      border-bottom: 1px solid $main_bg;
    }
  }

  &__checkbox {
    display: flex;

    .ant-checkbox {
      transform: translateY(2px);

      &:after {
        height: 17px;
      }
    }
  }

  &__iconWrapper {
    display: flex;
    cursor: move;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding-left: 10px;
    user-select: none;
    .anticon {
      font-size: 20px;
      color: $icon_disable;
    }
  }

  &__listItem {
    background-color: $main_bg;
    list-style: none;
    padding: 0 20px;
    width: 100%;
    z-index: 9999;
    &:hover {
      padding-top: 1px;
      padding-bottom: 1px;
      margin-top: -1px;
      margin-bottom: -1px;
    }
  }
}

@media (max-width: $sm) {
  .SettingItem {
    &__listItem {
      padding-left: 10px;
      padding-right: 10px;
    }
  }
}
