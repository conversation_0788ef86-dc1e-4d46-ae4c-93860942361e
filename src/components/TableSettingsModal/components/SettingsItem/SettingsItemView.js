import React from "react"
import { Checkbox } from "antd"
import { Icon } from "@ant-design/compatible"
import PropTypes from "prop-types"
import cn from "classnames"
import FormattedMessage from "components/FormattedMessage"

import Typography from "components/Typography"
import "components/TableSettingsModal/components/SettingsItem/settingsItem.scss"

const SettingsItemView = ({ isSubmitting, label, name, onChange, value }) => (
  <li
    className={cn("SettingItem__listItem", {
      checked: value,
    })}
  >
    <div className={"SettingItem__container"}>
      <Checkbox
        className={"SettingItem__checkbox"}
        name={name}
        checked={value}
        onChange={({ target: { checked } }) => onChange(name, checked)}
        disabled={isSubmitting}
      ></Checkbox>
      <div className={"SettingItem__iconWrapper"}>
        <Typography variant="text">
          <FormattedMessage id={label} />
        </Typography>
        <Icon type="drag" />
      </div>
    </div>
  </li>
)

SettingsItemView.propTypes = {
  isSubmitting: PropTypes.bool.isRequired,
  label: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.bool.isRequired,
}

export default SettingsItemView
