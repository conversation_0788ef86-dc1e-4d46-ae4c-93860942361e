import React from "react"
import { useSelector } from "react-redux"
import { Alert, Box } from "@develop/fe-library"

import { globalNotificationsListSelector } from "selectors/globalNotificationsSelector"

import styles from "./globalNotifications.module.scss"

export const GlobalNotifications = () => {
  // @ts-ignore
  const messages = useSelector(globalNotificationsListSelector)

  return (
    <Box
      flexDirection="column"
      gap="m"
      marginRight="l"
      position="fixed"
      right={0}
      top={70}
      width={270}
      zIndex={1002}
    >
      {messages?.map(({ message, type }, index) => {
        const key = `${type}-${message}-${index}`

        return (
          <div key={key} className={styles.animatedWrapper}>
            <Alert isClosable alertType={type} message={message} />
          </div>
        )
      })}
    </Box>
  )
}
