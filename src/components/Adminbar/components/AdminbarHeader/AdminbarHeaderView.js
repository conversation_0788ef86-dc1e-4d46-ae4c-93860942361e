import React, { useEffect, useRef, useState } from "react"
import { useSelector } from "react-redux"
import { Link } from "react-router-dom"
import { CloseOutlined, RightOutlined } from "@ant-design/icons"
import { Box, Icon, Popover, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import cn from "classnames"
import PropTypes from "prop-types"

import { customerIdSelector } from "selectors/customerSelectors"

import FormattedMessage from "components/FormattedMessage"
import { Button } from "components/shared/Buttons"

import l from "utils/intl"

import styles from "./adminbarHeader.module.scss"

const {
  ADMIN_ROUTES: { PATH_CUSTOMERS },
  GENERAL_ROUTES: { PATH_STAFF, PATH_USER_SETTINGS },
} = ROUTES

const surveyIcons = {
  no: "icnCheckCircle",
  yes: "icnCheckCircle",
  not_now: "icnCloseCircle",
}

const AdminbarHeaderView = ({
  canManageSurvey,
  canManageStatus,
  currentCaseActiveUser,
  customer,
  hideAccounts,
  onToggleAccounts,
  openSurvey,
  surveyState,
  isCaseView,
  onNextCase,
  onSkipCase,
  purgeCache,
}) => {
  const [isCopyPopoverVisible, setIsCopyPopoverVisible] = useState(false)
  const copyPopoverTimeout = useRef()
  const customerID = useSelector(customerIdSelector)

  const userMessage = (isMobile) =>
    currentCaseActiveUser && (
      <div className={`${styles.activeUser} ${isMobile ? styles.mobile : ""}`}>
        <Typography
          className={styles.activeUserLabel}
          color="--color-text-white"
          variant="--font-body-text-8"
        >
          <FormattedMessage
            defaultMessage="The case is being processed by {username}"
            id="The case is being processed by {username}"
            values={{
              username: `${currentCaseActiveUser.firstname} ${currentCaseActiveUser.lastname}`,
            }}
          />
        </Typography>
      </div>
    )

  const nextButtons = (isMobile) =>
    isCaseView && (
      <div className={`${styles.nextButtons} ${isMobile ? styles.mobile : ""}`}>
        <Button
          className={styles.button}
          disabled={!onNextCase}
          onClick={onNextCase}
        >
          <RightOutlined className={styles.icon} />
          <span>
            <FormattedMessage id="Next" />
          </span>
        </Button>
        <Button
          className={styles.button}
          disabled={!onSkipCase}
          onClick={onSkipCase}
        >
          <CloseOutlined className={styles.icon} />
          <span>
            <FormattedMessage id="Skip" />
          </span>
        </Button>
      </div>
    )

  const cleareCacheHandler = () => {
    const isNeedReload =
      window.location.pathname === PATH_USER_SETTINGS ? false : true

    return purgeCache(isNeedReload, undefined)
  }

  const showPopover = () => {
    setIsCopyPopoverVisible(true)
    copyPopoverTimeout.current = setTimeout(
      () => setIsCopyPopoverVisible(false),
      2000
    )
  }

  const hidePopover = () => {
    setIsCopyPopoverVisible(false)
    if (copyPopoverTimeout.current) {
      clearTimeout(copyPopoverTimeout.current)
    }
  }

  useEffect(() => {
    document.addEventListener("click", hidePopover)

    return () => {
      document.removeEventListener("click", hidePopover)

      if (copyPopoverTimeout.current) {
        clearTimeout(copyPopoverTimeout.current)
      }
    }
  }, [])

  const copyAndShowPopover = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      if (isCopyPopoverVisible) {
        hidePopover()
        setTimeout(() => showPopover(), 100)
      } else {
        showPopover()
      }
    })
  }

  const copyPageUrl = () => {
    const url = new URL(window.location.href)
    const isCustomerParamPresent = url.searchParams.has("customerID")

    if (isCustomerParamPresent) {
      copyAndShowPopover(url.href)

      return
    }

    url.searchParams.append("customerID", customerID)

    copyAndShowPopover(url.href)
  }

  return (
    <div
      className={cn(styles.container, {
        [styles.containerCollapsed]: hideAccounts,
      })}
    >
      <div className={styles.info}>
        <div className={styles.infoLabel}>
          <Typography
            className={styles.label}
            color="--color-text-second"
            variant="--font-body-text-3"
          >
            <FormattedMessage id="Customer information" />
          </Typography>
        </div>
        {userMessage(true)}
        <div className={styles.infoId}>
          <Typography
            className={styles.label}
            color="--color-text-second"
            variant="--font-body-text-3"
          >
            <FormattedMessage id="ID" />:
          </Typography>
          <Typography
            className={styles.value}
            color="--color-text-main"
            variant="--font-body-text-3"
          >
            {customer.id}
          </Typography>
        </div>
        <div className={styles.infoCompany} title={customer.title}>
          <Typography
            className={styles.label}
            color="--color-text-second"
            variant="--font-body-text-3"
          >
            <FormattedMessage id="Company" />:
          </Typography>
          <Typography
            className={styles.value}
            color="--color-text-main"
            variant="--font-body-text-3"
          >
            {customer.title}
          </Typography>
        </div>
      </div>
      {customer.status && customer.status !== "active" && (
        <div className={cn(styles.alertContaiber)}>
          <Popover
            content={
              <div className={styles.popoverContainer}>
                {customer.status === "hard_block" ? (
                  <FormattedMessage id="This customer is hard blocked and cannot access the SellerLogic platform." />
                ) : (
                  <FormattedMessage id="This customer is soft blocked and cannot use SellerLogic products." />
                )}
              </div>
            }
          >
            <div
              className={cn(styles.alertIsBloked, {
                [styles.alertIsHardBloked]: customer.status === "hard_block",
              })}
            >
              <Icon
                color="--color-icon-warning"
                name="icnWarning"
                size="--icon-size-3"
              />
              {!canManageStatus ? (
                <FormattedMessage id="Account suspended" />
              ) : (
                <Link to={`${PATH_CUSTOMERS}?sort=-idpage=1&id=${customer.id}`}>
                  <FormattedMessage id="Account suspended" />
                </Link>
              )}
            </div>
          </Popover>
        </div>
      )}
      <div className={styles.repricerActions}>
        <Button
          className={`${styles.button} ${styles.linkButton}`}
          onClick={cleareCacheHandler}
        >
          <FormattedMessage id="Clear cache" />
        </Button>
        {/* TODO: Should remove it in future */}
        <Button
          className={`${styles.button} ${styles.linkButton}`}
          style={{ display: "none" }}
        >
          <Link to="/userHistory">
            <FormattedMessage id="User history" />
          </Link>
        </Button>
        <Button className={`${styles.button} ${styles.linkButton}`}>
          <Link to={PATH_STAFF}>
            <FormattedMessage id="Users" />
          </Link>
        </Button>
        {canManageSurvey && (
          <Button
            disabled={!surveyState}
            icon={surveyIcons[surveyState] || surveyIcons.not_now}
            className={cn(
              styles.button,
              styles.surveyButton,
              styles[surveyState]
            )}
            onClick={openSurvey}
          >
            <FormattedMessage id="Survey" />
          </Button>
        )}
        <Popover
          hasMaxZIndex
          isVisible={isCopyPopoverVisible}
          placement="topRight"
          trigger="click"
          content={
            <Box align="center" gap="m">
              <Icon
                color="--color-icon-done"
                name="icnCheckCircle"
                size="--icon-size-5"
              />
              <Typography variant="--font-body-text-7">
                {l("Copied to clipboard")}
              </Typography>
            </Box>
          }
        >
          <Button
            className={cn(styles.button, styles.copyUrlButton)}
            onClick={copyPageUrl}
          >
            <FormattedMessage id="Page URL" />
          </Button>
        </Popover>
      </div>
      {userMessage()}
      {nextButtons()}
      <div className={styles.closeContainer}>
        {nextButtons(true)}
        <Icon
          className={styles.icon}
          name={hideAccounts ? "icnChevronDown" : "icnChevronUp"}
          size="--icon-size-2"
          onClick={onToggleAccounts}
        />
      </div>
    </div>
  )
}

AdminbarHeaderView.propTypes = {
  canManageSurvey: PropTypes.bool.isRequired,
  currentCaseActiveUser: PropTypes.object,
  customer: PropTypes.object.isRequired,
  hideAccounts: PropTypes.bool.isRequired,
  onToggleAccounts: PropTypes.func.isRequired,
  openSurvey: PropTypes.func.isRequired,
  purgeCache: PropTypes.func.isRequired,
  surveyState: PropTypes.string,
}

export default AdminbarHeaderView
