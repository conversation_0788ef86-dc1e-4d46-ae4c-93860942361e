import React from "react"
import { Checkbox } from "antd"
import { Typo<PERSON> } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import cn from "classnames"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import Link from "components/shared/Link"

import { getAmazonAccountLink } from "utils/links"

import { CONNECT_STATUS } from "consts"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"
import { SUBSCRIPTION_IDS } from "consts/repricerSubscription"

import styles from "./amazonAccount.module.scss"

const {
  GENERAL_ROUTES: { PATH_SUBSCRIPTION },
} = ROUTES

const AmazonAccountView = ({
  accountName,
  amazonZoneTitle,
  homeMarketplace: { id: homeMarketplaceId, sales_channel },
  marketplaces,
  mwsAuthorization,
  sellerId,
  useLost,
  useNewPricing,
  use_repricer_module,
  use_repricer_b2c_module,
  use_repricer_b2b_module,
  use_bas_module,
  id,
  repricer_module_connected_status,
  repricer_b2c_module_connected_status,
  repricer_b2b_module_connected_status,
  lost_module_connected_status,
  bas_module_connected_status,
  setAccountScrollTab,
  repricerSubscriptionB2C,
  repricerSubscriptionB2B,
  hasBasNextPaymentPlan,
  isBasSubscriptionTrial,
  isBasAutoRenewActive,
  repricerSubscriptionPlans,
}) => {
  const { next, current } = repricerSubscriptionPlans || {}

  const isNewRepricerNeverConnectedProduct =
    repricer_module_connected_status === CONNECT_STATUS.new

  const isRepricerB2CNeverConnectedProduct =
    repricer_b2c_module_connected_status === CONNECT_STATUS.new

  const isRepricerB2BNeverConnectedProduct =
    repricer_b2b_module_connected_status === CONNECT_STATUS.new

  const isLostNeverConnectedProduct =
    lost_module_connected_status === CONNECT_STATUS.new

  const isBasNeverConnectedProduct =
    bas_module_connected_status === CONNECT_STATUS.new

  const repricerActiveMarketplacesFilter = (repricerSubscription) =>
    repricerSubscription?.filter(({ active }) => {
      return active
    })

  const repricerB2CActiveMarketplaces = repricerActiveMarketplacesFilter(
    repricerSubscriptionB2C
  )

  const repricerB2BActiveMarketplaces = repricerActiveMarketplacesFilter(
    repricerSubscriptionB2B
  )

  const isNoSubscriptionRepricerMessageVisible = (repricerActiveMarketplaces) =>
    repricerActiveMarketplaces?.every(
      ({ deleted, auto_renew, nextSubscriptionTitle }) => {
        return !deleted && !auto_renew && !nextSubscriptionTitle
      }
    )

  const isNoSubscriptionRepricerB2CMessageVisible =
    isNoSubscriptionRepricerMessageVisible(repricerB2CActiveMarketplaces)

  const isNoSubscriptionRepricerB2BMessageVisible =
    isNoSubscriptionRepricerMessageVisible(repricerB2BActiveMarketplaces)

  const isRepricerNextSubscriptionDisabled =
    (!next && !current?.auto_renew) ||
    current?.payment_module_version_id === SUBSCRIPTION_IDS.TRIAL

  const isYellowCheckboxNewRepricer =
    use_repricer_module && isRepricerNextSubscriptionDisabled

  const isYellowCheckboxRepricerB2C =
    isNoSubscriptionRepricerB2CMessageVisible && use_repricer_b2c_module

  const isYellowCheckboxRepricerB2B =
    isNoSubscriptionRepricerB2BMessageVisible && use_repricer_b2b_module

  const isBasNextSubscriptionDisabled =
    (!hasBasNextPaymentPlan && !isBasAutoRenewActive) || isBasSubscriptionTrial
  const isYellowCheckboxBas = use_bas_module && isBasNextSubscriptionDisabled

  const handleLinkClick = () => {
    setAccountScrollTab(false)
    setAccountScrollTab(true)
  }

  const isNewRepricerNotConnected = !use_repricer_module
  const isRepricerNotConnected = !use_repricer_b2c_module

  return (
    <li className={styles.container}>
      <div className={`${styles.labelContainer} ${styles.marketplace}`}>
        <Typography
          className={styles.label}
          color="--color-text-second"
          variant="--font-body-text-3"
        >
          {useNewPricing ? (
            amazonZoneTitle
          ) : (
            <FormattedMessage id="Amazon marketplace" />
          )}
        </Typography>
        <Typography className={styles.value} variant="--font-body-text-3">
          {marketplaces}
        </Typography>
      </div>
      <div className={`${styles.labelContainer} ${styles.account}`}>
        <a
          rel="noopener noreferrer"
          target="_blank"
          href={getAmazonAccountLink(
            sales_channel,
            homeMarketplaceId,
            sellerId
          )}
        >
          <Typography
            className={`${styles.label} ${styles.nameLabel}`}
            color="--color-text-link"
            variant="--font-body-text-3"
          >
            {accountName}
          </Typography>
        </a>
      </div>
      <div className={`${styles.labelContainer} ${styles.account}`}>
        <Typography
          className={styles.label}
          color="--color-text-second"
          variant="--font-body-text-3"
        >
          {sellerId}
        </Typography>
      </div>
      <div className={`${styles.labelContainer} ${styles.mvsAuthorization}`}>
        <Typography
          className={styles.label}
          color="--color-text-second"
          variant="--font-body-text-3"
        >
          <FormattedMessage id="Amazon MWS authorization valid" />
        </Typography>
        <Checkbox
          checked
          className={styles.checkbox}
          disabled={!mwsAuthorization}
        />
      </div>
      <div className={`${styles.labelContainer} ${styles.lostFound}`}>
        <Link
          internal
          className={styles.label}
          label={PRODUCT_NAMES[PRODUCTS.lost]}
          type="div"
          url={`${PATH_SUBSCRIPTION}#tab=${PRODUCTS.lost}`}
          variant="textLarge"
          onClick={handleLinkClick}
        />
        <Checkbox
          checked={!isLostNeverConnectedProduct}
          disabled={isLostNeverConnectedProduct ? true : !useLost}
          className={cn(styles.checkbox, {
            [styles.checkboxYellow]: useLost === 1,
          })}
        />
      </div>
      {useNewPricing ? (
        <div className={`${styles.labelContainer} ${styles.repricer}`}>
          <Link
            internal
            className={styles.label}
            label={PRODUCT_NAMES[PRODUCTS.repricer]}
            type="div"
            url={`${PATH_SUBSCRIPTION}#tab=${PRODUCTS.repricer}`}
            variant="textLarge"
            onClick={handleLinkClick}
          />
          <Checkbox
            checked={!isNewRepricerNeverConnectedProduct}
            className={cn(styles.checkbox, {
              [styles.checkboxYellow]: isYellowCheckboxNewRepricer,
            })}
            disabled={
              isNewRepricerNeverConnectedProduct
                ? true
                : isNewRepricerNotConnected
            }
          />
        </div>
      ) : (
        <>
          <div className={`${styles.labelContainer} ${styles.repricer}`}>
            <Link
              internal
              className={styles.label}
              label={PRODUCT_NAMES[PRODUCTS.repricerB2C]}
              type="div"
              url={`${PATH_SUBSCRIPTION}#tab=${PRODUCTS.repricerB2C}`}
              variant="textLarge"
              onClick={handleLinkClick}
            />
            <Checkbox
              checked={!isRepricerB2CNeverConnectedProduct}
              className={cn(styles.checkbox, {
                [styles.checkboxYellow]: isYellowCheckboxRepricerB2C,
              })}
              disabled={
                isRepricerB2CNeverConnectedProduct
                  ? true
                  : isRepricerNotConnected
              }
            />
          </div>

          <div className={`${styles.labelContainer} ${styles.repricer}`}>
            <Link
              internal
              className={styles.label}
              label={PRODUCT_NAMES[PRODUCTS.repricerB2B]}
              type="div"
              url={`${PATH_SUBSCRIPTION}#tab=${PRODUCTS.repricerB2B}`}
              variant="textLarge"
              onClick={handleLinkClick}
            />
            <Checkbox
              checked={!isRepricerB2BNeverConnectedProduct}
              className={cn(styles.checkbox, {
                [styles.checkboxYellow]: isYellowCheckboxRepricerB2B,
              })}
              disabled={
                isRepricerB2BNeverConnectedProduct
                  ? true
                  : !use_repricer_b2b_module
              }
            />
          </div>
        </>
      )}

      <div className={`${styles.labelContainer} ${styles.bas}`}>
        <Link
          internal
          className={styles.label}
          label={PRODUCT_NAMES[PRODUCTS.bas]}
          type="div"
          url={`${PATH_SUBSCRIPTION}#tab=${PRODUCTS.bas}`}
          variant="textLarge"
          onClick={handleLinkClick}
        />
        <Checkbox
          checked={!isBasNeverConnectedProduct}
          disabled={isBasNeverConnectedProduct ? true : !use_bas_module}
          className={cn(styles.checkbox, {
            [styles.checkboxYellow]: isYellowCheckboxBas,
          })}
        />
      </div>
    </li>
  )
}

AmazonAccountView.propTypes = {
  accountId: PropTypes.string.isRequired,
  marketplaces: PropTypes.string.isRequired,
  mwsAuthorization: PropTypes.bool.isRequired,
  sellerId: PropTypes.string.isRequired,
  useLost: PropTypes.number.isRequired,
  useRepricer: PropTypes.bool.isRequired,
  use_bas_module: PropTypes.bool.isRequired,
  repricer_b2c_module_connected_status: PropTypes.string,
  repricer_b2b_module_connected_status: PropTypes.string,
  lost_module_connected_status: PropTypes.string,
  bas_module_connected_status: PropTypes.string,
  setAccountScrollTab: PropTypes.func.isRequired,
  hasBasNextPaymentPlan: PropTypes.bool.isRequired,
  isBasSubscriptionTrial: PropTypes.bool.isRequired,
  isBasAutoRenewActive: PropTypes.bool.isRequired,
}

export default AmazonAccountView
