import React, { Component } from "react"
import { withRouter } from "react-router-dom"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { isEqual } from "lodash"
import PropTypes from "prop-types"

import AdminbarView from "components/Adminbar/AdminbarView"
import CardsLayoutModal from "components/CardsLayoutModal"
import SimpleErrorBoundary from "components/hocs/SimpleErrorBoundary"
import { ConfirmModal } from "components/shared/Modal"

import l from "utils/intl"

class AdminbarContainer extends Component {
  state = {
    nextIsDisabled: false,
    canSendEmail: true,
  }
  componentDidMount() {
    const {
      isPasswordOutdated,
      canManageSurvey,
      getAccounts,
      getAmazonMarketplaces,
      getCustomerSurveyData,
      getStaff,
      useNewPricing,
      getRepricerCustomerPlanSubscriptions,
    } = this.props

    if (isPasswordOutdated) {
      return
    }

    if (canManageSurvey) {
      getCustomerSurveyData()
      getStaff(false)
    }

    if (useNewPricing) {
      getRepricerCustomerPlanSubscriptions({ cache: true })
    }
    //Remove cache amazonCustomerAccounts. We need always up-to-date data.
    getAccounts(false)
    getAmazonMarketplaces(undefined)
  }

  componentDidUpdate(prevProps) {
    const {
      customer: { id: prevId },
    } = prevProps

    const {
      customer: { id },
      canManageSurvey,
      getStaff,
    } = this.props

    if (canManageSurvey && prevId !== id) {
      getStaff(false)
    }
  }

  onToggle = () => {
    const { extended, extendAccounts, hideAccounts } = this.props

    extendAccounts({ extended: !extended, hideAccounts })
  }

  onSave = (payload, failureCallback, successCallback) => {
    const { update, toggleModal } = this.props

    update(
      payload,
      () => {
        successCallback && successCallback()
        if (payload.participate === "not_now" || payload.participate === "no") {
          toggleModal(false)
        }
      },
      failureCallback
    )
  }

  sendEmail = () => {
    const {
      customer: { id },
      sendEmail,
    } = this.props

    sendEmail(id, () => this.setState({ canSendEmail: false }))
  }

  onToggleAccounts = () => {
    const { extendAccounts, hideAccounts } = this.props

    extendAccounts({ extended: true, hideAccounts: !hideAccounts })
  }

  onNextCase = () => {
    const { currentCase, getNextCase, displayLastCaseModal } = this.props
    const { nextIsDisabled } = this.state

    if (!nextIsDisabled) this.setState({ nextIsDisabled: true })
    if (currentCase && currentCase.id) {
      getNextCase(
        currentCase.id,
        ({ id }) => {
          if (id === currentCase.id) {
            displayLastCaseModal(true)
          }
          this.setState({ nextIsDisabled: false })
        },
        () => this.setState({ nextIsDisabled: false })
      )
    }
  }

  onSkipCase = () => {
    const { currentCase, setSkipCase } = this.props
    const { nextIsDisabled } = this.state

    if (!nextIsDisabled) this.setState({ nextIsDisabled: true })
    if (currentCase && currentCase.id) {
      setSkipCase(currentCase.id, this.onNextCase)
    }
  }

  onFormChange = () => {
    this.setState({
      canSendEmail: true,
    })
  }

  isCaseView = () => {
    const {
      match: { url = "" },
    } = this.props
    const id = +url.replace(`${ROUTES.LOST_ROUTES.PATH_LOST_ALL_CASES}/`, "")

    return !!id
  }

  render() {
    const {
      accounts,
      canManageSurvey,
      currentCaseActiveUser,
      customer,
      extended,
      hideAccounts,
      isPasswordOutdated,
      modalVisible,
      staff,
      surveyData,
      toggleModal,
      currentCase,
      displayLastCaseModal,
      lastCaseModalVisible,
      purgeCache,
      canManageStatus,
      setAccountScrollTab,
      useNewPricing,
      repricerSubscriptionPlans,
    } = this.props

    const { canSendEmail, nextIsDisabled } = this.state
    const status = currentCase?.status

    if (isPasswordOutdated) {
      return null
    }

    let initialValues = {}

    if (staff && staff[0]) {
      const { id, firstname, lastname, email } = staff[0]

      initialValues = {
        staff_id: id,
        name: `${firstname} ${lastname}`,
        email,
      }
    }

    initialValues = {
      ...initialValues,
      ...(surveyData || {}),
    }

    return (
      <>
        <SimpleErrorBoundary>
          <AdminbarView
            accounts={extended ? accounts : accounts.slice(0, 2)}
            canManageStatus={canManageStatus}
            canManageSurvey={canManageSurvey}
            currentCaseActiveUser={currentCaseActiveUser}
            customer={customer}
            expanded={extended}
            hideAccounts={hideAccounts}
            isCaseView={this.isCaseView()}
            openSurvey={() => toggleModal(true)}
            purgeCache={purgeCache}
            repricerSubscriptionPlans={repricerSubscriptionPlans}
            setAccountScrollTab={setAccountScrollTab}
            showExpander={accounts.length > 2}
            surveyState={initialValues.participate}
            useNewPricing={useNewPricing}
            onNextCase={!nextIsDisabled ? this.onNextCase : undefined}
            onSkipCase={!nextIsDisabled ? this.onSkipCase : undefined}
            onToggle={this.onToggle}
            onToggleAccounts={this.onToggleAccounts}
          />
        </SimpleErrorBoundary>
        {lastCaseModalVisible && (
          <ConfirmModal
            visible
            cancelButtonProps={{ style: { display: "none" } }}
            message={l("All cases are already processed.")}
            onOk={() => displayLastCaseModal(false)}
          />
        )}
        {modalVisible && (
          <CardsLayoutModal
            withCloseConfirm
            className="servey-modal"
            closeAfterSubmit={false}
            initialValues={initialValues}
            title="Information about the rating request"
            width={540}
            buttons={[
              {
                disabled: !canSendEmail,
                isAvailable: (payload) => {
                  const notUse = { comments: "", id: "", customer_id: "" }

                  return (
                    surveyData.participate === "yes" &&
                    isEqual(
                      { ...payload, ...notUse },
                      { ...surveyData, ...notUse }
                    )
                  )
                },
                label: "Send email",
                onClick: this.sendEmail,
              },
            ]}
            controls={[
              {
                allowClear: false,
                name: "participate",
                type: "select",
                placeholder: "Rating request sent",
                required: true,
                options: [
                  { label: l("Not now"), value: "not_now" },
                  { label: l("Yes"), value: "yes" },
                  { label: l("No"), value: "no" },
                ],
              },
              {
                isAvailable: ({ participate }) => participate === "yes",
                allowClear: false,
                name: "staff_id",
                options: staff.map(({ id, firstname, lastname }) => ({
                  label: `${firstname} ${lastname}`,
                  value: id,
                })),
                placeholder: "User",
                type: "selectWithFilter",
                required: true,
                onChange: (value, setFieldValue) => {
                  const user = staff.find(({ id }) => id === value)
                  const { firstname, lastname, email } = user

                  setFieldValue("name", `${firstname} ${lastname}`, false)
                  setFieldValue("email", email, false)
                },
              },
              {
                isAvailable: ({ participate }) => participate === "yes",
                name: "name",
                placeholder: "User name",
                type: "text",
                required: true,
              },
              {
                isAvailable: ({ participate }) => participate === "yes",
                name: "email",
                placeholder: "Email",
                type: "text",
                required: true,
              },
              {
                name: "comments",
                placeholder: "Comments",
                type: "textArea",
              },
            ]}
            onClose={() => toggleModal(false)}
            onFormChange={this.onFormChange}
            onSubmit={this.onSave}
          />
        )}
      </>
    )
  }
}

AdminbarContainer.propTypes = {
  accounts: PropTypes.array.isRequired,
  canManageSurvey: PropTypes.bool.isRequired,
  currentCaseActiveUser: PropTypes.object,
  customer: PropTypes.object.isRequired,
  extendAccounts: PropTypes.func.isRequired,
  getAccounts: PropTypes.func.isRequired,
  getAmazonMarketplaces: PropTypes.func.isRequired,
  getCustomerSurveyData: PropTypes.func.isRequired,
  getMarketplaces: PropTypes.func.isRequired,
  getStaff: PropTypes.func.isRequired,
  isPasswordOutdated: PropTypes.bool.isRequired,
  modalVisible: PropTypes.bool.isRequired,
  isGuest: PropTypes.bool,
  purgeCache: PropTypes.func.isRequired,
  staff: PropTypes.array.isRequired,
  sendEmail: PropTypes.func.isRequired,
  surveyData: PropTypes.object,
  toggleModal: PropTypes.func.isRequired,
  update: PropTypes.func.isRequired,
  setAccountScrollTab: PropTypes.func.isRequired,
}

export default withRouter(AdminbarContainer)
