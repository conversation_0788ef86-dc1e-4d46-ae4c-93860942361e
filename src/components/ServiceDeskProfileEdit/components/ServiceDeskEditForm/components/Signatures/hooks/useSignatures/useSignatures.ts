import { useEffect, useMemo, useState } from "react"
import { use<PERSON><PERSON>roller, useField<PERSON>rray, useWatch } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { IconNames, Option } from "@develop/fe-library"
import { checkIsArray } from "@develop/fe-library/dist/utils"
import uniqueId from "lodash/uniqueId"

import serviceDeskActions from "actions/serviceDeskProfileActions"

import {
  isExpandedSelector,
  serviceDeskCurrentUserProfileSelector,
} from "selectors/serviceDeskProfileSelectors"

import {
  HEIGHT_HEADER_TITLE_FOOTER_MARGIN,
  HEIGHT_TITLES_DEFAULT_SIGNATURE,
} from "components/ServiceDeskProfileEdit/constants"

import l from "utils/intl"

import { UseSignaturesProps } from "./UseSignaturesTypes"
import { EmailSignature } from "types/ServiceDeskProfileTypes"

// @ts-expect-error
const { setIsExpanded, patchServiceDeskUserProfile } = serviceDeskActions

export const useSignatures = ({
  signatureIdFromPath,
  form,
  isEditFormDisabled,
}: UseSignaturesProps) => {
  const dispatch = useDispatch()
  const [selectedIndex, setSelectedIndex] = useState(0)

  const isExpanded = useSelector(isExpandedSelector)
  const serviceDeskCurrentUserProfile = useSelector(
    serviceDeskCurrentUserProfileSelector
  )

  const { control, trigger } = form

  const {
    field: { onChange },
    fieldState: { invalid },
  } = useController({
    name: "signatures",
    control,
  })

  const { prepend, remove, replace } = useFieldArray({
    control,
    name: "signatures",
  })

  const signatures = useWatch({
    control,
    name: "signatures",
  })

  const defaultOptions = useMemo(
    () =>
      signatures.map(({ title, id }) => ({
        label: title ?? l("Signature name"),
        value: id ?? uniqueId("new_"),
      })),
    [signatures]
  )

  useEffect(() => {
    if (signatureIdFromPath) {
      const foundIndex = signatures.findIndex(
        (item) => item.supportEmailId === signatureIdFromPath
      )

      setSelectedIndex(foundIndex)
    }
    // should be executed only once without dependencies
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleExpandEditingSection = (): void => {
    dispatch(setIsExpanded(!isExpanded))

    const textEditorHeight: number =
      window.innerHeight -
      HEIGHT_HEADER_TITLE_FOOTER_MARGIN -
      HEIGHT_TITLES_DEFAULT_SIGNATURE

    document.documentElement.style.setProperty(
      "--textEditorExpandedHeight",
      `${textEditorHeight}px`
    )
  }

  const handleDefaultSignatureChange = (option: Option): void => {
    const newSignatures: EmailSignature[] = signatures.map((signature) => ({
      ...signature,
      default: signature.id === option?.value,
    }))

    onChange(newSignatures)
  }

  const handleAddingNewSignature = (): void => {
    prepend({
      id: uniqueId("new_"),
      title: null,
      signature: "",
      default: false,
      supportEmailId: null,
    })

    setTimeout(() => {
      setSelectedIndex(0)
      trigger("signatures")
    }, 0)
  }

  const handleSignatureRemove = (index: number): void => {
    remove(index)

    setSelectedIndex(0)

    const isNoSignatures: boolean = signatures.length - 1 === 0

    if (isNoSignatures) {
      dispatch(
        patchServiceDeskUserProfile(
          {
            ...serviceDeskCurrentUserProfile,
            signatures: [],
          },
          () => {
            replace({
              id: uniqueId("new_"),
              title: null,
              signature: "",
              default: false,
              supportEmailId: null,
            })
          }
        )
      )
    }
  }

  const expandIcon: IconNames = isExpanded ? "icnShrink" : "icnArrowsAlt"
  const expandContent = isExpanded ? l("Shrink") : l("Expand")
  const defaultSignatureId = signatures?.find((entity) => entity.default)?.id
  const isOnlyNewSignature: boolean =
    signatures.length === 1 &&
    !!signatures[0].id?.startsWith("new_") &&
    !signatures[0].title

  const isDefaultSignatureFieldDisabled: boolean =
    isEditFormDisabled || isOnlyNewSignature

  const isAddDisabled: boolean =
    (checkIsArray(signatures) &&
      !!signatures.find((entity) => !entity?.title?.length)) ||
    isEditFormDisabled

  return {
    isExpanded,
    defaultOptions,
    defaultSignatureId,
    expandIcon,
    expandContent,
    isAddDisabled,
    isDefaultSignatureFieldDisabled,
    selectedIndex,
    signatures,
    invalid,
    isOnlyNewSignature,
    setSelectedIndex,
    handleAddingNewSignature,
    handleDefaultSignatureChange,
    handleExpandEditingSection,
    handleSignatureRemove,
  }
}
