import { Quill } from "react-quill"
import Emitter from "quill/core/emitter"
import { Range } from "quill/core/selection"
import { BaseTooltip } from "quill/themes/base"

const Icons = Quill.import("ui/icons")
const SnowTheme = Quill.import("themes/snow")
const LinkBlot = Quill.import("formats/link")

export class CustomSnowTheme extends SnowTheme {
  extendToolbar(toolbar) {
    toolbar.container.classList.add("ql-snow")

    this.buildButtons(
      [].slice.call(toolbar.container.querySelectorAll("button")),
      Icons
    )

    this.buildPickers(
      [].slice.call(toolbar.container.querySelectorAll("select")),
      Icons
    )

    this.tooltip = new CustomTooltip(this.quill, this.options.bounds)

    if (toolbar.container.querySelector(".ql-link")) {
      this.quill.keyboard.addBinding(
        { key: "K", shortKey: true },
        function (range, context) {
          toolbar.handlers["link"].call(toolbar, !context.format.link)
        }
      )
    }
  }
}

class CustomTooltip extends BaseTooltip {
  constructor(quill, bounds) {
    super(quill, bounds)

    this.preview = this.root.querySelector("a.ql-preview")

    // this part changed compared to snow theme tooltip to allow horizontal tooltip movement
    if (this.quill.root === this.quill.scrollingContainer) {
      this.quill.root.addEventListener("scroll", () => {
        this.root.style.marginLeft = -1 * this.quill.root.scrollLeft + "px"
      })
    }
  }

  listen() {
    super.listen()

    this.root
      .querySelector("a.ql-action")
      .addEventListener("click", (event) => {
        if (this.root.classList.contains("ql-editing")) {
          this.save()
        } else {
          this.edit("link", this.preview.textContent)
        }
        event.preventDefault()
      })

    this.root
      .querySelector("a.ql-remove")
      .addEventListener("click", (event) => {
        if (this.linkRange != null) {
          let range = this.linkRange

          this.restoreFocus()
          this.quill.formatText(range, "link", false, Emitter.sources.USER)
          delete this.linkRange
        }
        event.preventDefault()
        this.hide()
      })

    this.quill.on(
      Emitter.events.SELECTION_CHANGE,
      (range, oldRange, source) => {
        if (range == null) return

        if (range.length === 0 && source === Emitter.sources.USER) {
          let [link, offset] = this.quill.scroll.descendant(
            LinkBlot,
            range.index
          )

          if (link != null) {
            this.linkRange = new Range(range.index - offset, link.length())
            let preview = LinkBlot.formats(link.domNode)

            this.preview.textContent = preview
            this.preview.setAttribute("href", preview)
            this.show()
            this.position(this.quill.getBounds(this.linkRange))

            return
          }
        } else {
          delete this.linkRange
        }
        this.hide()
      }
    )
  }

  show() {
    super.show()
    this.root.removeAttribute("data-mode")
  }
}

CustomTooltip.TEMPLATE = [
  '<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>',
  '<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">',
  '<a class="ql-action"></a>',
  '<a class="ql-remove"></a>',
].join("")
