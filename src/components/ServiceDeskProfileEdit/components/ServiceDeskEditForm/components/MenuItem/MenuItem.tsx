import React from "react"
import { Box, Ellipsis } from "@develop/fe-library"

import { DeleteModal } from "components/ServiceDeskProfileEdit/components"

import l from "utils/intl"

import { MenuItemProps } from "./MenuItemTypes"

export const MenuItem = ({
  hasFormErrors = false,
  item,
  index,
  selectedIndex,
  isEditFormDisabled,
  canDelete = true,
  onClick,
  onSignatureRemove,
}: MenuItemProps) => {
  const handleItemClick = (): void => {
    onClick(index)
  }

  const handleConfirm = (): void => {
    onSignatureRemove(index)
  }

  const title = item?.title
  const isSelected = index === selectedIndex
  const textTitle = title?.length ? <span>{title}</span> : l("Signature name")
  const backgroundColor = isSelected ? "--color-row-select" : undefined
  const isItemDisabled: boolean = hasFormErrors && !isSelected
  const isNewWithoutTitle: boolean = !!item?.id?.startsWith("new_") && !title
  const isTextDisabled: boolean = isNewWithoutTitle && isSelected

  return (
    <Box
      align="center"
      backgroundColor={backgroundColor}
      display="flex"
      hasBorder={{ bottom: true }}
      height="38px"
      justify="space-between"
      paddingLeft="s"
      width="100%"
    >
      <Box
        align="center"
        cursor={isItemDisabled ? "not-allowed" : "pointer"} //"pointer"
        flexGrow={1}
        height="100%"
        maxWidth="calc(100% - var(--padding-xl))"
        onClick={isItemDisabled ? undefined : handleItemClick}
      >
        <Ellipsis
          typographyProps={{
            variant: "--font-body-text-9",
            color: isTextDisabled
              ? "--color-text-disable"
              : "--color-text-main",
            isFullWidth: true,
          }}
        >
          {textTitle}
        </Ellipsis>
      </Box>

      {canDelete ? (
        <DeleteModal
          isButtonAsIcon
          handleConfirm={handleConfirm}
          isDisabled={isEditFormDisabled}
          itemType={l("this entry")}
          title={l("Delete item")}
        />
      ) : null}
    </Box>
  )
}
