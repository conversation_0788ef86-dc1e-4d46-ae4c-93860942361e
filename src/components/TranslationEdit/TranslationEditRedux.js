import { connect } from "react-redux"

import TranslationEditView from "components/TranslationEdit/TranslationEditView"
import translationsActions from "actions/translationsActions"
import {
  currentTranslationItemSelector,
  commentsItemSelector,
} from "selectors/translationsSelectors"
import { permissionsSelector } from "selectors/userSelectors"

const {
  getComments,
  getNextTranslation,
  getTranslationDetails,
  markIgnored,
  markOpened,
  update,
  changeStatus,
  addComment,
  updateComment,
  deleteComment,
  addAttachment,
  deleteAttachment,
  generateSignedUrl,
  uploadAttachment,
} = translationsActions

const mapStateToProps = (state) => {
  const {
    staff: {
      currentUser: { id: currentUserId },
    },
    translations: { searchOptions },
  } = state
  const { translationManage } = permissionsSelector(state)

  return {
    canManage: translationManage,
    currentTranslation: currentTranslationItemSelector(state),
    currentUserId,
    searchOptions,
    comments: commentsItemSelector(state),
  }
}

export default connect(mapStateToProps, {
  addComment,
  getComments,
  getNextTranslation,
  getTranslationDetails,
  markIgnored,
  markOpened,
  update,
  changeStatus,
  updateComment,
  deleteComment,
  generateSignedUrl,
  uploadAttachment,
  addAttachment,
  deleteAttachment,
})(TranslationEditView)
