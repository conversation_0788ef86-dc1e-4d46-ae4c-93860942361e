import React, { useState } from "react"
import PropTypes from "prop-types"
import cn from "classnames"
import { Input, Image } from "antd"
import {
  SendOutlined,
  EditOutlined,
  DeleteOutlined,
  CloseOutlined,
  PaperClipOutlined,
} from "@ant-design/icons"
import moment from "moment"
import { ROUTES } from "@develop/fe-library/dist/routes"

import Typography from "components/Typography"
import FormattedMessage from "components/FormattedMessage"
import S3Uploader from "components/S3Uploader/S3Uploader"
import Pagination from "components/shared/Pagination"

import l from "utils/intl"
import {
  convertUtcMomentToUserTzMoment,
  convertToLocalDateTime,
  SERVER_DATE_FORMAT_WITH_TIME,
} from "utils/dateConverter"
import { setConfirm, DELETE_ITEM } from "utils/confirm"

import avatarPlaceholder from "assets/img/svg/avatar-placeholder.svg"

import styles from "./comments.module.scss"

const { TextArea } = Input

const CommentsView = ({
  activeTab,
  canManage,
  comments,
  onAddComment,
  onGetComments,
  onUpdateComment,
  setActiveTab,
  onDeleteComment,
  currentUserId,
  generateSignedUrl,
  uploadAttachment,
  onAddAttachments,
  onChangeAttachment,
  deleteAttachment,
}) => {
  const [commentText, setCommentText] = useState("")
  const [commentId, setCommentId] = useState(undefined)
  const [shouldClearAttachments, setShouldClearAttachments] = useState(false)
  const [editAttachments, setEditAttachments] = useState([])

  return (
    <>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.tabs}>
            <Typography
              className={cn(styles.tabButton, {
                [styles.active]: activeTab === 1,
              })}
              onClick={() => setActiveTab(1)}
              type="div"
              variant="text"
            >
              <FormattedMessage id="All" />
            </Typography>
            <Typography
              className={cn(styles.tabButton, {
                [styles.active]: activeTab === 2,
              })}
              onClick={() => setActiveTab(2)}
              type="div"
              variant="text"
            >
              <FormattedMessage id="Comments" />
            </Typography>
            <Typography
              className={cn(styles.tabButton, {
                [styles.active]: activeTab === 3,
              })}
              onClick={() => setActiveTab(3)}
              type="div"
              variant="text"
            >
              <FormattedMessage id="Changelog" />
            </Typography>
          </div>
        </div>
        <div className={styles.commentForm}>
          <TextArea
            className={styles.textArea}
            onChange={({ target: { value } }) => setCommentText(value)}
            placeholder={l("Enter comment here")}
            value={commentText}
          />
          <div className={styles.commentButtons}>
            <S3Uploader
              accept="image/*"
              content={<PaperClipOutlined className={styles.commentButton} />}
              onUploadFinish={onAddAttachments}
              getSignedUrl={(fileName, successCallback) =>
                generateSignedUrl({ objectKey: fileName }, successCallback)
              }
              uploadAttachment={uploadAttachment}
              className={cn(styles.uploadWrapper, {
                [styles.uploadWithNewComment]: !commentId,
              })}
              shouldClear={shouldClearAttachments}
              onChangeAttachment={onChangeAttachment}
              updateClearAttachment={() => setShouldClearAttachments(false)}
            />
            <SendOutlined
              className={cn(styles.commentButton, styles.submit, {
                [styles.disabled]: commentText.length === 0,
              })}
              onClick={() => {
                if (!commentText) {
                  return
                }

                if (commentId) {
                  onUpdateComment(
                    commentId,
                    commentText.replace(/\r?\n/g, "<br />"),
                    () => {
                      setCommentText("")
                      setCommentId(undefined)
                      setShouldClearAttachments(true)
                      setEditAttachments([])
                    }
                  )
                } else {
                  onAddComment(commentText.replace(/\r?\n/g, "<br />"), () => {
                    setCommentText("")
                    setShouldClearAttachments(true)
                  })
                }
              }}
            />
            {commentId && (
              <CloseOutlined
                className={cn(styles.commentButton, styles.clear)}
                onClick={() => {
                  setCommentText("")
                  setCommentId()
                  setEditAttachments([])
                  setShouldClearAttachments(true)
                }}
              />
            )}
          </div>
          {editAttachments.length ? (
            <div className={styles.editAttachmentsContainer}>
              {editAttachments.map((item) => (
                <div className={styles.editAttachmentItemContainer}>
                  <a
                    href={ROUTES.GENERAL_ROUTES.PATH_HOME}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.imageLink}
                  >
                    <img
                      src={item.file_url}
                      className={styles.image}
                      alt={item.file_name}
                    />
                  </a>
                  <Typography
                    type="div"
                    variant="text"
                    className={styles.imageLabel}
                  >
                    {item.file_name}
                  </Typography>
                  <DeleteOutlined
                    className={styles.deleteAttachmentIcon}
                    onClick={() => {
                      const attachmentId = item.id
                      setConfirm({
                        template: DELETE_ITEM,
                        onOk: () =>
                          deleteAttachment(attachmentId, () =>
                            setEditAttachments(
                              editAttachments.filter(
                                (item) => item.id !== attachmentId
                              )
                            )
                          ),
                      })
                    }}
                  />
                </div>
              ))}
            </div>
          ) : null}
        </div>
        <div className={styles.commentsContainer}>
          {comments.data.map(
            ({
              id,
              created_at,
              text,
              type,
              user: { id: userId, firstname, lastname },
              messageTranslationItemAttachments,
              prev_value_label,
              value_label,
            }) => {
              const system =
                ["CHANGE_STATUS", "CHANGE_TRANSLATION"].indexOf(type) !== -1
              const commentDate = convertToLocalDateTime(
                convertUtcMomentToUserTzMoment(
                  moment(created_at, SERVER_DATE_FORMAT_WITH_TIME)
                ).toDate()
              )

              return (
                <div className={styles.commentContainer} key={id}>
                  <div className={styles.logo}>
                    {system ? (
                      <Typography
                        className={styles.statusLabel}
                        type="div"
                        variant="text"
                      >
                        <FormattedMessage
                          id={
                            type === "CHANGE_STATUS"
                              ? "Status update"
                              : "Translation update"
                          }
                        />
                      </Typography>
                    ) : (
                      <img
                        alt="user logo"
                        className={styles.imgLogo}
                        src={avatarPlaceholder}
                      />
                    )}
                  </div>
                  {system ? (
                    <div className={styles.commentBody}>
                      <div className={styles.commentHead}>
                        <Typography
                          className={styles.statusContent}
                          type="div"
                          variant="text"
                        >
                          {type === "CHANGE_STATUS" ? (
                            <FormattedMessage
                              id="<b>{author}</b> changed the status from <b>{prev}</b> to <b>{next}</b>."
                              defaultMessage="<b>{author}</b> changed the status from <b>{prev}</b> to <b>{next}</b>."
                              values={{
                                author: `${firstname} ${lastname}`,
                                next: l(value_label),
                                prev: l(prev_value_label),
                                b: (...chunks) => <b>{chunks}</b>,
                              }}
                            />
                          ) : (
                            <FormattedMessage
                              id="<b>{author}</b> changed the translation from <b>{prev}</b> to <b>{next}</b>."
                              defaultMessage="<b>{author}</b> changed the translation from <b>{prev}</b> to <b>{next}</b>."
                              values={{
                                author: `${firstname} ${lastname}`,
                                next: value_label,
                                prev: prev_value_label,
                                b: (...chunks) => <b>{chunks}</b>,
                              }}
                            />
                          )}
                        </Typography>
                        <Typography
                          className={styles.smallStatusLabel}
                          type="div"
                          variant="text"
                        >
                          <FormattedMessage
                            id={
                              type === "CHANGE_STATUS"
                                ? "Status update"
                                : "Translation update"
                            }
                          />
                        </Typography>
                        <Typography
                          className={styles.dateLabel}
                          type="div"
                          variant="text"
                        >
                          {commentDate}
                        </Typography>
                      </div>
                      <Typography
                        className={styles.smallStatusContent}
                        type="div"
                        variant="text"
                      >
                        {type === "CHANGE_STATUS" ? (
                          <FormattedMessage
                            id="<b>{author}</b> changed the status from <b>{prev}</b> to <b>{next}</b>."
                            defaultMessage="<b>{author}</b> changed the status from <b>{prev}</b> to <b>{next}</b>."
                            values={{
                              author: `${firstname} ${lastname}`,
                              next: l(value_label),
                              prev: l(prev_value_label),
                              b: (...chunks) => <b>{chunks}</b>,
                            }}
                          />
                        ) : (
                          <FormattedMessage
                            id="<b>{author}</b> changed the translation from <b>{prev}</b> to <b>{next}</b>."
                            defaultMessage="<b>{author}</b> changed the translation from <b>{prev}</b> to <b>{next}</b>."
                            values={{
                              author: `${firstname} ${lastname}`,
                              next: value_label,
                              prev: prev_value_label,
                              b: (...chunks) => <b>{chunks}</b>,
                            }}
                          />
                        )}
                      </Typography>
                    </div>
                  ) : (
                    <div className={styles.commentBody}>
                      <div className={styles.headWrapper}>
                        <div className={styles.smallLogo}>
                          <img
                            alt="user logo"
                            className={styles.smallImgLogo}
                            src={avatarPlaceholder}
                          />
                        </div>
                        <div className={styles.commentHead}>
                          <Typography
                            className={styles.authorLabel}
                            type="div"
                            variant="text"
                          >
                            {`${firstname} ${lastname}`}
                          </Typography>
                          <Typography
                            className={styles.dateLabel}
                            type="div"
                            variant="text"
                          >
                            {commentDate}
                          </Typography>
                        </div>
                      </div>
                      {(canManage || currentUserId === userId) && (
                        <div className={styles.commentIcons}>
                          {currentUserId === userId && (
                            <EditOutlined
                              className={styles.commentIcon}
                              onClick={() => {
                                setCommentText(text.replace(/<br \/>/g, "\r\n"))
                                setCommentId(id)
                                if (messageTranslationItemAttachments.length) {
                                  setEditAttachments(
                                    messageTranslationItemAttachments
                                  )
                                }
                              }}
                            />
                          )}
                          {canManage && (
                            <DeleteOutlined
                              className={styles.commentIcon}
                              onClick={() => {
                                if (commentId === id) {
                                  setCommentText("")
                                  setCommentId()
                                }
                                onDeleteComment(id)
                              }}
                            />
                          )}
                        </div>
                      )}
                      <Typography
                        className={styles.commentContent}
                        dangerouslySetInnerHTML={{
                          __html: text,
                        }}
                        type="div"
                        variant="text"
                      ></Typography>
                      {messageTranslationItemAttachments.length ? (
                        <div className={styles.attachmentsContainer}>
                          <Image.PreviewGroup>
                            {messageTranslationItemAttachments.map(
                              ({ file_url }) => {
                                return <Image width={118} src={file_url} />
                              }
                            )}
                          </Image.PreviewGroup>
                        </div>
                      ) : null}
                    </div>
                  )}
                </div>
              )
            }
          )}
        </div>
      </div>
      {comments.page && (
        <div className={styles.paginationContainer}>
          <Pagination
            className={styles.pagination}
            current={comments.page}
            hideOnSinglePage
            onChange={(newPage) => {
              onGetComments(newPage)
            }}
            pageSize={5}
            total={comments.totalCount}
          />
        </div>
      )}
    </>
  )
}

CommentsView.propTypes = {
  activeTab: PropTypes.number.isRequired,
  canManage: PropTypes.bool.isRequired,
  comments: PropTypes.object.isRequired,
  onAddComment: PropTypes.func.isRequired,
  onGetComments: PropTypes.func.isRequired,
  onUpdateComment: PropTypes.func.isRequired,
  setActiveTab: PropTypes.func.isRequired,
  onDeleteComment: PropTypes.func.isRequired,
  currentUserId: PropTypes.number.isRequired,
}

export default CommentsView
