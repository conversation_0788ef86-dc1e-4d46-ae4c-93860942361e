import React, { useCallback, useEffect, useState } from "react"
import PropTypes from "prop-types"
import { Input, Select, Checkbox } from "antd"
import {
  CheckCircleOutlined,
  LikeOutlined,
  SyncOutlined,
} from "@ant-design/icons"

import { PrimaryButton } from "components/shared/Buttons"
import FormattedMessage from "components/FormattedMessage"
import Typography from "components/Typography"
import { Flag } from "@develop/fe-library"
import Modal from "components/shared/Modal"
import IconPopover from "components/shared/IconPopover"
import l from "utils/intl"

import styles from "./editForm.module.scss"

const statusOptions = [
  { label: "Pending", value: "PENDING" },
  { label: "Approved", value: "APPROVED" },
  { label: "Translated", value: "TRANSLATED" },
]

const translationStatus = {
  PENDING: "PENDING",
  APPROVED: "APPROVED",
  TRANSLATED: "TRANSLATED",
}

const statusIcons = {
  TRANSLATED: <CheckCircleOutlined className={styles.statusIcon} />,
  APPROVED: <LikeOutlined className={styles.statusIcon} />,
  PENDING: <SyncOutlined className={styles.statusIcon} />,
}

const statusLabels = {
  TRANSLATED: "Translated",
  APPROVED: "Approved",
  PENDING: "Pending",
}

const ENG_KEY = "en"

const { TextArea } = Input
const { Option } = Select

const EditFormView = ({
  canManage,
  currentTranslation,
  onChangeStatus,
  onSubmit,
  translationStatus: status,
  currentUserId,
}) => {
  const {
    activeUsers,
    actualMessage,
    language,
    languageName,
    message: { message },
    translation,
    messageTranslationDuplicates,
  } = currentTranslation
  const [translationValue, setTranslationValue] = useState(translation)
  const [isApplyToOtherLangs, setApplyToOtherLangs] = useState(true)
  const [suggestionModalVisible, setSuggestionModalVisible] = useState(false)

  useEffect(() => {
    setTranslationValue(currentTranslation.translation)
  }, [currentTranslation, setTranslationValue])

  const onTranslationChange = useCallback(
    ({ target: { value } }) => {
      setTranslationValue(value)
    },
    [setTranslationValue]
  )

  const closeModal = () => {
    setSuggestionModalVisible(false)
  }

  const toggleSuggestionModal = () => {
    setSuggestionModalVisible((status) => !status)
  }

  const toggleApplyToOtherLangs = (event) => {
    setApplyToOtherLangs(event.target.checked)
  }

  const handleSubmit = () => {
    onSubmit({
      translation: translationValue.trim(),
      applyForRelatedTranslations: isApplyToOtherLangs,
    })
  }

  const handleSuggestionSelect = () => {
    setTranslationValue(translation)
    setSuggestionModalVisible(false)
  }

  return (
    <div className={styles.container}>
      <div className={styles.editFormHeader}>
        <Typography className={styles.editFormTitle} type="div" variant="text">
          <FormattedMessage id="Localization" />
        </Typography>
        {activeUsers && (
          <Typography
            className={styles.inProgressLabel}
            type="div"
            variant="text"
          >
            <FormattedMessage
              defaultMessage="{username} is working on the task"
              id="{username} is working on the task"
              values={{
                username: `${activeUsers.firstname} ${activeUsers.lastname}`,
              }}
            />
          </Typography>
        )}
      </div>
      <div className={styles.translationsContainer}>
        <div className={styles.translationContainer}>
          <div className={styles.translationHeader}>
            <Typography
              className={styles.sourceLabel}
              type="div"
              variant="text"
            >
              <FormattedMessage id="Source from" />
            </Typography>
            <div className={styles.languageContainer}>
              <Typography
                className={styles.languageLabel}
                type="div"
                variant="text"
              >
                <FormattedMessage id="English" />
              </Typography>
              <Flag
                className={styles.flagIcon}
                locale="gb"
                borderRadius="--border-radius-circle"
                size={24}
              />
            </div>
          </div>
          <div className={styles.translationEditInputContainer}>
            <TextArea
              className={styles.translationEditInput}
              value={language !== ENG_KEY ? actualMessage : message}
              disabled
            />
          </div>
        </div>
        <div className={styles.translationContainer}>
          <div className={styles.translationHeader}>
            <Typography
              className={styles.sourceLabel}
              type="div"
              variant="text"
            >
              <FormattedMessage id="Translate to" />
            </Typography>

            <div className={styles.languageContainer}>
              {messageTranslationDuplicates &&
                !!messageTranslationDuplicates.length && (
                  <Typography
                    className={styles.suggestionIcon}
                    onClick={toggleSuggestionModal}
                    type="div"
                    variant="text"
                  >
                    {messageTranslationDuplicates.length}
                  </Typography>
                )}
              <Typography
                className={styles.languageLabel}
                type="div"
                variant="text"
              >
                {languageName}
              </Typography>
              <Flag
                className={styles.flagIcon}
                locale={language === ENG_KEY ? "gb" : language}
                borderRadius="--border-radius-circle"
                size={24}
              />
            </div>
          </div>
          <div className={styles.translationEditInputContainer}>
            <TextArea
              className={styles.translationEditInput}
              onChange={onTranslationChange}
              value={translationValue}
            />
          </div>
        </div>
      </div>
      <div className={styles.controlsContainer}>
        {canManage ? (
          <div className={styles.footerControls}>
            <div className={styles.changeStatusContainer}>
              {statusIcons[status]}
              <Select
                className={styles.statusSelect}
                value={currentTranslation.status}
                onChange={onChangeStatus}
              >
                {statusOptions.map(({ label, value }) => (
                  <Option value={value}>{l(label)}</Option>
                ))}
              </Select>
            </div>

            {language !== ENG_KEY ? null : (
              <div className={styles.applyLanguagesContainer}>
                <Checkbox
                  checked={isApplyToOtherLangs}
                  onChange={toggleApplyToOtherLangs}
                >
                  <Typography
                    className={styles.applyLanguagesLabel}
                    type="span"
                    variant="textSmall"
                  >
                    <FormattedMessage id="Apply for other languages" />
                  </Typography>
                </Checkbox>
              </div>
            )}
          </div>
        ) : (
          <Typography className={styles.statusLabel} type="div" variant="text">
            {status && l(statusLabels[status])}
          </Typography>
        )}
        <PrimaryButton
          className={styles.submitButton}
          onClick={handleSubmit}
          disabled={
            status === translationStatus.TRANSLATED &&
            currentTranslation.translated_by_user_id === currentUserId &&
            translationValue === translation
          }
        >
          <FormattedMessage
            id={
              status === translationStatus.TRANSLATED &&
              currentTranslation.translated_by_user_id === currentUserId
                ? "Update"
                : "Submit"
            }
          />
        </PrimaryButton>
      </div>
      {suggestionModalVisible && (
        <Modal
          onCancel={closeModal}
          footer={false}
          title={<FormattedMessage id="Translation variants" />}
          visible={true}
        >
          {messageTranslationDuplicates.map(({ translation }) => (
            <div className={styles.suggestionContainer}>
              <Typography
                className={styles.suggestionLabel}
                type="div"
                variant="textSmall"
              >
                {translation}
              </Typography>
              <IconPopover
                component={PrimaryButton}
                buttonSize="middle"
                content={l("Select suggestion")}
                placement="topRight"
                className={styles.suggestionButton}
                icon="icnPlus"
                onClick={handleSuggestionSelect}
              />
            </div>
          ))}
        </Modal>
      )}
    </div>
  )
}

EditFormView.propTypes = {
  canManage: PropTypes.bool.isRequired,
  currentTranslation: PropTypes.object.isRequired,
  onChangeStatus: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
}

export default EditFormView
