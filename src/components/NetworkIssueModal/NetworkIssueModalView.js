import React, { PureComponent } from "react"
import PropTypes from "prop-types"
import Modal from "components/shared/Modal"

import FormattedMessage from "components/FormattedMessage"
import Typography from "components/Typography"
import NetworkErrorIcon from "./components/NetworkErrorIcon"

import styles from "components/NetworkIssueModal/networkIssueModal.module.scss"

class NetworkIssueModalView extends PureComponent {
  componentDidMount() {
    window.addEventListener("online", this.onNetworkStateChange)
    window.addEventListener("offline", this.onNetworkStateChange)
  }

  componentWillUnmount() {
    window.removeEventListener("online", this.onNetworkStateChange)
    window.removeEventListener("offline", this.onNetworkStateChange)
  }

  onNetworkStateChange = () => {
    const { setNetworkState } = this.props

    setNetworkState(window.navigator.onLine)
  }

  render() {
    const { visible } = this.props

    return (
      visible && (
        <Modal
          className={styles.modal}
          closable={false}
          footer={null}
          title={
            <Typography
              className={styles.modalTitle}
              type="div"
              variant="textLarge"
            >
              <FormattedMessage id="Internet disconected" />
            </Typography>
          }
          visible
          width={728}
        >
          <div className={styles.container}>
            <NetworkErrorIcon />
            <Typography className={styles.title} type="div" variant="textLarge">
              <FormattedMessage id="Network problem!" />
            </Typography>
            <Typography className={styles.note} type="div" variant="textLarge">
              <FormattedMessage id="Check the internet connection and reload the page." />
            </Typography>
          </div>
        </Modal>
      )
    )
  }
}

NetworkIssueModalView.propTypes = {
  setNetworkState: PropTypes.func.isRequired,
  visible: PropTypes.bool.isRequired,
}

export default NetworkIssueModalView
