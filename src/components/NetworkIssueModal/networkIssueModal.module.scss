@import "assets/styles/variables.scss";

.container {
  align-items: center;
  display: flex;
  flex-direction: column;
  padding: 25px 0 75px;
}

.title.title {
  color: $text_main;
  font-size: 36px;
  font-weight: 500;
  margin-top: 20px;
}

.note.note {
  color: $text_second;
  font-size: 14px;
  margin-top: 20px;
}

@media (max-width: $xs) {
  .modal {
    :global(.ant-modal-body) {
      padding: 24px 10px;
    }
  }

  .container {
    padding: 25px 0 35px;
  }

  .title.title {
    font-size: 24px;
  }

  .note.note {
    font-size: 12px;
  }
}
