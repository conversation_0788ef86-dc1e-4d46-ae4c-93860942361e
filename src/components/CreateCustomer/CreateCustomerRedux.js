import { connect } from "react-redux"
import customerActions from "actions/customerActions"
import countriesActions from "actions/countriesActions"
import { countriesOptionsSelector } from "selectors/countriesSelectors"
import cacheApiActions from "actions/cacheAPIActions"
import CreateCustomerContainer from "./CreateCustomerContainer"
import staffActions from "actions/staffActions"
import userSettingsActions from "actions/userSettingsActions"

const { checkCode, update, displayRemindLaterModal } = staffActions
const { saveUserSettings } = userSettingsActions

const mapStateToProps = (state) => {
  const {
    staff: {
      currentUser: {
        firstname,
        lastname,
        is_phone_number_confirmed,
        id: userId,
        phone,
      },
      isRemindLaterModalVisible,
    },
    translations: { locale: language },
  } = state

  return {
    userId,
    language,
    phoneIsComfirmed: !!is_phone_number_confirmed,
    name: `${firstname} ${lastname}`,
    countryOptions: countriesOptionsSelector(state),
    hasPhone: !!phone,
    isRemindLaterModalVisible,
  }
}

const mapDispatchToProps = (dispatch) => ({
  checkCode: (payload, successCb, failureCb) =>
    dispatch(checkCode(payload, successCb, failureCb)),
  getAllCountries: () => dispatch(countriesActions.getAll()),
  updateUser: (payload, successCb, failureCb) =>
    dispatch(update(payload, successCb, failureCb)),
  createCustomer: (payload, successCallback, failureCallback) =>
    dispatch(
      cacheApiActions.purgeCache(false, () =>
        dispatch(
          customerActions.create(payload, successCallback, failureCallback)
        )
      )
    ),
  displayRemindLaterModal: (isRemindLaterModalVisible, initialValues) =>
    dispatch(displayRemindLaterModal(isRemindLaterModalVisible, initialValues)),
  saveUserSettings: (...params) => dispatch(saveUserSettings(...params)),
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(CreateCustomerContainer)
