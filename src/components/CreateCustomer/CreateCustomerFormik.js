import React from "react"
import { useDispatch } from "react-redux"
import PropTypes from "prop-types"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { checkIsArray } from "@develop/fe-library/dist/utils"

import FormikWithChangeValidation from "components/shared/FormikWithChangeValidation"
import CreateCustomerView from "./CreateCustomerView"

import staffActions from "actions/staffActions"

const { getCurrentUser } = staffActions

const CreateCustomerFormik = ({
  countryOptions,
  onSubmit,
  controls,
  name,
  isSetupTwoFactor,
  phone,
  isRemindLaterModalVisible,
  displayRemindLaterModal,
  currentStep,
  ...otherProps
}) => {
  const dispatch = useDispatch()

  const namesRequired = [
    "zip",
    "city",
    ...controls
      .filter(({ required, name }) => required && name)
      .map(({ name }) => name),
  ]

  return (
    <FormikWithChangeValidation
      initialValues={{}}
      namesRequired={namesRequired}
      onSubmit={(payload, { setErrors, setSubmitting }) => {
        onSubmit(
          payload,
          () => {
            if (isSetupTwoFactor) {
              window.location.href =
                ROUTES.GENERAL_ROUTES.PATH_USER_SETTINGS_SECURITY_SETTINGS
              return
            }
            otherProps.setCurrentStep(2)
          },
          (errors = []) => {
            otherProps.setCurrentStep(0)
            if (checkIsArray(errors)) {
              setErrors(
                errors.reduce((acc, { field, message }) => {
                  acc[field] = message
                  return acc
                }, {})
              )
            }
            dispatch(getCurrentUser(undefined))
            setSubmitting(false)
          }
        )
      }}
    >
      {(formikProps) => (
        <CreateCustomerView
          {...formikProps}
          {...otherProps}
          countryOptions={countryOptions}
          controls={controls}
          name={name}
          namesRequired={namesRequired}
          isSetupTwoFactor={isSetupTwoFactor}
          isRemindLaterModalVisible={isRemindLaterModalVisible}
          displayRemindLaterModal={displayRemindLaterModal}
          currentStep={currentStep}
        />
      )}
    </FormikWithChangeValidation>
  )
}

CreateCustomerFormik.propTypes = {
  onSubmit: PropTypes.func,
  controls: PropTypes.array,
  name: PropTypes.string,
}

export default CreateCustomerFormik
