@import "assets/styles/variables.scss";
.wraper {
  height: 100%;
  min-height: 100%;
  display: flex;
  align-items: center;
  overflow-y: auto;
}

.leftContainer,
.rightContainer {
  width: 50%;
  display: flex;
  justify-content: center;
  padding: 0 20px;
}

.formContainer {
  width: 100%;
  max-width: 600px;
  border-radius: 2px;
  :global(.error-wraper) {
    display: block;
  }
  :global(.main-modal-footer) {
    text-align: right;
  }
}

.titleBox,
.steps.steps {
  margin-bottom: var(--margin-l);
}
.title.title {
  margin-bottom: var(--margin-l);
}
.setupTwoFactor {
  margin-top: 20px;
  margin-bottom: 20px;
}

.rightContainer {
  svg {
    width: 100%;
    height: auto;
    max-width: 700px;
    max-height: 700px;
  }
}

.inviteUserSubTitle.inviteUserSubTitle {
  margin-bottom: var(--margin-m);
}
.inviteUserButton {
  margin-left: var(--margin-m);
}
.checkbox {
  margin-right: var(--margin-s);
}
@media screen and (max-width: $xxl) {
  .rightContainer {
    svg {
      max-width: 600px;
      max-height: 600px;
    }
  }
}

@media screen and (max-width: $xl) {
  .rightContainer {
    svg {
      max-width: 484px;
      max-height: 484px;
    }
  }
  .formContainer {
    width: 442px;
    padding: 20px;
  }
  .titleBox,
  .title {
    margin-bottom: 20px;
  }
}

@media screen and (max-width: $md) {
  .rightContainer {
    display: none;
  }
  .leftContainer {
    width: 100%;
  }
  .wraper {
    display: block;
  }
  .formContainer {
    width: 100%;
    max-width: 100%;
    border: none;
  }
}

@media screen and (max-width: $sm) {
  .formContainer {
    padding: 10px;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
    line-height: 26px;
  }

  .titleBox {
    margin-bottom: 10px;
  }

  .steps.steps,
  .title {
    margin-bottom: 20px;
  }

  .setupTwoFactor {
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .inviteUserButton {
    margin: 0 0 var(--margin-m);
  }
}
