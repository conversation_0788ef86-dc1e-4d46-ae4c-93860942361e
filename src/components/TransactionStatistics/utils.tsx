import React from "react"
import { PaginationProps } from "antd"
import { Option } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { CASE_TYPES_VALUES } from "./constants"

export const paginationItemRenderer: PaginationProps["itemRender"] = (
  page,
  type,
  originalElement
) =>
  type !== "page" ? (
    originalElement
  ) : (
    // eslint-disable-next-line jsx-a11y/anchor-is-valid
    <a>{ln(page)}</a>
  )

export const getCaseTypeOptions = (): Option[] => {
  return CASE_TYPES_VALUES.map(({ key, label }) => ({
    label: l(label),
    value: key,
  }))
}
