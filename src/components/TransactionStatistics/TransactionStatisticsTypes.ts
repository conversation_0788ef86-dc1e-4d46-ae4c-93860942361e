import { Option } from "@develop/fe-library"

export interface SearchParams
  extends Record<string, string | string[] | undefined> {
  from?: string
  to?: string
  customer?: string[]
  customer_id?: string
  case_type?: string[]
  caseType?: string[]
  countCases?: string
  amount?: string
  fee?: string
  period?: string
}

export type TransactionStatisticsParams = {
  page?: number
  pageSize?: number
  sort?: string
  customerId?: string[]
  createdAt?: string
  caseType?: string[]
  case_type?: string[]
  countCases?: string
  amount?: string
  fee?: string
  from?: string
  to?: string
}

export type TransactionsSummaryOnChangeFilterParams = {
  newSearchOptions: TransactionStatisticsParams & Record<string, any>
}

export type SelectFiltersOptions = Record<string, Option[]>

export type TransactionStatisticsListItem = {
  created_at: string
  currency_id: string
  currency_rate: string
  customer_id: number
  finalFee: string
  final_fee: string
  id: number
  lost_case_id: number
  processing_status: string
  reimbursementAmount: string
  reimbursement_amount: string
  reimbursement_type: string
  systemFee: string
  system_fee: string
  transaction_date: string
  type: string
}
