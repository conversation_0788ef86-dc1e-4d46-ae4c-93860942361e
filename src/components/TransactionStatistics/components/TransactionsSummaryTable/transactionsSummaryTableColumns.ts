import {
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "components/TableGridLayout/TableGridLayoutView"
import { CASE_TYPES } from "components/TransactionStatistics/constants"

import l from "utils/intl"
import ln from "utils/localeNumber"

export const tableColumns = [
  {
    title: "Case type",
    dataIndex: "caseType",
    key: "caseType",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
    render: (value: string) => {
      const caseTypeLabel = CASE_TYPES[value]?.label

      return caseTypeLabel ? l(caseTypeLabel) : value
    },
  },
  {
    title: "Cases",
    dataIndex: "countCases",
    key: "countCases",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    disabled: false,
  },
  {
    title: "Amount",
    dataIndex: "amount",
    key: "amount",
    sorter: true,
    type: COLUMN_INPUT_TYPE_NUMBER,
    disabled: false,
    render: (value: string) => ln(value, 2, { currency: "EUR" }),
  },
  {
    title: "Fee",
    dataIndex: "fee",
    key: "fee",
    sorter: true,
    type: COLUMN_INPUT_TYPE_NUMBER,
    disabled: false,
    render: (value: string) => ln(value, 2, { currency: "EUR" }),
  },
]
