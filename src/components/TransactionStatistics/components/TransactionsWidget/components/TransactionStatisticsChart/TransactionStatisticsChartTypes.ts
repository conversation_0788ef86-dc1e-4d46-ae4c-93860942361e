import { XAxisProps } from "recharts"

import { Y_AXIS_WIDTHS } from "./constants"

import { ChartTooltipCategory } from "./components/ChartTooltip/ChartTooltipTypes"

export type AxisWidthsType = keyof typeof Y_AXIS_WIDTHS

export type TransactionStatisticsChartCaseValue = {
  count: number
  amount: string | number
  fee: string | number
}

export type TransactionStatisticsChartCaseEntry = [
  string,
  TransactionStatisticsChartCaseValue
]

export type TransactionStatisticsChartCategory = {
  cases: Record<string, TransactionStatisticsChartCaseValue>
  date: string
  dateEnd: string
  dateStart: string
}

export type TransactionStatisticsChartProps = {
  resizeKey?: string
  minWidth?: number
  width?: number | string
  height?: number
  isChartEmpty?: boolean
  setIsChartEmpty: (isEmpty: boolean) => void
}

export type UseTransactionStatisticsChartArgs = {
  setIsChartEmpty: (isEmpty: boolean) => void
}

export type PayloadCategory = ChartTooltipCategory & {
  payload: TransactionStatisticsChartCategory
}

export type CustomTickProps = {
  y: number
  x: number
  payload: { value: number }
  width: number
  height: number
  index: number
  fill: string
  orientation: "right" | "left" | "top" | "bottom"
}

export type TickFormatterType = XAxisProps["tickFormatter"]

export type TickFormatterProps = {
  label: number
  axisPosition?: "left" | "right"
}
