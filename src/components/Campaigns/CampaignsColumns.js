import React from "react"

import { StatusColumn } from "components/Grid/components"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import {
  COLUMN_INPUT_TYPE_DATE,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "components/TableGridLayout/TableGridLayoutView"

import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"
import { snakeToSentenceCase } from "utils/snakeToSentenceCase"

import { STATUS_TAGS_MAPPER } from "consts"

import styles from "./campaigns.module.scss"

const campaignColumns = [
  {
    className: styles.leftAlignedCell,
    title: "Campaign",
    dataIndex: "name",
    key: "name",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    width: 300,
  },
  {
    title: "Campaign type",
    dataIndex: "advertising_type",
    key: "advertising_type",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT,
    width: 120,
    render: (type) => l(snakeToSentenceCase(type)),
  },
  {
    title: "Localization",
    dataIndex: "language",
    key: "language",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
    width: 120,
    render: (_, data) =>
      data.languages.map((lang) => lang && lang.code.toUpperCase()).join(", "),
  },
  {
    title: "Type",
    dataIndex: "type",
    key: "type",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
    width: 120,
    render: (type) => (type ? l(snakeToSentenceCase(type)) : l("N/A")),
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
    width: 210,
    min: 85,
    render: (status) => (
      <StatusColumn status={status} tagsConfig={STATUS_TAGS_MAPPER} />
    ),
  },
  {
    title: "Impressions",
    dataIndex: "views",
    key: "views",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    width: 120,
  },
  {
    title: "Clicks",
    dataIndex: "clicks",
    key: "clicks",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    width: 120,
  },
  {
    title: "Click-through rate",
    dataIndex: "ctr",
    key: "ctr",
    width: 120,
    render: (_, { views, clicks }) => {
      if (!Number(clicks)) {
        return 0
      }
      const ctr = ((clicks / views) * 100).toFixed(2)

      return `${ctr} %`
    },
  },
  {
    title: "Start date",
    dataIndex: "campaign_start_date",
    key: "campaign_start_date",
    width: 150,
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE,
    withDayTime: true,
    ellipsis: true,
    disabledDate: () => false,
    render: (campaign_start_date) => {
      if (!campaign_start_date) {
        return l("N/A")
      }

      return (
        <ExportValue>{convertToLocalDate(campaign_start_date)}</ExportValue>
      )
    },
  },
  {
    title: "End date",
    dataIndex: "campaign_end_date",
    key: "campaign_end_date",
    width: 150,
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE,
    withDayTime: true,
    ellipsis: true,
    disabledDate: () => false,
    render: (campaign_end_date) => {
      if (!campaign_end_date) {
        return l("N/A")
      }

      return <ExportValue>{convertToLocalDate(campaign_end_date)}</ExportValue>
    },
  },
]

export default campaignColumns
