import React from "react"

import { Campaigns as CampaignsTableSettings } from "initialState/tableSettings"

import tableColumns from "components/Campaigns/CampaignsColumns"
import { TableWrapper } from "components/shared/TableWrapper"

import { NewCampaignFormModal } from "./components"

import { useCampaigns } from "./hooks"

const PAGE_NAME = "Advertising"

export const Campaigns = () => {
  const {
    dataSource,
    searchOptions,
    canManage,
    canDelete,
    modalVisible,
    totalCount,
    selectFiltersOptions,
    getBannersHandler,
    updateIconHandler,
    displayModalHandler,
    createButtonHandler,
    deleteIconHandler,
    reloadBannersHandler,
  } = useCampaigns()

  return (
    <>
      <TableWrapper
        // @ts-ignore
        isAdditionalButtonContainerStyle
        isNeedSort
        actionsColumnWidth={120}
        componentTableSettings={CampaignsTableSettings}
        dataSource={dataSource}
        getData={getBannersHandler}
        pageTableSettings={CampaignsTableSettings}
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableColumns={tableColumns}
        tableGridTitle={PAGE_NAME}
        tableHeaderTitle={PAGE_NAME}
        totalCount={totalCount}
        tableButtons={[
          {
            checkAvailability: () => canManage,
            icon: "icnPlus",
            onClick: createButtonHandler,
            title: "Create",
            type: "primary",
          },
        ]}
        tableIcons={[
          {
            onClick: updateIconHandler,
            title: "Update",
            name: "icnEdit",
          },
          {
            onClick: deleteIconHandler,
            title: "Delete",
            name: "icnDeleteOutlined",
            checkAvailability: () => canDelete,
          },
        ]}
      />

      <NewCampaignFormModal
        reloadBanners={reloadBannersHandler}
        setVisible={displayModalHandler}
        visible={modalVisible}
      />
    </>
  )
}
