import { CAMPAIGN_TYPES } from "consts"

import type { NewCampaignData } from "types"

export const filterCampaignRequestPayload = (
  payload: Partial<NewCampaignData>
): Partial<NewCampaignData> => {
  if (payload.advertising_type === CAMPAIGN_TYPES.EXTERNAL) {
    return {
      advertising_type: payload.advertising_type,
      ad_campaign_name: payload.ad_campaign_name,
      is_active: payload.is_active,
      name: payload.name,
      image_url: payload.image_url,
      ad_campaign_type: payload.ad_campaign_type,
      language_id: payload.language_id,
      link: payload.link,
      ad_campaign_frequency_cap: payload.ad_campaign_frequency_cap,
    }
  }

  return {
    advertising_type: payload.advertising_type,
    ad_campaign_name: payload.ad_campaign_name,
    is_active: payload.is_active,
    name: payload.name,
    target: payload.target,
    title: payload.title,
    text: payload.text,
    language_id: payload.language_id,
    use_button: payload.use_button,
    button_label: payload.button_label,
    button_url: payload.button_url,
    target_operator: payload.target_operator,
  }
}
