import React, { Component } from "react"
import { checkIsArray } from "@develop/fe-library/dist/utils"
import _ from "lodash"
import PropTypes from "prop-types"

import { getVirtualEventCategory, pushEvent } from "utils/gtmLoader"

import { fbaValuesConstants } from "consts"
import { EVENTS_NAMES } from "consts/gtm"
import { PRODUCTS } from "consts/product"

import AmazonFbaSettingsModal from "./AmazonFbaSettingsModal"

import "./amazonFbaSettings.scss"

const { FBA_HOME } = fbaValuesConstants

class AmazonFbaSettingsContainer extends Component {
  state = {}

  static getDerivedStateFromProps({ accounts }, { propAccounts }) {
    if (!_.isEqual(accounts, propAccounts)) {
      return {
        accounts: accounts.map(
          ({
            countryOptions,
            countryValue,
            fbaStockLocationDate,
            fbaStockStorage,
            isFbaDeMarketplace,
            id,
            name,
            sellerId,
          }) => ({
            countryOptions,
            countryValue,
            fbaStockLocationDate,
            fbaStockStorage,
            isFbaDeMarketplace,
            id,
            name,
            sellerId,
          })
        ),
        propAccounts: accounts,
      }
    }

    return {}
  }

  componentDidMount() {
    const {
      getAccounts,
      getAccountMarketplaces,
      getAmazonCustomerAccountStock,
      getAmazonMarketplaces,
      productNames,
      productNamesFromProps,
    } = this.props
    const virtualEventCategory = getVirtualEventCategory({
      name: "modalWizardFBASettings",
      productNames: productNames || productNamesFromProps,
    })

    pushEvent({
      virtualEventAction: "show",
      virtualEventCategory,
      event: EVENTS_NAMES.updEvents,
    })

    getAmazonCustomerAccountStock({ successCallback: undefined })
    getAmazonMarketplaces()
    getAccountMarketplaces(false)
    getAccounts(false)
  }

  onCountryChange = (accountId, marketplaceId, deleteItem) => {
    this.setState({
      accounts: undefined,
    })
    const { accounts } = this.state
    const newAccounts = [...accounts]
    const accountIndex = newAccounts.findIndex(({ id }) => id === accountId)
    const updatedAccount = { ...newAccounts[accountIndex] }
    let countryValue = [...updatedAccount.countryValue]

    deleteItem
      ? countryValue.splice(
          countryValue.findIndex((id) => id === marketplaceId),
          1
        )
      : countryValue.push(marketplaceId)

    updatedAccount.countryValue = countryValue
    newAccounts[accountIndex] = updatedAccount

    this.setState({
      accounts: newAccounts,
    })
  }

  onUpdate = (accountId, payload) => {
    const { accounts } = this.state
    const newAccounts = [...accounts]
    const accountIndex = newAccounts.findIndex(({ id }) => id === accountId)
    const updatedAccount = { ...newAccounts[accountIndex], ...payload }

    newAccounts.splice(accountIndex, 1, updatedAccount)

    this.setState({
      accounts: newAccounts,
    })
  }

  onSave = () => {
    const {
      amazonCustomerAccountStocks,
      getAccounts,
      getAmazonCustomerAccountStock,
      moduleSetup,
      onClose,
      showModuleSetupWizard,
      updateFbaSettings,
      getCurrentCustomer,
      productNames,
      productNamesFromProps,
      repricerSubscriptionPlans,
      amazonAccountMarketplaces,
      accountId,
    } = this.props

    const virtualEventCategory = getVirtualEventCategory({
      name: "modalWizardFBASettings",
      productNames: productNames || productNamesFromProps,
    })

    const { accounts } = this.state

    const mappedAccounts = accounts.map(
      ({
        countryValue,
        fbaStockStorage,
        fbaStockLocationDate,
        id: accountId,
      }) => ({
        fba_stock_storage: fbaStockStorage ?? FBA_HOME,
        fba_stock_location_date: fbaStockLocationDate,
        id: accountId,
        stocksToAdd: countryValue.filter(
          (marketplaceId) =>
            !amazonCustomerAccountStocks.some(
              ({ amazon_customer_account_id, amazon_marketplace_id }) =>
                amazon_customer_account_id === accountId &&
                amazon_marketplace_id === marketplaceId
            )
        ),
        stocksToDelete: amazonCustomerAccountStocks.filter(
          ({ amazon_customer_account_id, amazon_marketplace_id }) =>
            amazon_customer_account_id === accountId &&
            countryValue.indexOf(amazon_marketplace_id) === -1
        ),
      })
    )

    updateFbaSettings(mappedAccounts, () => {
      getAccounts(false, () => {
        getAmazonCustomerAccountStock({ successCallback: undefined })
      })

      if (moduleSetup) {
        pushEvent({
          vHitNonInteraction: false,
          virtualEventAction: "click-save",
          virtualEventCategory,
          event: EVENTS_NAMES.updEvents,
        })
        getCurrentCustomer(
          false,
          ({
            has_dpa_contract,
            allow_lost_full_service,
            lost_module_signed_full_service,
          }) => {
            // Delete after Full service MVP
            const isFullServiceSetup =
              !!allow_lost_full_service && !!lost_module_signed_full_service

            const { last, current } = repricerSubscriptionPlans

            const canSetupRepricerTrial =
              !checkIsArray(amazonAccountMarketplaces) && last === null

            const isRepricerHasPlan = current !== null

            const nextStepRepricerTrialChecker = canSetupRepricerTrial
              ? "repricerTrialModal"
              : "repricerSubscriptions"

            const nextStepWithCheckRepricerSubscription = isRepricerHasPlan
              ? "summary"
              : nextStepRepricerTrialChecker

            const nextStepForRepricerWithDpaCheck =
              has_dpa_contract === 1
                ? nextStepWithCheckRepricerSubscription
                : "dpaSettings"

            const dpaCheckingRoute =
              has_dpa_contract === 1 ? "summary" : "dpaSettings"
            // Delete after Full service MVP
            const lostNextWithFullServiceSteps =
              has_dpa_contract === 1
                ? "createFullServiceAccount"
                : "dpaSettings"

            const lostNextStep = isFullServiceSetup
              ? lostNextWithFullServiceSteps
              : dpaCheckingRoute
            //

            // USE this code
            // const lostNextStep =
            //   has_dpa_contract === 1
            //     ? "createFullServiceAccount"
            //     : "dpaSettings"

            const wizardRoutes = {
              [PRODUCTS.repricer]: nextStepForRepricerWithDpaCheck,
              [PRODUCTS.repricerB2C]: dpaCheckingRoute,
              [PRODUCTS.repricerB2B]: dpaCheckingRoute,
              [PRODUCTS.lost]: lostNextStep,
              [PRODUCTS.bas]: dpaCheckingRoute,
            }

            const nextWizardStep = wizardRoutes[productNames] || "summary"

            showModuleSetupWizard(true, nextWizardStep, productNames, {
              isTriggerGTM4: true,
              accountId,
              isRepricerSubscriptionChange: false,
            })
            onClose()
          }
        )
      } else {
        onClose()
      }
    })
  }

  render() {
    const {
      canManage,
      language,
      moduleSetup,
      onClose,
      productNames,
      productNamesFromProps,
    } = this.props
    const { accounts } = this.state

    return (
      <AmazonFbaSettingsModal
        accounts={accounts}
        canManage={canManage}
        language={language}
        moduleSetup={moduleSetup}
        productNames={productNames || productNamesFromProps}
        onClose={onClose}
        onCountryChange={this.onCountryChange}
        onSave={this.onSave}
        onUpdate={this.onUpdate}
      />
    )
  }
}

AmazonFbaSettingsContainer.propTypes = {
  accounts: PropTypes.array.isRequired,
  amazonCustomerAccountStocks: PropTypes.array.isRequired,
  canManage: PropTypes.bool.isRequired,
  getAccounts: PropTypes.func.isRequired,
  getAccountMarketplaces: PropTypes.func.isRequired,
  getAmazonCustomerAccountStock: PropTypes.func.isRequired,
  getAmazonMarketplaces: PropTypes.func.isRequired,
  language: PropTypes.string.isRequired,
  moduleSetup: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
  showModuleSetupWizard: PropTypes.func.isRequired,
  updateFbaSettings: PropTypes.func.isRequired,
}

export default AmazonFbaSettingsContainer
