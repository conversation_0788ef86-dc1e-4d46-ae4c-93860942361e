import React from "react"
import { Radio } from "antd"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { fbaValuesConstants } from "consts"

import { FBAStoreCheckerProps } from "./FBAStoreCheckerTypes"

import styles from "./fbaStoreChecker.module.scss"

const { FBA_HOME, FBA_PL_CZ } = fbaValuesConstants

export const FBAStoreChecker = ({
  fbaStockStorage,
  onChangeFbaStockStorage,
}: FBAStoreCheckerProps) => {
  return (
    <Radio.Group
      value={fbaStockStorage ?? FBA_HOME}
      onChange={onChangeFbaStockStorage}
    >
      <Box flexDirection="column" paddingLeft="l">
        <Radio className={styles.firstRadioButton} value={FBA_HOME}>
          <Typography variant="--font-body-text-9">
            {l(
              "Store in Germany only. Pay an additional fulfillment fee of up to €0.26 on every FBA fulfillment for Germany"
            )}
          </Typography>
        </Radio>
        <Radio value={FBA_PL_CZ}>
          <Typography variant="--font-body-text-9">
            {l(
              "Store in Germany, Poland, and Czech Republic. Save up to €0.26 on every FBA fulfillment from Germany. VAT obligations in Poland and Czech Republic apply."
            )}
          </Typography>
        </Radio>
      </Box>
    </Radio.Group>
  )
}
