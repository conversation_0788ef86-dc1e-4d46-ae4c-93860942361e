import React from "react"
import { Box, Button, Icon, Typography } from "@develop/fe-library"

import { useActivateFullServiceWithOneByOne } from "hooks"

import l from "utils/intl"

import { FULL_SERVICE_CONTENT } from "./consts"

export const FullServiceBlock = () => {
  const activateFullServiceWithOneByOneHandler =
    useActivateFullServiceWithOneByOne()

  return (
    <Box
      backgroundColor="--color-row-select"
      flexDirection="column"
      padding="m"
      mLG={{
        padding: "l",
      }}
    >
      {/*Title*/}
      <Box>
        <Typography variant="--font-body-text-7">
          {l(
            "Switch now to Lost & Found Full-Service to access all benefits without any workload on your side:"
          )}
        </Typography>
      </Box>
      {/*Content*/}
      <Box
        flexDirection="column"
        flexWrap="wrap"
        mLG={{ flexDirection: "row", gap: "l" }}
      >
        {FULL_SERVICE_CONTENT.map(
          ({ iconName, iconColor, text, textColor, textVariant }) => (
            <Box
              key={`${iconName}-${text}`}
              align="flex-start"
              flexDirection="column"
              marginTop="m"
              mLG={{
                width: "clamp(170px, 100%, 172px)",
              }}
            >
              <Icon color={iconColor} name={iconName} />
              <Box marginTop="s">
                <Typography color={textColor} variant={textVariant}>
                  {l(text)}
                </Typography>
              </Box>
            </Box>
          )
        )}
      </Box>
      {/*Button Block*/}
      <Box marginTop="m" mXL={{ width: "min-content" }} width="100%">
        <Button fullWidth onClick={activateFullServiceWithOneByOneHandler}>
          {l("Enable Full-Service")}
        </Button>
      </Box>
    </Box>
  )
}
