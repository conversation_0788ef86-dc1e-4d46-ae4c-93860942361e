import React, { useEffect, useState } from "react"
import { useHistory } from "react-router-dom"
import { Select } from "antd"
import {
  <PERSON>ert,
  Box,
  ButtonPopover,
  Grid,
  Popover,
  Typography,
} from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import classNames from "classnames"
import { Field, Form } from "formik"
import { get, isEmpty, isEqual } from "lodash"

import FormattedMessage from "components/FormattedMessage"
import { FormikWithRoleValidation } from "components/RoleEditor"
import { Button } from "components/shared/Buttons"
import {
  SelectField as DefaultSelect,
  withErrorField,
} from "components/shared/FormFields"
import RoleOperations from "components/shared/RoleOperations"

import usePrevious from "hooks/usePrevious"

import { checkIsArray } from "utils/arrayHelpers"
import {
  hasCustomRole,
  isAdminRole,
  mapInitialValues,
} from "utils/privacyPolicyUtils"
import { buildTextRepricerActivation } from "utils/repricerHelpers"

import { USER_ROLES } from "consts/user"

import styles from "./privacyPolicy.module.scss"

const {
  ADMIN_ROUTES: { PATH_USERS },
} = ROUTES

const SelectField = withErrorField(DefaultSelect, true, false)

export const PrivacyPolicyEditor = ({
  getUserOperations,
  initialValues,
  isEditable,
  listOfOperations,
  roles,
  updateSelectedUser,
  user,
  defaultOperations,
  isUserPermissionsAllowed,
  isUpgradeRepricerSubscription,
}) => {
  const roleInitialValue = get(user, "user.role", "")
  const [selectedRole, setSelectedRole] = useState(roleInitialValue)
  const prevInitialValue = usePrevious(initialValues)
  const [formikValues, setFormikValues] = useState(initialValues)
  const [activeKeys, setActiveKeys] = useState()

  const history = useHistory()

  const { pathname } = history.location

  const handleChange = (value) => {
    setSelectedRole(value)
  }

  const handleSubmit = ({ role, ...rest }, { setFieldValue, setErrors }) => {
    const customRole = {
      operations: getObjectKeys(rest).filter((x) => rest[x]),
    }
    const payload = {
      role,
      customer_id: user.customer_id,
      ...(hasCustomRole(role) ? customRole : {}),
    }
    const params = { id: user.id }

    return updateSelectedUser(
      { params, payload },
      () => {
        if (role !== "admin") {
          getUserOperations(role)
        }
        setFieldValue(role)
      },
      (errors) => {
        if (checkIsArray(errors)) {
          setErrors(
            errors.reduce((acc, { field, message }) => {
              acc[field] = message

              return acc
            }, {})
          )
        }
      }
    )
  }

  const handleReset = ({ resetForm, setFieldValue }) => {
    return () => {
      resetForm()
      setFieldValue("role", roleInitialValue)
      setSelectedRole(roleInitialValue)
    }
  }

  useEffect(() => {
    if (roleInitialValue !== selectedRole) {
      setSelectedRole(roleInitialValue)
    }
    // eslint-disable-next-line
  }, [roleInitialValue])

  useEffect(() => {
    if (!selectedRole) {
      return
    }
    if (isAdminRole(selectedRole)) {
      setFormikValues(mapInitialValues(initialValues, () => true))

      return
    }
    if (hasCustomRole(selectedRole)) {
      if (!hasCustomRole(roleInitialValue)) {
        setFormikValues(mapInitialValues(initialValues, () => false))

        return
      }
      if (isEqual(initialValues, prevInitialValue)) {
        setFormikValues(initialValues)

        return
      }
    }
    if (isEqual(initialValues, prevInitialValue)) {
      setFormikValues(initialValues)
    }
    getUserOperations(selectedRole)
    // eslint-disable-next-line
  }, [selectedRole, setFormikValues, getUserOperations, roleInitialValue])

  useEffect(() => {
    if (
      !isAdminRole(selectedRole) &&
      !hasCustomRole(selectedRole) &&
      !isEqual(initialValues, prevInitialValue)
    ) {
      setFormikValues(initialValues)
    }

    const isAdminRoleInitialValuesLoaded =
      isAdminRole(selectedRole) && isEmpty(prevInitialValue)

    if (isAdminRoleInitialValuesLoaded) {
      setFormikValues(mapInitialValues(initialValues, () => true))

      return
    }

    const hasCustomRoleAndSelectedRoleIsInitialValue =
      hasCustomRole(roleInitialValue) && selectedRole === roleInitialValue

    if (hasCustomRoleAndSelectedRoleIsInitialValue) {
      setFormikValues(initialValues)
    }
    // eslint-disable-next-line
  }, [initialValues, prevInitialValue, roleInitialValue])

  const roleOptions = (() => {
    const filterMapper = {
      [USER_ROLES.invitedUser]: [
        USER_ROLES.invitedSlUser,
        USER_ROLES.invitedTranslatorUser,
      ],
      [USER_ROLES.invitedSlUser]: [
        USER_ROLES.invitedUser,
        USER_ROLES.invitedTranslatorUser,
      ],
      [USER_ROLES.invitedTranslatorUser]: [
        USER_ROLES.invitedUser,
        USER_ROLES.invitedSlUser,
      ],
    }

    const trasformedRoles = roles?.map((role) => {
      const isCustomUserAndUserPermissionsNotAllowed =
        !isUserPermissionsAllowed &&
        role?.value?.includes("customUser") &&
        !pathname.includes(PATH_USERS)

      return {
        ...role,
        disabled: isCustomUserAndUserPermissionsNotAllowed,
        popoverText: isCustomUserAndUserPermissionsNotAllowed
          ? buildTextRepricerActivation(isUpgradeRepricerSubscription)
          : "",
      }
    })

    if (roleInitialValue in filterMapper) {
      return trasformedRoles?.filter(
        (role) => !filterMapper[roleInitialValue].includes(role.value)
      )
    }

    return trasformedRoles?.filter(
      (role) =>
        role.value !== USER_ROLES.invitedUser &&
        role.value !== USER_ROLES.invitedSlUser &&
        role.value !== USER_ROLES.invitedTranslatorUser
    )
  })()

  const roleLabel = roles.find((role) => role.value === roleInitialValue)?.label
  const isRoleLabelVisible = !isEditable && !!roleLabel
  const isCheckboxesDisabled = !isEditable || !hasCustomRole(selectedRole)
  const isCustomUserAndUserPermissionsNotAllowed =
    !isUserPermissionsAllowed &&
    selectedRole?.includes("customUser") &&
    !pathname.includes(PATH_USERS)

  return (
    <>
      <FormikWithRoleValidation
        children={({ isSubmitting, errors, setFieldValue, resetForm }) => {
          return (
            <Form noValidate>
              <Grid container flexDirection="column" flexWrap="nowrap">
                <Grid item>
                  <Box
                    className={classNames(styles.titleBox, styles.block)}
                    flexDirection="column"
                    gap="l"
                  >
                    <Typography variant="--font-headline-3">
                      <FormattedMessage id={"Role administration"} />
                    </Typography>

                    <Grid
                      container
                      align="center"
                      flexWrap="nowrap"
                      gapMLG="m"
                      gapMSM="s"
                    >
                      <Grid
                        item
                        always="155px"
                        mSM={isEditable ? 12 : undefined}
                      >
                        <Typography variant="--font-body-text-3">
                          <FormattedMessage id="Role" />
                        </Typography>
                      </Grid>

                      <Grid item mLG mSM={isEditable ? 12 : undefined}>
                        {isEditable ? (
                          <Field
                            component={SelectField}
                            disabled={!isEditable}
                            name="role"
                            onChange={handleChange}
                          >
                            {roleOptions.map(
                              ({
                                value,
                                label,
                                disabled = false,
                                popoverText = "",
                              }) => (
                                <Select.Option
                                  key={value}
                                  disabled={disabled}
                                  value={value}
                                >
                                  <Popover content={popoverText}>
                                    <div>
                                      {label ? (
                                        <FormattedMessage id={label} />
                                      ) : (
                                        ""
                                      )}
                                    </div>
                                  </Popover>
                                </Select.Option>
                              )
                            )}
                          </Field>
                        ) : null}
                        {isRoleLabelVisible ? (
                          <Typography
                            className={styles.content}
                            variant="--font-body-text-4"
                          >
                            <FormattedMessage id={roleLabel} />
                          </Typography>
                        ) : null}
                      </Grid>
                    </Grid>

                    {errors.operations ? (
                      <Alert alertType="error" message={errors.operations} />
                    ) : null}
                  </Box>
                </Grid>

                <Grid item>
                  <RoleOperations
                    activeKeys={activeKeys}
                    disabled={isCheckboxesDisabled}
                    options={listOfOperations}
                    setActiveKeys={setActiveKeys}
                  />
                </Grid>

                {isEditable ? (
                  <Grid item>
                    <Box className={styles.block} display="block">
                      <Grid container gap="m" justify="end">
                        <Grid item mLG="auto" mSM={6}>
                          <Button
                            fullWidth
                            disabled={isSubmitting}
                            htmlType="reset"
                            type="reset"
                            onClick={handleReset({ resetForm, setFieldValue })}
                          >
                            <FormattedMessage id="Cancel" />
                          </Button>
                        </Grid>

                        <Grid item mLG="auto" mSM={6}>
                          <ButtonPopover
                            content={
                              isCustomUserAndUserPermissionsNotAllowed
                                ? buildTextRepricerActivation(
                                    isUpgradeRepricerSubscription
                                  )
                                : ""
                            }
                            disabled={
                              isCustomUserAndUserPermissionsNotAllowed ||
                              isSubmitting
                            }
                          >
                            <FormattedMessage id="Save" />
                          </ButtonPopover>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                ) : null}
              </Grid>
            </Form>
          )
        }}
        enableReinitialize
        controller={isEditable ? listOfOperations : []}
        initialValues={{
          role: selectedRole,
          ...formikValues,
        }}
        onSubmit={handleSubmit}
      />
    </>
  )
}
