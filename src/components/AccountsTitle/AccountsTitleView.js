import React, { useEffect } from "react"
import { Box, Typography } from "@develop/fe-library"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import AppHeader from "components/shared/appHeader/AppHeader"
import { RestrictedButtonPopover } from "components/shared/Buttons"

import { pushEvent } from "utils/gtmLoader"
import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"
import { EVENTS_NAMES } from "consts/gtm"

const pageTitle = "Amazon seller account management"

export const AccountsTitle = ({ accountsCount, onAddNew, isDemoAccount }) => {
  useEffect(() => {
    pushEvent({
      virtualEventAction: "show",
      virtualEventCategory: "accounts",
      event: EVENTS_NAMES.updEvents,
    })
  }, [])

  const iconHoverText = isDemoAccount
    ? l("This feature is available in full version.")
    : l("Add/Restore account")

  return (
    <Box
      align="center"
      gap="l"
      justify="space-between"
      mSM={{
        padding: "m",
      }}
      mXL={{
        padding: "m l",
      }}
    >
      <Box flexDirection="column">
        <Typography variant="--font-headline-3">
          <FormattedMessage id={pageTitle} />
          <AppHeader title={pageTitle} />
        </Typography>
        <Typography
          color="--color-text-placeholders"
          variant="--font-body-text-4"
        >
          <FormattedMessage
            defaultMessage="Number of accounts: {count}"
            id="Number of accounts: {count}"
            values={{ count: accountsCount }}
          />
        </Typography>
      </Box>

      <RestrictedButtonPopover
        iconOnly
        content={iconHoverText}
        disabled={isDemoAccount}
        icon="icnPlus"
        managePermission={permissionKeys.amazonCustomerAccountManage}
        placement="topRight"
        popoverMessage={restrictPopoverMessages.setupAccount}
        size="small"
        variant="primary"
        onClick={onAddNew}
      />
    </Box>
  )
}

AccountsTitle.propTypes = {
  accountsCount: PropTypes.number.isRequired,
  onAddNew: PropTypes.func.isRequired,
}
