import React from "react"

import { TransactionTableSettings } from "initialState/tableSettings"

import { TableWrapper } from "components/shared/TableWrapper"
import tableColumns from "components/Transaction/transactionTableColumns"

import { useTransaction } from "./hooks"

import "./transaction.scss"

const PAGE_NAME = "Transaction"

export const Transaction = () => {
  const {
    dataSource,
    totalCount,
    searchOptions,
    getCustomerInvoiceTransactionsHandler,
  } = useTransaction()

  return (
    <TableWrapper
      // @ts-ignore
      isNeedSort
      actionsColumnWidth={1}
      canShowColumnsButtons={false}
      className="transaction-grid"
      componentTableSettings={TransactionTableSettings}
      dataSource={dataSource}
      exportParseFromColumn={1}
      getData={getCustomerInvoiceTransactionsHandler}
      pageTableSettings={TransactionTableSettings}
      searchOptions={searchOptions}
      tableColumns={tableColumns}
      tableGridTitle={PAGE_NAME}
      tableHeaderTitle={PAGE_NAME}
      totalCount={totalCount}
    />
  )
}
