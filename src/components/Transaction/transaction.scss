@import "assets/styles/variables.scss";

.transaction-grid {
  .ant-table-thead > tr:first-child > th:first-child {
    padding-right: 5px;
  }
  .ant-table-tbody > tr > td {
    position: relative;

    &:after {
      content: "";
      position: absolute;
      font-size: 0;
      background-color: $hover_grid;
      width: 1px;
      right: 0;
      top: 13px;
      bottom: 13px;
    }
  }
  .ant-table-column-title {
    white-space: nowrap;
  }
}
