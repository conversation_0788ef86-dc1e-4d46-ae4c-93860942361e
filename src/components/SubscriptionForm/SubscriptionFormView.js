import React, { Component } from "react"
import { Input } from "antd"

import Typography from "components/Typography"

import styles from "components/SubscriptionForm/subscriptionForm.module.scss"

const { Search } = Input

class SubscriptionFormView extends Component {
  render() {
    return (
      <div className={styles.container}>
        <Typography type="div" variant="smallTitle">
          Subscribe for newsletter
        </Typography>
        <div className={styles.formContainer}>
          <Search
            className={styles.input}
            enterButton="Subscribe"
            placeholder="Enter your email"
          />
        </div>
      </div>
    )
  }
}

export default SubscriptionFormView
