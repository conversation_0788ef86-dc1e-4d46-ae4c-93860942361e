import { useDispatch, useSelector } from "react-redux"

import CustomerInvoiceSummaryActions from "actions/customerInvoiceSummaryActions"

import {
  customerInvoiceSummaryItemsSelector,
  customerInvoiceSummarySearchOptionsSelector,
  customerInvoiceSummaryTotalCountSelector,
} from "selectors/customerInvoiceSummarySelectors"

// @ts-ignore
const { download, get: getCustomerInvoiceSummaryData } =
  CustomerInvoiceSummaryActions

export const useCustomerInvoiceSummary = () => {
  const dataSource = useSelector(customerInvoiceSummaryItemsSelector)
  const searchOptions = useSelector(customerInvoiceSummarySearchOptionsSelector)
  const totalCount = useSelector(customerInvoiceSummaryTotalCountSelector)
  const selectFiltersOptions = {}

  const dispatch = useDispatch()

  const downloadFileHandler = <Type,>(id: Type, version: number) => {
    return dispatch(download(id, version))
  }

  const getCustomerInvoiceSummary = <Type,>(searchOptions: Type) => {
    return dispatch(getCustomerInvoiceSummaryData(searchOptions))
  }

  const downloadFileIconHandler = <Type,>({ id }: { id: Type }) => {
    return downloadFileHandler(id, 34)
  }

  return {
    dataSource,
    searchOptions,
    totalCount,
    selectFiltersOptions,
    getCustomerInvoiceSummary,
    downloadFileIconHandler,
  }
}
