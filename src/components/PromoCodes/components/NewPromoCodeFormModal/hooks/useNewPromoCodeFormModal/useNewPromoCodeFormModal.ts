import { use<PERSON>allback, useEffect, useMemo } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { FormItem, Option } from "@develop/fe-library"
import { PRODUCT_NAMES, PRODUCTS } from "@develop/fe-library/dist/consts"
import { buildFormSubmitFailureCallback } from "@develop/fe-library/dist/utils"
import { yupResolver } from "@hookform/resolvers/yup"

import partnerChannelActions from "actions/partnerChannelActions"
import partnersActions from "actions/partnersActions"
import promoCodeActions from "actions/promoCodeActions"

import { partnerChannelsorSpecificPartnerSelector } from "selectors/partnerChannelSelectors"
import { partnersSelector } from "selectors/partnersSelectors"
import { userTimezoneSelector } from "selectors/timezoneSelectors"
import { languageSelector } from "selectors/translationsSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import { getDateTimePickerLabels } from "utils/datePicker"
import l from "utils/intl"

import { LOCALES } from "consts/locales"

import { buildSchema, formatCreatePromoCodeRequestBody } from "./utils"

import { NewPromoCodeData } from "types"

import { UseNewPromoCodeFormModal } from "./UseNewPromoCodeFormModalTypes"

// @ts-expect-error
const { getForSpecificPartner } = partnerChannelActions

// @ts-expect-error
const { getAll: getPartners } = partnersActions

const { createPromoCode } = promoCodeActions

export const useNewPromoCodeFormModal: UseNewPromoCodeFormModal = ({
  reloadPromoCodes,
  setVisible,
}) => {
  const dispatch = useDispatch()

  const { partnerList: canViewPartnerList } = useSelector(permissionsSelector)

  const userTimezone = useSelector(userTimezoneSelector)
  const language = useSelector(languageSelector)
  const partners = useSelector(partnersSelector)
  const channels = useSelector(partnerChannelsorSpecificPartnerSelector)

  const form = useForm<NewPromoCodeData>({
    mode: "onChange",
    resolver: yupResolver(buildSchema()),
    defaultValues: {
      active: 1,
    },
  })

  const {
    setError,
    handleSubmit: formHandleSubmit,
    reset,
    setValue,
    formState: { isDirty, isValid, isSubmitting },
  } = form

  const isSaveButtonDisabled: boolean = !isDirty || !isValid || isSubmitting
  const isCancelButtonDisabled: boolean = isSubmitting

  const getPartnerChannelsForSpecificPartner = useCallback(
    (id: number | string, callback?: () => void) => {
      dispatch(getForSpecificPartner(id, callback))
    },
    [dispatch]
  )

  const handleChangePartnerId = useCallback(
    (option: Option) => {
      const id = option?.value

      if (!id) {
        setValue("partner_channel_id", null)

        return
      }

      getPartnerChannelsForSpecificPartner(id, () => {
        const defaultChannel = channels.find((channel) => !!channel.is_default)
        let channelId = null

        if (defaultChannel) {
          channelId = defaultChannel.id
        } else if (channels.length) {
          const [firstChannel] = channels

          channelId = firstChannel.id
        }

        setValue("partner_channel_id", channelId)
      })
    },
    [channels, getPartnerChannelsForSpecificPartner, setValue]
  )

  useEffect(() => {
    if (canViewPartnerList) {
      dispatch(getPartners())
    }
  }, [dispatch, canViewPartnerList])

  const handleCancel = () => {
    setVisible(false)
    reset()
  }

  const handleSubmit = formHandleSubmit(async (payload: NewPromoCodeData) => {
    const successCallback = () => {
      reloadPromoCodes()
      reset()
      setVisible(false)
    }

    await dispatch(
      createPromoCode({
        payload: formatCreatePromoCodeRequestBody({
          payload,
          timezone: userTimezone,
        }),
        failureCallback: buildFormSubmitFailureCallback<NewPromoCodeData>({
          setError,
        }),
        successCallback,
      })
    )
  })

  const formItems = useMemo<FormItem[]>(() => {
    const dateTimePickerLabels = getDateTimePickerLabels({})

    return [
      {
        type: "text",
        name: "code",
        inputProps: {
          label: l("Name"),
          isRequired: true,
        },
        gridItemProps: { always: 12 },
      },
      {
        type: "switch",
        name: "active",
        inputProps: {
          label: l("Active"),
          isNumeric: true,
          isRequired: true,
        },
        gridItemProps: { always: 12 },
      },
      {
        type: "dateTime",
        name: "start_date",
        inputProps: {
          label: l("Start date"),
          timezone: userTimezone,
          language: LOCALES[language],
          locale: LOCALES[language],
          labels: dateTimePickerLabels,
        },
        gridItemProps: { mSM: 12, tb: 6 },
      },
      {
        type: "dateTime",
        name: "end_date",
        inputProps: {
          label: l("End date"),
          timezone: userTimezone,
          language: LOCALES[language],
          locale: LOCALES[language],
          labels: dateTimePickerLabels,
        },
        gridItemProps: { mSM: 12, tb: 6 },
      },
      {
        type: "numeric",
        name: "lost_found_credit",
        inputProps: {
          label: l("Money amount for L&F"),
          isDecimalAllowed: false,
          isNegativeAllowed: false,
        },
        gridItemProps: { always: 12 },
      },
      ...(canViewPartnerList
        ? [
            {
              type: "select",
              name: "partner_id",
              inputProps: {
                label: l("Partner ID"),
                isGlobal: true,
                hasClearIcon: true,
                options: partners.map(({ id, title }) => ({
                  label: title,
                  value: id,
                })),
                onChange: handleChangePartnerId,
              },
              gridItemProps: { mSM: 12, tb: 6 },
            },
            {
              type: "select",
              name: "partner_channel_id",
              inputProps: {
                label: l("Partner channel ID"),
                isGlobal: true,
                hasClearIcon: true,
                options: channels.map(({ id, name }) => ({
                  label: name || id,
                  value: id,
                })),
              },
              gridItemProps: { mSM: 12, tb: 6 },
            },
          ]
        : []),
      {
        type: "numeric",
        name: "repricer_customer_discount",
        inputProps: {
          label: l("Permanent discount on {repricer}", {
            repricer: PRODUCT_NAMES[PRODUCTS.REPRICER],
          }),
          isDecimalAllowed: false,
          isNegativeAllowed: false,
          suffixIcons: [
            { name: "icnPercentage", color: "--color-icon-static" },
          ],
        },
        gridItemProps: { always: 12 },
      },
      {
        type: "numeric",
        name: "repricer_extended_trial_period_days",
        inputProps: {
          label: l("{repricer} extended trial days", {
            repricer: PRODUCT_NAMES[PRODUCTS.REPRICER],
          }),
          isDecimalAllowed: false,
          isNegativeAllowed: false,
        },
        gridItemProps: { always: 12 },
      },
      {
        type: "dateTime",
        name: "repricer_extended_trial_period_start_date",
        inputProps: {
          label: l("{repricer} extended trial start date", {
            repricer: PRODUCT_NAMES[PRODUCTS.REPRICER],
          }),
          timezone: userTimezone,
          language: LOCALES[language],
          locale: LOCALES[language],
          labels: dateTimePickerLabels,
        },
        gridItemProps: { mSM: 12, tb: 6 },
      },
      {
        type: "dateTime",
        name: "repricer_extended_trial_period_end_date",
        inputProps: {
          label: l("{repricer} extended trial end date", {
            repricer: PRODUCT_NAMES[PRODUCTS.REPRICER],
          }),
          timezone: userTimezone,
          language: LOCALES[language],
          locale: LOCALES[language],
          labels: dateTimePickerLabels,
        },
        gridItemProps: { mSM: 12, tb: 6 },
      },
      {
        type: "numeric",
        name: "ba_customer_discount",
        inputProps: {
          label: l("Permanent discount on BA"),
          isDecimalAllowed: false,
          isNegativeAllowed: false,
          suffixIcons: [
            { name: "icnPercentage", color: "--color-icon-static" },
          ],
        },
        gridItemProps: { always: 12 },
      },
      {
        type: "numeric",
        name: "bas_extended_trial_period_days",
        inputProps: {
          label: l("BA extended trial days"),
          isDecimalAllowed: false,
          isNegativeAllowed: false,
        },
        gridItemProps: { always: 12 },
      },
      {
        type: "dateTime",
        name: "bas_extended_trial_period_start_date",
        inputProps: {
          label: l("BA extended trial start date"),
          timezone: userTimezone,
          language: LOCALES[language],
          locale: LOCALES[language],
          labels: dateTimePickerLabels,
        },
        gridItemProps: { mSM: 12, tb: 6 },
      },
      {
        type: "dateTime",
        name: "bas_extended_trial_period_end_date",
        inputProps: {
          label: l("BA extended trial end date"),
          timezone: userTimezone,
          language: LOCALES[language],
          locale: LOCALES[language],
          labels: dateTimePickerLabels,
        },
        gridItemProps: { mSM: 12, tb: 6 },
      },
    ]
  }, [
    canViewPartnerList,
    channels,
    language,
    partners,
    userTimezone,
    handleChangePartnerId,
  ])

  return {
    form,
    formItems,
    isSaveButtonDisabled,
    isCancelButtonDisabled,
    handleCancel,
    handleSubmit,
  }
}
