import React from "react"
import { FormItems, Modal } from "@develop/fe-library"

import l from "utils/intl"

import { useNewPromoCodeFormModal } from "./hooks"

import { NewPromoCodeData } from "types"

import { NewPromoCodeFormModalProps } from "./NewPromoCodeFormModalTypes"

export const NewPromoCodeFormModal = ({
  reloadPromoCodes,
  setVisible,
  visible,
}: NewPromoCodeFormModalProps) => {
  const {
    form,
    formItems,
    isSaveButtonDisabled,
    isCancelButtonDisabled,
    handleCancel,
    handleSubmit,
  } = useNewPromoCodeFormModal({
    reloadPromoCodes,
    setVisible,
  })

  return (
    <Modal
      cancelButtonText={l("Cancel")}
      isDisabledFooter={false}
      okButtonText={l("Save")}
      title={l("Create promocode")}
      visible={visible}
      width="--modal-size-m"
      cancelButtonProps={{
        disabled: isCancelButtonDisabled,
      }}
      okButtonProps={{
        disabled: isSaveButtonDisabled,
      }}
      onCancel={handleCancel}
      onOk={handleSubmit}
    >
      <FormItems<NewPromoCodeData>
        boxContainerProps={{ width: "100%" }}
        // @ts-expect-error
        form={form}
        gridContainerProps={{ gap: "m" }}
        items={formItems}
      />
    </Modal>
  )
}
