import { formatDateInUtc } from "@develop/fe-library/dist/utils"

import { FormatEditPromoCodeRequestBody } from "./formatEditPromoCodeRequestBodyTypes"

export const formatEditPromoCodeRequestBody: FormatEditPromoCodeRequestBody = ({
  payload: {
    start_date,
    end_date,
    repricer_extended_trial_period_start_date,
    repricer_extended_trial_period_end_date,
    bas_extended_trial_period_start_date,
    bas_extended_trial_period_end_date,
    ...restData
  },
  timezone,
}) => {
  const converLocalDateToUtcString = (
    date: Date | null | undefined
  ): string | null | undefined =>
    date ? formatDateInUtc({ date, timezone }) : date

  return {
    ...restData,
    start_date: converLocalDateToUtcString(start_date),
    end_date: converLocalDateToUtcString(end_date),
    repricer_extended_trial_period_start_date: converLocalDateToUtcString(
      repricer_extended_trial_period_start_date
    ),
    repricer_extended_trial_period_end_date: converLocalDateToUtcString(
      repricer_extended_trial_period_end_date
    ),
    bas_extended_trial_period_start_date: converLocalDateToUtcString(
      bas_extended_trial_period_start_date
    ),
    bas_extended_trial_period_end_date: converLocalDateToUtcString(
      bas_extended_trial_period_end_date
    ),
  }
}
