import * as yup from "yup"

import { buildErrorMessagesMapper } from "utils/formHelpers/buildErrorMessagesMapper"

export const buildSchema = () => {
  const { required: requiredErrorMessage } = buildErrorMessagesMapper()

  return yup
    .object()
    .shape({
      active: yup.number().oneOf([0, 1]).required(requiredErrorMessage),
      code: yup.string().required(requiredErrorMessage),
    })
    .required()
}
