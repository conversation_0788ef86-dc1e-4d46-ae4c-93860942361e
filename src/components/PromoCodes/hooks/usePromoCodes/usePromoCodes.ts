import { useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import partnerChannelActions from "actions/partnerChannelActions"
import promoCodeActions from "actions/promoCodeActions"

import {
  promoCodeSearchOptionsSelector,
  promoCodesSelector,
  promoCodeTotalCountSelector,
} from "selectors/promoCodeSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import l from "utils/intl"

import type { PromoCode } from "types/Models"

const { getPromoCodes, getPromoCodeById } = promoCodeActions

// @ts-expect-error
const { getForSpecificPartner } = partnerChannelActions

export const usePromoCodes = () => {
  const [isVisibleCreatePromoCodeModal, setIsVisibleCreatePromoCodeModal] =
    useState(false)
  const [isVisibleEditPromoCodeModal, setIsVisibleEditPromoCodeModal] =
    useState(false)

  const dispatch = useDispatch()

  const promoCodes = useSelector(promoCodesSelector)
  const totalCount = useSelector(promoCodeTotalCountSelector)
  const searchOptions = useSelector(promoCodeSearchOptionsSelector)
  const { promoCodeManage } = useSelector(permissionsSelector)

  const selectFiltersOptions = {
    active: [
      { label: l("Yes"), value: "1" },
      { label: l("No"), value: "0" },
    ],
  }

  const getData = <Type>(searchOptions: Type) => {
    return dispatch(getPromoCodes({ searchOptions }))
  }

  const reloadPromoCodes = () => {
    dispatch(getPromoCodes({ searchOptions }))
  }

  const handleOpenCreateModal = () => {
    setIsVisibleCreatePromoCodeModal(true)
  }

  const handleClickEditIcon = ({ id, partner_id }: PromoCode) => {
    dispatch(getPromoCodeById({ id }))
    dispatch(getForSpecificPartner(partner_id, () => {}))
    setIsVisibleEditPromoCodeModal(true)
  }

  return {
    canManage: !!promoCodeManage,
    promoCodes,
    searchOptions,
    selectFiltersOptions,
    totalCount,
    isVisibleCreatePromoCodeModal,
    isVisibleEditPromoCodeModal,
    getData,
    handleOpenCreateModal,
    handleClickEditIcon,
    reloadPromoCodes,
    setIsVisibleCreatePromoCodeModal,
    setIsVisibleEditPromoCodeModal,
  }
}
