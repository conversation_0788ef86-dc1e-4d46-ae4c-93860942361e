import React, { Fragment } from "react"
import { <PERSON> } from "react-router-dom"
import { Box, Ellipsis, IconPopover, Typography } from "@develop/fe-library"
import { checkIsArray, checkIsFunction } from "@develop/fe-library/dist/utils"

import { RestrictedIconPopover } from "components/Restrict"

import { setConfirm } from "utils/confirm"
import l from "utils/intl"

import { LABEL_PROPS } from "./constants"

import { CardsLayoutProps } from "./CardsLayoutTypes"

import styles from "./cardsLayout.module.scss"

export const CardsLayout = ({
  icons = [],
  items,
  hasStatusIcon = true,
}: CardsLayoutProps) => {
  if (!checkIsArray(items)) {
    return null
  }

  return (
    <Box
      display="grid"
      gap="l"
      padding="l"
      dMD={{
        gridTemplateColumns: "repeat(5, minmax(0, 1fr))",
      }}
      dSM={{
        gridTemplateColumns: "repeat(4, minmax(0, 1fr))",
      }}
      mSM={{
        gridTemplateColumns: "repeat(1, minmax(0, 1fr))",
      }}
      mXL={{
        gridTemplateColumns: "repeat(2, minmax(0, 1fr))",
      }}
      tb={{
        gridTemplateColumns: "repeat(3, minmax(0, 1fr))",
      }}
    >
      {items.map(({ id, labels, title, active, item }) => {
        const cardBackgroundColor =
          !hasStatusIcon || active
            ? "--color-main-background"
            : "--color-background-disable"

        const statusIconBackgroundColor = active
          ? "--color-alert-background-done"
          : "--color-alert-background-error"

        const statusIconColor = active
          ? "--color-icon-done"
          : "--color-icon-error"

        const statusIconName = active ? "icnCheck" : "icnClose"

        return (
          <Box
            key={id}
            hasBorder
            backgroundColor={cardBackgroundColor}
            borderRadius="--border-radius"
            flexDirection="column"
            gap="m"
            padding="l"
          >
            <Box align="start" gap="m" justify="space-between">
              <Ellipsis typographyProps={{ variant: "--font-body-text-2" }}>
                {title}
              </Ellipsis>

              <Box align="center" gap="m">
                {hasStatusIcon ? (
                  <Box
                    align="center"
                    backgroundColor={statusIconBackgroundColor}
                    borderRadius="--border-radius-circle"
                    display="flex"
                    height={24}
                    justify="center"
                    width={24}
                  >
                    <IconPopover
                      color={statusIconColor}
                      name={statusIconName}
                      size="--icon-size-1"
                    />
                  </Box>
                ) : null}

                {icons?.map(
                  (
                    {
                      checkAvailability,
                      name,
                      onClick,
                      confirm,
                      title,
                      url,
                      newTab,
                      viewPermission = true,
                      managePermission,
                      popoverMessage,
                    },
                    index
                  ) => {
                    const isVisible = !checkAvailability || checkAvailability()

                    if (!isVisible) {
                      return null
                    }

                    const handleClick = () => {
                      const hasOnClick = onClick && checkIsFunction(onClick)

                      if (confirm) {
                        setConfirm({
                          onCancel: () => {},
                          ...confirm,
                          onOk: () =>
                            confirm.onOk
                              ? confirm.onOk(item)
                              : onClick && onClick(item),
                        })
                      } else if (hasOnClick) {
                        onClick(item)
                      }
                    }

                    if (url) {
                      return (
                        <Link
                          key={index}
                          className={styles.link}
                          target={newTab ? "_blank" : "_self"}
                          to={url(item)}
                        >
                          <RestrictedIconPopover
                            content={title && l(title)}
                            managePermission={managePermission}
                            name={name}
                            popoverMessage={popoverMessage}
                            size="--icon-size-4"
                            viewPermission={viewPermission}
                            onClick={handleClick}
                          />
                        </Link>
                      )
                    }

                    return (
                      <Fragment key={index}>
                        <RestrictedIconPopover
                          content={title && l(title)}
                          managePermission={managePermission}
                          name={name}
                          popoverMessage={popoverMessage}
                          size="--icon-size-4"
                          viewPermission={viewPermission}
                          onClick={handleClick}
                        />
                      </Fragment>
                    )
                  }
                )}
              </Box>
            </Box>

            {checkIsArray(labels) ? (
              <Box component="ul" flexDirection="column" margin="0" padding="0">
                {labels?.map((label, index) =>
                  typeof label === "object" ? (
                    <Box key={index} component="li" gap="m" listStyle="none">
                      <Typography {...LABEL_PROPS}>
                        {l(label.label)}:
                      </Typography>

                      <Ellipsis typographyProps={LABEL_PROPS}>
                        {label.value}
                      </Ellipsis>
                    </Box>
                  ) : (
                    <Box key={index} component="li" listStyle="none">
                      <Ellipsis typographyProps={LABEL_PROPS}>{label}</Ellipsis>
                    </Box>
                  )
                )}
              </Box>
            ) : null}
          </Box>
        )
      })}
    </Box>
  )
}
