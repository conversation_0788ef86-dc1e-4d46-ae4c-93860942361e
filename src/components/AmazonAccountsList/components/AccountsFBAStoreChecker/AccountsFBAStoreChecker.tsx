import React from "react"
import { Box, RadioGroup, Typography } from "@develop/fe-library"

import { Restrict } from "components/Restrict"

import l from "utils/intl"

import {
  fbaValuesConstants,
  permissionKeys,
  restrictPopoverMessages,
} from "consts"

import { AccountsFBAStoreCheckerProps } from "./AccountsFBAStoreCheckerTypes"

const { FBA_HOME, FBA_PL_CZ } = fbaValuesConstants

export const AccountsFBAStoreChecker = ({
  fbaStockStorage,
  onChangeFbaStockStorage,
}: AccountsFBAStoreCheckerProps) => {
  return (
    <Restrict
      managePermission={permissionKeys.amazonCustomerAccountManage}
      popoverMessage={restrictPopoverMessages.alter}
    >
      {(isDisabled: boolean) => {
        return (
          <Box paddingLeft="l">
            <RadioGroup
              selectedValue={fbaStockStorage ?? FBA_HOME}
              items={[
                {
                  value: FBA_HOME,
                  isDisabled,
                  label: (
                    <Typography variant="--font-body-text-9">
                      {l(
                        "Store in Germany only. Pay an additional fulfillment fee of up to €0.26 on every FBA fulfillment for Germany"
                      )}
                    </Typography>
                  ),
                },
                {
                  value: FBA_PL_CZ,
                  isDisabled,
                  label: (
                    <Typography variant="--font-body-text-9">
                      {l(
                        "Store in Germany, Poland, and Czech Republic. Save up to €0.26 on every FBA fulfillment from Germany. VAT obligations in Poland and Czech Republic apply."
                      )}
                    </Typography>
                  ),
                },
              ]}
              onChange={onChangeFbaStockStorage}
            />
          </Box>
        )
      }}
    </Restrict>
  )
}
