import { connect } from "react-redux"

import ProductContextMenuView from "./ProductContextMenuView"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import customerActions from "actions/customerActions"

const { displayModal } = amazonCustomerAccountsActions
const { getCurrentCustomer } = customerActions

const { getBasPlan, deleteBasPlan, getBasSubscriptions } =
  basCustomerPlanActions

export default connect(null, {
  displayModal,
  deleteBasPlan,
  getCurrentCustomer,
  getBasPlan,
  getBasSubscriptions,
})(ProductContextMenuView)
