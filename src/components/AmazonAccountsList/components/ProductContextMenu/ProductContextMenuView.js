import React, { useCallback, useMemo } from "react"
import { DropdownMenu, Icon, Typography } from "@develop/fe-library"
import PropTypes from "prop-types"

import { useRestrict } from "hooks/useRestrict"

import { setConfirm } from "utils/confirm"
import l from "utils/intl"

import { permissionKeys } from "consts"
import { PRODUCTS } from "consts/product"

import styles from "./productContextMenu.module.scss"

const ProductContextMenuView = ({
  accountMarketplaceId,
  displayModal,
  product,
  isRevertSubscriptionDisabled,
  deleteBasPlan,
  getCurrentCustomer,
  getBasPlan,
  getBasSubscriptions,
  isDisabled,
}) => {
  const { canView } = useRestrict({
    viewPermission: permissionKeys.rootManager,
  })

  const handleDeleteBasPlan = useCallback(() => {
    setConfirm({
      title: l("Delete current subscription for Business Analytics"),
      message: (
        <>
          <Typography color="--color-text-main" variant="--font-body-text-7">
            {l(
              "You are about to delete your current subscription for Business Analytics. This action cannot be reverted. If the Trial subscription is currently running, the customer will be able to launch the Trial subscription again."
            )}
          </Typography>
          <Typography
            className={styles.deleBasPlanModalText}
            color="--color-text-main"
            variant="--font-body-text-7"
          >
            {l("Are you sure you want to proceed?")}
          </Typography>
        </>
      ),
      okText: l("Confirm"),
      onOk: () => {
        deleteBasPlan(null, () => {
          getCurrentCustomer(false, () => {
            displayModal(false, "subscriptionBasSetting")
            getBasPlan({ cache: false, successCallback: null })
            getBasSubscriptions()
          })
        })
      },
    })
  }, [accountMarketplaceId, product])

  const handleRevertSubscription = useCallback(() => {
    displayModal(true, "revertPlan", {
      accountMarketplaceId,
      product,
    })
  }, [accountMarketplaceId, product])

  const options = useMemo(() => {
    const result = []

    if (!isRevertSubscriptionDisabled) {
      result.push({
        value: "revertPlan",
        iconLeft: "icnStop",
        label: l("Revert subscription"),
      })
    }

    if (product === PRODUCTS.bas) {
      result.push({
        value: "deletePlan",
        iconLeft: "icnDeleteOutlined",
        label: l("Delete current subscription"),
      })
    }

    return result
  }, [isRevertSubscriptionDisabled, product])

  const handleSelect = useCallback(
    (selectedIndex) => {
      if (isDisabled) {
        return
      }

      const { value } = options.find((option, index) => index === selectedIndex)

      const callback = {
        deletePlan: handleDeleteBasPlan,
        revertPlan: handleRevertSubscription,
      }[value]

      callback()
    },
    [handleDeleteBasPlan, handleRevertSubscription, isDisabled]
  )

  if (!canView) {
    return null
  }

  // return disabled trigger because the DropdownMenu is not disabling manually
  if (isDisabled) {
    return <Icon isDisabled name="icnMore" size="--icon-size-3" />
  }

  return (
    <DropdownMenu options={options} onSelect={handleSelect}>
      <Icon isHovered name="icnMore" size="--icon-size-3" />
    </DropdownMenu>
  )
}

ProductContextMenuView.propTypes = {
  accountMarketplaceId: PropTypes.number.isRequired,
  isRevertSubscriptionDisabled: PropTypes.bool,
  getBasPlan: PropTypes.func,
  getBasSubscriptions: PropTypes.func,
  placement: PropTypes.string,
}

export default ProductContextMenuView
