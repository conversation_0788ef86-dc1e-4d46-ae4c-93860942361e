@import "assets/styles/variables.scss";

.container.container {
  box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 0.3);

  :global(.ant-dropdown-menu-item) {
    padding: 0;
  }

  :global(.ant-dropdown-menu-title-content) {
    align-items: center;
    color: $text_main;
    display: flex;
    height: 32px;
    line-height: 32px;
    margin-bottom: 0;
    margin-top: 0;
    padding: var(--padding-m);
    gap: var(--gap-m);

    &:hover {
      background-color: $hover_grid;
      color: $text_link;
    }
  }
}

.deleBasPlanModalText.deleBasPlanModalText {
  margin-top: var(--margin-m);
}
