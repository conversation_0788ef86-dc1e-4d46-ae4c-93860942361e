import React from "react"
import { useSelector } from "react-redux"
import { Box, Icon, Typography } from "@develop/fe-library"
import cn from "classnames"
import PropTypes from "prop-types"

import {
  customerIdSelector,
  customerProductsInfoSelector,
} from "selectors/customerSelectors"
import { staffUserIdSelector } from "selectors/staffSelectors"

import { RestrictedPrimaryButton } from "components/shared/Buttons"

import {
  buildBasicRestrictionAndTransferToNewPricing,
  buildTransferToNewPricingPopoverModifier,
} from "utils/customRestrictions"
import { pushEvent } from "utils/gtmLoader"
import l from "utils/intl"

import { permissionKeys } from "consts"
import { EVENTS_NAMES, PRODUCT_ORDER } from "consts/gtm"
import { PERMISSIONS_CONTENT } from "consts/permissions"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import styles from "./amazonConnectAcount.module.scss"

const AmazonConnectAccountView = ({
  productName,
  onClick,
  gtmProduct,
  classNameWrapper,
  isPaddingUses = true,
  showLastSubscriptionModal = () => {},
  isBasModuleStarted = false,
  isBasActive = false,
  hasMinHeight = true,
  basModuleConnectedStatus,
}) => {
  const title = {
    [PRODUCTS.repricer]: `${
      PRODUCT_NAMES[PRODUCTS.repricer]
    } is not enabled for this account`,

    [PRODUCTS.repricerB2C]: `${
      PRODUCT_NAMES[PRODUCTS.repricerB2C]
    } is not enabled for this account`,

    [PRODUCTS.repricerB2B]: `${
      PRODUCT_NAMES[PRODUCTS.repricerB2B]
    } is not enabled for this account`,

    [PRODUCTS.lost]: `${
      PRODUCT_NAMES[PRODUCTS.lost]
    } is not enabled for this account`,

    [PRODUCTS.bas]: `${
      PRODUCT_NAMES[PRODUCTS.bas]
    } is not enabled for this account`,
  }

  const isBasNeverConnectedProduct = basModuleConnectedStatus === "NEW"

  const isBas = productName === PRODUCTS.bas
  const isOldRepricer =
    productName === PRODUCTS.repricerB2C || productName === PRODUCTS.repricerB2B
  const isRepricer = productName === PRODUCTS.repricer || isOldRepricer

  const hasBasActiveOnce = isBas && isBasModuleStarted

  const isNeedToShowPreviousSubscriptionText =
    hasBasActiveOnce && !isBasNeverConnectedProduct

  const isShowPreviousItems = hasBasActiveOnce && !isBasActive

  const user_id = useSelector(staffUserIdSelector)
  const { isSelectedForNewPricing } = useSelector(customerProductsInfoSelector)
  const customer_id = useSelector(customerIdSelector)

  const gtmProductInfo = {
    [PRODUCTS.repricer]: {
      item_name: PRODUCT_NAMES[PRODUCTS.repricer],
      item_id: PRODUCT_ORDER[PRODUCTS.repricer],
    },
    [PRODUCTS.bas]: {
      item_name: PRODUCT_NAMES[PRODUCTS.bas],
      item_id: PRODUCT_ORDER[PRODUCTS.bas],
    },
  }

  const connect = () => {
    if (gtmProduct) {
      pushEvent({ ecommerce: null })

      pushEvent({
        event: EVENTS_NAMES.addToCart,
        customer_id,
        user_id,
        ecommerce: {
          items: [
            {
              ...gtmProductInfo[gtmProduct],
              index: 0,
              quantity: 1,
            },
          ],
        },
      })

      pushEvent({
        vHitNonInteraction: false,
        virtualEventAction: `click-account-connect-${gtmProduct}`,
        virtualEventCategory: "accounts",
        event: EVENTS_NAMES.updEvents,
      })
    }

    onClick && onClick()
  }

  const hasPermission = ({ basicRestriction }) =>
    basicRestriction && isShowPreviousItems

  const setupText = isShowPreviousItems ? "Setup Business Analytics" : "Enable"
  const paddingWithMinHeightFromTablet = hasMinHeight ? "0 l" : "l"

  const wrapperPaddingMobile = isPaddingUses ? "m" : "0"
  const wrapperPaddingFromTablet = isPaddingUses
    ? paddingWithMinHeightFromTablet
    : "0"

  const isRepricerSelectedForNewPricing = isSelectedForNewPricing && isRepricer

  return (
    <Box
      align="center"
      className={cn(styles.amazonConnectWrapper, classNameWrapper)}
      flexDirection="column"
      gap="m"
      height="100%"
      justify="center"
      minHeight={hasMinHeight ? "420px" : "auto"}
      padding={wrapperPaddingMobile}
      width="100%"
      tb={{
        padding: wrapperPaddingFromTablet,
        gap: "l",
      }}
    >
      <Icon
        color="--color-icon-static"
        name="icnWarning"
        size="--icon-size-11"
      />
      <Typography
        variant="--font-headline-3"
        tb={{
          variant: "--font-headline-2",
        }}
      >
        {l(title[productName])}
      </Typography>

      {isNeedToShowPreviousSubscriptionText ? (
        <Typography color="--color-text-second" variant="--font-body-text-7">
          {l("Business Analytics was previously enabled for this account.")}
        </Typography>
      ) : null}

      <Box
        flexDirection="column"
        gap="m"
        width="100%"
        mLG={{
          flexDirection: "row",
          width: "auto",
        }}
        tb={{
          gap: "l",
        }}
      >
        <RestrictedPrimaryButton
          fullWidth
          disabled={isRepricerSelectedForNewPricing}
          managePermission={permissionKeys.amazonCustomerAccountManage}
          popoverMessage={l(PERMISSIONS_CONTENT.allowSetupAccount)}
          popoverPlacement="top"
          customManageRestriction={buildBasicRestrictionAndTransferToNewPricing(
            isRepricerSelectedForNewPricing
          )}
          customPopoverModifier={buildTransferToNewPricingPopoverModifier(
            isRepricerSelectedForNewPricing
          )}
          onClick={connect}
        >
          {l(setupText)}
        </RestrictedPrimaryButton>

        <RestrictedPrimaryButton
          fullWidth
          customViewRestriction={hasPermission}
          viewPermission={permissionKeys.amazonCustomerAccountList}
          onClick={showLastSubscriptionModal}
        >
          {l("Last subscription")}
        </RestrictedPrimaryButton>
      </Box>
    </Box>
  )
}

AmazonConnectAccountView.propTypes = {
  productName: PropTypes.string.isRequired,
  onClick: PropTypes.func.isRequired,
  gtmProduct: PropTypes.string,
  classNameWrapper: PropTypes.string,
  showLastSubscriptionModal: PropTypes.func,
  isBasModuleStarted: PropTypes.bool,
  isBasActive: PropTypes.bool,
}

export default AmazonConnectAccountView
