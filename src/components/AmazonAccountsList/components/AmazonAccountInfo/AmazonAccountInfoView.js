import React, { useEffect, useRef, useState } from "react"
import { useDispatch, useStore } from "react-redux"
import { withRouter } from "react-router-dom"
import withSizes from "react-sizes"
import {
  Box,
  Icon,
  IconPopover,
  Switch,
  TextInput,
  Typography,
} from "@develop/fe-library"
import cn from "classnames"
import debounce from "lodash.debounce"
import PropTypes from "prop-types"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import amazonCustomerAccountStockActions from "actions/amazonCustomerAccountStockActions"

import { AccountsFBAStoreChecker } from "components/AmazonAccountsList/components"
import FormattedMessage from "components/FormattedMessage"
import withOutlineLabel from "components/hocs/withOutlineLabel"
import KeypressHandler from "components/KeypressHandler"
import {
  Restrict,
  RestrictedCheckbox,
  RestrictedNumericInput,
  RestrictedSelect,
  RestrictedSpan,
  RestrictedSwitch,
} from "components/Restrict"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>on,
  RestrictedButton,
} from "components/shared/Buttons"
import DatePicker from "components/shared/DatePicker"

import { checkIsArray } from "utils/arrayHelpers"
import { getBreakpoint, LG, lowerThan } from "utils/breakpoints"
import { basicRestrictionAndAdminMode } from "utils/customRestrictions"
import {
  convertMomentToServerDate,
  convertStringDateToMoment,
} from "utils/dateConverter"
import l from "utils/intl"
import { setItem } from "utils/storage"

import {
  fbaValuesConstants,
  permissionKeys,
  restrictPopoverMessages,
} from "consts"

import { INPUT_BOX_PROPS, ROW_PROPS } from "./constants"

import styles from "./amazonAccountInfo.module.scss"

const { getAll: getAmazonCustomerAccountStock } =
  amazonCustomerAccountStockActions
const { redirectToAmazon } = amazonCustomerAccountsActions

const { DE_MARKETPLACE } = fbaValuesConstants

const DatePickerWithLabel = withOutlineLabel(DatePicker)

const AmazonAccountInfoView = ({
  account: {
    amazonRegion,
    amazonMarketplacesOptions,
    countryOptions,
    countryValue,
    dateCreated,
    fbaStockLocationDate,
    fbaStockStorage,
    isFbaDeMarketplace,
    globalShipping,
    id,
    ignoreAmazonShipping,
    loadOrders,
    marketplaceId,
    name,
    sellerId,
    deleted,
    mwsAuthorization,
    accountZoneId,
    amazon_notification_enabled,
  },
  onCountryChange,
  onUpdate,
  breakpoint,
}) => {
  const [editAccountName, setEditAccountName] = useState(false)
  const [accountName, setAccountName] = useState(name)
  const [accountNameError, setAccountNameError] = useState(undefined)
  const [isStartDateChange, setIsStartDateChange] = useState(false)

  const store = useStore()

  const editAccountNameInput = useRef()
  const editAccountNameOk = useRef()
  const editAccountNameCancel = useRef()

  const dispatch = useDispatch()

  const isShowRadioButtonBlock =
    isFbaDeMarketplace && !lowerThan(breakpoint, LG)

  const paddingIsValidOrExpiredBlock = lowerThan(breakpoint, LG) ? "m" : "l"
  const titleIsValidOrExpired = mwsAuthorization ? l("Valid") : l("Expired")

  const onChangeStartDate = (value) => {
    setIsStartDateChange(true)
    onUpdate(id, {
      fba_stock_location_date: convertMomentToServerDate(value),
    })
  }

  const onChangeFbaStockStorage = (value) => {
    return onUpdate(id, {
      fba_stock_storage: value,
    })
  }

  const onChangeHomeMarketplace = (home_marketplace_id) => {
    onUpdate(
      id,
      {
        home_marketplace_id,
        fba_stock_storage: fbaStockStorage,
      },
      () =>
        dispatch(getAmazonCustomerAccountStock({ successCallback: undefined }))
    )
  }

  const renewAmazonTokenHandler = () => {
    const {
      moduleSetupWizard: { productNames },
      customer: {
        customer: { bas_module_started },
      },
      basCustomerPlan: {
        customer: { data },
      },
    } = store.getState()

    const isBasModuleStarted = bas_module_started

    const activeBasCustomerPlans = checkIsArray(
      data?.filter((item) => item.active === 1)
    )

    setItem("sellerIdRefresh", sellerId)

    dispatch(
      redirectToAmazon({
        region: accountZoneId,
        productNames: productNames,
        homeMarketplace: marketplaceId,
        marketplaces: [],
        accountModule: false,
        renewToken: true,
        isBasActive: activeBasCustomerPlans,
        isBasModuleStarted: isBasModuleStarted,
        loginSellerUri: false,
      })
    )
  }

  const handleAccountEdit = () => {
    setEditAccountName(true)
  }

  const handleAccountNameChange = (value) => setAccountName(value)

  const handleKeypressEnter = () => {
    if (editAccountNameOk?.current?.buttonNode) {
      editAccountNameOk.current.click()
    }
  }

  const handleKeypressEscape = () => {
    if (editAccountNameCancel?.current?.buttonNode) {
      editAccountNameCancel.current.click()
    }
  }

  const handleSave = () => {
    setAccountNameError(undefined)

    onUpdate(id, { customerAccountTitle: accountName }, (errors) => {
      if (errors) {
        setAccountNameError(errors[0].message)
      } else {
        setEditAccountName(false)
      }
    })
  }

  const handleAccountNameEditCancel = () => {
    setAccountName(name)
    setEditAccountName(false)
  }

  const handleLoadOrdersChange = (is_load_order) =>
    onUpdate(id, { is_load_order })

  const handleGlobalShippingCostsChange = (isChecked) => {
    onUpdate(id, { ignore_amazon_shipping: isChecked ? 1 : 0 })
  }

  const handleGlobalShippingChange = debounce((global_shipping) => {
    onUpdate(id, {
      global_shipping: global_shipping.replace(/\.$/, ""),
    })
  }, 300)

  const handleNotificationsToAmazonSellerCentral = (isChecked) => {
    onUpdate(id, {
      amazon_notification_enabled: isChecked ? 1 : 0,
    })
  }

  useEffect(() => {
    const shouldFocus = editAccountName && editAccountNameInput.current

    if (shouldFocus) {
      editAccountNameInput.current.focus()
    }
  }, [editAccountName])

  useEffect(() => {
    setIsStartDateChange(false)
  }, [fbaStockLocationDate])

  const { mSM, ...ROW_PROPS_WITHOUT_mSM } = ROW_PROPS

  return (
    <Typography
      variant="--font-body-text-7"
      className={cn({
        [styles.deleted]: deleted,
      })}
    >
      <Restrict
        customViewRestriction={basicRestrictionAndAdminMode}
        viewPermission={permissionKeys.sellerLogicManager}
      >
        <Box {...ROW_PROPS}>
          <span>
            <FormattedMessage id="Account ID" />:
          </span>
          <span>{id}</span>
        </Box>
      </Restrict>

      <Box {...ROW_PROPS}>
        <span>
          <FormattedMessage id="Amazon account name" />:
        </span>

        {!editAccountName ? (
          <RestrictedSpan
            disabledClassName={styles.value}
            managePermission={permissionKeys.amazonCustomerAccountManage}
            popoverMessage={restrictPopoverMessages.alter}
            onClick={handleAccountEdit}
          >
            <Typography color="--color-text-link" variant="--font-body-text-7">
              {accountName || "Empty"}
            </Typography>
          </RestrictedSpan>
        ) : (
          <Box
            display="grid"
            gap="m"
            mSM={{
              gridTemplateColumns: "100%",
            }}
            tb={{
              gridTemplateColumns: "260px auto max-content",
            }}
          >
            <TextInput
              ref={editAccountNameInput}
              isFullWidth
              errorMessage={accountNameError}
              value={accountName}
              onChange={handleAccountNameChange}
            />
            <KeypressHandler
              onEnter={handleKeypressEnter}
              onEscape={handleKeypressEscape}
            />
            <Box
              gap="m"
              mSM={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
              }}
              tb={{
                display: "flex",
              }}
            >
              <PrimaryButton ref={editAccountNameOk} onClick={handleSave}>
                <FormattedMessage id="Save" />
              </PrimaryButton>
              <Button
                ref={editAccountNameCancel}
                onClick={handleAccountNameEditCancel}
              >
                <FormattedMessage id="Cancel" />
              </Button>
            </Box>
          </Box>
        )}
      </Box>

      <Restrict viewPermission={permissionKeys.amazonCustomerAccountList}>
        <Box {...ROW_PROPS}>
          <span>
            <FormattedMessage id="Amazon seller ID" />:
          </span>

          <span>{sellerId}</span>
        </Box>

        <Box
          mSM={{
            ...ROW_PROPS.mSM,
            gridTemplateColumns: "1fr auto",
          }}
          {...ROW_PROPS_WITHOUT_mSM}
        >
          <span>
            <FormattedMessage id="Show important notifications in Amazon Seller Central" />
            :
          </span>

          <Switch
            isChecked={amazon_notification_enabled}
            onChange={handleNotificationsToAmazonSellerCentral}
          />
        </Box>

        <Box {...ROW_PROPS}>
          <span>
            <FormattedMessage id="Amazon API authorization status" />:
          </span>

          <Box align="center">
            <Box paddingRight={paddingIsValidOrExpiredBlock}>
              <Typography variant="--font-body-text-7">
                {titleIsValidOrExpired}
              </Typography>
            </Box>

            <RestrictedButton
              managePermission={permissionKeys.amazonCustomerAccountManage}
              popoverMessage={restrictPopoverMessages.alter}
              onClick={renewAmazonTokenHandler}
            >
              {l("Renew")}
            </RestrictedButton>

            {mwsAuthorization ? null : (
              <Box paddingLeft="m">
                <Icon
                  color="--color-icon-error"
                  name="icnWarning"
                  size="--icon-size-5"
                />
              </Box>
            )}
          </Box>
        </Box>

        <Box {...ROW_PROPS}>
          <span>
            <FormattedMessage id="Amazon’s account  region" />:
          </span>
          <span>{amazonRegion}</span>
        </Box>

        <Box {...ROW_PROPS}>
          <span>
            <FormattedMessage id="Created" />:
          </span>
          <span>{dateCreated}</span>
        </Box>
      </Restrict>

      <Box
        {...ROW_PROPS}
        align={ignoreAmazonShipping ? "start" : "center"}
        padding="l"
        tb={{
          ...ROW_PROPS.tb,
          align: "center",
        }}
      >
        <Box align="center">
          <span>
            <FormattedMessage id="Ignore Amazon shipping costs" />:
          </span>
          <Box
            display="flex"
            marginLeft="m"
            tb={{
              display: "none",
            }}
          >
            <RestrictedSwitch
              isChecked={!!ignoreAmazonShipping}
              managePermission={permissionKeys.amazonCustomerAccountManage}
              popoverMessage={restrictPopoverMessages.alter}
              onChange={handleGlobalShippingCostsChange}
            />
          </Box>
        </Box>

        <Box
          display={ignoreAmazonShipping ? "flex" : "none"}
          flexDirection="column"
          gap="m"
          tb={{
            flexDirection: "row",
            align: "center",
            display: "flex",
          }}
        >
          <Box
            display="none"
            tb={{
              display: "flex",
            }}
          >
            <RestrictedSwitch
              isChecked={!!ignoreAmazonShipping}
              managePermission={permissionKeys.amazonCustomerAccountManage}
              popoverMessage={restrictPopoverMessages.alter}
              onChange={handleGlobalShippingCostsChange}
            />
          </Box>

          {ignoreAmazonShipping ? ( //INIT: !!ignoreAmazonShipping
            <Box align="center" gap="m">
              <Box {...INPUT_BOX_PROPS} tb={{ width: "210px" }}>
                <RestrictedNumericInput
                  isDecimalAllowed
                  isFullWidth
                  isStringMode
                  defaultValue={globalShipping}
                  isNegativeAllowed={false}
                  label={l("Global shipping costs")}
                  managePermission={permissionKeys.amazonCustomerAccountManage}
                  maximumFractionDigits={2}
                  minimumFractionDigits={2}
                  popoverMessage={restrictPopoverMessages.alter}
                  suffixIcons={[
                    {
                      name: "icnQuestionCircle",
                      color: "--color-icon-active",
                      content: l("Shipping costs in home marketplace currency"),
                    },
                  ]}
                  onChange={handleGlobalShippingChange}
                />
              </Box>

              <IconPopover
                isHovered
                color="--color-icon-active"
                maxWidth="250px"
                name="icnInfoCircle"
                placement="right"
                size="--icon-size-5"
                content={l(
                  "By activating the global shipping costs, all shipping costs transmitted by Amazon are ignored and replaced by the value of the global shipping costs. Please note that this setting affects all products."
                )}
              />
            </Box>
          ) : null}
        </Box>
      </Box>

      <Restrict
        customViewRestriction={basicRestrictionAndAdminMode}
        viewPermission={permissionKeys.rootManager}
      >
        <Box {...ROW_PROPS}>
          <span>{l("Load orders")}:</span>

          <Box {...INPUT_BOX_PROPS}>
            <RestrictedSelect
              isFullWidth
              value={loadOrders}
              options={[
                {
                  label: l("Yes"),
                  value: 1,
                },
                {
                  label: l("No"),
                  value: 0,
                },
              ]}
              onChangeValue={handleLoadOrdersChange}
            />
          </Box>
        </Box>
      </Restrict>

      <Box {...ROW_PROPS}>
        <span>{l("Home marketplace")}:</span>

        <Box {...INPUT_BOX_PROPS}>
          <RestrictedSelect
            isFullWidth
            managePermission={permissionKeys.amazonCustomerAccountManage}
            options={amazonMarketplacesOptions}
            popoverMessage={restrictPopoverMessages.alter}
            value={marketplaceId}
            onChangeValue={onChangeHomeMarketplace}
          />
        </Box>
      </Box>

      {amazonRegion === "EU Region" ? (
        <Box {...ROW_PROPS} align="start" hasBorder={false} padding="l">
          <span>{l("Fulfillment by Amazon")}:</span>

          <Box flexDirection="column" gap="m">
            <span>{l("Allow inventory to be stored in other countries")}:</span>

            {checkIsArray(countryOptions) ? (
              <Box
                gap="l"
                dSM={{
                  flexDirection: "row",
                }}
                mSM={{
                  flexDirection: "column",
                }}
              >
                {countryOptions.map(({ label, value, disabled, checked }) => {
                  const isShowRadioButton =
                    label === DE_MARKETPLACE &&
                    lowerThan(breakpoint, LG) &&
                    isFbaDeMarketplace

                  const handleCountryChange = () => {
                    const isDeletedMarketplace = !!countryValue.find(
                      (marketplace) => value === marketplace
                    )

                    return onCountryChange(id, value, isDeletedMarketplace)
                  }

                  return isShowRadioButton ? (
                    <div key={value}>
                      <RestrictedCheckbox
                        checked={checked}
                        disabled={disabled}
                        label={label}
                        popoverMessage={restrictPopoverMessages.alter}
                        managePermission={
                          permissionKeys.amazonCustomerAccountManage
                        }
                        onChange={handleCountryChange}
                      />
                      <div>
                        <AccountsFBAStoreChecker
                          fbaStockStorage={fbaStockStorage}
                          onChangeFbaStockStorage={onChangeFbaStockStorage}
                        />
                      </div>
                    </div>
                  ) : (
                    <RestrictedCheckbox
                      key={value}
                      checked={checked}
                      disabled={disabled}
                      label={label}
                      popoverMessage={restrictPopoverMessages.alter}
                      managePermission={
                        permissionKeys.amazonCustomerAccountManage
                      }
                      onChange={handleCountryChange}
                    />
                  )
                })}
              </Box>
            ) : null}

            {isShowRadioButtonBlock ? (
              <AccountsFBAStoreChecker
                fbaStockStorage={fbaStockStorage}
                onChangeFbaStockStorage={onChangeFbaStockStorage}
              />
            ) : null}

            <Box
              align="center"
              display="grid"
              gap="m"
              gridTemplateColumns="260px min-content"
            >
              <Restrict
                managePermission={permissionKeys.amazonCustomerAccountManage}
                popoverMessage={restrictPopoverMessages.alter}
              >
                {(isDisabled) => {
                  return (
                    <DatePickerWithLabel
                      isOneDayDate
                      allowClear={false}
                      disabled={isDisabled}
                      outlineIsActive={isStartDateChange}
                      placeholder={l("Start date")}
                      value={convertStringDateToMoment(fbaStockLocationDate)}
                      onChange={onChangeStartDate}
                    />
                  )
                }}
              </Restrict>

              <IconPopover
                isHovered
                color="--color-icon-active"
                maxWidth="250px"
                name="icnInfoCircle"
                placement="right"
                size="--icon-size-5"
                content={l(
                  `If the Amazon penalty fee is set in "other fees" or in a different field, the amount must be adjusted. The FBA penalty fee will be calculated automatically as soon as this setting is saved.`
                )}
              />
            </Box>
          </Box>
        </Box>
      ) : null}
    </Typography>
  )
}

AmazonAccountInfoView.propTypes = {
  account: PropTypes.shape({
    amazonRegion: PropTypes.string.isRequired,
    amazonMarketplacesOptions: PropTypes.array.isRequired,
    authToken: PropTypes.string.isRequired,
    countryOptions: PropTypes.array.isRequired,
    countryValue: PropTypes.array.isRequired,
    dateCreated: PropTypes.string.isRequired,
    fbaStockStorage: PropTypes.string,
    isFbaDeMarketplace: PropTypes.bool,
    fbaStockLocationDate: PropTypes.string,
    globalShipping: PropTypes.string.isRequired,
    id: PropTypes.number.isRequired,
    ignoreAmazonShipping: PropTypes.number.isRequired,
    loadOrders: PropTypes.number.isRequired,
    marketplaceId: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    sellerId: PropTypes.string.isRequired,
    mwsAuthorization: PropTypes.bool.isRequired,
    accountZoneId: PropTypes.bool.isRequired,
  }).isRequired,
  onCountryChange: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  breakpoint: PropTypes.string,
}

const mapSizesToProps = ({ width }) => ({
  breakpoint: getBreakpoint(width),
})

export default withRouter(withSizes(mapSizesToProps)(AmazonAccountInfoView))
