import React, { useCallback } from "react"
import { useDispatch } from "react-redux"
import { useHistory } from "react-router-dom"
import { Box, Button, Icon, StatusTag, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"

import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { RestrictedButton } from "components/shared/Buttons"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import { RelatedComponentProps } from "../../RelatedProductsTypes"

const { toggleModal } = moduleSetupWizardActions

export const RepricerB2CCard = ({
  accountId,
  use_repricer_b2c_module,
  deleted,
}: RelatedComponentProps) => {
  const dispatch = useDispatch()
  const history = useHistory()

  const handleEnableRepricerB2C = useCallback(() => {
    dispatch(
      toggleModal(true, "connectChoseAmazonAccount", PRODUCTS.repricerB2C, {
        selectedRegion: null,
        selectedMarketplaces: null,
        connectAccountId: accountId,
      })
    )
  }, [accountId, dispatch])

  const handleRedirectToSubscriptionDetails = () => {
    history.push({
      pathname: ROUTES.GENERAL_ROUTES.PATH_SUBSCRIPTION,
      hash: `#tab=${PRODUCTS.repricerB2C}`,
    })
  }

  const isProductEnabled: boolean = use_repricer_b2c_module === 1 && !deleted

  return (
    <Box
      flexDirection="column"
      gap="l"
      hasBorder={!isProductEnabled}
      padding="l"
      backgroundColor={
        isProductEnabled ? "--color-row-select" : "--color-main-background"
      }
    >
      <Box align="center" justify="space-between" width="100%">
        <Icon
          color="--color-icon-static"
          name="icnB2CRepricer"
          size="--icon-size-7"
        />

        {isProductEnabled ? (
          <StatusTag
            hasEllipsis
            colorStatus="--color-status-1"
            iconName="icnCheck"
            name={l("Active")}
          />
        ) : null}
      </Box>

      <Box flexDirection="column" gap="m">
        <Typography variant="--font-headline-2">
          {PRODUCT_NAMES[PRODUCTS.repricerB2C]}
        </Typography>

        {isProductEnabled ? (
          <Box>
            <Button onClick={handleRedirectToSubscriptionDetails}>
              {l("Subscription details")}
            </Button>
          </Box>
        ) : (
          <Box>
            <RestrictedButton
              managePermission={permissionKeys.amazonCustomerAccountManage}
              popoverMessage={restrictPopoverMessages.alter}
              popoverPlacement="top"
              variant="primary"
              onClick={handleEnableRepricerB2C}
            >
              {l("Enable")}
            </RestrictedButton>
          </Box>
        )}
      </Box>
    </Box>
  )
}
