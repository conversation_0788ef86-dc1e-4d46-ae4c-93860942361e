import { FC } from "react"

import { PRODUCT_NAMES } from "consts/product"

import { AmazonAccountItemForEdit } from "types/AmazonAccountItemForEditTypes"

export type RelatedProductsProps = {
  account: AmazonAccountItemForEdit
}

export type PickedRelatedComponentProps = Pick<
  AmazonAccountItemForEdit,
  | "deleted"
  | "useLostModule"
  | "use_repricer_b2c_module"
  | "use_repricer_b2b_module"
>

export type RelatedComponentProps = PickedRelatedComponentProps & {
  accountId: number
  excludeFromRepricer: AmazonAccountItemForEdit["exclude_from_repricer"]
}

export type RelatedComponent = [
  keyof typeof PRODUCT_NAMES,
  FC<RelatedComponentProps>
]
