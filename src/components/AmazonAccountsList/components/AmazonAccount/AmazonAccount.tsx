import React, { useCallback, useEffect, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory, useLocation } from "react-router-dom"
import { Alert, Box, Typography } from "@develop/fe-library"
import {
  getUrlSearchParams,
  removeUrlSearchParam,
} from "@develop/fe-library/dist/utils"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import customerActions from "actions/customerActions"
import fullServiceAction from "actions/fullServiceActions"

import { isAmazonMarketplacesLoadingSelector } from "selectors/amazonMarketplaceSelectors"
import { isDemoAccountSelector } from "selectors/customerSelectors"

import {
  AmazonAccountInfo,
  RelatedProducts,
  RequestPayment,
} from "components/AmazonAccountsList/components"
import { RestrictedPrimaryButton } from "components/shared/Buttons"
import TabsComponent from "components/TabsComponent/TabsComponent"

import { DELETE_ITEM, FORM_IS_CHANGED, setConfirm } from "utils/confirm"
import {
  buildBasicRestrictionAndNotDemoAccount,
  buildDemoAccountPopoverModifier,
} from "utils/customRestrictions"
import l from "utils/intl"

import {
  FULL_SERVICE_CREDENTIAL_STATUS,
  permissionKeys,
  restrictPopoverMessages,
} from "consts"

import { ID_REGEX, TABS_HASHES } from "./constants"

import { AmazonAccountProps, UrlSearchParams } from "./AmazonAccountTypes"

import styles from "./amazonAccount.module.scss"

const { getCurrentCustomer } = customerActions
const { getAll: getAccounts, setAccountScrollTab } =
  amazonCustomerAccountsActions
const { fullServiceUpdateCredentialsStatus } = fullServiceAction

export const AmazonAccount = ({
  account,
  isScrollTab,
  onCountryChange,
  onDelete,
  onUpdate,
}: AmazonAccountProps) => {
  const dispatch = useDispatch()
  const history = useHistory()

  const {
    id,
    name,
    sellerId,
    isDeletable,
    deleted,
    allowInitiatePayout,
    payoutMarketplaces,
    fs_credentials_status,
  } = account

  const { pathname, hash } = useLocation()

  const [activeTab, setActiveTab] = useState("1")
  const [isTabChangeAllowed, setIsTabChangeAllowed] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)

  const isDemoAccount = useSelector(isDemoAccountSelector)
  const isAmazonMarketplacesLoading: boolean = useSelector(
    isAmazonMarketplacesLoadingSelector
  )
  const urlSearchParams = getUrlSearchParams<UrlSearchParams>({
    locationSearch: document.location.search,
  })

  const { confirmPermissions } = urlSearchParams

  const isDeleteButtonVisible: boolean =
    activeTab === "1" && isDeletable && !deleted

  useEffect(() => {
    if (!isScrollTab) {
      return
    }

    const [, accountId, tabName] = ID_REGEX.exec(hash) || []

    if (accountId === id.toString()) {
      setTimeout(() => {
        containerRef.current?.scrollIntoView()
        const currentScroll = window.scrollY

        if (currentScroll) {
          const header = document.getElementsByTagName("header")?.[0]

          window.scroll(0, currentScroll - header?.offsetHeight || 0)
        }
        setActiveTab(TABS_HASHES[tabName])
        dispatch(setAccountScrollTab(false))
      }, 100)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isScrollTab])

  const activateAccountHandler = useCallback(() => {
    dispatch(
      fullServiceUpdateCredentialsStatus({
        id,
        status: FULL_SERVICE_CREDENTIAL_STATUS.CORRECT,
        successCallback: () => {
          dispatch(
            getAccounts(false, () => {
              dispatch(getCurrentCustomer(false, undefined))
            })
          )
        },
      })
    )
  }, [dispatch, id])

  useEffect(() => {
    const isNeedShowConfirmModal =
      confirmPermissions === sellerId &&
      fs_credentials_status === FULL_SERVICE_CREDENTIAL_STATUS.INCORRECT

    if (isNeedShowConfirmModal) {
      const urlSearchParamsAfterRemoving = removeUrlSearchParam({
        locationSearch: document.location.search,
        paramName: "confirmPermissions",
      })

      // Need fix in FE-LIBRARY urlSearchParamsAfterRemoving
      const searchData =
        urlSearchParamsAfterRemoving === "?" ? "" : urlSearchParamsAfterRemoving

      const closeModalHandler = () => {
        history.replace(`${pathname}${searchData}${hash}`)
      }

      setConfirm({
        title: l("Permissions confirmation"),
        message: l(
          "By pressing the confirm button, you indicate that you have provided SELLERLOGIC with the required User Permissions."
        ),
        okText: l("Confirm"),
        onOk: activateAccountHandler,
        onCancel: closeModalHandler,
      })
    }
  }, [sellerId, fs_credentials_status, confirmPermissions])

  const onSetActiveTab = (tabKey: string, optionalId?: string) => {
    if (optionalId) {
      dispatch(setAccountScrollTab(true))
    }

    window.location.hash = `#/${optionalId ? optionalId : id}/${Object.keys(
      TABS_HASHES
    ).find((tabHash) => TABS_HASHES[tabHash] === tabKey)}`

    setActiveTab(tabKey)
  }

  const handlePreventedChangeTab = (tabKey: string) => {
    // @ts-expect-error
    setConfirm({
      template: FORM_IS_CHANGED,
      onOk: () => {
        setIsTabChangeAllowed(true)
        onSetActiveTab(tabKey)
      },
    })
  }

  const handleAccountDelete = () => {
    // @ts-expect-error
    setConfirm({
      template: DELETE_ITEM,
      onOk: () => onDelete(id),
    })
  }

  const isPayoutMarketplacesAbsent: boolean =
    !isAmazonMarketplacesLoading && payoutMarketplaces.length === 0

  const tabs = [
    {
      key: "1",
      label: l("Account information"),
      icon: "icnUser",
      contents: (
        <AmazonAccountInfo
          // @ts-expect-error - Should refactor and switch to TS
          account={account}
          onCountryChange={onCountryChange}
          onUpdate={onUpdate}
        />
      ),
    },
    {
      key: "2",
      label: l("Related products"),
      icon: "icnSellerLogicNew",
      contents: <RelatedProducts account={account} />,
    },
    {
      key: "3",
      label: l("Automatic payouts"),
      icon: "icnCreditCard",
      contents: (
        <RequestPayment
          accountId={id}
          isAmazonMarketplacesLoading={isAmazonMarketplacesLoading}
          marketplaces={payoutMarketplaces}
          setIsTabChangeAllowed={setIsTabChangeAllowed}
        />
      ),
      disabled: !allowInitiatePayout || isPayoutMarketplacesAbsent,
      disabledPopoverMessage: l(
        "Amazon SP-API is not supporting this feature for any marketplace of this account region yet."
      ),
    },
  ]

  return (
    <div ref={containerRef} id={id.toString()}>
      <Box
        className={styles.titleWrapper}
        flexDirection="column"
        gap="m"
        justify="space-between"
        padding="m"
      >
        <Box gap="m" marginRight="auto">
          <Typography variant="--font-body-text-7">
            {l("Amazon account")}:
          </Typography>

          <Typography color="--color-text-second" variant="--font-body-text-7">
            {name}
          </Typography>
        </Box>

        {isDeleteButtonVisible ? (
          <RestrictedPrimaryButton
            managePermission={permissionKeys.amazonCustomerAccountManage}
            popoverMessage={restrictPopoverMessages.alter}
            customManageRestriction={buildBasicRestrictionAndNotDemoAccount(
              isDemoAccount
            )}
            customPopoverModifier={buildDemoAccountPopoverModifier(
              isDemoAccount
            )}
            onClick={handleAccountDelete}
          >
            {l("Delete account")}
          </RestrictedPrimaryButton>
        ) : null}

        {!!deleted ? (
          <Alert
            isContentFullWidth
            alertType="warning"
            message={l("Account deleted")}
          />
        ) : null}
      </Box>

      <TabsComponent
        // @ts-expect-error
        hideIconsOnMobile
        activeTab={activeTab}
        isTabChangeAllowed={isTabChangeAllowed}
        tabs={tabs}
        tabsContainerClassName={styles.tabsContainer}
        onChangeTab={onSetActiveTab}
        onPreventedChangeTab={handlePreventedChangeTab}
      />
    </div>
  )
}
