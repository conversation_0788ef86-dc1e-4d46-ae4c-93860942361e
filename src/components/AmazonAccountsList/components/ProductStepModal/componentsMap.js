import AddMarketplace from "./components/AddMarketplace"
import { BasNextSubscription } from "./components/BasNextSubscription"
import BASProductInformation from "./components/BASProductInformation"
import ChangeDuration from "./components/ChangeDuration"
import CurrentSubscription from "./components/CurrentSubscription"
import LostFoundProductInformation from "./components/LostFoundProductInformation"
import LostFoundProductInformationMVP from "./components/LostFoundProductInformationMVP"
import { PreviousBasSubscription } from "./components/PreviousBasSubscription"
import { RepricerNextSubscription } from "./components/RepricerNextSubscription"
import { RepricerNextSubscriptionNewPricingModal } from "./components/RepricerNextSubscriptionNewPricingModal"
import RevertPlan from "./components/RevertPlan"
import SubscriptionBasSetting from "./components/SubscriptionBasSetting"

export const components = {
  addMarketplace: AddMarketplace,
  changeDuration: ChangeDuration,
  currentSubscription: CurrentSubscription,
  lostFoundProductInformation: LostFoundProductInformation,
  repricerNextSubscription: RepricerNextSubscription,
  repricerNextSubscriptionNewPricing: RepricerNextSubscriptionNewPricingModal,
  basNextSubscription: BasNextSubscription,
  revertPlan: RevertPlan,
  basProductInformation: BASProductInformation,
  subscriptionBasSetting: SubscriptionBasSetting,
  previousBasSubscription: PreviousBasSubscription,
  //   Delete this after FULL SERVICE MVP
  lostFoundProductInformationMVP: LostFoundProductInformationMVP,
}
