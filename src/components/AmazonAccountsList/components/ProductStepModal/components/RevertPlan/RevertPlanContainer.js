import React, { useLayoutEffect } from "react"
import PropTypes from "prop-types"

import { PRODUCTS } from "consts/product"

import RevertPlanFormik from "./RevertPlanFormik"

const RevertPlanContainer = ({
  accountMarketplaceId,
  getAccountMarketplaces,
  onClose,
  onSubmit,
  plansOptions,
  onFormIsChanged,
  revertBasPlan,
  getBasPlan,
  product,
  getBasSubscriptions,
  getRevertPlans,
  ...modalProps
}) => {
  const isOldRepricer =
    product === PRODUCTS.repricerB2C || product === PRODUCTS.repricerB2B

  useLayoutEffect(() => {
    isOldRepricer && getRevertPlans(accountMarketplaceId)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOldRepricer])

  return (
    <RevertPlanFormik
      accountMarketplaceId={accountMarketplaceId}
      getAccountMarketplaces={getAccountMarketplaces}
      getBasPlan={getBasPlan}
      getBasSubscriptions={getBasSubscriptions}
      isOldRepricer={isOldRepricer}
      plansOptions={plansOptions}
      product={product}
      revertBasPlan={revertBasPlan}
      onClose={onClose}
      onFormIsChanged={onFormIsChanged}
      onSubmit={onSubmit}
      {...modalProps}
    />
  )
}

RevertPlanContainer.propTypes = {
  accountMarketplaceId: PropTypes.number.isRequired,
  getRevertPlans: PropTypes.func.isRequired,
}

export default RevertPlanContainer
