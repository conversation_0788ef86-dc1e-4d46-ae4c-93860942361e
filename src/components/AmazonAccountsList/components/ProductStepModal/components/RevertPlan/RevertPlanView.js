import React from "react"
import { Select } from "antd"
import { Field, Form } from "formik"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import { SelectField, withErrorField } from "components/shared/FormFields"
import { FooterModal } from "components/shared/Modal"
import Typography from "components/Typography"

import l from "utils/intl"

import { PRODUCTS } from "consts/product"

import styles from "./revertPlan.module.scss"

const SelectFieldWithError = withErrorField(SelectField)
const { Option } = Select

const RevertPlanView = ({
  isSubmitting,
  onClose,
  plansOptions,
  plansOptionsBas,
  values,
  product,
  isOldRepricer,
}) => {
  const currentPlanOption = {
    [PRODUCTS.repricerB2C]: plansOptions || [],
    [PRODUCTS.repricerB2B]: plansOptions || [],
    bas: plansOptionsBas || [],
  }

  const isRepricerRender =
    isOldRepricer &&
    currentPlanOption[product]?.some(
      ({ isNoRender, label }) => !isNoRender && label
    )

  const isDisableSubmitButton =
    isSubmitting ||
    !values?.amazonCustomerAccountMarketplacePlanId ||
    !isRepricerRender

  return (
    <Form noValidate className={styles.form}>
      <div className={styles.container}>
        <div className={styles.controlsContainer}>
          <div className={styles.controlContainer}>
            <Typography className={styles.label} type="div" variant="text">
              <FormattedMessage id="Set subscription" />
              {" *"}
            </Typography>
            <div className={styles.controlWrapper}>
              <Field
                className={styles.dateField}
                component={SelectFieldWithError}
                disabled={isSubmitting}
                dropdownClassName={styles.dateField}
                name="amazonCustomerAccountMarketplacePlanId"
                placeholder={
                  isRepricerRender
                    ? l("Select subscription")
                    : l("No valid subscription available")
                }
              >
                {currentPlanOption[product].map(
                  ({ value, label, isNoRender }) =>
                    !isNoRender ? (
                      <Option
                        key={value}
                        className={styles.option}
                        value={value}
                      >
                        {label}
                      </Option>
                    ) : null
                )}
              </Field>
            </div>
          </div>
        </div>
        <FooterModal
          layoutWithFixPaddings
          withLayout
          okText={<FormattedMessage id="Reset subscription" />}
          style={{ marginTop: 0 }}
          cancelButtonProps={{
            disabled: isSubmitting,
          }}
          okButtonProps={{
            disabled: isDisableSubmitButton,

            htmlType: "submit",
          }}
          onCancel={() => onClose(false, undefined, {})}
        />
      </div>
    </Form>
  )
}

RevertPlanView.propTypes = {
  isSubmitting: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  plansOptions: PropTypes.array.isRequired,
  plansOptionsBas: PropTypes.array.isRequired,
}

export default RevertPlanView
