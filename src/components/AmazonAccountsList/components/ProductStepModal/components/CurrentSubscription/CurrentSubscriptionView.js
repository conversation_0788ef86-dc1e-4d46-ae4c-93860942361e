import React from "react"
import PropTypes from "prop-types"
import { Flag } from "@develop/fe-library"
import Typography from "components/Typography"
import FormattedMessage from "components/FormattedMessage"
import { RestrictedSwitch } from "components/Restrict"
import { RestrictedPrimaryButton } from "components/shared/Buttons"

import { permissionKeys, restrictPopoverMessages } from "consts"

import styles from "./currentSubscription.module.scss"

const CurrentSubscriptionView = ({
  accountMarketplaceId,
  active,
  autoRenew,
  dateFinish,
  dateStart,
  displayModal,
  duration,
  extension,
  getAccountMarketplaces,
  marketplaceTitle,
  nextPlan,
  trial,
  updateAccountMarketplace,
  product,
}) => (
  <div className={styles.container}>
    <div className={styles.marketplace}>
      <Flag
        className={styles.imgIcon}
        locale={extension}
        borderRadius="--border-radius-circle"
        size={24}
      />
      <Typography className={styles.marketplaceTitle} type="div" variant="text">
        {marketplaceTitle}
      </Typography>
    </div>
    <div className={styles.durationInfo}>
      <div className={styles.field}>
        <Typography className={styles.label} type="div" variant="textLarge">
          <FormattedMessage id="Duration" />:
        </Typography>
        <Typography className={styles.value} type="div" variant="textLarge">
          <FormattedMessage id={duration} />
        </Typography>
      </div>
      <div className={styles.field}>
        <Typography className={styles.label} type="div" variant="textLarge">
          <FormattedMessage id="Start date" />:
        </Typography>
        <Typography className={styles.value} type="div" variant="textLarge">
          {dateStart}
        </Typography>
      </div>
      <div className={styles.field}>
        <Typography className={styles.label} type="div" variant="textLarge">
          <FormattedMessage id="End date" />:
        </Typography>
        <Typography className={styles.value} type="div" variant="textLarge">
          {dateFinish}
        </Typography>
      </div>
    </div>
    <div className={styles.devider} />
    {!trial && active && (
      <>
        <div className={styles.autorenewContainer}>
          <Typography className={styles.label} type="div" variant="textLarge">
            <FormattedMessage id="Auto renew subscription" />:
          </Typography>
          <Typography className={styles.value} type="div" variant="textLarge">
            {nextPlan ? (
              <Typography type="div" variant="text">
                <FormattedMessage id="Next plan is set, auto renewal is not available" />
              </Typography>
            ) : (
              <RestrictedSwitch
                managePermission={permissionKeys.amazonCustomerAccountManage}
                popoverMessage={restrictPopoverMessages.alter}
                isChecked={autoRenew}
                onChange={(value) => {
                  updateAccountMarketplace(
                    accountMarketplaceId,
                    {
                      auto_renew: value ? 1 : 0,
                    },
                    () => getAccountMarketplaces(false)
                  )
                }}
              />
            )}
          </Typography>
        </div>
        {!nextPlan && (
          <Typography className={styles.notification} type="div" variant="text">
            {autoRenew ? (
              <FormattedMessage id="Your subscription will be automatically renewed after the expiration date unless an extension is selected or an automatic renewal is disabled." />
            ) : (
              <FormattedMessage
                id={`Activate "Auto Renewal" and your subscription will automatically renew for the same period after the current subscription expires if no other tariff is selected or "Auto Renewal" is deactivated before the subscription expires.`}
              />
            )}
          </Typography>
        )}
      </>
    )}
    {!active && (
      <Typography className={styles.notification} type="div" variant="text">
        <FormattedMessage id="Your subscription expired." />
        <RestrictedPrimaryButton
          managePermission={permissionKeys.amazonCustomerAccountManage}
          popoverMessage={restrictPopoverMessages.alter}
          className={styles.updateButton}
          onClick={() =>
            displayModal(true, "repricerNextSubscription", {
              marketplaces: [
                {
                  accountMarketplaceId,
                  showRemindButtons: false,
                },
              ],
              product,
            })
          }
        >
          <FormattedMessage id="Renew now" />
        </RestrictedPrimaryButton>
      </Typography>
    )}
    {active && trial && (
      <Typography className={styles.notification} type="div" variant="text">
        <FormattedMessage id="Your subscription is in trial period." />
        <RestrictedPrimaryButton
          managePermission={permissionKeys.amazonCustomerAccountManage}
          popoverMessage={restrictPopoverMessages.alter}
          className={styles.updateButton}
          onClick={() =>
            displayModal(true, "repricerNextSubscription", {
              marketplaces: [
                {
                  accountMarketplaceId,
                  showRemindButtons: false,
                },
              ],
              product,
            })
          }
        >
          <FormattedMessage id="Update now" />
        </RestrictedPrimaryButton>
      </Typography>
    )}
  </div>
)

CurrentSubscriptionView.propTypes = {
  accountMarketplaceId: PropTypes.number.isRequired,
  active: PropTypes.bool.isRequired,
  autoRenew: PropTypes.bool.isRequired,
  dateFinish: PropTypes.string.isRequired,
  dateStart: PropTypes.string.isRequired,
  displayModal: PropTypes.func.isRequired,
  duration: PropTypes.string.isRequired,
  extension: PropTypes.string.isRequired,
  getAccountMarketplaces: PropTypes.func.isRequired,
  marketplaceTitle: PropTypes.string.isRequired,
  nextPlan: PropTypes.bool.isRequired,
  trial: PropTypes.bool.isRequired,
  updateAccountMarketplace: PropTypes.func.isRequired,
}

export default CurrentSubscriptionView
