@import "assets/styles/variables.scss";

.container {
  padding-top: 6px;
}

.marketplace {
  align-items: center;
  display: flex;
  margin-bottom: 10px;
}

.marketplaceTitle.marketplaceTitle {
  color: $text_second;
}

.imgIcon {
  margin-right: 10px;
}

.field {
  align-items: center;
  display: flex;
  height: 40px;
}

.label,
.value {
  text-align: start;
  width: 50%;
}

.label.label {
  font-weight: 700;
  padding-right: 20px;
}

.devider {
  background: #e7e7e7;
  height: 1px;
  left: -20px;
  position: relative;
  width: calc(100% + 40px);
}

.autorenewContainer {
  align-items: center;
  display: flex;
  padding-top: 20px;
}

.notification {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}

.updateButton {
  flex-shrink: 0;
  margin-left: 10px;
}

@media (max-width: $xs) {
  .notification {
    align-items: center;
    flex-direction: column;
  }

  .devider {
    left: -10px;
    width: calc(100% + 20px);
  }

  .autorenewContainer {
    padding-top: 15px;
    margin-bottom: 15px;
  }

  .updateButton {
    margin-left: 0;
    margin-top: 10px;
  }
}
