import { connect } from "react-redux"

import CurrentSubscriptionView from "./CurrentSubscriptionView"
import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import { currentSubscriptionFormSelector } from "selectors/amazonAccountsSelectors"
import { permissionsSelector } from "selectors/userSelectors"

const { displayModal } = amazonCustomerAccountsActions

const { getAll: getAccountMarketplaces, update: updateAccountMarketplace } =
  amazonCustomerAccountMarketplaceActions

const mapStateToProps = (state) => {
  const { amazonCustomerAccountManage } = permissionsSelector(state)

  return {
    canManage: amazonCustomerAccountManage,
    ...currentSubscriptionFormSelector(state),
    ...state.amazonCustomerAccounts.modals.currentSubscription,
  }
}

export default connect(mapStateToProps, {
  displayModal,
  getAccountMarketplaces,
  updateAccountMarketplace,
})(CurrentSubscriptionView)
