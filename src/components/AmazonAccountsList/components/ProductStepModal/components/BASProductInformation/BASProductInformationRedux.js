import { connect } from "react-redux"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"
import BASProductInformationView from "./BASProductInformationView"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import { isBasActive } from "selectors/basCustomerPlanSelector"

const { displayModal } = amazonCustomerAccountsActions
const { toggleModal: showModuleSetupWizard } = moduleSetupWizardActions

const mapStateToProps = (state) => {
  const {
    staff: {
      currentUser: { id: userId },
    },
    customer: {
      customer: { hasPaymentData, bas_module_started },
    },
    amazonCustomerAccounts: { modalName, modals },
  } = state

  return {
    ...modals[modalName],
    userId,
    hasPaymentData,
    isBasModuleStarted: !!bas_module_started,
    isBasActive: isBasActive(state),
  }
}

export default connect(mapStateToProps, {
  showModuleSetupWizard,
  displayModal,
})(BASProductInformationView)
