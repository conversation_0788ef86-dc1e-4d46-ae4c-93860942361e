@import "assets/styles/variables.scss";

.text,
.checkedTextItem:first-child {
  margin-top: 20px;
}

.textColor,
.text {
  color: $text_second;
}

.checkedTextContainer {
  padding: 0;
  margin: 0;
  list-style: none;
}

.checkedTextItem {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.checkedIcon svg {
  height: 24px;
  width: 24px;
  color: $icon_done;
}

.checkedText {
  font-weight: bold;
  margin-left: 10px;
}

.textWithRequired {
  display: flex;
}

@media (max-width: $sm) {
  .checkedTextItem {
    margin-top: 15px;
  }

  .text,
  .checkedTextItem:first-child {
    margin-top: 10px;
  }

  .required {
    margin-right: 8px;
  }

  .textNoRequired {
    margin-left: 14px;
  }
}
