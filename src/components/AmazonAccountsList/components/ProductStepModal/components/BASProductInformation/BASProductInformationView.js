import React, { useCallback } from "react"
import { CheckOutlined } from "@ant-design/icons"

import Typography from "components/Typography"
import { FooterModal } from "components/shared/Modal"

import l from "utils/intl"
import { pushEvent } from "utils/gtmLoader"

import { EVENTS_NAMES } from "consts/gtm"
import { PRODUCTS } from "consts/product"

import { values } from "./constants"

import styles from "./basProductInformation.module.scss"

const BASProductInformationView = ({
  displayModal,
  showModuleSetupWizard,
  countOfAmazonAccounts,
  isAccountSettings,
  isConnectInAccount,
  connectAccountId,
  hasPaymentData,
  isBasModuleStarted,
  isBasActive,
  isSetupBas = true,
  isNeedSkipSelectAccountToNewSubscriptionStep,
  amazonCustomerAccountIds,
  isSetupNewAccount = false,
}) => {
  const wizardNextStep = () => {
    const isNeedConnectChoseAmazonAccount =
      countOfAmazonAccounts > 0 && !isAccountSettings

    isConnectInAccount
      ? showModuleSetupWizard(true, "connectChoseAmazonAccount", PRODUCTS.bas, {
          connectAccountId,
          selectedRegion: null,
          selectedMarketplaces: null,
        })
      : showModuleSetupWizard(
          true,
          isNeedConnectChoseAmazonAccount
            ? "connectChoseAmazonAccount"
            : "connectChoseRegion",
          PRODUCTS.bas,
          {
            connectAccountId,
            selectedRegion: null,
            selectedMarketplaces: null,
          }
        )

    onCancel(false)
  }

  const payOrNextStep = useCallback(() => {
    if (!hasPaymentData) {
      showModuleSetupWizard(true, "billingInformation", PRODUCTS.bas, {
        selectedRegion: null,
        selectedMarketplaces: null,
        isAccountSettings,
        connectAccountId,
        isConnectInAccount,
        isSetupBas,
        isNeedSkipSelectAccountToNewSubscriptionStep,
        isSetupNewAccount,
      })
    } else {
      if (!isSetupNewAccount) {
        showModuleSetupWizard(false, undefined, PRODUCTS.bas, { noTrack: true })
        displayModal(true, "basNextSubscription", {
          isSetupBas,
          isAccountSettings,
          connectAccountId,
          isConnectInAccount,
          hideRemindLaterButton: true,
          showRemindButtons: false,
          isNeedSkipSelectAccountToNewSubscriptionStep,
          amazonCustomerAccountIds,
        })
      } else {
        wizardNextStep()
      }
    }
  }, [
    hasPaymentData,
    showModuleSetupWizard,
    connectAccountId,
    displayModal,
    isAccountSettings,
    isConnectInAccount,
    isSetupBas,
    isNeedSkipSelectAccountToNewSubscriptionStep,
  ])

  const onCancel = useCallback(
    (useGTM = true) => {
      displayModal(false, undefined, {})
      useGTM &&
        pushEvent({
          vHitNonInteraction: false,
          virtualEventAction: "click-cancel",
          virtualEventCategory:
            "modalWizardProductInformationBusinessAnalytics",
          event: EVENTS_NAMES.updEvents,
        })
    },
    [displayModal]
  )

  const goToNextStep = useCallback(() => {
    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: "click-next",
      virtualEventCategory: "modalWizardProductInformationBusinessAnalytics",
      event: EVENTS_NAMES.updEvents,
    })
    if (!isBasActive && isBasModuleStarted) {
      payOrNextStep()
    } else {
      wizardNextStep()
    }
  }, [
    countOfAmazonAccounts,
    isAccountSettings,
    isConnectInAccount,
    showModuleSetupWizard,
    connectAccountId,
    isBasActive,
    isBasModuleStarted,
    payOrNextStep,
    onCancel,
  ])

  return (
    <div>
      <Typography variant="textLarge" type="div" className={styles.textColor}>
        {l(
          "Analyze and measure your performance to generate insights and keep business processes under control."
        )}
      </Typography>
      <ul className={styles.checkedTextContainer}>
        {values.map(({ label }) => (
          <li className={styles.checkedTextItem} key={label}>
            <CheckOutlined className={styles.checkedIcon} />
            <Typography
              variant="textLarge"
              type="div"
              className={styles.checkedText}
            >
              {l(label)}
            </Typography>
          </li>
        ))}
      </ul>
      <Typography
        variant="textLarge"
        type="div"
        className={`${styles.text} ${styles.textWithRequired}`}
      >
        <div className={styles.required}>*</div>
        {l(
          "When setting up Business Analytics for the first time, you can add an unlimited number of accounts - they are included in one subscription."
        )}
      </Typography>
      <Typography
        variant="textLarge"
        type="div"
        className={`${styles.text} ${styles.textNoRequired}`}
      >
        {l(
          "All new accounts added after the trial period ends will be included in the same subscription with a monthly payment calculated based on your order volume."
        )}
      </Typography>
      <FooterModal
        withLayout
        layoutWithFixPaddings
        okText={l("Next")}
        onOk={goToNextStep}
        okButtonProps={{
          className: styles.okBtn,
        }}
        cancelButtonProps={{
          className: styles.cancelBtn,
        }}
        cancelText={l("Cancel")}
        onCancel={onCancel}
      />
    </div>
  )
}

export default BASProductInformationView
