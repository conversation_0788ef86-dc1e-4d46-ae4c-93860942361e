import { connect } from "react-redux"
import { PreviousBasSubscription } from "./PreviousBasSubscription"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"

import {
  activeSubscriptionId,
  oldSubscriptionId,
} from "selectors/basCustomerPlanSelector"
import { permissionsSelector } from "selectors/userSelectors"

const { displayModal } = amazonCustomerAccountsActions

const mapStateToProps = (state) => {
  const { amazonCustomerAccountEditSubscriptionDuration } =
    permissionsSelector(state)

  const {
    amazonCustomerAccounts: { modalName, modals },
  } = state

  return {
    ...modals[modalName],
    activeSubscriptionId: activeSubscriptionId(state),
    oldSubscriptionId: oldSubscriptionId(state),
    canManageDuration: amazonCustomerAccountEditSubscriptionDuration,
  }
}

export default connect(mapStateToProps, {
  displayModal,
})(PreviousBasSubscription)
