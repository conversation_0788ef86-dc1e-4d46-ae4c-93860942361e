import React from "react"
import { Box, Typography } from "@develop/fe-library"

import { Restrict, RestrictedIconPopover } from "components/Restrict"
import ProductContextMenu from "components/AmazonAccountsList/components/ProductContextMenu"

import l from "utils/intl"
import { basicRestrictionAndSellerLogicUser } from "utils/customRestrictions"
import { convertStringDateToMoment } from "utils/dateConverter"

import { permissionKeys, productConstants } from "consts"

const { PRODUCTS } = productConstants

export const PreviousBasSubscription = ({
  activeSubscriptionId,
  oldSubscriptionId,
  period,
  datePeriod,
  startDate,
  endDate,
  canManageDuration,
  displayModal,
  deleted,
}) => {
  const openChangeDuration = () => {
    if (canManageDuration) {
      displayModal(true, "changeDuration", {
        product: PRODUCTS.bas,
        disabledRangePickerInputByIndex: 0,
        disabledDate: (currentDate) => {
          return currentDate?.isSameOrBefore(
            convertStringDateToMoment(startDate),
            "days"
          )
        },
        initialValues: {
          date_start: startDate,
          date_finish: endDate,
        },
      })
    }
  }

  return (
    <Box
      backgroundColor="--color-alert-background-warning"
      justify="space-between"
      mSM={{
        padding: "m",
        gap: "m",
      }}
      mXL={{
        padding: "l",
        gap: "l",
      }}
    >
      <Box
        flexDirection="column"
        gap="s"
        mLG={{
          flexDirection: "row",
          justify: "space-between",
          width: "100%",
        }}
        mXL={{
          gap: "m",
        }}
      >
        <Box gap="m">
          <Typography variant="--font-body-text-5">
            {l("Previous subscription")}
          </Typography>

          <RestrictedIconPopover
            viewPermission={permissionKeys.sellerLogicManager}
            customViewRestriction={basicRestrictionAndSellerLogicUser}
            content={`${l("Subscription ID:")} ${
              activeSubscriptionId ? activeSubscriptionId : oldSubscriptionId
            }`}
            name="icnInfoCircle"
            size="--icon-size-3"
            color="--color-icon-active"
          />
        </Box>

        <Box
          mSM={{ display: "block" }}
          mXL={{ display: "flex", flexDirection: "column", gap: "s" }}
        >
          <Typography variant="--font-body-text-5">{period}</Typography>

          <Typography
            color={canManageDuration ? "--color-text-link" : undefined}
            variant="--font-body-text-9"
            onClick={openChangeDuration}
          >
            {datePeriod}
          </Typography>
        </Box>
      </Box>

      <Restrict viewPermission={permissionKeys.rootManager}>
        {!deleted ? (
          <div>
            <ProductContextMenu
              product={PRODUCTS.bas}
              isRevertSubscriptionDisabled
            />
          </div>
        ) : null}
      </Restrict>
    </Box>
  )
}
