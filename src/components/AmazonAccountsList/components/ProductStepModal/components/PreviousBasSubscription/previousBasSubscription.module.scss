.settingBasHeader {
  display: flex;
  justify-content: space-between;
  background-color: var(--color-alert-background-warning);
  padding: var(--padding-l);
  border-bottom: var(--border-checkbox);
}

.settingCurrentSubscriptionContainer {
  display: flex;
}

.settingCurrentSubscription.settingCurrentSubscription {
  margin-right: var(--margin-m);
}

.settingBasHeaderDateContainer {
  max-width: 100%;
}

.settingBasHeaderDatePeriod.settingBasHeaderDatePeriod {
  margin-top: var(--margin-m);
}

.settingBasHeaderDate.settingBasHeaderDate {
  margin-top: var(--margin-m);
}

.settingBasHeaderFullDate {
  margin-top: var(--margin-s);
}

.settingBasHeaderDateManage {
  color: var(--color-text-link);
  cursor: pointer;
}

.settingBasHeaderPreviousSubscriptionContainer {
  display: flex;
  width: 100%;
  max-width: 16px;
  justify-content: space-between;
}

.settingBasContextMenu {
  height: 24px;
  width: 24px;
}

.currentSubscriptionDateManage.currentSubscriptionDateManage {
  cursor: pointer;
}

@media (max-width: 576px) {
  .settingBasHeader {
    padding: var(--padding-m);
    flex-direction: column;
    align-items: flex-start;
  }

  .settingBasHeaderDate.settingBasHeaderDate {
    margin-top: var(--margin-s);
  }

  .settingBasHeaderPreviousSubscriptionContainer {
    max-width: 100%;
    margin-top: var(--margin-m);
  }
}
