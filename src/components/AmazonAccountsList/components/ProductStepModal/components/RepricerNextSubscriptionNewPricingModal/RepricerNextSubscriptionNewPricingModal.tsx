import React from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, Button, Modal } from "@develop/fe-library"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import repricerSubscriptionActions from "actions/repricerSubscriptionActions"

import { repricerCurrentSubscriptionPlanSelector } from "selectors/repricerSubscriptionSelectors"

import { RepricerSubscriptionSelection } from "components/RepricerSubscriptionSelection"

import l from "utils/intl"

import { RepricerNextSubscriptionNewPricingModalProps } from "./RepricerNextSubscriptionNewPricingModalTypes"

const { displayModal } = amazonCustomerAccountsActions

const { ignoreNextSubscriptionAlert, remindLaterNextSubscriptionAlert } =
  repricerSubscriptionActions

export const RepricerNextSubscriptionNewPricingModal = ({
  onClose,
  isFooterButtonVisible = true,
  title,
  isClosable = true,
}: RepricerNextSubscriptionNewPricingModalProps) => {
  const dispatch = useDispatch()

  const repricerCurrentSubscriptionPlan = useSelector(
    repricerCurrentSubscriptionPlanSelector
  )

  const remindMeLaterHandler = (): void => {
    dispatch(remindLaterNextSubscriptionAlert({ successCallback: onClose }))
  }

  const ignoreAlertHandler = (): void => {
    dispatch(ignoreNextSubscriptionAlert({ successCallback: onClose }))
  }

  const handleSetPlanSuccess = (): void => {
    dispatch(displayModal(false, "repricerNextSubscriptionNewPricing", {}))

    onClose?.()
  }

  return (
    <Modal
      isWithoutBodyPadding
      visible
      isClosable={isClosable}
      title={title ?? l("Next subscription")}
      width="--modal-size-l"
      footer={
        isFooterButtonVisible ? (
          <Box display="flex" gap="m" justify="flex-end" width="100%">
            {repricerCurrentSubscriptionPlan ? (
              <Box flexGrow={1} tb={{ flexGrow: 0 }}>
                <Button
                  fullWidth
                  variant="secondary"
                  onClick={remindMeLaterHandler}
                >
                  {l("Remind me later")}
                </Button>
              </Box>
            ) : null}

            <Box flexGrow={1} tb={{ flexGrow: 0 }}>
              <Button
                fullWidth
                variant="secondary"
                onClick={ignoreAlertHandler}
              >
                {l("Do not renew")}
              </Button>
            </Box>
          </Box>
        ) : null
      }
      onClose={onClose}
    >
      <RepricerSubscriptionSelection
        isSetNextPlan
        onSuccess={handleSetPlanSuccess}
      />
    </Modal>
  )
}
