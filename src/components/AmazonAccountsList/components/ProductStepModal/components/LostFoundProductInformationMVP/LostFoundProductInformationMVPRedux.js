import { connect } from "react-redux"

import LostFoundProductInformationMPVView from "./LostFoundProductInformationMPVView"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import customerActions from "actions/customerActions"
import { permissionsSelector } from "selectors/userSelectors"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

const { getCurrentCustomer, signLostContractMVP } = customerActions
const { toggleModal: showModuleSetupWizard } = moduleSetupWizardActions

const {
  displayModal,
  getAll: getAccounts,
  setUseLost,
} = amazonCustomerAccountsActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccounts: {
      modals: { lostFoundProductInformationMVP },
    },
    translations: { locale: language },
    customer: {
      customer: { hasPaymentData },
    },
  } = state
  const { amazonCustomerAccountManage } = permissionsSelector(state)

  return {
    canManage: amazonCustomerAccountManage,
    lostFoundProductInformationMVP,
    language,
    lostRate: state.customer.customer.lost_rate,
    hasPaymentData,
  }
}

export default connect(mapStateToProps, {
  getAccounts,
  getCurrentCustomer,
  onClose: displayModal,
  setUseLost,
  signLostContractMVP,
  showModuleSetupWizard,
})(LostFoundProductInformationMPVView)
