import React from "react"
import PropTypes from "prop-types"
import { CheckOutlined } from "@ant-design/icons"

import Typography from "components/Typography"
import FormattedMessage from "components/FormattedMessage"
import { Button, PrimaryButton } from "components/shared/Buttons"

import { pushEvent } from "utils/gtmLoader"

import { features } from "./constants"
import { EVENTS_NAMES } from "consts/gtm"

import { replaceLanguageCode } from "utils/replaceLanguageCode"

import styles from "./lostFoundProductInformationMVP.module.scss"

const LostFoundProductInformationView = ({
  getAccounts,
  getCurrentCustomer,
  canManage,
  onClose,
  setUseLost,
  signLostContractMVP,
  lostRate,
  lostFoundProductInformationMVP: {
    accountId,
    enable,
    countOfAmazonAccounts,
    isAccountSettings,
    connectAccountId,
  },
  showModuleSetupWizard,
  hasPaymentData,
  language,
}) => {
  const wizardAccountNextStep =
    countOfAmazonAccounts > 0
      ? "connectChoseAmazonAccount"
      : "connectChoseRegion"

  const wizardNextStep = !hasPaymentData
    ? "billingInformation"
    : wizardAccountNextStep

  const closeHandler = () => {
    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: "click-cancel",
      virtualEventCategory: "modalWizardContractLostAndFound",
      event: EVENTS_NAMES.updEvents,
    })

    onClose(false, undefined, {})
  }

  const goToNextStep = () => {
    if (enable) {
      pushEvent({
        vHitNonInteraction: false,
        virtualEventAction: "click-find-reimbursements-now",
        virtualEventCategory: "modalWizardContractLostAndFound",
        event: EVENTS_NAMES.updEvents,
      })

      signLostContractMVP(undefined, () => {
        getCurrentCustomer(false, () => {
          showModuleSetupWizard(true, wizardNextStep, "lost-and-found", {
            selectedRegion: null,
            selectedMarketplaces: null,
            isAccountSettings,
            connectAccountId,
          })
        })
      })
    } else {
      signLostContractMVP(undefined, () => {
        setUseLost(accountId, { value: 1 }, () => {
          getAccounts(false)
          getCurrentCustomer(false, undefined)
        })
      })
    }

    onClose(false, undefined, {})
  }

  const feesLink = replaceLanguageCode({
    url: "https://support.sellerlogic.com/{localeCode}/cases/cases",
    language,
  })

  return (
    <div className={styles.container}>
      <Typography className={styles.title} type="div" variant="textLarge">
        <FormattedMessage id="The tool that automatically and without any exception analyzes all FBA transactions and shows hidden reimbursement claims." />
      </Typography>
      <ul className={styles.features}>
        {features.map((feature) => (
          <li className={styles.feature}>
            <CheckOutlined className={styles.icon} />
            <Typography
              className={styles.featureTitle}
              type="div"
              variant="textLarge"
            >
              <FormattedMessage id={feature} />
            </Typography>
          </li>
        ))}
      </ul>
      <div className={styles.clarification}>
        <Typography
          className={styles.clarificationTitle}
          type="div"
          variant="textLarge"
        >
          <FormattedMessage
            id="Only {lostRate}% of the reimbursement"
            defaultMessage="Only {lostRate}% of the reimbursement"
            values={{ lostRate: lostRate }}
          />
        </Typography>
        <Typography
          className={styles.clarificationNote}
          type="div"
          variant="textLarge"
        >
          <FormattedMessage id="No additional costs if all cases are submitted and processed on time*" />
        </Typography>
      </div>
      <div className={styles.note}>
        <Typography
          className={styles.noteContent}
          type="div"
          variant="textLarge"
        >
          <FormattedMessage
            id="No monthly fee. This  means you do not face  any costs untill errors are found and reimbursed by Amazon. More information about <a>Fees</a> can be found in our Support Center."
            defaultMessage="No monthly fee. This  means you do not face  any costs untill errors are found and reimbursed by Amazon. More information about <a>Fees</a> can be found in our Support Center."
            values={{
              a: (...chunks) => (
                <a
                  className={styles.link}
                  href={feesLink}
                  target="_blank"
                  rel="noreferrer noopener"
                >
                  {chunks}
                </a>
              ),
              lb: (...chunks) => (
                <>
                  <br />
                  {chunks}
                </>
              ),
            }}
          />
          <br />
          <br />
          <FormattedMessage id="Unless otherwise stated, our prices are exclusive of applicable VAT" />
        </Typography>
      </div>
      {!canManage && (
        <div className={styles.accessNotification}>
          <Typography type="div" variant="textLarge">
            <FormattedMessage id="The account administrator in the company can activate Lost & Found from your SellerLogic customer account. Please contact him directly." />
          </Typography>
        </div>
      )}
      <div className={styles.buttonsContainer}>
        <Button className={styles.cancelButton} onClick={closeHandler}>
          <Typography variant="controlLabel">
            <FormattedMessage id="Cancel" />
          </Typography>
        </Button>
        {canManage && (
          <PrimaryButton
            className={styles.submitButton}
            htmlType="submit"
            onClick={goToNextStep}
          >
            <Typography variant="controlLabel">
              <FormattedMessage id="Find reimbursements now" />
            </Typography>
          </PrimaryButton>
        )}
      </div>
    </div>
  )
}

LostFoundProductInformationView.propTypes = {
  canManage: PropTypes.bool,
  getAccounts: PropTypes.func.isRequired,
  getCurrentCustomer: PropTypes.func.isRequired,
  language: PropTypes.string.isRequired,
  lostFoundProductInformation: PropTypes.object.isRequired,
  lostRate: PropTypes.number.isRequired,
  onClose: PropTypes.func.isRequired,
  setUseLost: PropTypes.func.isRequired,
  signLostContractMVP: PropTypes.func.isRequired,
  showModuleSetupWizard: PropTypes.func.isRequired,
}

export default LostFoundProductInformationView
