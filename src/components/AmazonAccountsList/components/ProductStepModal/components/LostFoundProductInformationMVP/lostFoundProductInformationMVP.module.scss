@import "assets/styles/variables.scss";

.container {
  display: flex;
  flex-direction: column;
}

.title.title {
  color: $text_second;
  line-height: 1.4;
  margin-bottom: 20px;
}

.features {
  list-style: none;
  margin: 0;
  padding: 0;
}

.feature {
  display: flex;
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 20px;
  }
}

.icon {
  color: $icon_done;
  font-size: 18px;
  margin-right: 10px;
}

.featureTitle.featureTitle {
  font-weight: 700;
}

.clarificationTitle.clarificationTitle {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 15px;
}

.clarificationNote.clarificationNote {
  font-weight: 700;
}

.clarification {
  background-color: rgba(139, 195, 74, 0.2);
  border: 1px solid rgba(139, 195, 74, 0.3);
  border-radius: 2px;
  margin-bottom: 20px;
  padding: 20px 20px 25px;
}

.note {
  margin-bottom: 40px;
  padding-left: 20px;
  position: relative;

  &:before {
    color: $text_second;
    content: "*";
    left: 8px;
    position: absolute;
  }
}

.noteContent.noteContent {
  color: $text_second;
}

.checkbox {
  margin-right: 10px;
}

.termsContainer {
  margin-bottom: 20px;
}

.cancelButton {
  margin-right: 10px;
}

.buttonsContainer {
  border-top: 1px solid $hover_grid;
  display: flex;
  justify-content: flex-end;
  padding: 10px 30px 0;
  position: relative;
  right: 30px;
  width: calc(100% + 60px);
}

.submitButton {
}

.accessNotification {
  background-color: rgba(248, 145, 145, 0.2);
  border: 1px solid rgba(248, 145, 145, 0.3);
  border-radius: 2px;
  padding: 20px;
  margin-bottom: 20px;
}

@media (max-width: $xs) {
  .note {
    margin-bottom: 20px;
  }

  .clarificationTitle.clarificationTitle {
    font-size: 16px;
  }

  .buttonsContainer {
    flex-wrap: wrap;
    width: calc(100% + 20px);
    right: auto;
    padding-left: 10px;
    padding-right: 10px;
    margin-left: -10px;
  }

  .submitButton,
  .cancelButton {
    flex-basis: 100%;
    margin-right: 0;
  }

  .cancelButton {
    margin-bottom: 10px;
  }
}
