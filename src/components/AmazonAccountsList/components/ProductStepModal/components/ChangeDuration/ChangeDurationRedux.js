import { connect } from "react-redux"

import ChangeDurationFormik from "./ChangeDurationFormik"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"
import customerActions from "actions/customerActions"

const { displayModal } = amazonCustomerAccountsActions
const { getBasPlan, changeBasDuration, getBasSubscriptions } =
  basCustomerPlanActions
const { changePlanDuration, getAll: getAccountMarketplaces } =
  amazonCustomerAccountMarketplaceActions
const { getCurrentCustomer } = customerActions

const mapStateToProps = (state) => ({
  ...state.amazonCustomerAccounts.modals.changeDuration,
})

export default connect(mapStateToProps, {
  getAccountMarketplaces,
  onClose: displayModal,
  onSubmit: changePlanDuration,
  getBasPlan,
  changeBasDuration,
  getBasSubscriptions,
  getCurrentCustomer,
})(ChangeDurationFormik)
