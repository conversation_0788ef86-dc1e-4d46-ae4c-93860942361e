import React, { useCallback } from "react"
import PropTypes from "prop-types"

import { FormKeypressHandler } from "components/KeypressHandler"
import FormikWithChangeValidation from "components/shared/FormikWithChangeValidation"

import { PRODUCTS } from "consts/product"

import ChangeDurationView from "./ChangeDurationView"

const ChangeDurationFormik = ({
  accountMarketplaceId,
  getAccountMarketplaces,
  initialValues,
  onClose: propOnClose,
  onSubmit,
  onFormIsChanged,
  product,
  getBasPlan,
  changeBasDuration,
  getBasSubscriptions,
  getCurrentCustomer,
  ...modalProps
}) => {
  const isOldRepricer =
    product === PRODUCTS.repricerB2C || product === PRODUCTS.repricerB2B

  const onClose = useCallback(() => {
    modalProps?.onModalClose
      ? modalProps.onModalClose()
      : propOnClose(false, undefined, {})
  }, [modalProps, propOnClose])

  const repricerOnSubmit = useCallback(
    (payload, setSubmitting, setErrors) => {
      onSubmit(
        accountMarketplaceId,
        payload,
        (errors) => {
          setErrors(
            errors.reduce((acc, { field, message }) => {
              const newField =
                field === "date_start" || field === "date_finish"
                  ? "dateRange"
                  : field

              acc[newField] = !acc[newField] ? message : !acc[newField]

              return acc
            }, {})
          )

          setSubmitting(false)
        },
        () => {
          getAccountMarketplaces(false)
          propOnClose()
        }
      )
    },
    [accountMarketplaceId, getAccountMarketplaces, onSubmit, propOnClose]
  )

  const basOnSubmit = useCallback(
    (payload, setSubmitting, setErrors) => {
      changeBasDuration(
        payload,
        () => {
          getCurrentCustomer(false, () => {
            getBasPlan({ cache: false, successCallback: null })
            getBasSubscriptions()
            propOnClose()
          })
        },
        (errors) => {
          setErrors(
            errors.reduce((acc, { field, message }) => {
              const newField =
                field === "date_start" || field === "date_finish"
                  ? "dateRange"
                  : field

              acc[newField] = !acc[newField] ? message : !acc[newField]

              return acc
            }, {})
          )

          setSubmitting(false)
        }
      )
    },
    [
      changeBasDuration,
      getBasPlan,
      propOnClose,
      getBasSubscriptions,
      getCurrentCustomer,
    ]
  )

  return (
    <FormikWithChangeValidation
      clearSubmitErrorsOnChange
      initialValues={initialValues}
      onFormIsChanged={onFormIsChanged}
      onSubmit={(payload, { setErrors, setSubmitting }) => {
        isOldRepricer
          ? repricerOnSubmit(payload, setSubmitting, setErrors)
          : basOnSubmit(payload, setSubmitting, setErrors)
      }}
    >
      {(formikProps) => (
        <>
          <FormKeypressHandler
            onClose={onClose}
            onSubmit={() =>
              !formikProps.isSubmitting && formikProps.submitForm()
            }
          />
          <ChangeDurationView
            {...{ ...formikProps, ...modalProps }}
            onClose={onClose}
          />
        </>
      )}
    </FormikWithChangeValidation>
  )
}

ChangeDurationFormik.propTypes = {
  accountMarketplaceId: PropTypes.number.isRequired,
  getAccountMarketplaces: PropTypes.func.isRequired,
  initialValues: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  onFormIsChanged: PropTypes.func,
}

export default ChangeDurationFormik
