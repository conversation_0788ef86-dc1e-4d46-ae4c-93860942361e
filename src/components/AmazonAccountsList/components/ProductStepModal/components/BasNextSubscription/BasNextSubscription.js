import React, { useCallback, useEffect, useState } from "react"
import cn from "classnames"
import { Select } from "antd"
import PropTypes from "prop-types"

import Typography from "components/Typography"

import l from "utils/intl"
import { checkIsArray } from "utils/arrayHelpers"
import { pushEvent } from "utils/gtmLoader"

import { Button } from "components/shared/Buttons"
import { SubscriptionPlan } from "../SubscriptionPlan"

import { EVENTS_NAMES, GTM_SUBSCRIPTION_TYPES, PRODUCT_ORDER } from "consts/gtm"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import styles from "./basNextSubscription.module.scss"

export const BasNextSubscription = ({
  dateRange,
  paymentMatrix,
  paymentPeriods,
  hideRemindLaterButton = true,
  showRemindButtons = false,
  language,
  setBasPlan,
  displayModal,
  getBasPlan,
  isBasActive,
  nextBasPaymentPlan,
  currentBasPaymentPeriod,
  isAutoRenewActive,
  nextBasPaymentPeriod,
  cancelBasNextPlan,
  remindBasLater,
  ignoreBasAlert,
  isSetupBas,
  isAccountSettings,
  changeStep,
  countOfAmazonAccounts,
  isConnectInAccount,
  connectAccountId,
  ordersLastMonth,
  desiredPeriod,
  getBasPaymentPlans,
  getBasCustomerStatistic,
  isRedirect,
  getCurrentCustomer,
  user_id,
  customer_id,
  isNeedSkipSelectAccountToNewSubscriptionStep = false,
  hasUserDPAContract,
  amazonCustomerAccountIds,
}) => {
  const currentSubscriptionCheckIn = nextBasPaymentPlan
    ? GTM_SUBSCRIPTION_TYPES.next
    : GTM_SUBSCRIPTION_TYPES.current

  useEffect(() => {
    pushEvent({ ecommerce: null })

    pushEvent({
      event: EVENTS_NAMES.subscriptionOfferView,
      customer_id,
      user_id,
      ecommerce: {
        items: [
          {
            item_name: PRODUCT_NAMES[PRODUCTS.bas],
            item_id: PRODUCT_ORDER[PRODUCTS.bas],
            subscription_type: currentSubscriptionCheckIn,
            index: 0,
            quantity: 1,
          },
        ],
      },
    })

    getBasPaymentPlans({ cache: true })
    getBasCustomerStatistic()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const conditionsBas =
    !checkIsArray(dateRange) ||
    !checkIsArray(paymentMatrix) ||
    !checkIsArray(paymentPeriods)

  const { Option } = Select

  const { id } = desiredPeriod
  const [periodId, setPeriodId] = useState(id)
  const [isVisible, setIsVisible] = useState(false)
  const currentDate = dateRange.find(({ id: dateId }) => dateId === periodId)
  const hasOrders = currentDate && (ordersLastMonth !== 0 || isVisible)
  const currentPeriod = hasOrders
    ? `${currentDate.from} - ${currentDate.to}`
    : l("Select the number of orders")

  const matrix = paymentMatrix.filter(
    ({ payment_module_flexible_size_id }) =>
      payment_module_flexible_size_id === periodId
  )

  const cancelSubscriptionGTMEvent = () => {
    pushEvent({ ecommerce: null })

    pushEvent({
      event: EVENTS_NAMES.subscriptionUpdate,
      customer_id,
      user_id,
      ecommerce: {
        items: [
          {
            item_name: PRODUCT_NAMES[PRODUCTS.bas],
            item_id: PRODUCT_ORDER[PRODUCTS.bas],
            item_variant: "Canceled",
            subscription_type: GTM_SUBSCRIPTION_TYPES.next,
            index: 0,
            quantity: 1,
          },
        ],
      },
    })
  }
  const nextSubscriptionGTMEvent = ({ subscription }) => {
    const currentSubscriptionCheckOut = nextBasPaymentPlan
      ? GTM_SUBSCRIPTION_TYPES.current
      : GTM_SUBSCRIPTION_TYPES.next

    pushEvent({ ecommerce: null })

    pushEvent({
      event: EVENTS_NAMES.subscriptionUpdate,
      customer_id,
      user_id,
      ecommerce: {
        items: [
          {
            item_name: PRODUCT_NAMES[PRODUCTS.bas],
            item_id: PRODUCT_ORDER[PRODUCTS.bas],
            item_variant: subscription,
            subscription_type: currentSubscriptionCheckOut,
            index: 0,
            quantity: 1,
          },
        ],
      },
    })
  }

  const connectBasAccount = useCallback(() => {
    changeStep(true, "connectChoseAmazonAccount", "bas", {
      connectAccountId,
      selectedRegion: null,
      selectedMarketplaces: null,
    })
  }, [changeStep, connectAccountId])

  const goToNextWizardStep = useCallback(() => {
    const dpaCheckingRoute = hasUserDPAContract ? "summary" : "dpaSettings"

    changeStep(true, dpaCheckingRoute, PRODUCTS.bas, {
      isTriggerGTM4: true,
    })
  }, [changeStep, countOfAmazonAccounts, isAccountSettings])

  useEffect(() => {
    setPeriodId(id)
  }, [desiredPeriod.id])

  const handleCloseSubscriptionModal = () => displayModal(false, undefined, {})

  const isShow = isVisible || ordersLastMonth !== 0
  if (conditionsBas) {
    return null
  }

  const setBasPlanWithoutSelect = ({
    paymentModuleFlexiblePeriodId,
    index,
  }) => {
    const isNeedUseActivatePreviousAccounts =
      isRedirect || !checkIsArray(amazonCustomerAccountIds)

    const activateAccountFromRedirectOrNormalConnect =
      isNeedUseActivatePreviousAccounts
        ? { activateAllPreviousAccounts: isRedirect ? 1 : 0 }
        : { amazonCustomerAccountIds }

    return setBasPlan({
      paymentModuleFlexiblePeriodId,
      language,
      ...activateAccountFromRedirectOrNormalConnect,
      successCallback: () => {
        nextSubscriptionGTMEvent({
          subscription: paymentPeriods[index].title,
        })
        getBasPlan({ cache: false, successCallback: null })
        getCurrentCustomer(false, null)
        if (isSetupBas) {
          if (isConnectInAccount) {
            connectBasAccount()
          } else {
            goToNextWizardStep()
          }
        }
        displayModal(false, undefined, {})
      },
    })
  }

  const setBasPlanWithSelect = ({ paymentModuleFlexiblePeriodId }) => {
    changeStep(true, "connectAmazonAccountsForBAS", PRODUCTS.bas, {
      paymentModuleFlexiblePeriodId,
      connectAccountId,
    })

    displayModal(false, undefined, {})
  }

  const basSetPlanHandler =
    ({ paymentModuleFlexiblePeriodId, index }) =>
    () =>
      isNeedSkipSelectAccountToNewSubscriptionStep
        ? setBasPlanWithoutSelect({ paymentModuleFlexiblePeriodId, index })
        : setBasPlanWithSelect({ paymentModuleFlexiblePeriodId })

  return (
    <div className={`${styles.containerBas} `}>
      <div className={styles.devider} />
      <div className={cn(styles.selectContainer)}>
        <Typography
          className={styles.productsNumberBas}
          type="div"
          variant="text"
        >
          {l("Number of orders per month:")}
        </Typography>
        <Select
          className={styles.selectControl}
          onChange={(id) => {
            setPeriodId(id)
            setIsVisible(true)
          }}
          value={currentPeriod}
        >
          {dateRange.map(({ id, from, to }) => (
            <Option value={id} key={id}>
              {from} - {to}
            </Option>
          ))}
        </Select>
      </div>
      {isShow ? (
        <div className={styles.plansContainer}>
          {matrix.map(
            ({ value, id, payment_module_flexible_period_id }, index) => {
              const isBasButtonDisabled =
                isBasActive &&
                ((isAutoRenewActive &&
                  !nextBasPaymentPlan &&
                  currentBasPaymentPeriod ===
                    payment_module_flexible_period_id) ||
                  nextBasPaymentPeriod === payment_module_flexible_period_id)

              return (
                <SubscriptionPlan
                  disabled={false}
                  disabledButton={isBasButtonDisabled}
                  value={value}
                  id={id}
                  iterator={index}
                  paymentPeriods={paymentPeriods[index].title}
                  buttonTitle="Subscribe"
                  onClick={basSetPlanHandler({
                    paymentModuleFlexiblePeriodId:
                      payment_module_flexible_period_id,
                    index,
                  })}
                />
              )
            }
          )}
        </div>
      ) : null}
      <div className={styles.footerWithButtonContainer}>
        <Typography
          className={styles.disclaimer}
          type="div"
          variant="textSmall"
        >
          {l(
            "Unless otherwise stated, our prices are exclusive of applicable VAT."
          )}
        </Typography>
        <div className={styles.buttonContainer}>
          {nextBasPaymentPlan && (
            <div
              className={`${styles.footerButtons} ${styles.footerButtonsBas}`}
            >
              <Button
                className={`${styles.footerButton}`}
                onClick={() => {
                  cancelSubscriptionGTMEvent()
                  cancelBasNextPlan("", () =>
                    getBasPlan({ successCallback: null, cache: false })
                  )
                }}
              >
                {l("Cancel next subscription")}
              </Button>
            </div>
          )}
          {showRemindButtons && (
            <div
              className={`${styles.footerButtons} ${styles.footerButtonsBas}`}
            >
              {!hideRemindLaterButton && (
                <Button
                  className={`${styles.footerButton} ${styles.remindButton}`}
                  buttonSize="middle"
                  remindBasLater
                  onClick={() => {
                    remindBasLater("", () =>
                      getBasPlan({
                        cache: false,
                        successCallback: handleCloseSubscriptionModal,
                      })
                    )
                  }}
                >
                  {l("Remind me later")}
                </Button>
              )}
              <Button
                className={`${styles.footerButton} ${styles.remindButton}`}
                buttonSize="middle"
                onClick={() => {
                  ignoreBasAlert("", () =>
                    getBasPlan({
                      cache: false,
                      successCallback: handleCloseSubscriptionModal,
                    })
                  )
                }}
              >
                {l("Do not renew")}
              </Button>
            </div>
          )}
        </div>
      </div>
      <Typography className={styles.note} type="div" variant="text">
        {l(
          "By selecting the subscription, you select a new subscription tarif. The new subscription will be activated after the current subscription expiration. The renewal can be changed or canceled at any time until the renewal comes into effect."
        )}
      </Typography>
    </div>
  )
}

BasNextSubscription.propTypes = {
  dateRange: PropTypes.array.isRequired,
  paymentMatrix: PropTypes.array.isRequired,
  paymentPeriods: PropTypes.array.isRequired,
  hideRemindLaterButton: PropTypes.bool,
  showRemindButtons: PropTypes.bool,
  isSetupBas: PropTypes.bool,
  isAccountSettings: PropTypes.bool,
  language: PropTypes.string.isRequired,
  basSetPlan: PropTypes.func.isRequired,
  displayModal: PropTypes.func.isRequired,
  getBasPlan: PropTypes.func.isRequired,
  isBasActive: PropTypes.bool.isRequired,
  nextBasPaymentPlan: PropTypes.bool.isRequired,
  currentBasPaymentPeriod: PropTypes.number.isRequired,
  isAutoRenewActive: PropTypes.bool.isRequired,
  changeStep: PropTypes.func.isRequired,
  countOfAmazonAccounts: PropTypes.number.isRequired,
}
