.containerBas {
  padding-top: 15px;
}

.selectContainer {
  color: var(--color-text-second);
  display: flex;
  height: 100%;
  align-items: center;
}

.devider {
  background: #e7e7e7;
  height: 1px;
  left: calc(-1 * var(--margin-l));
  position: relative;
  width: calc(100% + 40px);
}

.selectControl {
  width: 200px;
  margin: var(--margin-l) 0 var(--margin-m) var(--margin-l);
}

.productsNumberBas {
  margin: var(--margin-l) 0 var(--margin-m);
  white-space: nowrap;
}

.plansContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.footerButton {
  padding: 6px var(--padding-l);
}

.footerButton {
  &.remindButton {
    color: var(--color-text-main);

    &:first-child {
      margin-right: var(--margin-m);
    }
  }
}

.note.note {
  color: var(--color-text-second);
}

.disclaimer.disclaimer {
  color: var(--color-text-second);
  margin: var(--margin-m) 0;
  text-align: left;
  white-space: nowrap;
}

.footerWithButtonContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--padding-l) 0;
}

.buttonContainer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  width: 100%;
}

@media (max-width: 940px) {
  .selectControl {
    width: 100%;
  }

  .footerButton {
    &.remindButton {
      width: 100%;
      padding: 6px 0;
    }
  }

  .footerWithButtonContainer {
    flex-direction: column;
    align-items: flex-start;
    padding: 0 0 var(--padding-l);
  }

  .footerButtonsBas {
    width: 100%;
    margin: var(--margin-m) 0 0;
  }

  .footerButtons {
    display: flex;
    justify-content: center;
    margin-top: var(--margin-m);
    width: 100%;
  }

  .productsNumberBas {
    margin: 7px 0 0;
  }

  .buttonContainer {
    margin: 0;
  }
}

@media (max-width: 768px) {
  .disclaimer.disclaimer {
    margin: var(--margin-l) 0 var(--margin-m);
  }
}

@media (max-width: 576px) {
  .devider {
    left: -10px;
    width: calc(100% + var(--margin-l));
  }
}

@media (max-width: 480px) {
  .productsNumberBas {
    margin: var(--margin-m) 0;
  }

  .footerWithButtonContainer {
    align-items: center;
    padding: 0 0 var(--padding-m);
  }

  .disclaimer.disclaimer {
    white-space: normal;
    margin: var(--margin-m) 0;
    text-align: center;
  }

  .footerButtons {
    margin-top: 0;
  }

  .note.note {
    text-align: center;
  }

  .selectContainer {
    flex-direction: column;
    align-items: flex-start;
  }

  .selectControl {
    margin: 0 0 var(--margin-m);
  }
}
