import { connect } from "react-redux"

import basPaymentsActions from "actions/basPaymentsActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"
import customerActions from "actions/customerActions"
import {
  isBasActive,
  nextBasPaymentPlan,
  currentBasPaymentPeriod,
  isAutoRenewActive,
  nextBasPaymentPeriod,
  basCustomerStatisticCurrentSelector,
} from "selectors/basCustomerPlanSelector"
import { staffUserIdSelector } from "selectors/staffSelectors"
import {
  customerIdSelector,
  hasCurrentUserDPAContractSelector,
} from "selectors/customerSelectors"

import { BasNextSubscription } from "./BasNextSubscription"

const {
  setBasPlan,
  getBasPlan,
  cancelBasNextPlan,
  remindBasLater,
  ignoreBasAlert,
  getBasCustomerStatistic,
} = basCustomerPlanActions

const { getCurrentCustomer } = customerActions
const { getBasPaymentPlans } = basPaymentsActions

const { displayModal } = amazonCustomerAccountsActions
const { toggleModal } = moduleSetupWizardActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccounts: { modalName, modals, amazonCustomerAccounts },
    basPayments: { dateRange, paymentMatrix, paymentPeriods },
    translations: { locale },
    basCustomerPlan: {
      customerStatistic: { ordersLastMonth },
    },
  } = state

  const {
    isAccountSettings = false,
    isSetupBas = false,
    isConnectInAccount,
    connectAccountId,
    hideRemindLaterButton,
    showRemindButtons,
    isRedirect = false,
    isNeedSkipSelectAccountToNewSubscriptionStep,
    amazonCustomerAccountIds = [],
  } = modals[modalName]

  return {
    dateRange,
    paymentMatrix,
    paymentPeriods,
    language: locale,
    hasUserDPAContract: hasCurrentUserDPAContractSelector(state),
    currentBasPaymentPeriod: currentBasPaymentPeriod(state),
    nextBasPaymentPeriod: nextBasPaymentPeriod(state),
    isAutoRenewActive: isAutoRenewActive(state),
    isBasActive: isBasActive(state),
    nextBasPaymentPlan: nextBasPaymentPlan(state),
    isAccountSettings,
    isSetupBas,
    countOfAmazonAccounts: amazonCustomerAccounts.filter(
      ({ deleted }) => !deleted
    ).length
      ? 1
      : 0,
    isConnectInAccount,
    connectAccountId,
    desiredPeriod: basCustomerStatisticCurrentSelector(state),
    ordersLastMonth,
    hideRemindLaterButton,
    showRemindButtons,
    isRedirect,
    user_id: staffUserIdSelector(state),
    customer_id: customerIdSelector(state),
    isNeedSkipSelectAccountToNewSubscriptionStep,
    amazonCustomerAccountIds,
  }
}

export const BasNextSubscriptionRedux = connect(mapStateToProps, {
  displayModal,
  setBasPlan,
  getBasPaymentPlans,
  getBasPlan,
  cancelBasNextPlan,
  remindBasLater,
  ignoreBasAlert,
  changeStep: toggleModal,
  getBasCustomerStatistic,
  getCurrentCustomer,
})(BasNextSubscription)
