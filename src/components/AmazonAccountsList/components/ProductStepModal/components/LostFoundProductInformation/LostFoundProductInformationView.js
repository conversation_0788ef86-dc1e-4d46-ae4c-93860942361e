import React from "react"
import PropTypes from "prop-types"

import { Box, Typography, Icon, Button, Alert } from "@develop/fe-library"

import FormattedMessage from "components/FormattedMessage"

import { pushEvent } from "utils/gtmLoader"
import l from "utils/intl"
import { replaceLanguageCode } from "utils/replaceLanguageCode"

import { EVENTS_NAMES } from "consts/gtm"
import { PRODUCTS } from "consts/product"

import { features } from "./constants"

import styles from "./lostFoundProductInformation.module.scss"

const LostFoundProductInformationView = ({
  getAccounts,
  getCurrentCustomer,
  canManage,
  onClose,
  setUseLost,
  lostRate,
  lostFoundProductInformation: {
    isOnlySignInContract = false,
    accountId,
    enable,
    countOfAmazonAccounts,
    isAccountSettings,
    connectAccountId,
    fullServiceSellerId = undefined,
    accounts,
    isFullServiceOnlySetup = null,
    mwsAuthorization,
    connectOnlyFullServiceType,
    nextConnectingAccounts = [],
  },
  showModuleSetupWizard,
  hasPaymentData,
  language,
  activateFullService,
  displayAmazonTokenRenewModal,
  signLostContract,
}) => {
  const wizardAccountNextStep =
    countOfAmazonAccounts > 0
      ? "connectChoseAmazonAccount"
      : "connectChoseRegion"

  const wizardNextStep = !hasPaymentData
    ? "billingInformation"
    : wizardAccountNextStep

  const closeHandler = () => {
    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: "click-cancel",
      virtualEventCategory: "modalWizardContractLostAndFound",
      event: EVENTS_NAMES.updEvents,
    })

    onClose(false, undefined, {})
  }

  const goToNextStep = () => {
    if (isOnlySignInContract) {
      signLostContract(undefined, () => {
        activateFullService({
          successCallback: () => {
            getAccounts(false)
            getCurrentCustomer(false, undefined)
          },
        })
      })

      onClose(false, undefined, {})

      return
    }

    if (isFullServiceOnlySetup) {
      signLostContract(undefined, () => {
        activateFullService({
          successCallback: () => {
            getCurrentCustomer(false, () => {
              getAccounts(false)
              if (!mwsAuthorization) {
                displayAmazonTokenRenewModal({
                  visible: true,
                  accounts: accounts,
                  closable: true,
                  maskClosable: false,
                  isFullServiceOnlySetup,
                  connectOnlyFullServiceType,
                  nextConnectingAccounts,
                })
              } else {
                showModuleSetupWizard(
                  true,
                  "createFullServiceAccount",
                  PRODUCTS.lost,
                  {
                    fullServiceSellerId,
                    connectOnlyFullServiceType,
                    nextConnectingAccounts,
                  }
                )
              }
            })
          },
        })
      })

      onClose(false, undefined, {})

      return
    }

    if (enable) {
      pushEvent({
        vHitNonInteraction: false,
        virtualEventAction: "click-find-reimbursements-now",
        virtualEventCategory: "modalWizardContractLostAndFound",
        event: EVENTS_NAMES.updEvents,
      })

      signLostContract(undefined, () => {
        activateFullService({
          successCallback: () => {
            getCurrentCustomer(false, () => {
              showModuleSetupWizard(true, wizardNextStep, "lost-and-found", {
                selectedRegion: null,
                selectedMarketplaces: null,
                isAccountSettings,
                connectAccountId,
              })
            })
          },
        })
      })
    } else {
      signLostContract(undefined, () => {
        activateFullService({
          successCallback: () => {
            setUseLost(accountId, { value: 1 }, () => {
              getAccounts(false)
              getCurrentCustomer(false, undefined)
            })
          },
        })
      })
    }

    onClose(false, undefined, {})
  }

  const feesLink = replaceLanguageCode({
    url: "https://support.sellerlogic.com/{localeCode}/cases/cases",
    language,
  })

  return (
    <>
      <Box
        flexDirection="column"
        margin="m"
        mLG={{
          margin: "l",
        }}
      >
        <Box
          marginBottom="m"
          mLG={{
            marginBottom: "l",
          }}
        >
          <Typography variant="--font-body-text-4" color="--color-text-second">
            <FormattedMessage id="Lost & Found Full-Service audits your FBA reports 24/7 to claim back what's yours." />
          </Typography>
        </Box>

        <ul className={styles.features}>
          {features.map((feature) => (
            <Box
              component="li"
              marginBottom="m"
              align="center"
              className={styles.feature}
            >
              <Icon
                name="icnCheck"
                color="--color-icon-done"
                size="--icon-size-6"
              />
              <Box marginLeft="m">
                <Typography variant="--font-body-text-2">
                  <FormattedMessage id={feature} />
                </Typography>
              </Box>
            </Box>
          ))}
        </ul>
        <Box
          flexDirection="column"
          backgroundColor="--color-alert-background-done"
          padding="l"
          marginBottom="m"
        >
          <Box marginBottom="m">
            <Typography
              mLG={{
                variant: "--font-headline-2",
              }}
              variant="--font-headline-5"
            >
              <FormattedMessage
                id="Only {lostRate}% of the recovered reimbursement"
                defaultMessage="Only {lostRate}% of the recovered reimbursement"
                values={{ lostRate: lostRate }}
              />
            </Typography>
          </Box>
          <Typography variant="--font-body-text-2">
            <FormattedMessage id="Only a professional solution can detect FBA problems skillfully*" />
          </Typography>
        </Box>
        <Box>
          <Box
            marginRight="m"
            mLG={{
              marginRight: "0",
            }}
          >
            <Typography
              variant="--font-body-text-4"
              color="--color-text-second"
            >
              *
            </Typography>
          </Box>
          <Box flexDirection="column">
            <Typography
              variant="--font-body-text-4"
              color="--color-text-second"
            >
              <FormattedMessage
                id="No extra, hidden, or monthly charges. Pay upon successful reimbursement. More information about <a>Fees</a> can be found in our Support Center."
                defaultMessage="No extra, hidden, or monthly charges. Pay upon successful reimbursement. More information about <a>Fees</a> can be found in our Support Center."
                values={{
                  a: (...chunks) => (
                    <a
                      href={feesLink}
                      target="_blank"
                      rel="noreferrer noopener"
                    >
                      {chunks}
                    </a>
                  ),
                }}
              />
              <br />
              <br />
              <FormattedMessage id="Unless otherwise stated, our prices are exclusive of applicable VAT." />
            </Typography>
          </Box>
        </Box>
        {!canManage ? (
          <Box marginTop="m">
            <Alert
              alertType="error"
              description={l(
                "The account administrator in the company can activate Lost & Found from your SellerLogic customer account. Please contact him directly."
              )}
            />
          </Box>
        ) : null}
      </Box>
      <Box
        padding="m"
        hasBorder={{ top: true }}
        borderTopColor="--color-border-main"
        flexDirection="column"
        className="wizard-footer"
        mLG={{
          justify: "flex-end",
          display: "flex",
          flexDirection: "row",
          padding: "m l",
        }}
      >
        <Button onClick={closeHandler} variant="secondary">
          {l("Cancel")}
        </Button>
        {canManage ? (
          <Box
            marginTop="m"
            mLG={{
              marginLeft: "m",
              marginTop: "0",
            }}
          >
            <Button onClick={goToNextStep} fullWidth>
              {l("Setup to Full-Service")}
            </Button>
          </Box>
        ) : null}
      </Box>
    </>
  )
}

LostFoundProductInformationView.propTypes = {
  canManage: PropTypes.bool,
  getAccounts: PropTypes.func.isRequired,
  getCurrentCustomer: PropTypes.func.isRequired,
  language: PropTypes.string.isRequired,
  lostFoundProductInformation: PropTypes.object.isRequired,
  lostRate: PropTypes.number.isRequired,
  onClose: PropTypes.func.isRequired,
  setUseLost: PropTypes.func.isRequired,
  signLostContract: PropTypes.func.isRequired,
  showModuleSetupWizard: PropTypes.func.isRequired,
}

export default LostFoundProductInformationView
