import { connect } from "react-redux"

import LostFoundProductInformationView from "./LostFoundProductInformationView"
import { permissionsSelector } from "selectors/userSelectors"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import customerActions from "actions/customerActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"
import fullServiceAction from "actions/fullServiceActions"

const { getCurrentCustomer, signLostContract } = customerActions
const { toggleModal: showModuleSetupWizard } = moduleSetupWizardActions
const { activateFullService } = fullServiceAction

const {
  displayModal,
  getAll: getAccounts,
  setUseLost,
  displayAmazonTokenRenewModal,
} = amazonCustomerAccountsActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccounts: {
      modals: { lostFoundProductInformation },
    },
    translations: { locale: language },
    customer: {
      customer: { hasPaymentData },
    },
  } = state
  const { amazonCustomerAccountManage } = permissionsSelector(state)

  return {
    canManage: amazonCustomerAccountManage,
    lostFoundProductInformation,
    language,
    lostRate: state?.customer?.customer?.lost_rate_full_service || "",
    hasPaymentData,
  }
}

export default connect(mapStateToProps, {
  getAccounts,
  getCurrentCustomer,
  onClose: displayModal,
  setUseLost,
  signLostContract,
  showModuleSetupWizard,
  activateFullService,
  displayAmazonTokenRenewModal,
})(LostFoundProductInformationView)
