import React, { Component } from "react"
import { withRouter } from "react-router-dom"
import { SubscriptionSettingHeader } from "./components/SubscriptionSettingHeader"
import { SubscriptionBasSettingView } from "./components/SubscriptionBasSettingView"

class SubscriptionBasSettingContainer extends Component {
  render() {
    const { subscriptions } = this.props

    const isIncludeInNextBasSubscription = subscriptions.some(
      (item) =>
        item.amazonCustomerAccount.include_in_next_bas_subscription === 1
    )

    return (
      <div>
        <SubscriptionSettingHeader
          {...this.props}
          isIncludeInNextBasSubscription={isIncludeInNextBasSubscription}
        />
        <SubscriptionBasSettingView
          {...this.props}
          isIncludeInNextBasSubscription={isIncludeInNextBasSubscription}
        />
      </div>
    )
  }
}

export default withRouter(SubscriptionBasSettingContainer)
