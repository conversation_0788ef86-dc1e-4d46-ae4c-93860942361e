import { connect } from "react-redux"
import SubscriptionBasSettingContainer from "./SubscriptionBasSettingContainer"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import {
  nextBasPaymentPlan,
  nextBasPaymentPeriod,
  isAutoRenewActive,
  activeSubscriptionId,
  oldSubscriptionId,
  dateBasStart,
  dateBasFinish,
  currentBasPaymentPeriod,
  basDateFinish,
  isBasSubscriptionError,
} from "selectors/basCustomerPlanSelector"
import { permissionsSelector } from "selectors/userSelectors"
import { VIEW_MODE } from "consts/viewMode"

const { displayModal, setIncludeNextBasSubscription } =
  amazonCustomerAccountsActions
const { getBasPlan, getBasSubscriptions, setAutoRenew, cancelBasNextPlan } =
  basCustomerPlanActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccountList,
    amazonCustomerAccountManage,
    rootManager,
    sellerLogicManager,
    amazonCustomerAccountEditSubscriptionDuration,
  } = permissionsSelector(state)

  const {
    amazonCustomerAccounts: { modalName, modals },
    basCustomerPlan: { subscriptions },
    basPayments: { paymentPeriods },
    translations: { locale: language },
  } = state

  return {
    ...modals[modalName],
    activeSubscriptionId: activeSubscriptionId(state),
    oldSubscriptionId: oldSubscriptionId(state),
    subscriptions,
    nextBasPaymentPlan: nextBasPaymentPlan(state),
    nextBasPaymentPeriod: nextBasPaymentPeriod(state),
    isAutoRenewActive: isAutoRenewActive(state),
    basDateFinish: basDateFinish(state),
    paymentPeriods,
    dateBasStart: dateBasStart(state),
    dateBasFinish: dateBasFinish(state),
    currentBasPaymentPeriod: currentBasPaymentPeriod(state),
    language,
    canManage: amazonCustomerAccountManage,
    canManageRoot: rootManager && state.viewMode.mode === VIEW_MODE.admin,
    canManageSL: sellerLogicManager && state.viewMode.mode === VIEW_MODE.admin,
    canView: amazonCustomerAccountList,
    canManageDuration: amazonCustomerAccountEditSubscriptionDuration,
    isBasSubscriptionError: isBasSubscriptionError(state),
  }
}

export default connect(mapStateToProps, {
  displayModal,
  getBasSubscriptions,
  setIncludeNextBasSubscription,
  setAutoRenew,
  getBasPlan,
  cancelBasNextPlan,
})(SubscriptionBasSettingContainer)
