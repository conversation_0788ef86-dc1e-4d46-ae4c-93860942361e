import React, { useCallback } from "react"
import { Box, IconPopover, Typography } from "@develop/fe-library"

import { RestrictedSwitch } from "components/Restrict"

import { setConfirm } from "utils/confirm"
import l from "utils/intl"

import {
  permissionKeys,
  restrictPopoverMessages,
  productConstants,
} from "consts"

const { PRODUCT_NAMES } = productConstants

export const SubscriptionBasSettingView = ({
  displayModal,
  subscriptions,
  getBasSubscriptions,
  setIncludeNextBasSubscription,
  onSetActiveTab,
  setAutoRenew,
  getBasPlan,
  currentBasPaymentPeriod,
  isBasSubscriptionError,
  cancelBasNextPlan,
  nextBasPaymentPlan,
}) => {
  let totalOrders = 0

  const includeBasSubscriptionItems = subscriptions.filter(
    (item) => item.amazonCustomerAccount.include_in_next_bas_subscription === 1
  )
  const changeIncludeSubscription = useCallback(
    ({ state, id, includeInNextBasSubscription }) => {
      includeBasSubscriptionItems.length === 1 && !state
        ? setConfirm({
            title: `${l("Turning subscription off for")} ${PRODUCT_NAMES.bas}`,
            message: l(
              "You are disconnecting the last account from the active subscription. Auto-renew and the next subscription selection won't be available. If any account will be added or switched back payment auto-renew will be restored."
            ),
            onOk: () => {
              setIncludeNextBasSubscription(id, { value: 0 }, () => {
                getBasSubscriptions()
                nextBasPaymentPlan &&
                  cancelBasNextPlan("", () => {
                    getBasPlan({ cache: false, successCallback: null })
                  })
                if (currentBasPaymentPeriod !== 10) {
                  setAutoRenew({ value: 0 }, () => {
                    getBasPlan({ cache: false, successCallback: null })
                  })
                }
              })
            },
          })
        : setIncludeNextBasSubscription(
            id,
            { value: includeInNextBasSubscription ? 0 : 1 },
            () => {
              getBasSubscriptions()
              if (
                includeBasSubscriptionItems.length === 0 &&
                currentBasPaymentPeriod !== 10
              ) {
                setAutoRenew({ value: 1 }, () => {
                  getBasPlan({ cache: false, successCallback: null })
                })
              }
            }
          )
    },
    [
      currentBasPaymentPeriod,
      getBasSubscriptions,
      getBasPlan,
      includeBasSubscriptionItems.length,
      setAutoRenew,
      setIncludeNextBasSubscription,
      cancelBasNextPlan,
      nextBasPaymentPlan,
    ]
  )

  return (
    <Box flexDirection="column">
      {subscriptions?.map(({ amazonCustomerAccount, ordersCount }) => {
        const title = amazonCustomerAccount.customerAccount.title
        const includeInNextBasSubscription =
          amazonCustomerAccount.include_in_next_bas_subscription
        const id = amazonCustomerAccount.id

        totalOrders += +ordersCount

        const handleToggleSwitch = (state) => {
          changeIncludeSubscription({
            state,
            id,
            includeInNextBasSubscription,
          })
        }

        const backgroundColor = includeInNextBasSubscription
          ? "--color-main-background"
          : "--color-background-disable"

        const mainTextColor = includeInNextBasSubscription
          ? "--color-text-main"
          : "--color-text-disable"

        const clickableTextColor = includeInNextBasSubscription
          ? "--color-text-link"
          : "--color-text-disable"

        const secondaryTextColor = includeInNextBasSubscription
          ? "--color-text-second"
          : "--color-text-disable"

        const handleTitleClick = () => {
          displayModal(false, undefined)
          onSetActiveTab("4", id)
        }

        return (
          <Typography
            component="div"
            variant="--font-body-text-9"
            color={mainTextColor}
          >
            <Box
              flexDirection="column"
              gap="s"
              padding="m"
              backgroundColor={backgroundColor}
            >
              <Box cursor="pointer">
                <Typography
                  variant="--font-body-text-9"
                  color={clickableTextColor}
                  onClick={handleTitleClick}
                >
                  {title}
                </Typography>
              </Box>

              <Box gap="m" justify="space-between" align="center">
                <Box gap="s">
                  <span>{l("Orders")}</span>

                  <IconPopover
                    content={l("Orders during current billing period")}
                    placement="bottomLeft"
                    maxWidth={200}
                    name="icnInfoCircle"
                    size="--icon-size-3"
                    color="--color-icon-active"
                  />
                </Box>

                <Typography
                  variant="--font-body-text-9"
                  color={secondaryTextColor}
                >
                  {ordersCount}
                </Typography>
              </Box>
            </Box>

            <Box
              backgroundColor="--color-background-second"
              hasBorder={{ bottom: true }}
              align="center"
              justify="space-between"
              mSM={{
                padding: "m",
                gap: "m",
              }}
            >
              <Box mSM={{ gap: "s" }}>
                <Typography
                  variant="--font-body-text-9"
                  color={secondaryTextColor}
                >
                  {l("Include in next subscription")}
                </Typography>

                <IconPopover
                  content={l(
                    `When “Off”, an account won't be included in the next subscription and will be disconnected from “Business Analytics”. Account cannot be excluded from the current subscription period.`
                  )}
                  maxWidth={200}
                  placement="bottomLeft"
                  name="icnInfoCircle"
                  size="--icon-size-3"
                  color="--color-icon-active"
                />
              </Box>

              <RestrictedSwitch
                managePermission={permissionKeys.amazonCustomerAccountManage}
                popoverMessage={restrictPopoverMessages.alter}
                isChecked={includeInNextBasSubscription}
                onChange={handleToggleSwitch}
                disabled={isBasSubscriptionError}
              />
            </Box>
          </Typography>
        )
      })}
      <Box
        align="center"
        justify="space-between"
        mSM={{
          padding: "m",
          gap: "m",
        }}
      >
        <Typography variant="--font-body-text-8">
          {l("Total orders")}
        </Typography>

        <span>{!Number.isNaN(totalOrders) ? totalOrders : `N/A`}</span>
      </Box>
    </Box>
  )
}
