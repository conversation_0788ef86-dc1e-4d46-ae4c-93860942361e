import React, { useMemo } from "react"
import { Box, Typography } from "@develop/fe-library"

import ProductContextMenu from "components/AmazonAccountsList/components/ProductContextMenu"
import {
  Restrict,
  RestrictedIconPopover,
  RestrictedSwitch,
} from "components/Restrict"

import { basicRestrictionAndSellerLogicUser } from "utils/customRestrictions"
import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"
import { PRODUCTS } from "consts/product"

const INFO_ICON_PROPS = {
  size: "--icon-size-3",
  color: "--color-icon-active",
  name: "icnInfoCircle",
}

export const SubscriptionSettingHeader = ({
  activeSubscriptionId,
  oldSubscriptionId,
  period,
  datePeriod,
  displayModal,
  deleted,
  nextBasPaymentPlan,
  nextBasPaymentPeriod,
  paymentPeriods,
  getBasPlan,
  setAutoRenew,
  isAutoRenewActive,
  dateBasStart,
  dateBasFinish,
  isIncludeInNextBasSubscription,
  currentBasPaymentPeriod,
  basDateFinish,
}) => {
  const nextPeriod = paymentPeriods.filter(
    (item) => item.id === nextBasPaymentPeriod
  )?.[0]?.title
  const buildOpenChangeDuration = (isDisabled) => () => {
    if (isDisabled) {
      return
    }
    displayModal(true, "changeDuration", {
      product: PRODUCTS.bas,
      initialValues: {
        date_start: dateBasStart,
        date_finish: dateBasFinish,
      },
    })
  }

  const buildHandleSetSubscription = (isDisabled) => () => {
    if (isDisabled) {
      return
    }
    displayModal(true, "basNextSubscription", {
      isSetupBas: false,
      isNoSeeChanges: true,
      hideRemindLaterButton: true,
      showRemindButtons: false,
      isNeedSkipSelectAccountToNewSubscriptionStep: true,
      amazonCustomerAccountIds: [],
    })
  }

  const isAutoRenewalNotAvailable =
    currentBasPaymentPeriod !== 10 &&
    isIncludeInNextBasSubscription &&
    nextBasPaymentPlan

  const isAutoRenewalAvailable =
    currentBasPaymentPeriod !== 10 &&
    isIncludeInNextBasSubscription &&
    !nextBasPaymentPlan

  const subscriptionStatusText = useMemo(() => {
    const isSubscriptionExpired =
      currentBasPaymentPeriod === 10 && basDateFinish
    const isSubscriptionInTrial =
      currentBasPaymentPeriod === 10 && !basDateFinish

    let result = null

    if (isSubscriptionExpired) {
      return l("Your subscription expired.")
    }
    if (isSubscriptionInTrial) {
      return l("Your subscription is in trial period.")
    }

    return result
  }, [currentBasPaymentPeriod, basDateFinish])

  const handleAutoRenew = () => {
    if (!isAutoRenewalAvailable) {
      return
    }
    setAutoRenew({ value: isAutoRenewActive ? 0 : 1 }, () => {
      getBasPlan({ cache: false, successCallback: null })
    })
  }

  return (
    <Box
      backgroundColor="--color-alert-background-warning"
      display="grid"
      hasBorder={{ bottom: true }}
      mSM={{
        gridTemplateColumns: "1fr",
        padding: "m",
        gap: "m",
      }}
      mXL={{
        gridTemplateColumns: "1fr 1fr",
        padding: "l",
        gap: "l",
      }}
    >
      <Box flexDirection="column" mSM={{ gap: "s" }} mXL={{ gap: "m" }}>
        <Box gap="m">
          <Typography variant="--font-body-text-5">
            {l("Current subscription")}
          </Typography>

          <RestrictedIconPopover
            customViewRestriction={basicRestrictionAndSellerLogicUser}
            popoverMessage={restrictPopoverMessages.view}
            viewPermission={permissionKeys.sellerLogicManager}
            content={`${l("Subscription ID:")} ${
              activeSubscriptionId ? activeSubscriptionId : oldSubscriptionId
            }`}
            {...INFO_ICON_PROPS}
          />
        </Box>

        <Restrict
          popoverMessage={restrictPopoverMessages.alter}
          managePermission={
            permissionKeys.amazonCustomerAccountEditSubscriptionDuration
          }
        >
          {(isDisabled) => {
            return (
              <Box
                cursor="pointer"
                mSM={{ display: "block" }}
                mXL={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "s",
                }}
                onClick={buildOpenChangeDuration(isDisabled)}
              >
                <Typography
                  variant="--font-body-text-5"
                  color={
                    isDisabled ? "--color-text-disable" : "--color-text-link"
                  }
                >
                  {period}
                </Typography>

                <Typography
                  variant="--font-body-text-7"
                  color={
                    isDisabled ? "--color-text-disable" : "--color-text-link"
                  }
                >
                  {datePeriod}
                </Typography>
              </Box>
            )
          }}
        </Restrict>

        {isAutoRenewalNotAvailable ? (
          <Box>
            <Typography variant="--font-body-text-9">
              {l("Next plan is set, auto renewal is not available")}
            </Typography>

            <RestrictedIconPopover
              managePermission={permissionKeys.amazonCustomerAccountManage}
              popoverMessage={restrictPopoverMessages.view}
              content={l(
                `Your subscription will be automatically renewed after the expiration date unless an extension is selected or an automatic renewal is disabled.`
              )}
              {...INFO_ICON_PROPS}
              maxWidth={200}
            />
          </Box>
        ) : null}

        {isAutoRenewalAvailable ? (
          <Box align="center" gap="m">
            <RestrictedSwitch
              disabled={nextBasPaymentPlan}
              isChecked={isAutoRenewActive}
              managePermission={permissionKeys.amazonCustomerAccountManage}
              popoverMessage={restrictPopoverMessages.alter}
              onChange={handleAutoRenew}
            />

            <Typography variant="--font-body-text-9">
              {l("Auto-renew")}
            </Typography>

            <RestrictedIconPopover
              managePermission={permissionKeys.amazonCustomerAccountManage}
              popoverMessage={restrictPopoverMessages.view}
              content={l(
                `Your subscription will be automatically renewed after the expiration date unless an extension is selected or an automatic renewal is disabled.`
              )}
              {...INFO_ICON_PROPS}
              maxWidth={200}
            />
          </Box>
        ) : null}

        {subscriptionStatusText ? (
          <Typography variant="--font-body-text-5">
            {subscriptionStatusText}
          </Typography>
        ) : null}
      </Box>

      <Box gap="m" justify="space-between">
        <Box flexDirection="column" mSM={{ gap: "s" }} mXL={{ gap: "m" }}>
          <Typography variant="--font-body-text-5">
            {l("Next subscription")}
          </Typography>

          <Restrict
            managePermission={permissionKeys.amazonCustomerAccountManage}
            popoverMessage={restrictPopoverMessages.alter}
          >
            {(isDisabled) => {
              return (
                <Box
                  mSM={{ display: "block" }}
                  mXL={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "s",
                  }}
                >
                  {isIncludeInNextBasSubscription ? (
                    <Box cursor="pointer" display="block">
                      <Typography
                        variant="--font-body-text-5"
                        color={
                          isDisabled
                            ? "--color-text-disable"
                            : "--color-text-link"
                        }
                        onClick={buildHandleSetSubscription(isDisabled)}
                      >
                        {nextBasPaymentPlan
                          ? l("Change subscription")
                          : l("Set up subscription")}
                      </Typography>
                    </Box>
                  ) : null}

                  <Typography variant="--font-body-text-7">
                    {!nextBasPaymentPlan
                      ? isAutoRenewActive
                        ? l("Auto renewal is enabled")
                        : l("No subscription")
                      : l(nextPeriod)}
                  </Typography>
                </Box>
              )
            }}
          </Restrict>
        </Box>

        {!deleted ? (
          <ProductContextMenu isRevertSubscriptionDisabled product="bas" />
        ) : null}
      </Box>
    </Box>
  )
}
