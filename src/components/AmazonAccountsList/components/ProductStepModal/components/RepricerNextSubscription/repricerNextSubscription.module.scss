@import "assets/styles/variables.scss";

.container {
  padding-top: 6px;
}

.marketplace {
  align-items: center;
  display: flex;
  margin-bottom: 10px;
  div {
    pointer-events: none;
  }
}

.marketplaceTitle.marketplaceTitle {
  color: $text_second;
}

.imgIcon {
  margin-right: 10px;
}

.devider {
  background: #e7e7e7;
  height: 1px;
  left: -20px;
  position: relative;
  width: calc(100% + 40px);
}

.productsNumber.productsNumber {
  margin: 20px 0 10px;
}

.productsNumberBas {
  margin: 20px 0 10px;
  white-space: nowrap;
}

.plansContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.plan {
  align-items: center;
  border: 1px solid $border_main;
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  padding: 20px;
  width: 200px;
}

.discountContainer {
  align-items: center;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.duration.duration {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 20px;
}

.discount.discount {
  background-color: #00a646;
  border-radius: 41px;
  color: $white_text;
  font-weight: 500;
  min-width: 120px;
  width: 100%;
  padding: 10px 0;
  text-align: center;

  &.discountHidden {
    visibility: hidden;
  }
}

.signupContainer {
  align-items: center;
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  width: 100%;
}

.priceContainer {
  margin-bottom: 23px;
}

.price.price {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 5px;
}

.monthLabel.monthLabel {
  color: $text_second;
  font-weight: 500;
  text-align: center;
}

.button {
  color: $text_link;
  width: 100%;
}

.footerButton {
  padding: 6px 20px;
}

.disclaimer.disclaimer {
  color: $text_second;
  margin: 10px 0 10px;
  text-align: left;
  white-space: nowrap;
}

.checkbox {
  margin-right: 10px;
}

.footerButton {
  &.remindButton {
    color: #000000;

    &:first-child {
      margin-right: 10px;
    }
  }
}

.note.note {
  color: #666666;
}

.buttonHidden {
  visibility: hidden;
}

.terms {
  position: relative;
}

.errorTermsLabel {
  bottom: -15px;
  color: $error_text;
  left: -10px;
  position: absolute;
}

.footerWithbuttonContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.buttonContainer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  width: 100%;
}

@media (max-width: 940px) {
  .discount.discount {
    width: 160px;
  }

  .plan {
    width: calc(50% - 10px);
    &:nth-child(1),
    &:nth-child(2) {
      margin-bottom: 20px;
    }
  }

  .footerButton {
    &.remindButton {
      width: 100%;
      padding: 6px 0;
    }
  }

  .footerWithbuttonContainer {
    flex-direction: column;
    align-items: flex-start;
    padding: 0 0 20px;
  }

  .footerButtonsBas {
    width: 100%;
    margin: 10px 0 0;
  }

  .footerButtons {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    width: 100%;
  }

  .footerButtonColor {
    width: 100%;
  }

  .buttonContainer {
    margin: 0;
  }
}

@media (max-width: $md) {
  .productsNumber.productsNumber {
    margin: 10px 0 10px;
  }

  .productsNumberBas {
    margin: 7px 0 0;
  }

  .disclaimer.disclaimer {
    margin: 20px 0 10px;
  }

  .disclaimerBas {
    margin: 10px 0 0 0;
  }
}

@media (max-width: 576px) {
  .devider {
    left: -10px;
    width: calc(100% + 20px);
  }
}

@media (max-width: 480px) {
  .disclaimer.disclaimer {
    white-space: normal;
  }

  .productsNumberBas {
    margin: 10px 0 10px;
  }

  .monthLabel.monthLabel {
    text-align: start;
  }
  .plan {
    margin-bottom: 10px;
    padding: 10px;
    width: 100%;

    &:nth-child(1),
    &:nth-child(2) {
      margin-bottom: 10px;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .footerWithbuttonContainer {
    align-items: center;
    padding: 0 0 10px;
  }

  .discountContainer {
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;
    width: 100%;
  }

  .duration.duration {
    margin-bottom: 0;
  }

  .signupContainer {
    align-items: center;
    display: flex;
    position: relative;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 0;
    width: 100%;
  }

  .priceContainer {
    position: absolute;
    top: -10px;
    left: 0;
    margin-bottom: 0;
  }

  .price.price {
    font-size: 28px;
    margin-bottom: 0;
  }

  .discount.discount {
    width: 110px;
  }

  .button {
    height: 32px;
    width: 110px;
  }

  .disclaimer.disclaimer {
    margin: 10px 0 10px;
    text-align: center;
  }

  .footerButtons {
    margin-top: 0;
  }

  .termsLabel.termsLabel {
    font-size: 12px;
  }

  .terms {
    align-items: center;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .note.note {
    text-align: center;
  }

  .termsLink {
    margin-left: 3px;
  }

  .errorTermsLabel {
    text-align: center;
    width: 100%;
  }

  .discount.discount {
    font-size: 12px;
    line-height: 18px;
    padding: 6px 0;
    min-width: 90px;
  }
}
