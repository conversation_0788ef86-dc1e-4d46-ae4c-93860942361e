import React, { Component } from "react"
import PropTypes from "prop-types"

import { checkIsArray } from "utils/arrayHelpers"
import RepricerNextSubscriptionView from "./components/RepricerNextSubscriptionView"

class RepricerNextSubscriptionContainer extends Component {
  componentDidMount() {
    const { marketplaces, getPlans } = this.props

    if (marketplaces[0]) {
      getPlans(marketplaces[0].accountMarketplaceId)
    }
  }

  componentWillUnmount() {
    const { clearFlexibleItems } = this.props

    clearFlexibleItems()
  }

  render() {
    const { flexibleItems, marketplaces } = this.props

    const conditionsRepricer = !checkIsArray(flexibleItems) || !marketplaces[0]

    if (conditionsRepricer) {
      return null
    }

    return <RepricerNextSubscriptionView {...this.props} {...marketplaces[0]} />
  }
}

RepricerNextSubscriptionContainer.propTypes = {
  clearFlexibleItems: PropTypes.func.isRequired,
  flexibleItems: PropTypes.array.isRequired,
  getPlans: PropTypes.func.isRequired,
  marketplaces: PropTypes.array.isRequired,
}

export default RepricerNextSubscriptionContainer
