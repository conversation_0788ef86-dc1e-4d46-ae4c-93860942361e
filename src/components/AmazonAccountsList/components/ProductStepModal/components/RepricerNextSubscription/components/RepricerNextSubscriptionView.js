import React, { useEffect } from "react"
import PropTypes from "prop-types"
import cn from "classnames"
import { Flag } from "@develop/fe-library"
import ln from "utils/localeNumber"

import Typography from "components/Typography"
import FormattedMessage from "components/FormattedMessage"
import { SubscriptionPlan } from "../../SubscriptionPlan"
import { Button } from "components/shared/Buttons"

import { pushEvent } from "utils/gtmLoader"

import { EVENTS_NAMES, GTM_SUBSCRIPTION_TYPES, PRODUCT_ORDER } from "consts/gtm"
import {
  PRODUCT_NAMES,
  PRODUCTS,
  SELECTED_REPRICER_PRODUCT,
} from "consts/product"
import { SUBSCRIPTION_TITLES } from "consts/titles"

import styles from "../repricerNextSubscription.module.scss"

const RepricerNextSubscriptionView = ({
  accountMarketplaceId,
  active,
  autoRenew,
  cancelNextPlan,
  currentPaymentModulePeriodId,
  disabled,
  extension,
  flexibleItems,
  flexibleSize: { from, to },
  getAccountMarketplaces,
  hideRemindLaterButton,
  ignoreAlert,
  language,
  marketplaceTitle,
  marketplaces,
  nextPaymentModulePeriodId,
  nextPlan,
  displayModal,
  remindLater,
  setPlan,
  showRemindButtons,
  user_id,
  customer_id,
}) => {
  const hasNotFlexibleItem = flexibleItems.length === 0

  useEffect(() => {
    if (!hasNotFlexibleItem) {
      pushEvent({ ecommerce: null })

      pushEvent({
        event: EVENTS_NAMES.subscriptionOfferView,
        customer_id,
        user_id,
        ecommerce: {
          items: [
            {
              item_name: PRODUCT_NAMES[PRODUCTS.repricer],
              item_id: PRODUCT_ORDER[PRODUCTS.repricer],
              subscription_type: currentSubscriptionCheckIn,
              marketplace: country,
              index: 0,
              quantity: 1,
            },
          ],
        },
      })
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [flexibleItems.length])

  if (hasNotFlexibleItem) {
    return null
  }

  const isExpiredAccount = !active && !nextPlan

  const hasCurrentSubscriptionWithoutPlan = active && !nextPlan

  const isCurrentSubscription =
    isExpiredAccount || hasCurrentSubscriptionWithoutPlan

  const currentSubscriptionCheckIn = isCurrentSubscription
    ? GTM_SUBSCRIPTION_TYPES.current
    : GTM_SUBSCRIPTION_TYPES.next

  const country = marketplaceTitle?.slice(-2)

  const updateMarketplaceAndCloseModalHandler = async () => {
    await getAccountMarketplaces(false)

    if (marketplaces.length > 1) {
      const [nextMarketplace] = marketplaces.slice(1)
      const currentMarketplaceOfferType = nextMarketplace.offer_type

      return displayModal(true, "repricerNextSubscription", {
        marketplaces: marketplaces.slice(1),
        product: SELECTED_REPRICER_PRODUCT[currentMarketplaceOfferType],
      })
    }

    displayModal(false, undefined, {})
  }

  const cancelSubscriptionGTMEvent = () => {
    pushEvent({ ecommerce: null })

    pushEvent({
      event: EVENTS_NAMES.subscriptionUpdate,
      customer_id,
      user_id,
      ecommerce: {
        items: [
          {
            item_name: PRODUCT_NAMES[PRODUCTS.repricer],
            item_id: PRODUCT_ORDER[PRODUCTS.repricer],
            item_variant: "Canceled",
            subscription_type: GTM_SUBSCRIPTION_TYPES.next,
            marketplace: country,
            index: 0,
            quantity: 1,
          },
        ],
      },
    })
  }

  const setNextPlanHandler =
    ({ id, subscription }) =>
    () => {
      const currentSubscriptionCheckOut = isExpiredAccount
        ? GTM_SUBSCRIPTION_TYPES.current
        : GTM_SUBSCRIPTION_TYPES.next

      pushEvent({ ecommerce: null })

      pushEvent({
        event: EVENTS_NAMES.subscriptionUpdate,
        customer_id,
        user_id,
        ecommerce: {
          items: [
            {
              item_name: PRODUCT_NAMES[PRODUCTS.repricer],
              item_id: PRODUCT_ORDER[PRODUCTS.repricer],
              item_variant: subscription,
              subscription_type: currentSubscriptionCheckOut,
              marketplace: country,
              index: 0,
              quantity: 1,
            },
          ],
        },
      })

      setPlan(
        accountMarketplaceId,
        {
          paymentModuleFlexiblePeriodId: id,
          language,
        },
        updateMarketplaceAndCloseModalHandler
      )
    }

  const remindMeLaterHandler = () => {
    remindLater(accountMarketplaceId, updateMarketplaceAndCloseModalHandler)
  }

  const ignoreAlertHandler = () => {
    ignoreAlert(accountMarketplaceId, updateMarketplaceAndCloseModalHandler)
  }

  return (
    <div className={styles.container}>
      <div className={cn(styles.marketplace, "next-subscription-marketplace")}>
        <Flag
          borderRadius="--border-radius-circle"
          size={24}
          className={styles.imgIcon}
          locale={extension}
        />

        <Typography
          className={styles.marketplaceTitle}
          type="div"
          variant="text"
        >
          {marketplaceTitle}
        </Typography>
      </div>
      <div className={styles.devider} />
      <Typography className={styles.productsNumber} type="div" variant="text">
        <FormattedMessage id="Number of products" /> {ln(from)}-{ln(to)}
      </Typography>
      <div className={styles.plansContainer}>
        {flexibleItems.map(({ value, period: { id, title } }, i) => (
          <SubscriptionPlan
            disabled={disabled}
            disabledButton={
              active &&
              ((autoRenew &&
                !nextPlan &&
                currentPaymentModulePeriodId === id) ||
                nextPaymentModulePeriodId === id)
            }
            value={value}
            id={id}
            iterator={i}
            paymentPeriods={title}
            buttonTitle="Subscribe"
            onClick={setNextPlanHandler({
              id,
              subscription: SUBSCRIPTION_TITLES[title],
            })}
          />
        ))}
      </div>
      <div className={styles.footerWithbuttonContainer}>
        <Typography
          className={styles.disclaimer}
          type="div"
          variant="textSmall"
        >
          <FormattedMessage id="Unless otherwise stated, our prices are exclusive of applicable VAT." />
        </Typography>
        <div className={styles.buttonContainer}>
          {nextPlan && !disabled && (
            <div className={styles.footerButtons}>
              <Button
                className={styles.footerButton}
                onClick={() => {
                  cancelNextPlan(accountMarketplaceId, () => {
                    cancelSubscriptionGTMEvent()
                    getAccountMarketplaces(false)
                  })
                }}
              >
                <FormattedMessage id="Cancel next subscription" />
              </Button>
            </div>
          )}
          {showRemindButtons && (
            <div className={styles.footerButtons}>
              {!hideRemindLaterButton && (
                <Button
                  className={cn(styles.footerButton, styles.remindButton)}
                  buttonSize="middle"
                  onClick={remindMeLaterHandler}
                >
                  <FormattedMessage id="Remind me later" />
                </Button>
              )}
              <Button
                className={cn(styles.footerButton, styles.remindButton)}
                buttonSize="middle"
                onClick={ignoreAlertHandler}
              >
                <FormattedMessage id="Do not renew" />
              </Button>
            </div>
          )}
        </div>
      </div>
      <Typography className={styles.note} type="div" variant="text">
        <FormattedMessage id="By selecting the subscription, you select a new subscription tarif. The new subscription will be activated after the current subscription expiration. The renewal can be changed or canceled at any time until the renewal comes into effect." />
      </Typography>
    </div>
  )
}

RepricerNextSubscriptionView.propTypes = {
  accountMarketplaceId: PropTypes.number.isRequired,
  autoRenew: PropTypes.bool.isRequired,
  cancelNextPlan: PropTypes.func.isRequired,
  currentPaymentModulePeriodId: PropTypes.number.isRequired,
  disabled: PropTypes.bool,
  extension: PropTypes.string.isRequired,
  getAccountMarketplaces: PropTypes.func.isRequired,
  hideRemindLaterButton: PropTypes.bool,
  ignoreAlert: PropTypes.func.isRequired,
  language: PropTypes.string.isRequired,
  marketplaceTitle: PropTypes.string.isRequired,
  marketplaces: PropTypes.array.isRequired,
  nextPaymentModulePeriodId: PropTypes.number,
  nextPlan: PropTypes.bool.isRequired,
  displayModal: PropTypes.func.isRequired,
  remindLater: PropTypes.func.isRequired,
  showRemindButtons: PropTypes.bool,
  setPlan: PropTypes.func.isRequired,
}

export default RepricerNextSubscriptionView
