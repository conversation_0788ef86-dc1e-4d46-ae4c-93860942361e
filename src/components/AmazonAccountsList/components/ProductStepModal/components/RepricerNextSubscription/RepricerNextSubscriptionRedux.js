import { connect } from "react-redux"

import RepricerNextSubscriptionContainer from "./RepricerNextSubscriptionContainer"
import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"
import { nextSubscriptionFormSelector } from "selectors/amazonAccountsSelectors"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"

const {
  cancelNextPlan,
  clearFlexibleItems,
  getAll: getAccountMarketplaces,
  getPlans,
  ignoreAlert,
  remindLater,
  setPlan,
} = amazonCustomerAccountMarketplaceActions

const { displayModal } = amazonCustomerAccountsActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccountMarketplace: { flexibleItems, flexibleSize },
    translations: { locale },
  } = state

  return {
    marketplaces: nextSubscriptionFormSelector(state),
    flexibleItems,
    flexibleSize,
    language: locale,
  }
}

export default connect(mapStateToProps, {
  cancelNextPlan,
  clearFlexibleItems,
  getAccountMarketplaces,
  getPlans,
  ignoreAlert,
  displayModal,
  remindLater,
  setPlan,
})(RepricerNextSubscriptionContainer)
