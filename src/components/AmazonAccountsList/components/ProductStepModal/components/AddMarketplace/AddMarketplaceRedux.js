import { connect } from "react-redux"

import AddMarketplaceFormik from "./AddMarketplaceFormik"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"
import { addMarketplaceFormSelector } from "selectors/amazonAccountsSelectors"
import { staffUserIdSelector } from "selectors/staffSelectors"
import { customerIdSelector } from "selectors/customerSelectors"

const { displayModal } = amazonCustomerAccountsActions
const { add, getAll: getAccountMarketplaces } =
  amazonCustomerAccountMarketplaceActions

const mapStateToProps = (state) => ({
  ...addMarketplaceFormSelector(state),
  language: state.translations.locale,
  user_id: staffUserIdSelector(state),
  customer_id: customerIdSelector(state),
})

export default connect(mapStateToProps, {
  getAccountMarketplaces,
  onClose: displayModal,
  onSubmit: add,
})(AddMarketplaceFormik)
