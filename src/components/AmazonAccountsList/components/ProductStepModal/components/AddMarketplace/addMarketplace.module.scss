@import "assets/styles/variables.scss";

.controlContainer {
  align-items: center;
  display: flex;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 20px;
  }
}

.label {
  width: 115px;
}

.selectField {
  flex-grow: 1;
}

.controlWrapper {
  flex-grow: 1;

  > span {
    width: 100%;
  }

  :global(.ant-select) {
    width: 100%;
  }
}

.checkboxField {
  margin-right: 10px;
}

@media (max-width: $xs) {
  .label {
    width: 100px;
  }

  .checkboxControlContainer {
    .label {
      display: none;
    }
  }
}
