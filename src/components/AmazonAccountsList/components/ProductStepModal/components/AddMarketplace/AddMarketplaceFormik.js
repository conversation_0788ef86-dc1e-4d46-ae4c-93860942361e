import React, { useCallback } from "react"
import FormikWithChangeValidation from "components/shared/FormikWithChangeValidation"
import PropTypes from "prop-types"
import { <PERSON><PERSON><PERSON>pressHandler } from "components/KeypressHandler"

import AddMarketplaceView from "./AddMarketplaceView"
import { pushEvent } from "utils/gtmLoader"

import {
  EVENTS_NAMES,
  GTM_SUBSCRIPTION_LENGTHS,
  PRODUCT_ORDER,
} from "consts/gtm"
import { PRODUCT_NAMES, PRODUCTS, REPRICER_OFFER_TYPE } from "consts/product"

const AddMarketplaceFormik = ({
  accountId,
  getAccountMarketplaces,
  language,
  marketplaceOptions,
  onClose: propOnClose,
  onSubmit,
  onFormIsChanged,
  user_id,
  customer_id,
  product,
  ...modalProps
}) => {
  const onClose = useCallback(() => {
    modalProps?.onModalClose
      ? modalProps.onModalClose()
      : propOnClose(false, undefined, {})
  }, [modalProps, propOnClose])

  return (
    <FormikWithChangeValidation
      initialValues={{}}
      onFormIsChanged={onFormIsChanged}
      onSubmit={({ amazonMarketplaceId }) => {
        const [currentMarketplace] =
          marketplaceOptions?.filter(
            (marketplace) => marketplace.value === amazonMarketplaceId
          ) || []

        const marketplace = currentMarketplace?.country

        pushEvent({ ecommerce: null })

        //NEED FIX THIS EVENT REPRICER B2b|B2c
        pushEvent({
          event: EVENTS_NAMES.purchase,
          customer_id,
          user_id,
          ecommerce: {
            items: [
              {
                item_id: PRODUCT_ORDER[PRODUCTS.repricer],
                item_name: PRODUCT_NAMES[PRODUCTS.repricer],
                subscription_length:
                  GTM_SUBSCRIPTION_LENGTHS[PRODUCTS.repricer],
                is_trial: true,
                index: 0,
                quantity: 1,
                marketplace,
              },
            ],
          },
        })

        onSubmit(
          accountId,
          {
            amazonMarketplaceId,
            language,
            offerType: REPRICER_OFFER_TYPE[product],
          },
          () => {
            getAccountMarketplaces(false)
            propOnClose(false, undefined, {})
          }
        )
      }}
    >
      {(formikProps) => (
        <>
          <FormKeypressHandler
            onSubmit={() =>
              !formikProps.isSubmitting && formikProps.submitForm()
            }
            onClose={onClose}
          />
          <AddMarketplaceView
            {...{ ...formikProps, ...modalProps }}
            language={language}
            marketplaceOptions={marketplaceOptions}
            onClose={onClose}
          />
        </>
      )}
    </FormikWithChangeValidation>
  )
}

AddMarketplaceFormik.propTypes = {
  accountId: PropTypes.string.isRequired,
  getAccountMarketplaces: PropTypes.func.isRequired,
  language: PropTypes.string.isRequired,
  marketplaceOptions: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
}

export default AddMarketplaceFormik
