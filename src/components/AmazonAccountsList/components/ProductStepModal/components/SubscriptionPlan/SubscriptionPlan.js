import React from "react"
import { But<PERSON> } from "components/shared/Buttons"
import Typography from "components/Typography"
import FormattedMessage from "components/FormattedMessage"
import cn from "classnames"
import ln from "utils/localeNumber"

import styles from "./subscriptionPlan.module.scss"

export const SubscriptionPlan = ({
  value,
  id,
  iterator,
  paymentPeriods,
  onClick,
  disabled,
  disabledButton,
  buttonTitle = "",
  classNames = "",
}) => {
  const { buttonSubscribe = "" } = classNames

  return (
    <div className={styles.plan} key={id}>
      <div className={styles.discountContainer}>
        <Typography className={styles.duration} type="div" variant="textLarge">
          <FormattedMessage id={paymentPeriods} />
        </Typography>
        <Typography
          className={cn(styles.discount, {
            [styles.discountHidden]: !iterator,
          })}
          type="div"
          variant="textSmall"
        >
          <FormattedMessage id="Discount" /> {iterator * 5}%
        </Typography>
      </div>
      <div className={styles.signupContainer}>
        <div className={styles.priceContainer}>
          <Typography className={styles.price} type="div" variant="textLarge">
            {ln(value, 2)}€
          </Typography>
          <Typography
            className={styles.monthLabel}
            type="div"
            variant="textSmall"
          >
            <FormattedMessage id="per month" />
          </Typography>
        </div>
        {!disabled && (
          <Button
            className={`${styles.button} ${buttonSubscribe}`}
            buttonSize="middle"
            disabled={disabledButton}
            onClick={onClick}
          >
            <FormattedMessage id={buttonTitle} />
          </Button>
        )}
      </div>
    </div>
  )
}
