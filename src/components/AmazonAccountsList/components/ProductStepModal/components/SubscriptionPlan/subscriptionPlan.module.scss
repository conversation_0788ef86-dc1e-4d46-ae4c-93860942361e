.plan {
  align-items: center;
  border: var(--border-main);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  padding: var(--padding-l);
  width: 200px;
}

.discountContainer {
  align-items: center;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.duration.duration {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: var(--margin-l);
}

.discount.discount {
  background-color: var(--color-badge-3);
  border-radius: 41px;
  color: var(--color-text-white);
  font-weight: 500;
  min-width: 120px;
  width: 100%;
  padding: var(--padding-m) 0;
  text-align: center;

  &.discountHidden {
    visibility: hidden;
  }
}

.signupContainer {
  align-items: center;
  display: flex;
  flex-direction: column;
  margin-top: var(--margin-l);
  width: 100%;
}

.priceContainer {
  margin-bottom: 23px;
}

.price.price {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: var(--margin-s);
}

.monthLabel.monthLabel {
  color: var(--color-text-second);
  font-weight: 500;
  text-align: center;
}

.button {
  color: var(--color-text-link);
  width: 100%;
}

@media (max-width: 940px) {
  .discount.discount {
    width: 160px;
  }

  .plan {
    width: calc(50% - 10px);
    &:nth-child(1),
    &:nth-child(2) {
      margin-bottom: var(--margin-l);
    }
  }
}

@media (max-width: 480px) {
  .monthLabel.monthLabel {
    text-align: start;
  }
  .plan {
    margin-bottom: var(--margin-m);
    padding: var(--padding-m);
    width: 100%;

    &:nth-child(1),
    &:nth-child(2) {
      margin-bottom: var(--margin-m);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .price.price {
    font-size: 28px;
    margin-bottom: 0;
  }

  .discountContainer {
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: var(--margin-m);
    width: 100%;
  }

  .duration.duration {
    margin-bottom: 0;
  }

  .signupContainer {
    align-items: center;
    display: flex;
    position: relative;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 0;
    width: 100%;
  }

  .priceContainer {
    position: absolute;
    top: -10px;
    left: 0;
    margin-bottom: 0;
  }

  .discount.discount {
    width: 110px;
    font-size: 12px;
    line-height: 18px;
    padding: 6px 0;
    min-width: 90px;
  }

  .button {
    height: 32px;
    width: 110px;
  }
}
