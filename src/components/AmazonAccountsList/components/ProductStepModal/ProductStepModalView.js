import React, { useCallback, useEffect, useState } from "react"
import cn from "classnames"
import PropTypes from "prop-types"

import Modal from "components/shared/Modal"

import { FORM_IS_CHANGED, setConfirm } from "utils/confirm"
import { pushEvent } from "utils/gtmLoader"
import l from "utils/intl"

import { wizardModalTitlesConstants } from "consts"
import { EVENTS_NAMES } from "consts/gtm"
import { PRODUCTS, SELECTED_REPRICER_PRODUCT } from "consts/product"

import { gtmModalNames } from "./constants"

import { RepricerNextSubscriptionNewPricingModal } from "./components/RepricerNextSubscriptionNewPricingModal"
import { components } from "./componentsMap"

import styles from "./productStepModalView.module.scss"

const { ADDITIONAL_EXCEPTIONS_MODAL_TITLES } = wizardModalTitlesConstants

const wizardStepGTMHandler = ({ eventAction, eventCategory }) => {
  pushEvent({
    vHitNonInteraction: false,
    virtualEventAction: eventAction,
    virtualEventCategory: eventCategory,
    event: EVENTS_NAMES.updEvents,
  })
}

const ProductStepModalView = ({
  className = "",
  flexibleItems,
  paymentMatrix,
  marketplaces,
  modalName,
  displayModal,
  title,
  visible,
  width,
  module,
  isNoSeeChanges = false,
  closable = true,
  maskClosable = true,
  useNewPricing,
  isCustomModal,
  isFooterButtonVisible = true,
}) => {
  const [formIsChanged, setFormIsChanged] = useState(false)
  const isModalNextSubscription =
    modalName === "repricerNextSubscription" ||
    modalName === "repricerNextSubscriptionNewPricing"
  const cancelEvent = "click-cancel"

  useEffect(() => {
    gtmModalNames[modalName] &&
      pushEvent({
        virtualEventAction: "show",
        virtualEventCategory: gtmModalNames[modalName],
        event: EVENTS_NAMES.updEvents,
      })
  }, [modalName])

  const onClose = useCallback(() => {
    if (useNewPricing && isModalNextSubscription) {
      displayModal(false, undefined, {})

      return
    }

    if (modalName === "nextSubscriptionRepricerNewPricing") {
      displayModal(false, undefined, {})

      return
    }

    const _onClose = () => {
      if (isModalNextSubscription && marketplaces.length > 1) {
        const [nextMarketplace] = marketplaces.slice(1)
        const currentMarketplaceOfferType = nextMarketplace.offer_type

        return displayModal(true, "repricerNextSubscription", {
          marketplaces: marketplaces.slice(1),
          product: SELECTED_REPRICER_PRODUCT[currentMarketplaceOfferType],
        })
      }
      if (modalName === "lostFoundProductInformation") {
        wizardStepGTMHandler({
          eventAction: cancelEvent,
          eventCategory: "modalWizardContractLostAndFound",
        })
      } else if (modalName === "basProductInformation") {
        wizardStepGTMHandler({
          eventAction: cancelEvent,
          eventCategory: "modalWizardProductInformationBusinessAnalytics",
        })
      }
      displayModal(false, undefined, {})
    }

    if (formIsChanged && !isNoSeeChanges) {
      setConfirm({
        template: FORM_IS_CHANGED,
        onOk: _onClose,
      })

      return
    }
    _onClose()
  }, [
    useNewPricing,
    displayModal,
    marketplaces,
    modalName,
    formIsChanged,
    isNoSeeChanges,
    isModalNextSubscription,
  ])

  const isRepricerNextSubscriptionNewPricingModalVisible =
    visible && isCustomModal

  if (isRepricerNextSubscriptionNewPricingModalVisible) {
    return (
      <RepricerNextSubscriptionNewPricingModal
        isClosable={closable}
        isFooterButtonVisible={isFooterButtonVisible}
        title={l(ADDITIONAL_EXCEPTIONS_MODAL_TITLES.repricerNextSubscription)}
        onClose={onClose}
      />
    )
  }

  const FormComponent = components[modalName]

  if (!FormComponent) {
    return null
  }

  const isVisibility =
    isModalNextSubscription &&
    (((module === PRODUCTS.repricerB2C || module === PRODUCTS.repricerB2B) &&
      flexibleItems.length === 0) ||
      (module === PRODUCTS.bas && paymentMatrix.length === 0))

  const visibilityModal = isVisibility ? "hidden" : "visible"

  return (
    visible && (
      <Modal
        key={modalName}
        visible
        className={cn(styles[modalName], styles[className])}
        closable={closable}
        footer={null}
        keyboard={isModalNextSubscription || "currentSubscription"}
        maskClosable={maskClosable}
        title={l(title)}
        width={width}
        style={{
          visibility: visibilityModal,
        }}
        onCancel={onClose}
      >
        <FormComponent
          onFormIsChanged={setFormIsChanged}
          onModalClose={onClose}
        />
      </Modal>
    )
  )
}

ProductStepModalView.propTypes = {
  className: PropTypes.string,
  flexibleItems: PropTypes.array.isRequired,
  marketplaces: PropTypes.array.isRequired,
  modalName: PropTypes.string,
  displayModal: PropTypes.func.isRequired,
  title: PropTypes.string,
  visible: PropTypes.bool.isRequired,
  width: PropTypes.number,
  closable: PropTypes.bool,
  maskClosable: PropTypes.bool,
  useNewPricing: PropTypes.bool,
  isCustomModal: PropTypes.bool,
  isFooterButtonVisible: PropTypes.bool,
}

export default ProductStepModalView
