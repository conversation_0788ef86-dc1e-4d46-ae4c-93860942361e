@import "assets/styles/variables.scss";

.nextSubscription.nextSubscription {
  min-width: 900px;
  :global(.ant-modal-header.ant-modal-header) {
    border-bottom: none;
    padding-bottom: 0;
  }
  :global(.ant-modal-body) {
    padding-top: 0;
  }
}

.subscriptionBasSettings,
.previousBasSubscription {
  :global(.ant-modal-body) {
    padding: 0 !important;
  }
}

.lostProductInfo {
  :global(.ant-modal-header.ant-modal-header.ant-modal-header.ant-modal-header) {
    display: flex;
    height: var(--modal-height-m);
    border-bottom: var(--border-main);
    padding: var(--padding-m) var(--padding-l);
    align-items: center;
  }

  :global(.ant-modal-body.ant-modal-body) {
    padding: 0;
  }

  // it`s crutch for old modal
  :global(.ant-modal-close-x.ant-modal-close-x) {
    font-size: 20px;
    line-height: 54px;
  }
}

//   Delete this after FULL SERVICE MVP
.lostProductInfoMVP {
  .title.title {
    color: $text_main;
    font-size: 16px;
  }

  :global(.ant-modal-header.ant-modal-header) {
    border-bottom: 1px solid $hover_grid;
    padding: 14px 20px;
  }

  :global(.ant-modal-body) {
    padding: 20px 30px 10px;
  }
}

@media (max-width: 940px) {
  .nextSubscription.nextSubscription {
    min-width: unset;
    max-width: 460px;
    top: 40px;
  }
}

@media (max-width: 576px) {
  .subscriptionBasSettings,
  .previousBasSubscription {
    max-width: calc(100vw - 20px);

    :global(.ant-modal-body) {
      padding: 0 !important;
    }
  }
}

@media (max-width: 480px) {
  .nextSubscription.nextSubscription {
    max-width: calc(100vw - 20px);
  }

  :global(.ant-modal-header.ant-modal-header) {
    padding-left: 10px;
  }

  :global(.ant-modal-body.ant-modal-body) {
    padding-left: 10px;
    padding-right: 10px;
  }

  .nextSubscription.nextSubscription {
    :global(.ant-modal-body.ant-modal-body.ant-modal-body) {
      padding-bottom: 20px;
    }
  }

  .lostProductInfo {
    :global(.ant-modal-body.ant-modal-body) {
      padding: 0;
    }
  }
  //   Delete this after FULL SERVICE MVP
  .lostProductInfoMVP {
    .title.title {
      color: $text_main;
      font-size: 14px;
    }

    :global(.ant-modal-body) {
      padding: 20px 10px 10px;
    }
  }

  .subscriptionBasSettings,
  .previousBasSubscription {
    :global(.ant-modal-body) {
      padding: 0 !important;
    }
  }
}
