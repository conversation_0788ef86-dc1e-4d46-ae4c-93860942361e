import { connect } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"

import { nextSubscriptionFormSelector } from "selectors/amazonAccountsSelectors"
import { customerProductsInfoSelector } from "selectors/customerSelectors"

import ProductStepModalView from "./ProductStepModalView"

const { displayModal } = amazonCustomerAccountsActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccounts: { modalName, modalVisible, modals },
    amazonCustomerAccountMarketplace: { flexibleItems },
    basPayments: { paymentMatrix },
  } = state

  const { useNewPricing } = customerProductsInfoSelector(state)

  return {
    ...modals[modalName],
    modalName,
    visible: modalVisible,
    flexibleItems,
    marketplaces: nextSubscriptionFormSelector(state),
    paymentMatrix,
    useNewPricing,
  }
}

export default connect(mapStateToProps, {
  displayModal,
})(ProductStepModalView)
