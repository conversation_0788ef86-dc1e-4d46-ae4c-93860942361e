import { useEffect, useState } from "react"
import { useForm, useWatch } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import type { Option } from "@develop/fe-library"
import {
  buildFormSubmitFailureCallback,
  getObjectEntries,
  getObjectKeys,
} from "@develop/fe-library/dist/utils"
import { yupResolver } from "@hookform/resolvers/yup"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"

import {
  timezonesInOffsetOrderSelector,
  userTimezoneIdSelector,
} from "selectors/timezoneSelectors"

import { toggleArrayValue } from "utils/arrayHelpers"
import { cronMapper, getUpdatedState } from "utils/cronHelpers"

import { DEFAULT_CRON_EXPRESSION, PAYOUT_FIELDS } from "consts/payoutSettings"

import { buildSchema, getUpdatedCronExpression } from "./utils"

import type { AmazonCustomerAccountPayoutSetting } from "types/Models"

import type {
  PayoutFormData,
  UseRequestPaymentArgs,
} from "./UseRequestPaymentTypes"

const {
  getAmazonCustomerAccountPayoutSettings,
  updateAmazonCustomerAccountPayoutSettings,
} = amazonCustomerAccountsActions

export const useRequestPayment = ({
  accountId,
  marketplaces,
  isAmazonMarketplacesLoading,
  setIsTabChangeAllowed,
}: UseRequestPaymentArgs) => {
  const [selectedHour, setSelectedHour] = useState<number>(8)
  const [selectedDays, setSelectedDays] = useState<number[]>([1])

  const dispatch = useDispatch()

  const timezoneOptions = useSelector(timezonesInOffsetOrderSelector)
  const userTimezoneId = useSelector(userTimezoneIdSelector)

  const initialValues: PayoutFormData = {
    [PAYOUT_FIELDS.isEnabled]: false,
    [PAYOUT_FIELDS.marketplaces]: [],
    [PAYOUT_FIELDS.expression]: DEFAULT_CRON_EXPRESSION,
    [PAYOUT_FIELDS.timezoneId]: userTimezoneId,
  }

  const form = useForm<PayoutFormData>({
    defaultValues: initialValues,
    mode: "onSubmit",
    resolver: yupResolver(buildSchema()),
  })

  const {
    handleSubmit,
    setValue,
    setError,
    reset,
    control,
    formState: { dirtyFields, isSubmitting, errors },
  } = form

  const [isEnabled, selectedMarketplaces, expression, timezoneId] = useWatch({
    name: [
      PAYOUT_FIELDS.isEnabled,
      PAYOUT_FIELDS.marketplaces,
      PAYOUT_FIELDS.expression,
      PAYOUT_FIELDS.timezoneId,
    ],
    control,
  })

  const marketplacesErrorMessage: string | undefined =
    errors.marketplaces?.message
  const expressionErrorMessage: string | undefined = errors.expression?.message
  const timezoneIdErrorMessage: string | undefined = errors.timezoneId?.message

  const isDirty: boolean = !!getObjectKeys(dirtyFields).length

  const isSaveButtonDisabled: boolean = !isDirty || isSubmitting

  const isAllMarketplacesSelected: boolean =
    !isAmazonMarketplacesLoading &&
    !!marketplaces.length &&
    marketplaces.every(({ value }) => selectedMarketplaces.includes(value))

  // DESC: Shows native browser alert if user tries to leave the page with unsaved changes
  useEffect(() => {
    setIsTabChangeAllowed(!isDirty)

    if (!isDirty) {
      return
    }

    const onBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault()
      e.returnValue = true
    }

    window.addEventListener("beforeunload", onBeforeUnload)

    return () => {
      window.removeEventListener("beforeunload", onBeforeUnload)
    }
  }, [isDirty])

  useEffect(() => {
    dispatch(
      getAmazonCustomerAccountPayoutSettings({
        id: accountId,
        successCallback: (data: AmazonCustomerAccountPayoutSetting) => {
          if (!data?.id) {
            return
          }

          reset({
            expression: data?.expression || initialValues.expression,
            isEnabled: !!data?.enabled,
            marketplaces: data?.marketplaces || initialValues.marketplaces,
            timezoneId: data?.timezone?.id || initialValues.timezoneId,
          })
        },
      })
    )
  }, [accountId])

  useEffect(() => {
    const { hours, days } = getUpdatedState(expression) || {}

    setSelectedHour(hours?.specificHour?.[0] || 0)
    setSelectedDays(days?.specificDay || [1])
  }, [expression])

  const handleChangeAllMarketplacesCheckbox = (): void => {
    const updatedSelectedMarketplaces: string[] = isAllMarketplacesSelected
      ? []
      : marketplaces.map(({ value }) => value)

    setValue(PAYOUT_FIELDS.marketplaces, updatedSelectedMarketplaces, {
      shouldDirty: true,
    })
  }

  const handleChangeSwitch = (isChecked: boolean): void => {
    setValue(PAYOUT_FIELDS.isEnabled, isChecked, { shouldDirty: true })
  }

  const handleClickMarketplace = (marketplace: string): void => {
    const updatedSelectedMarketplaces: string[] = toggleArrayValue(
      selectedMarketplaces,
      marketplace
    )

    setValue(PAYOUT_FIELDS.marketplaces, updatedSelectedMarketplaces, {
      shouldDirty: true,
      shouldValidate: true,
    })
  }

  const handleTimezoneChange = ({ value }: Option): void => {
    setValue(PAYOUT_FIELDS.timezoneId, +value, { shouldDirty: true })
  }

  const handleTimeChange = ({ value }: Option): void => {
    const hoursParsed = Number(value)

    setSelectedHour(hoursParsed)

    const updatedExpression: string = getUpdatedCronExpression({
      mapper: cronMapper.hours,
      expression,
      selectedValues: [hoursParsed],
    })

    setValue(PAYOUT_FIELDS.expression, updatedExpression, { shouldDirty: true })
  }

  const handleDayChange = (day: number): void => {
    setSelectedDays((prev) => {
      const updatedSelectedDays: number[] = toggleArrayValue(prev, day)

      const updatedExpression: string = getUpdatedCronExpression({
        mapper: cronMapper.days,
        expression,
        selectedValues: updatedSelectedDays,
      })

      setValue(PAYOUT_FIELDS.expression, updatedExpression, {
        shouldDirty: true,
      })

      return updatedSelectedDays
    })
  }

  const handleClickSaveButton = handleSubmit(
    ({ isEnabled, ...otherData }): void => {
      const failureCallback = buildFormSubmitFailureCallback<PayoutFormData>({
        setError,
      })

      dispatch(
        updateAmazonCustomerAccountPayoutSettings({
          id: accountId,
          payload: { ...otherData, payoutEnabled: isEnabled ? 1 : 0 },
          successCallback: (data: AmazonCustomerAccountPayoutSetting) => {
            reset({
              expression: data?.expression || otherData.expression,
              isEnabled: !!data?.enabled ?? isEnabled,
              marketplaces: data?.marketplaces || otherData.marketplaces,
              timezoneId: data?.timezone?.id || otherData.timezoneId,
            })
          },
          failureCallback,
        })
      )
    }
  )

  // DESC: Sets initial values. Do NOT reset the form!
  const handleClickResetButton = (): void => {
    getObjectEntries(initialValues).forEach(([key, value]) => {
      setValue(key, value, { shouldDirty: true })
    })
  }

  const handleClickCancelButton = (): void => {
    reset()
  }

  return {
    expression,
    expressionErrorMessage,
    isAllMarketplacesSelected,
    isCancelButtonDisabled: isSaveButtonDisabled,
    isEnabled,
    isSaveButtonDisabled,
    marketplacesErrorMessage,
    selectedDays,
    selectedMarketplaces,
    selectedHour,
    timezoneOptions,
    timezoneId,
    timezoneIdErrorMessage,
    handleClickMarketplace,
    handleChangeAllMarketplacesCheckbox,
    handleChangeSwitch,
    handleClickCancelButton,
    handleClickResetButton,
    handleClickSaveButton,
    handleDayChange,
    handleTimeChange,
    handleTimezoneChange,
  }
}
