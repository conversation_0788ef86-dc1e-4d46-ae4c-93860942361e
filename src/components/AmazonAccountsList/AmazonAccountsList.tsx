import React, { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box } from "@develop/fe-library"

import amazonAdsAccountsActions from "actions/amazonAdsAccountsActions"
import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import amazonCustomerAccountStockActions from "actions/amazonCustomerAccountStockActions"
import amazonMarketplaceActions from "actions/amazonMarketplacesActions"
import amazonSellerCentralAccountActions from "actions/amazonSellerCentralAccountActions"
import customerActions from "actions/customerActions"
import repricerSubscriptionActions from "actions/repricerSubscriptionActions"
import timezoneActions from "actions/timezoneActions"

import {
  amazonAccountItemsForEditSelector,
  amazonCustomerAccountStocksSelector,
  isScrollTabSelector,
} from "selectors/amazonAccountsSelectors"
import { isDemoAccountSelector } from "selectors/customerSelectors"
import {
  isSellerUserSelector,
  permissionsSelector,
} from "selectors/userSelectors"

import { AmazonAccount } from "./components"

import { AmazonAccountItemForEdit } from "types/AmazonAccountItemForEditTypes"

const { getRepricerCustomerPlanSubscriptions } = repricerSubscriptionActions

const {
  delete: deleteAccount,
  getAll: getAccounts,
  update: updateAccount,
  setAccountScrollTab,
} = amazonCustomerAccountsActions

const { getAll: getAmazonMarketplaces } = amazonMarketplaceActions
const {
  // @ts-expect-error
  add: addAmazonCustomerAccountStock,
  // @ts-expect-error
  delete: deleteAmazonCustomerAccountStock,
  // @ts-expect-error
  getAll: getAmazonCustomerAccountStock,
} = amazonCustomerAccountStockActions
const { getAll: getAccountMarketplaces } =
  amazonCustomerAccountMarketplaceActions
const { getCurrentCustomer } = customerActions
const { getAmazonAdsAccounts } = amazonAdsAccountsActions
const { getAllAmazonSellerCentralAccounts } = amazonSellerCentralAccountActions
// @ts-expect-error
const { getAll: getTimezones } = timezoneActions

export const AmazonAccountsList = () => {
  const dispatch = useDispatch()

  const { amazonAdsAccountList, amazonSellerCentralAccountsManage } =
    useSelector(permissionsSelector)

  const accounts: AmazonAccountItemForEdit[] = useSelector(
    amazonAccountItemsForEditSelector
  )
  const isDemoAccount: boolean = useSelector(isDemoAccountSelector)
  const isSellerUser: boolean = useSelector(isSellerUserSelector)
  const amazonCustomerAccountStocks = useSelector(
    amazonCustomerAccountStocksSelector
  )
  const isScrollTab: boolean = useSelector(isScrollTabSelector)

  useEffect(() => {
    dispatch(getAmazonCustomerAccountStock({ successCallback: undefined }))
    dispatch(getAmazonMarketplaces(undefined))
    dispatch(getAccounts(false))
    dispatch(getAccountMarketplaces(false))
    dispatch(setAccountScrollTab(true))
    dispatch(getTimezones())
    dispatch(getRepricerCustomerPlanSubscriptions({ cache: false }))
  }, [dispatch])

  const onCountryChange = (
    accountId: number,
    marketplaceId: number,
    deleteItem: boolean
  ) => {
    if (deleteItem) {
      dispatch(
        deleteAmazonCustomerAccountStock(
          amazonCustomerAccountStocks.find(
            ({ amazon_customer_account_id, amazon_marketplace_id }) =>
              amazon_customer_account_id === accountId &&
              marketplaceId === amazon_marketplace_id
          ).id,
          () => {
            dispatch(
              getAmazonCustomerAccountStock({ successCallback: undefined })
            )
          }
        )
      )

      return
    }

    dispatch(
      addAmazonCustomerAccountStock(
        {
          amazon_customer_account_id: accountId,
          amazon_marketplace_id: marketplaceId,
        },
        () => {
          dispatch(
            getAmazonCustomerAccountStock({ successCallback: undefined })
          )
        }
      )
    )
  }

  const onUpdate = (
    accountId: number,
    payload: unknown,
    successCallback: () => void,
    errorCallback: (errors: unknown) => void
  ) => {
    dispatch(
      updateAccount(
        accountId,
        payload,
        () => {
          dispatch(getAccounts(false))

          successCallback?.()
        },
        errorCallback
      )
    )
  }

  const onDelete = (accountId: number) => {
    dispatch(
      deleteAccount(accountId, () => {
        dispatch(getAccounts(false))
        dispatch(getCurrentCustomer(false, undefined))
      })
    )
  }

  useEffect(() => {
    const shouldGetAmazonSellerCentralAccounts: boolean =
      isSellerUser && !!amazonSellerCentralAccountsManage && !isDemoAccount

    if (shouldGetAmazonSellerCentralAccounts) {
      dispatch(getAllAmazonSellerCentralAccounts({ is_active: 1 }))
    }
  }, [isSellerUser, amazonSellerCentralAccountsManage, isDemoAccount, dispatch])

  useEffect(() => {
    const shouldGetAmazonAdsAccounts: boolean =
      amazonAdsAccountList && !isDemoAccount

    if (shouldGetAmazonAdsAccounts) {
      dispatch(getAmazonAdsAccounts({ is_deleted: "0" }, false, () => {}))
    }
  }, [amazonAdsAccountList, isDemoAccount, dispatch])

  return (
    <Box flexDirection="column" gap="xl">
      {accounts.map((account) => (
        <AmazonAccount
          key={account.id}
          account={account}
          isScrollTab={isScrollTab}
          onCountryChange={onCountryChange}
          onDelete={onDelete}
          onUpdate={onUpdate}
        />
      ))}
    </Box>
  )
}
