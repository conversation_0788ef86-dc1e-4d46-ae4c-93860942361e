@import "assets/styles/variables.scss";

.subTitle {
  font-size: 14px;
  font-weight: bold;
  color: var(--color-text-main);
}

.progressContainer {
  position: relative;
  background-color: var(--color-row-select);
  padding: var(--padding-l);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.progress {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  background-color: var(--color-int-on-disable);
  z-index: 5;
  transition: 0.7s;
}

.bulkList {
  margin-top: 10px;
}

.resultStatus {
  text-align: center;
}

.content {
  position: relative;
  z-index: 10;
}

.statusIcon,
.statusTitle.statusTitle,
.statusDescription.statusDescription {
  margin-bottom: 15px;
}
