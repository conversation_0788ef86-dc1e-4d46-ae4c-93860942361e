import React from "react"
import PropTypes from "prop-types"
import cn from "classnames"

import Typography from "components/Typography"

import l from "utils/intl"

import styles from "./bulkProcessingConfirmList.module.scss"

const BulkProcessingConfirmList = ({ title, list = [], className }) => {
  return (
    <>
      {!!list?.length && (
        <div className={cn(className, styles.listContainer)}>
          <Typography className={styles.listTitle} type="div" variant="text">
            {`${l(title)}:`}
          </Typography>
          <Typography className={styles.list} type="div" variant="text">
            {list.map((title, idx) => (
              <span key={idx}>
                {!!idx && ", "}
                <Typography
                  className={styles.itemTitle}
                  key={idx}
                  type="span"
                  variant="text"
                >
                  {title}
                </Typography>
              </span>
            ))}
          </Typography>
        </div>
      )}
    </>
  )
}

BulkProcessingConfirmList.propTypes = {
  title: PropTypes.string,
  className: PropTypes.string,
  list: PropTypes.array,
}

BulkProcessingConfirmList.defaultProps = {
  title: "",
  list: [],
  className: "",
}

export { BulkProcessingConfirmList }
