import React from "react"
import { Tag } from "antd"
import { Grid, Popover } from "@develop/fe-library"
import { checkIsString, getObjectKeys } from "@develop/fe-library/dist/utils"
import PropTypes from "prop-types"

import { summarizeBulkProcessingErrorItemErrors } from "utils/bulkHelper"

import styles from "./bulkProcessingErrorItem.module.scss"

const BulkProcessingErrorItem = ({ id, errors, onClick }) => {
  return (
    <Grid item className={styles.warningItem} mMD={6} mXL={4} tb={3}>
      <Popover
        placement="top"
        content={
          <div className={styles.popoverBox}>
            {checkIsString(errors)
              ? errors
              : getObjectKeys(errors).map((key, idx) => (
                  <div key={errors[key]}>{`${idx + 1}. ${errors[key]}`}</div>
                ))}
          </div>
        }
      >
        <div>
          <a onClick={onClick}>{`${id} `}</a>
          <Tag color="red">
            {summarizeBulkProcessingErrorItemErrors(errors)}
          </Tag>
        </div>
      </Popover>
    </Grid>
  )
}

BulkProcessingErrorItem.propTypes = {
  id: PropTypes.number.isRequired,
  errors: PropTypes.object.isRequired,
  onClick: PropTypes.func.isRequired,
}

export { BulkProcessingErrorItem }
