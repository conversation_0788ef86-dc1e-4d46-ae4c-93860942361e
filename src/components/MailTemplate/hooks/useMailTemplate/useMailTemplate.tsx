import { useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { getObjectEntries } from "@develop/fe-library/dist/utils"

import globalNotificationsActions from "actions/globalNotificationsActions"
import MailTemplateActions, {
  PREVIEW_TYPE_HTML,
  PREVIEW_TYPE_TEXT,
} from "actions/mailTemplateActions"

import {
  mailTemplatesConfirmationModalVisibleSelector,
  mailTemplatesInitialValuesSelector,
  mailTemplatesModalVisibleSelector,
  mailTemplatesOptionsSelector,
  mailTemplatesPreviewSelector,
  mailTemplatesSearchOptionsSelector,
  mailTemplatesSelector,
  mailTemplatesTotalCountSelector,
} from "selectors/mailTemplatesSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import l, { availableLanguages } from "utils/intl"

import { GLOBAL_NOTIFICATION_TYPES } from "consts/globalNotifications"

const { showGlobalNotification } = globalNotificationsActions
const {
  // @ts-ignore
  get: getMailTemplatesData,
  // @ts-ignore
  delete: deleteMailTemplate,
  // @ts-ignore
  getAll: getAllOptimizationTemplates,
  // @ts-ignore
  displayModal,
  // @ts-ignore
  showConfirmationModal,
  // @ts-ignore
  update,
  // @ts-ignore
  onSendTest: sendTestMailTemplateData,
  // @ts-ignore
  onPreview,
} = MailTemplateActions

export const useMailTemplate = () => {
  const [hasFormChanged, setHasFormChanged] = useState(false)

  const searchOptions = useSelector(mailTemplatesSearchOptionsSelector)
  const mailTemplates = useSelector(mailTemplatesSelector)
  const mailTemplatesOptions = useSelector(mailTemplatesOptionsSelector)
  const totalCount = useSelector(mailTemplatesTotalCountSelector)
  const modalVisible = useSelector(mailTemplatesModalVisibleSelector)
  const initialValues = useSelector(mailTemplatesInitialValuesSelector)
  const confirmationModalVisible = useSelector(
    mailTemplatesConfirmationModalVisibleSelector
  )
  const preview = useSelector(mailTemplatesPreviewSelector)
  const { mailerTemplateManage } = useSelector(permissionsSelector)

  const selectFiltersOptions = {
    language_id: [
      ...getObjectEntries(availableLanguages).map(([value, label]) => ({
        value,
        label,
      })),
    ],
    is_sync_ignored: [
      { value: "1", label: l("Yes") },
      { value: "0", label: l("No") },
    ],
  }

  const dispatch = useDispatch()

  const getAllMailTemplates = () => dispatch(getAllOptimizationTemplates())

  const getMailTemplates = <Type,>(searchOptions: Type) => {
    return dispatch(getMailTemplatesData(searchOptions))
  }

  const toggleModal = <Type,>(visible: boolean, initialValues: Type) => {
    dispatch(displayModal(visible, initialValues))
  }

  const toggleConfirmationModal = <Type,>(
    visible: boolean,
    initialValues: Type
  ) => {
    return dispatch(showConfirmationModal(visible, initialValues))
  }

  const getHtmlPreview = <Type,>(formValues: Type) => {
    return dispatch(onPreview(formValues, PREVIEW_TYPE_HTML, true))
  }

  const getTextPreview = <Type,>(formValues: Type) => {
    return dispatch(onPreview(formValues, PREVIEW_TYPE_TEXT, true))
  }

  const sendTestMailTemplate = <Type,>(
    payload: Type,
    successCallback?: <F>(data: F) => void
  ) => {
    return dispatch(sendTestMailTemplateData(payload, successCallback))
  }

  const updateMailTemplate = <Type,>(
    payload: Type,
    callback?: () => void,
    failureCallback?: (errors: any) => void
  ) => dispatch(update(payload, callback, failureCallback))

  const reloadMailTemplates = () => {
    return getMailTemplates(searchOptions)
  }

  const getAdditionalData = () => {
    return getAllMailTemplates()
  }

  const modalFormChangeHandler = (touched: boolean) => {
    return setHasFormChanged(touched)
  }

  const editIconHandler = <Type,>(productGroup: Type) => {
    return toggleModal(true, productGroup)
  }

  const submitMailTemplateHandler = <Type,>(
    payload: Type,
    {
      setErrors,
      setSubmitting,
    }: {
      setErrors: (errors: any) => void
      setSubmitting: (payload: boolean) => void
    }
  ) => {
    return updateMailTemplate(
      { ...payload },
      () => reloadMailTemplates(),
      (errors) => {
        setErrors(errors)
        setSubmitting(false)
      }
    )
  }

  const deleteIconHandler = ({ id }: { id: number | string }) => {
    return dispatch(deleteMailTemplate(id, reloadMailTemplates))
  }

  const htmlPreviewHandler = <Type,>(values: Type) => {
    return getHtmlPreview(values)
  }

  const textPreviewHandler = <Type,>(values: Type) => {
    return getTextPreview(values)
  }

  const closePreviewModalHandler = () => {
    return dispatch(onPreview(null, null, false))
  }

  const sendTemplateHandler = <Type,>(values: Type) => {
    return sendTestMailTemplate(values, <F,>(payload: F) => {
      dispatch(
        showGlobalNotification({
          type: GLOBAL_NOTIFICATION_TYPES.SUCCESS,
          message: l(payload.message),
          isCustomMessage: true,
        })
      )
    })
  }

  const createButtonHandler = () => toggleModal(true, {})

  const closeCreateOrUpdateMailTemplateModalHandler = () => {
    if (hasFormChanged) {
      toggleConfirmationModal(true, undefined)

      return
    }

    return toggleModal(false, undefined)
  }

  const confirmMailTemplateModalHandler = () => {
    toggleConfirmationModal(false, undefined)
    toggleModal(false, undefined)
    modalFormChangeHandler(false)
  }

  const closeConfirmModalHandler = () => {
    return toggleConfirmationModal(false, undefined)
  }

  return {
    canManage: mailerTemplateManage,
    mailTemplates,
    mailTemplatesOptions,
    searchOptions,
    selectFiltersOptions,
    totalCount,
    modalVisible,
    initialValues,
    confirmationModalVisible,
    preview,
    getMailTemplates,
    getAdditionalData,
    createButtonHandler,
    closeCreateOrUpdateMailTemplateModalHandler,
    editIconHandler,
    deleteIconHandler,
    sendTemplateHandler,
    submitMailTemplateHandler,
    htmlPreviewHandler,
    textPreviewHandler,
    modalFormChangeHandler,
    confirmMailTemplateModalHandler,
    closeConfirmModalHandler,
    closePreviewModalHandler,
  }
}
