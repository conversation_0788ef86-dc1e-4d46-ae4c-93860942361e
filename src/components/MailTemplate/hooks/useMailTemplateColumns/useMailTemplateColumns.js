import React from "react"
import { useSelector } from "react-redux"

import { mailTemplatesOptionsSelector } from "selectors/mailTemplatesSelectors"

import ExportValue from "components/TableGridLayout/components/ExportValue"
import {
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "components/TableGridLayout/TableGridLayoutView"
import Typography from "components/Typography"

import l, { availableLanguages } from "utils/intl"

export const useMailTemplateColumns = () => {
  const mailTemplatesOptions = useSelector(mailTemplatesOptionsSelector)

  const tableColumns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      ellipsis: true,
      width: 50,
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      ellipsis: true,
      width: 120,
      onCell: () => ({
        style: {
          textAlign: "left",
        },
      }),
    },
    {
      title: "Language",
      dataIndex: "language_id",
      key: "language_id",
      render: (langId) => (
        <Typography type={"div"} variant="textSmall">
          <ExportValue>{l(availableLanguages[langId])}</ExportValue>
        </Typography>
      ),
      sorter: true,
      type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
      ellipsis: true,
      width: 120,
      onCell: () => ({
        style: {
          textAlign: "left",
        },
      }),
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 250,
      onCell: () => ({
        style: {
          textAlign: "left",
        },
      }),
    },
    {
      title: "Ignore synchronization",
      dataIndex: "is_sync_ignored",
      key: "is_sync_ignored",
      sorter: true,
      type: COLUMN_INPUT_TYPE_SELECT,
      ellipsis: true,
      width: 120,
      render: (is_sync_ignored) => {
        return l(is_sync_ignored ? "Yes" : "No")
      },
    },
    {
      title: "From",
      dataIndex: "from",
      key: "from",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 200,
      onCell: () => ({
        style: {
          textAlign: "left",
        },
      }),
    },
    {
      title: "CC",
      dataIndex: "cc",
      key: "cc",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 200,
      onCell: () => ({
        style: {
          textAlign: "left",
        },
      }),
    },
    {
      title: "BCC",
      dataIndex: "bcc",
      key: "bcc",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 200,
      onCell: () => ({
        style: {
          textAlign: "left",
        },
      }),
    },
    {
      title: "Subject",
      dataIndex: "subject",
      key: "subject",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 250,
      onCell: () => ({
        style: {
          textAlign: "left",
          padding: 5,
        },
      }),
    },
    {
      title: "Layout name",
      dataIndex: "layout_id",
      key: "layout_id",
      render: (layout_id) => {
        const template = mailTemplatesOptions.find(({ id }) => id === layout_id)

        if (template) {
          return (
            <Typography type={"div"} variant="textSmall">
              <ExportValue>{template.name}</ExportValue>
            </Typography>
          )
        }

        return null
      },
      sorter: false,
      ellipsis: true,
      width: 120,
    },
  ]

  return {
    tableColumns,
  }
}
