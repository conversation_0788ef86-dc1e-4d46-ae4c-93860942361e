@import "assets/styles/variables.scss";

.container {
}
.fileWraper {
  &.isEnter {
    background-color: #f1f1f1;
  }
  transition: 0.3s;
  padding-top: 10px;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  .box {
    text-align: center;
  }
}
.uploadIcon {
  font-size: 34px;
  color: $icon_static;
  margin-bottom: 12px;
  &.uploaded {
    color: $icon_done;
  }
}
.fileName {
  color: $text_main;
  font-size: 14px;
  font-weight: bold;
}
.process {
  color: $text_second;
  margin-top: 10px;
  font-size: 14px;
}
.info {
  text-align: center;
  .iconBox {
    display: inline-block;
    margin-right: 30px;
    margin-top: 10px;
    & > div {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
  }
  .infoIconValue {
    display: block;
    color: $icon_clicable;
    font-size: 13px;
  }
  .infoIcon {
    font-size: 20px;
    color: $icon_static;
    margin-right: 8px;
  }
  &:last-child {
    margin-right: 0;
  }
}
