import React, { useState } from "react"
import withSizes from "react-sizes"
import { Tabs } from "@develop/fe-library"
import cn from "classnames"
import PropTypes from "prop-types"

import { getBreakpoint, LG, lowerThan } from "utils/breakpoints"

import styles from "./tabsComponent.module.scss"

const TabsComponent = ({
  activeTab: propActiveTab,
  breakpoint,
  onChangeTab = () => {},
  onPreventedChangeTab = () => {},
  tabs = [],
  defaultActiveTab = "1",
  hideIconsOnMobile = false,
  isTabChangeAllowed = true,
  tabsContainerClassName = "",
  mobileBreakPoint = LG,
}) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab)

  const onSetActiveTab = ({ key }) => {
    if (!isTabChangeAllowed) {
      onPreventedChangeTab(key)

      return
    }
    setActiveTab(key)
    onChangeTab(key)
  }

  const mobileItems = tabs.map(({ label, ...rest }) => rest)
  const isMobile = lowerThan(breakpoint, mobileBreakPoint)
  const items = hideIconsOnMobile && isMobile ? mobileItems : tabs
  const tabsPosition = isMobile ? "top" : "left"
  const tabsSize = isMobile ? "medium" : "big"

  return (
    <div className={cn(styles.tabs, tabsContainerClassName)}>
      <Tabs
        activeKey={propActiveTab || activeTab}
        isFullWidth={lowerThan(breakpoint, mobileBreakPoint)}
        isTabChangeAllowed={isTabChangeAllowed}
        items={items}
        position={tabsPosition}
        selectedTriggerBackgroundColor="#fff"
        size={tabsSize}
        triggersContainerBackgroundColor="#f6f8f9"
        variant="outlined"
        onChange={onSetActiveTab}
      />
    </div>
  )
}
const mapSizesToProps = ({ width }) => ({
  breakpoint: getBreakpoint(width),
})

TabsComponent.propTypes = {
  mobileBreakPoint: PropTypes.string,
  activeTab: PropTypes.string,
  onChangeTab: PropTypes.func,
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      key: PropTypes.string.isRequired,
      title: PropTypes.string,
      icon: PropTypes.any,
      component: PropTypes.any,
    })
  ),
  tabsContainerClassName: PropTypes.string,
  defaultActiveTab: PropTypes.string,
  hideIconsOnMobile: PropTypes.bool,
}

export default withSizes(mapSizesToProps)(TabsComponent)
