import React from "react"

import { SellerCentralInvitationsTableSettings } from "initialState/tableSettings"

import { TableWrapper } from "components/shared/TableWrapper"

import {
  useSellerCentralInvitations,
  useSellerCentralInvitationsColumns,
} from "./hooks"

const PAGE_NAME = "Seller Central invitations"

export const SellerCentralInvitations = () => {
  const {
    dataSource,
    selectFiltersOptions,
    searchOptions,
    totalCount,
    canManage,
    getSellerCentralInvitationsHandler,
    updateInvitationStatusHandler,
    redirectToSellerCentralInvitationHandler,
  } = useSellerCentralInvitations()

  const tableColumns = useSellerCentralInvitationsColumns()

  return (
    <TableWrapper
      // @ts-expect-error
      isNeedSort
      actionsColumnWidth={60}
      componentTableSettings={SellerCentralInvitationsTableSettings}
      dataSource={dataSource}
      getData={getSellerCentralInvitationsHandler}
      pageTableSettings={SellerCentralInvitationsTableSettings}
      searchOptions={searchOptions}
      selectFiltersOptions={selectFiltersOptions}
      tableColumns={tableColumns}
      tableGridTitle={PAGE_NAME}
      tableHeaderTitle={PAGE_NAME}
      totalCount={totalCount}
      tableIcons={[
        {
          checkAvailability: () => canManage,
          onClick: redirectToSellerCentralInvitationHandler,
          name: "icnLink",
          title: "Invitation link",
        },
      ]}
      onSaveCell={updateInvitationStatusHandler}
    />
  )
}
