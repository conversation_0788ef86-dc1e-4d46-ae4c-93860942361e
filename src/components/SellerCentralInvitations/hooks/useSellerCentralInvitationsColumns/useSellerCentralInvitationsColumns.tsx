import React from "react"
import { ROUTES } from "@develop/fe-library/dist/routes"

import Link from "components/shared/Link"
import {
  COLUMN_INPUT_TYPE_DATE_RANGE,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
} from "components/TableGridLayout/TableGridLayoutView"

import { permissionKeys, SELLER_CENTRAL_INVITATIONS_STATUSES } from "consts"

const { DONE } = SELLER_CENTRAL_INVITATIONS_STATUSES

export const useSellerCentralInvitationsColumns = () => {
  return [
    {
      title: "SellerID",
      dataIndex: "seller_id",
      key: "seller_id",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 100,
    },
    {
      title: "Related FS email",
      dataIndex: "email",
      key: "email",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 120,
    },
    {
      title: "Invitation hash",
      dataIndex: "hash",
      key: "hash",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 120,
    },
    {
      title: "Seller name",
      dataIndex: "seller_name",
      key: "seller_name",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 120,
    },
    {
      title: "Reason",
      dataIndex: "reason",
      key: "reason",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 120,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      sorter: false,
      type: COLUMN_INPUT_TYPE_SELECT,
      width: 120,
      editable: {
        checkIfEditable: ({ statusType }) =>
          statusType?.toUpperCase() !== DONE.toUpperCase(),
        canDisableSubmitButton: (
          value: keyof typeof SELLER_CENTRAL_INVITATIONS_STATUSES
        ) => value?.toUpperCase() !== DONE.toUpperCase(),
        onBuildCustomOptions: ({ status }) => {
          return [
            {
              label: SELLER_CENTRAL_INVITATIONS_STATUSES[status],
              value: status,
            },
            {
              label: SELLER_CENTRAL_INVITATIONS_STATUSES.DONE,
              value: DONE.toUpperCase(),
            },
          ]
        },
        title: "Update status",
        description:
          "Please note that once “Done“ is selected, changes cannot be made",
        type: "select",
        shouldTranslateOptions: true,
      },
      permission: permissionKeys.amazonSellerCentralInvitationsManage,
    },
    {
      title: "User",
      dataIndex: "userFirstName",
      key: "userFirstName",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 120,
      render: (text, { user_id, userFirstName, userLastName }) => {
        const hasFirstOrLastName: boolean = userFirstName || userLastName
        const userName: string = hasFirstOrLastName
          ? `${userFirstName ?? ""} ${userLastName ?? ""}`.trim()
          : ""

        return text ? (
          <Link
            internal={false}
            styleType="primary"
            target="_blank"
            text={userName}
            url={`${ROUTES.ADMIN_ROUTES.PATH_USERS}/${user_id}`}
            variant="textSmall"
          />
        ) : null
      },
    },
    {
      title: "CustomerID",
      dataIndex: "customerId",
      key: "customerId",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 120,
      render: (text) =>
        text ? (
          <Link
            internal={false}
            styleType="primary"
            target="_blank"
            text={text}
            url={`${ROUTES.GENERAL_ROUTES.PATH_AMAZON_ACCOUNTS}?customerID=${text}`}
            variant="textSmall"
          />
        ) : null,
    },
    {
      title: "Invitation time",
      dataIndex: "created_at",
      key: "created_at",
      sorter: true,
      type: COLUMN_INPUT_TYPE_DATE_RANGE,
      width: 120,
    },
  ]
}
