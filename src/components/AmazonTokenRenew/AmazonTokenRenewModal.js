import React from "react"
import PropTypes from "prop-types"
import moment from "moment"
import { Alert, Box } from "@develop/fe-library"

import l from "utils/intl"

import { useSetAlertToUserSettings } from "hooks"

import Modal from "components/shared/Modal"
import FormattedMessage from "components/FormattedMessage"
import Typography from "components/Typography"

import { Button, PrimaryButton } from "components/shared/Buttons"

import {
  wizardModalTitlesConstants,
  ALERT_KEYS_FROM_USER_SETTINGS,
} from "consts"

import styles from "./amazonTokenRenew.module.scss"

const { ADDITIONAL_EXCEPTIONS_MODAL_TITLES } = wizardModalTitlesConstants

const AmazonTokenRenewModal = ({
  account: { sellerId, title, id },
  canManage,
  onClose,
  onIgnore,
  onNext,
  showIgnoreButton,
  closable,
  maskClosable,
}) => {
  const setAlertToUserSettings = useSetAlertToUserSettings()

  const onCloseRedirectModalHandler = () => {
    setAlertToUserSettings({
      alertKey:
        ALERT_KEYS_FROM_USER_SETTINGS.RENEW_TOKEN_MODAL_WITHOUT_PERMISSIONS_KEY,
      data: {
        [id]: moment().add(1, "days"),
      },
      successCallback: () => {
        onClose()
      },
    })
  }

  return (
    <Modal
      className="token-renew-modal"
      footer={null}
      onCancel={() => onClose(false)}
      title={l(ADDITIONAL_EXCEPTIONS_MODAL_TITLES.renewAmazonToken)}
      visible={true}
      width={415}
      closable={closable}
      maskClosable={maskClosable}
    >
      <div className={styles.content}>
        <Typography className={styles.contentLabel} type="div" variant="text">
          <FormattedMessage
            id="Your Amazon MWS token for Seller Id {sellerId} ({accountTitle}) is not valid or will expire soon."
            defaultMessage="Your Amazon MWS token for Seller Id {sellerId} ({accountTitle}) is not valid or will expire soon."
            values={{
              accountTitle: title,
              sellerId,
            }}
          />
        </Typography>
        <Typography className={styles.contentLabel} type="div" variant="text">
          <FormattedMessage id="To renew, follow the steps on Amazon" />
        </Typography>

        {canManage ? null : (
          <Box display="block" marginTop="l">
            <Alert
              alertType="warning"
              message={l(
                "You don't have permission to renew Amazon API token. Please contact your administrator."
              )}
            />
          </Box>
        )}
      </div>
      <div className={styles.buttonsContainer}>
        {canManage ? (
          <>
            {showIgnoreButton ? (
              <Button className={styles.cancelButton} onClick={onIgnore}>
                <Typography variant="controlLabel">
                  <FormattedMessage id="Ignore" />
                </Typography>
              </Button>
            ) : null}
            <PrimaryButton className={styles.submitButton} onClick={onNext}>
              <Typography variant="controlLabel">
                <FormattedMessage id="Next" />
              </Typography>
            </PrimaryButton>
          </>
        ) : (
          <PrimaryButton
            className={styles.submitButton}
            onClick={onCloseRedirectModalHandler}
          >
            <Typography variant="controlLabel">
              <FormattedMessage id="Close" />
            </Typography>
          </PrimaryButton>
        )}
      </div>
    </Modal>
  )
}

AmazonTokenRenewModal.propTypes = {
  account: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired,
  onIgnore: PropTypes.func.isRequired,
  onNext: PropTypes.func.isRequired,
  canManage: PropTypes.bool.isRequired,
  showIgnoreButton: PropTypes.bool.isRequired,
}

export default AmazonTokenRenewModal
