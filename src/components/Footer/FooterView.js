import React from "react"
import PropTypes from "prop-types"
import {
  FacebookOutlined,
  LinkedinOutlined,
  MailOutlined,
  MobileOutlined,
} from "@ant-design/icons"
import { Row, Col } from "antd"

import LinksList from "components/LinksList"
import SubscriptionForm from "components/SubscriptionForm"
import Typography from "components/Typography"

import styles from "./footer.module.scss"

const FooterView = ({ links, contactInformation, availableLanguages }) => (
  <div className={styles.container}>
    <Row className={styles.row}>
      {links.map((linksList) => (
        <Col key={linksList.title} span={4}>
          <LinksList {...linksList} />
        </Col>
      ))}
      <Col span={8}>
        <SubscriptionForm />
        <img src="http://via.placeholder.com/150x150" alt="some logo" />
      </Col>
    </Row>
    <Row className={styles.row}>
      <Col span={8}>
        <MobileOutlined />
        <a href={`tel:${contactInformation.phoneNumber.number}`}>
          <Typography>{contactInformation.phoneNumber.displayText}</Typography>
        </a>
      </Col>
      <Col span={8}>
        <MailOutlined />
        <a href={`mailto:${contactInformation.email}`}>
          <Typography>{contactInformation.email}</Typography>
        </a>
      </Col>
      <Col span={8}>
        <Typography>Become a part of our community</Typography>
        <FacebookOutlined />
        <LinkedinOutlined />
      </Col>
    </Row>
    <Row className={styles.row}>
      <Col span={8}>
        <Typography>Language</Typography>
        {availableLanguages.map(({ code, displayText, url }) => (
          <a key={code} href={url}>
            <Typography>{displayText}</Typography>
          </a>
        ))}
      </Col>
      <Col span={8}>
        <Typography>
          Copyright © {new Date().getFullYear()} SellerLogic. All rights
          reserved.
        </Typography>
      </Col>
    </Row>
  </div>
)

FooterView.propTypes = {
  links: PropTypes.array.isRequired,
  contactInformation: PropTypes.shape({
    phoneNumber: PropTypes.shape({
      number: PropTypes.string.isRequired,
      displayText: PropTypes.string.isRequired,
    }),
    email: PropTypes.string.isRequired,
  }).isRequired,
  availableLanguages: PropTypes.arrayOf(
    PropTypes.shape({
      code: PropTypes.string.isRequired,
      displayText: PropTypes.string.isRequired,
      url: PropTypes.string.isRequired,
    })
  ),
}

export default FooterView
