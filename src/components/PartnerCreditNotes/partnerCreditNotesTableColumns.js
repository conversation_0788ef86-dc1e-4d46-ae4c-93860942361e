import React from "react"
import { ROUTES } from "@develop/fe-library/dist/routes"

import {
  COLUMN_INPUT_TYPE_DATE_RANGE,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_MONTH,
} from "components/TableGridLayout/TableGridLayoutView"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import Link from "components/shared/Link"
import Typography from "components/Typography"

export default [
  {
    title: "ID",
    dataIndex: "number",
    key: "number",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
  },
  {
    title: "Partner",
    dataIndex: "partnerTitle",
    key: "partnerTitle",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    render: (text, { partnerTitle, partner_id }) => (
      <ExportValue>
        <Link
          internal={true}
          url={`${ROUTES.ADMIN_ROUTES.PATH_PARTNERS}?id=${partner_id}`}
          styleType="primary"
          variant="textSmall"
          text={`${partnerTitle} (${partner_id})`}
        />
      </ExportValue>
    ),
    width: 200,
    onCell: () => ({
      style: {
        textAlign: "left",
      },
    }),
  },
  {
    title: "Month",
    dataIndex: "year",
    key: "yearMonth",
    type: COLUMN_INPUT_TYPE_MONTH,
    width: 150,
    isMonthPickerRange: false,
    render: (text, { year, f_month }) => (
      <Typography type="span" variant="textSmall">
        <ExportValue>{`${f_month} ${year}`}</ExportValue>
      </Typography>
    ),
  },
  {
    title: "Amount",
    dataIndex: "amount",
    key: "amount",
    sorter: true,
    type: COLUMN_INPUT_TYPE_NUMBER,
    isNumber: true,
    numberSettings: {
      fixedTo: 2,
    },
  },
  {
    title: "VAT",
    dataIndex: "vat",
    key: "vat",
    sorter: true,
    type: COLUMN_INPUT_TYPE_NUMBER,
    isNumber: true,
    numberSettings: {
      fixedTo: 2,
    },
  },
  {
    title: "Total",
    dataIndex: "total",
    key: "total",
    sorter: true,
    type: COLUMN_INPUT_TYPE_NUMBER,
    isNumber: true,
    numberSettings: {
      fixedTo: 2,
    },
  },
  {
    title: "Date inserted",
    dataIndex: "date_inserted",
    key: "date_inserted",
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE_RANGE,
    width: 150,
  },
  {
    title: "Date updated",
    dataIndex: "date_updated",
    key: "date_updated",
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE_RANGE,
    width: 150,
  },
  {
    title: "Date payed",
    dataIndex: "date_payed",
    key: "date_payed",
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE_RANGE,
    width: 150,
  },
  {
    title: "Status",
    dataIndex: "statusLabel",
    key: "status",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT,
  },
]
