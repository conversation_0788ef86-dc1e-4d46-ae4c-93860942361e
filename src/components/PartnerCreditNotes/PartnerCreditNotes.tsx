import React from "react"

import { PartnerCreditNotesTableSettings } from "initialState/tableSettings"

import tableColumns from "components/PartnerCreditNotes/partnerCreditNotesTableColumns"
import { TableWrapper } from "components/shared/TableWrapper"

import { DELETE_ITEM } from "utils/confirm"
import l from "utils/intl"

import { usePartnerCreditNotes } from "./hooks"

import { STATUS_TYPE } from "./consts"

import { Status } from "./types/status"

const PAGE_NAME = "Partner credit notes"

const { NEW, PUBLISHED, PAYED } = STATUS_TYPE

export const PartnerCreditNotes = () => {
  const {
    canManage,
    dataSource,
    selectFiltersOptions,
    searchOptions,
    totalCount,
    getPartnerCreditNotes,
    payIconHandler,
    downloadFileHandler,
    deleteIconHandler,
    submitHandler,
  } = usePartnerCreditNotes()

  return (
    <TableWrapper
      // @ts-ignore
      isNeedSort
      actionsColumnWidth={120}
      componentTableSettings={PartnerCreditNotesTableSettings}
      customDefaultSort="-number"
      dataSource={dataSource}
      getData={getPartnerCreditNotes}
      pageTableSettings={PartnerCreditNotesTableSettings}
      searchOptions={searchOptions}
      selectFiltersOptions={selectFiltersOptions}
      tableColumns={tableColumns}
      tableGridTitle={PAGE_NAME}
      tableHeaderTitle={PAGE_NAME}
      totalCount={totalCount}
      tableIcons={[
        {
          onClick: downloadFileHandler,
          name: "icnDownload",
          title: "Download",
          checkAvailability: ({ status }: Status) => status !== NEW,
        },
        {
          onClick: payIconHandler,
          confirm: {
            title: l("Payment confirmation"),
            message: l("Do you really want to mark as Payed?"),
          },
          name: "icnDollar",
          title: "Mark paid",
          checkAvailability: ({ status }: Status) =>
            status === PUBLISHED && canManage,
        },
        {
          onClick: deleteIconHandler,
          confirm: {
            template: DELETE_ITEM,
          },
          name: "icnDeleteOutlined",
          title: "Delete",
          checkAvailability: ({ status }: Status) =>
            status !== PAYED && canManage,
        },
      ]}
      onSaveCell={submitHandler}
    />
  )
}
