import { useDispatch, useSelector } from "react-redux"

import partnerCreditNotesActions from "actions/partnerCreditNotesActions"

import {
  filtersOptionsSelector,
  partnerCreditNoteItemsSelector,
  partnerCreditNotesSearchOptionsSelector,
  partnerCreditNotesTotalCountSelector,
} from "selectors/partnerCreditNotesSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import { STATUS_TYPE } from "../consts"

const { DELETED, PAYED } = STATUS_TYPE
// @ts-ignore
const { get: getPartnerCreditNotesData, update } = partnerCreditNotesActions

export const usePartnerCreditNotes = () => {
  const { partnerCreditNoteManage } = useSelector(permissionsSelector)
  const dataSource = useSelector(partnerCreditNoteItemsSelector)
  const selectFiltersOptions = useSelector(filtersOptionsSelector)
  const searchOptions = useSelector(partnerCreditNotesSearchOptionsSelector)
  const totalCount = useSelector(partnerCreditNotesTotalCountSelector)

  const dispatch = useDispatch()

  const getPartnerCreditNotes = <Type,>(searchOptions: Type) => {
    return dispatch(getPartnerCreditNotesData(searchOptions))
  }

  const submitHandler = ({
    id,
    status,
  }: {
    id: number | string
    status: string
  }) => {
    return dispatch(
      update(
        {
          id,
          status,
        },
        () => {
          getPartnerCreditNotes(searchOptions)
        }
      )
    )
  }

  const downloadFileHandler = ({
    s3_url,
  }: {
    s3_url: string | URL | undefined
  }) => {
    return window.open(s3_url, "_blank")?.focus()
  }

  const payIconHandler = ({ id }: { id: number | string }) => {
    return submitHandler({ id, status: PAYED })
  }

  const deleteIconHandler = ({ id }: { id: number | string }) => {
    return submitHandler({ id, status: DELETED })
  }

  return {
    canManage: partnerCreditNoteManage,
    dataSource,
    selectFiltersOptions,
    searchOptions,
    totalCount,
    getPartnerCreditNotes,
    payIconHandler,
    downloadFileHandler,
    deleteIconHandler,
    submitHandler,
  }
}
