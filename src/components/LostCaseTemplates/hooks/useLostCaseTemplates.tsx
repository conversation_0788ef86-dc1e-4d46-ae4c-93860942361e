import { useDispatch, useSelector } from "react-redux"

import lostCaseTemplatesAction from "actions/lostCaseTemplatesActions"

import {
  lostCaseTemplatesInitialValuesSelector,
  lostCaseTemplatesModalVisibleSelector,
  lostCaseTemplatesSearchOptionsSelector,
  lostCaseTemplatesSelector,
  lostCaseTemplatesTotalCountSelector,
} from "selectors/lostCaseTemplatesSelectors"
import { permissionsSelector } from "selectors/userSelectors"

const {
  // @ts-ignore
  delete: deleteItem,
  // @ts-ignore
  displayModal,
  // @ts-ignore
  get: getLostCaseTemplatesData,
  // @ts-ignore
  update,
} = lostCaseTemplatesAction

export const useLostCaseTemplates = () => {
  const { lostCaseTemplateManage, lostCaseTemplateView } =
    useSelector(permissionsSelector)
  const initialValues = useSelector(lostCaseTemplatesInitialValuesSelector)
  const dataSource = useSelector(lostCaseTemplatesSelector)
  const modalVisible = useSelector(lostCaseTemplatesModalVisibleSelector)
  const searchOptions = useSelector(lostCaseTemplatesSearchOptionsSelector)
  const totalCount = useSelector(lostCaseTemplatesTotalCountSelector)

  const canDisabledModal = !lostCaseTemplateManage && lostCaseTemplateView

  const dispatch = useDispatch()

  const getLostCaseTemplates = <Type,>(searchOptions: Type) => {
    return dispatch(getLostCaseTemplatesData(searchOptions))
  }

  const toggleModal = <Type,>(visible: boolean, initialValues: Type) => {
    return dispatch(displayModal(visible, initialValues))
  }

  const submitModalHandler = <Type,>(
    payload: Type,
    failureCallback?: () => void
  ) => {
    return dispatch(
      update(
        payload,
        () => getLostCaseTemplates(searchOptions),
        failureCallback
      )
    )
  }

  const updateIconHandler = <Type,>(payload: Type) => {
    return toggleModal(true, payload)
  }

  const deleteIconHandler = ({ id }: { id: number | string }) => {
    return dispatch(deleteItem(id, () => getLostCaseTemplates(searchOptions)))
  }

  const createButtonIconHandler = () => toggleModal(true, {})

  const closeModalHandler = () => {
    return toggleModal(false, undefined)
  }

  return {
    canManage: lostCaseTemplateManage,
    canView: lostCaseTemplateView,
    initialValues,
    modalVisible,
    dataSource,
    totalCount,
    searchOptions,
    canDisabledModal,
    getLostCaseTemplates,
    createButtonIconHandler,
    updateIconHandler,
    submitModalHandler,
    deleteIconHandler,
    closeModalHandler,
  }
}
