export const tableColumns = [
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
    sorter: true,
    type: "input",
    ellipsis: true,
    width: 50,
  },
  {
    title: "Type",
    dataIndex: "type",
    key: "type",
    sorter: true,
    type: "input",
    ellipsis: true,
    onCell: () => ({
      style: { textAlign: "left" },
    }),
  },
  {
    title: "Description",
    dataIndex: "description",
    key: "description",
    sorter: true,
    type: "input",
    ellipsis: true,
    onCell: () => ({
      style: { textAlign: "left" },
    }),
  },
  {
    title: "Amazon title",
    dataIndex: "title",
    key: "title",
    sorter: true,
    type: "input",
    onCell: () => ({
      style: { textAlign: "left" },
    }),
  },
]
