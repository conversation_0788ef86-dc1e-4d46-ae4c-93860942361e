import { connect } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import ModuleSetupModal from "components/ModuleSetupWizard/ModuleSetupWizardModal"

const { toggleModal } = moduleSetupWizardActions
const { displayRenewTokenModal } = amazonCustomerAccountsActions

const mapStateToProps = (state) => {
  const {
    moduleSetupWizard: { currentStep, steps, visible, productNames },
    amazonCustomerAccounts: {
      displayRenewTokenModal: { loginSellerUrl },
    },
  } = state

  return {
    step: currentStep,
    stepProps: steps[currentStep],
    visible,
    loginSellerUrl,
    productNames,
  }
}

export default connect(mapStateToProps, {
  onClose: toggleModal,
  displayRenewTokenModal,
})(ModuleSetupModal)
