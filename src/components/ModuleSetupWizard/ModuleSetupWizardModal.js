import React, { useCallback, useEffect, useState } from "react"
import { Flag } from "@develop/fe-library"
import cn from "classnames"
import localForage from "localforage"
import PropTypes from "prop-types"

import styles from "components/ModuleSetupWizard/moduleSetupWizard.module.scss"
import Modal from "components/shared/Modal"
import Typography from "components/Typography"

import { getVirtualEventCategory, pushEvent } from "utils/gtmLoader"
import l from "utils/intl"
import { makeFormatWizardTitle } from "utils/wizard"

import { gtmMainModalNames, wizardModalTitlesConstants } from "consts"
import { EVENTS_NAMES } from "consts/gtm"

import { components } from "./componentsMap"

const { ADDITIONAL_EXCEPTIONS_MODAL_TITLES } = wizardModalTitlesConstants

const localForageInstance = localForage.createInstance({
  driver: localForage.INDEXEDDB,
  name: "SLR Account Storage",
  storeName: "slraccountstorage",
})

const ModuleSetupWizardModal = ({
  onClose,
  step,
  stepProps,
  loginSellerUrl,
  displayRenewTokenModal,
  productNames,
}) => {
  const closeHandler = useCallback(() => {
    loginSellerUrl &&
      displayRenewTokenModal({
        sellingPartnerId: "",
        loginSellerUrl: "",
      })
    onClose(false, "billingInformation", undefined, {
      error: null,
      noTrack: true,
    })
    onClose(false, undefined, undefined, undefined)
  }, [onClose, loginSellerUrl, displayRenewTokenModal])

  const [renewToken, setRenewToken] = useState(false)
  const StepComponent = components[step]
  const [allowClose, setAllowClose] = useState(!stepProps?.doNotAllowClose)
  const [hidden, setHidden] = useState(stepProps?.hideByDefault)

  const transformedModalTitle = makeFormatWizardTitle({
    modalName: step,
    productName: productNames,
  })

  const modalTitle = renewToken
    ? l(ADDITIONAL_EXCEPTIONS_MODAL_TITLES.renewAmazonToken)
    : transformedModalTitle

  useEffect(() => {
    setHidden(stepProps.hideByDefault)
  }, [setHidden, stepProps])

  useEffect(() => {
    setAllowClose(!stepProps?.doNotAllowClose)
  }, [stepProps?.doNotAllowClose])

  const virtualEventCategory = getVirtualEventCategory({
    name: gtmMainModalNames[step],
    productNames,
  })

  const defaultModalWidth = renewToken ? 415 : 728

  const modalWidth = stepProps?.modalWidth || defaultModalWidth

  useEffect(() => {
    // in gtmMainModalNames `gtmMainModalNames[step] return null, we don`t use gtm
    if (gtmMainModalNames[step]) {
      if (gtmMainModalNames[step] !== gtmMainModalNames.createAccount) {
        pushEvent({
          virtualEventAction: "show",
          virtualEventCategory,
          event: EVENTS_NAMES.updEvents,
        })
      } else {
        ;(async () => {
          const { productNames } =
            (await localForageInstance.getItem("newaccountdata")) || {}
          const createAccountEvent = getVirtualEventCategory({
            name: gtmMainModalNames[step],
            productNames,
          })

          pushEvent({
            virtualEventAction: "show",
            virtualEventCategory: createAccountEvent,
            event: EVENTS_NAMES.updEvents,
          })
        })()
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [StepComponent])

  return (
    <Modal
      visible
      centered={stepProps?.modalCentered}
      closable={allowClose}
      footer={false}
      maskClosable={false}
      width={modalWidth}
      className={cn(styles.modal, {
        [styles.renew]: renewToken,
        [styles.hidden]: hidden,
      })}
      title={
        <>
          <Typography
            className={styles.modalTitle}
            type="div"
            variant="textLarge"
          >
            {modalTitle}
          </Typography>
          {step === "subscriptionUpdate" && (
            <div className={styles.marketplaceContainer}>
              <Flag
                borderRadius="--border-radius-circle"
                className={styles.imgIcon}
                locale={stepProps.marketplace.amazonMarketplace.extension}
                size={24}
              />
              <Typography
                className={styles.marketplaceTitle}
                type="div"
                variant="text"
              >
                {stepProps.marketplace.amazonMarketplace.title}
              </Typography>
            </div>
          )}
        </>
      }
      onCancel={allowClose ? closeHandler : undefined}
    >
      <StepComponent
        {...stepProps}
        setHidden={setHidden}
        title={undefined}
        onAllowClose={setAllowClose}
        onRenewToken={setRenewToken}
      />
    </Modal>
  )
}

ModuleSetupWizardModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  step: PropTypes.string.isRequired,
  stepProps: PropTypes.object.isRequired,
}

export default ModuleSetupWizardModal
