import { connect } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { isBasActive } from "selectors/basCustomerPlanSelector"

import ConnectChoseHomeMarketplaceView from "./ConnectChoseHomeMarketplaceView"

const { toggleModal } = moduleSetupWizardActions
const { redirectToAmazon } = amazonCustomerAccountsActions

const mapStateToProps = (state) => {
  const {
    moduleSetupWizard: { productNames, isRepricer },
    customer: {
      customer: { bas_module_started },
    },
    amazonCustomerAccounts: {
      displayRenewTokenModal: { loginSellerUrl },
    },
  } = state

  return {
    productNames,
    isRepricer,
    isBasActive: isBasActive(state),
    isBasModuleStarted: !!bas_module_started,
    loginSellerUrl: loginSellerUrl,
  }
}

export default connect(mapStateToProps, {
  changeStep: toggleModal,
  redirectToAmazon,
})(ConnectChoseHomeMarketplaceView)
