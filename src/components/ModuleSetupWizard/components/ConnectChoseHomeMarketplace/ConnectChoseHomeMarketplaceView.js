import React, { useCallback, useState } from "react"
import { TeamOutlined } from "@ant-design/icons"
import cn from "classnames"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import { Button, PrimaryButton } from "components/shared/Buttons"
import Typography from "components/Typography"

import { getVirtualEventCategory, pushEvent } from "utils/gtmLoader"

import { EVENTS_NAMES } from "consts/gtm"

import { marketplaceRegionsLabels } from "../../constants"

import MarketplacesSelector from "../MarketplacesSelector"

import styles from "./connectChoseHomeMarketplace.module.scss"

const ConnectChoseHomeMarketplaceView = ({
  changeStep,
  redirectToAmazon,
  productNames,
  selectedRegion,
  selectedMarketplaces,
  selectedRegionMarketplaces,
  selectedLostMarketplace,
  withoutModule,
  isBasActive,
  isBasModuleStarted,
  loginSellerUrl,
}) => {
  const virtualEventCategory = getVirtualEventCategory({
    name: "modalWizardChoseHomeMarketplace",
    productNames,
  })
  const [homeMarketplace, setHomeMarketplace] = useState(
    selectedMarketplaces.length === 1
      ? selectedMarketplaces[0]
      : selectedLostMarketplace || undefined
  )

  const closeHandler = useCallback(() => {
    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: "click-back",
      virtualEventCategory,
      event: EVENTS_NAMES.updEvents,
    })

    changeStep(true, "connectChoseRegion", productNames, {
      selectedRegion,
      selectedMarketplaces,
    })
  }, [
    changeStep,
    productNames,
    selectedRegion,
    selectedMarketplaces,
    virtualEventCategory,
  ])

  const onSelectMarketplace = useCallback(
    (region, marketplaceId) =>
      setHomeMarketplace(
        homeMarketplace === marketplaceId ? undefined : marketplaceId
      ),
    [homeMarketplace, setHomeMarketplace]
  )

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.iconWrapper}>
          <TeamOutlined className={styles.icon} />
        </div>
        <div className={styles.titleWrapper}>
          <Typography className={styles.title} type="div" variant="textLarge">
            <FormattedMessage id="Select home marketplace" />
          </Typography>
          <Typography className={styles.subtitle} type="div" variant="text">
            <FormattedMessage id="Please choose Amazon home marketplace for your region" />
          </Typography>
        </div>
      </div>
      <div>
        <MarketplacesSelector
          marketplaces={selectedRegionMarketplaces}
          productNames={productNames}
          region={selectedRegion}
          regionLabel={marketplaceRegionsLabels[selectedRegion]}
          selectedMarketplaces={[homeMarketplace]}
          onSelectMarketplace={onSelectMarketplace}
        />
      </div>
      <div className={styles.note}>
        <Typography className={styles.noteLabel} type="div" variant="textLarge">
          <FormattedMessage id="To connect an Amazon account, you will be redirected to the Amazon Seller Central login." />
        </Typography>
      </div>
      <div className={cn(styles.footer, "wizard-footer")}>
        <Button className={styles.button} onClick={closeHandler}>
          <FormattedMessage id="Back" />
        </Button>
        <PrimaryButton
          className={styles.button}
          disabled={!homeMarketplace}
          onClick={() => {
            pushEvent({
              vHitNonInteraction: false,
              virtualEventAction: "click-next",
              virtualEventCategory,
              event: EVENTS_NAMES.updEvents,
            })
            if (withoutModule) {
              loginSellerUrl
                ? redirectToAmazon({
                    region: selectedRegion,
                    productNames: productNames,
                    homeMarketplace: homeMarketplace,
                    marketplaces: [],
                    accountModule: false,
                    renewToken: false,
                    isBasActive: undefined,
                    isBasModuleStarted: undefined,
                    loginSellerUri: loginSellerUrl,
                  })
                : redirectToAmazon({
                    region: selectedRegion,
                    productNames: productNames,
                    homeMarketplace: homeMarketplace,
                    marketplaces: [],
                    accountModule: true,
                    renewToken: false,
                    isBasActive: undefined,
                    isBasModuleStarted: undefined,
                    loginSellerUri: false,
                  })
            } else {
              redirectToAmazon({
                region: selectedRegion,
                productNames: productNames,
                homeMarketplace: homeMarketplace,
                marketplaces: selectedMarketplaces,
                accountModule: false,
                renewToken: false,
                isBasActive: isBasActive,
                isBasModuleStarted: isBasModuleStarted,
                loginSellerUri: false,
              })
            }
          }}
        >
          <FormattedMessage id="Next" />
        </PrimaryButton>
      </div>
    </div>
  )
}

ConnectChoseHomeMarketplaceView.propTypes = {
  changeStep: PropTypes.func.isRequired,
  redirectToAmazon: PropTypes.func.isRequired,
  selectedRegion: PropTypes.string.isRequired,
  selectedMarketplaces: PropTypes.array.isRequired,
  selectedRegionMarketplaces: PropTypes.array.isRequired,
  withoutModule: PropTypes.bool,
}

export default ConnectChoseHomeMarketplaceView
