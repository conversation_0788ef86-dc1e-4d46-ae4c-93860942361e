import React, { useCallback } from "react"
import { Box, Flag, Icon, Typography } from "@develop/fe-library"
import cn from "classnames"

import { checkIsArray } from "utils/arrayHelpers"
import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"

import { REPRICER_OFFER_TYPE } from "consts/product"
import { SUBSCRIPTION_TITLES } from "consts/titles"

import { SummaryOldRepricerProps } from "./SummaryOldRepricerTypes"

import styles from "./summaryOldRepricer.module.scss"

export const SummaryOldRepricer = ({
  accounts,
  onSubscriptionUpdate,
  productNames,
}: SummaryOldRepricerProps) => {
  const updateSubscriptionHandler = useCallback(
    (marketplace) => (): void => {
      onSubscriptionUpdate(marketplace)
    },
    [onSubscriptionUpdate]
  )

  if (!checkIsArray(accounts)) {
    return null
  }

  return (
    <>
      {accounts.map(
        ({
          accountName,
          homeMarketplace,
          id,
          sellerId,
          allAccountMarketplaces: marketplaces,
        }) => (
          <ul key={id} className={styles.account}>
            <li className={styles.prop}>
              <Typography
                className={styles.label}
                color="--color-text-main"
                variant="--font-body-text-7"
              >
                {l("Account name")}:
              </Typography>

              <Typography
                className={styles.value}
                color="--color-text-second"
                variant="--font-body-text-9"
              >
                {accountName}
              </Typography>
            </li>

            <li className={styles.prop}>
              <Typography
                className={styles.label}
                color="--color-text-main"
                variant="--font-body-text-7"
              >
                {l("Amazon seller ID")}:
              </Typography>

              <Typography
                className={styles.value}
                color="--color-text-second"
                variant="--font-body-text-9"
              >
                {sellerId}
              </Typography>
            </li>

            <li className={styles.prop}>
              <Typography
                className={styles.label}
                color="--color-text-main"
                variant="--font-body-text-7"
              >
                {l("Home marketplace")}:
              </Typography>

              {homeMarketplace?.country ? (
                <div className={styles.homeMarketplaceFlag}>
                  <Flag
                    borderRadius="--border-radius-circle"
                    className={styles.flagIcon}
                    locale={homeMarketplace.country.toLowerCase()}
                    size={24}
                  />
                </div>
              ) : null}
            </li>

            {checkIsArray(marketplaces) ? (
              <li className={styles.marketplacesProp}>
                <Typography
                  className={`${styles.label} ${styles.addMarketPlaceTitle}`}
                  color="--color-text-main"
                  variant="--font-body-text-7"
                >
                  {l("Added marketplaces:")}
                </Typography>

                <div className={styles.flags}>
                  {marketplaces
                    .filter(
                      ({ deleted, offer_type }) =>
                        !deleted &&
                        offer_type === REPRICER_OFFER_TYPE[productNames]
                    )
                    .map((marketplace) => {
                      const {
                        active,
                        amazonMarketplace: { country },
                        currentAmazonCustomerAccountMarketplacePlan: {
                          date_start = "",
                          date_finish = "",
                          paymentModuleFlexiblePeriod: { title } = {},
                        },
                      } = marketplace

                      const marketplaceCurrentPlan: string = `(${convertToLocalDate(
                        date_start
                      )} - ${convertToLocalDate(date_finish)})`

                      const trialPlan: string =
                        title === "14 Tage" ? ` ${marketplaceCurrentPlan}` : ""

                      const currentPlanTitle: string =
                        SUBSCRIPTION_TITLES[title] || title

                      return (
                        <Box
                          align="center"
                          marginBottom="m"
                          className={cn(styles.flagWrapper, {
                            [styles.expired]: !active,
                          })}
                          onClick={updateSubscriptionHandler(marketplace)}
                        >
                          {active ? (
                            <>
                              <Flag
                                borderRadius="--border-radius-circle"
                                className={styles.flagIcon}
                                locale={country.toLowerCase()}
                                size={24}
                              />

                              {title ? (
                                <Typography
                                  color="--color-text-link"
                                  variant="--font-body-text-9"
                                >
                                  {l(`${currentPlanTitle}${trialPlan}`)}
                                </Typography>
                              ) : null}
                            </>
                          ) : (
                            <Box
                              align="center"
                              justify="space-between"
                              width="100%"
                            >
                              <Box align="center">
                                <Flag
                                  borderRadius="--border-radius-circle"
                                  className={styles.flagIcon}
                                  locale={country.toLowerCase()}
                                  size={24}
                                />

                                <Typography
                                  color="--color-text-link"
                                  variant="--font-body-text-9"
                                >
                                  {l(
                                    title === "14 Tage"
                                      ? "Trial period has expired"
                                      : "Subscription has expired"
                                  )}
                                </Typography>
                              </Box>

                              <Icon
                                className={styles.warningIcon}
                                color="--color-icon-warning"
                                name="icnWarning"
                                size="--icon-size-5"
                              />
                            </Box>
                          )}
                        </Box>
                      )
                    })}
                </div>
              </li>
            ) : null}
          </ul>
        )
      )}
    </>
  )
}
