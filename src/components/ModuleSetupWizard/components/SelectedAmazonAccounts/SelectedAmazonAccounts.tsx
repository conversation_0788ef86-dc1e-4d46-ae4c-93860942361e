import React from "react"
import {
  Al<PERSON>,
  Box,
  Checkbox,
  Flag,
  Grid,
  Typography,
} from "@develop/fe-library"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { SelectedAmazonAccountsTypes } from "./SelectedAmazonAccountsTypes"

export const SelectedAmazonAccounts: SelectedAmazonAccountsTypes = ({
  id,
  sellerId,
  usedModules,
  accountName,
  homeMarketplace,
  allRegionMarketplaces,
  isIncludeInPreviousSubscription,
  selectedAmazonAccountsData,
  onSetSelectedAmazonAccountsData = undefined,
  withoutPaddingsInWrapper = false,
}) => {
  const isAccountChecked: boolean = selectedAmazonAccountsData
    ? selectedAmazonAccountsData.has(id)
    : false

  const backgroundColor = isAccountChecked
    ? "--color-background-second"
    : "--color-main-background"

  const onChangeChecked = (): void =>
    onSetSelectedAmazonAccountsData
      ? onSetSelectedAmazonAccountsData(id)
      : undefined

  return (
    <Box
      backgroundColor={backgroundColor}
      flexDirection="column"
      hasBorder={{ top: true, bottom: true }}
      mLG={{ padding: withoutPaddingsInWrapper ? "0" : "l" }}
      padding={withoutPaddingsInWrapper ? "0" : "m"}
    >
      {onSetSelectedAmazonAccountsData ? (
        <Box>
          <Checkbox checked={isAccountChecked} onChange={onChangeChecked} />
          <Box marginLeft="m">
            <Typography variant="--font-body-text-6">
              {l("Select Amazon account to setup Business Analytics")}
            </Typography>
          </Box>
        </Box>
      ) : null}
      {isIncludeInPreviousSubscription ? (
        <Box
          flexDirection="column"
          marginTop={onSetSelectedAmazonAccountsData ? "m" : "0"}
        >
          <Alert
            alertType="warning"
            message={l("Account was included in the previous subscription")}
          />
        </Box>
      ) : null}

      <Box flexDirection="column" marginTop="m">
        <Grid container>
          <Grid item mSM={12} mXL={6}>
            <Box flexDirection="column">
              <Box
                flexDirection="column"
                mXL={{
                  flexDirection: "row",
                }}
              >
                <Box
                  minWidth="100%"
                  mXL={{
                    minWidth: "110px",
                  }}
                >
                  <Typography
                    color="--color-text-main"
                    variant="--font-body-text-9"
                  >
                    {l("Account name")}:
                  </Typography>
                </Box>
                <Box
                  marginLeft="0"
                  marginTop="s"
                  mXL={{
                    marginTop: "0",
                    marginLeft: "m",
                  }}
                >
                  <Typography
                    color="--color-text-second"
                    variant="--font-body-text-9"
                  >
                    {accountName}
                  </Typography>
                </Box>
              </Box>
              <Box
                flexDirection="column"
                marginTop="m"
                mXL={{
                  marginTop: "m",
                  flexDirection: "row",
                }}
              >
                <Box minWidth="100%" mXL={{ minWidth: "110px" }}>
                  <Typography
                    color="--color-text-main"
                    variant="--font-body-text-9"
                  >
                    {l("Seller ID")}:
                  </Typography>
                </Box>
                <Box
                  marginLeft="0"
                  marginTop="m"
                  mXL={{
                    marginTop: "0",
                    marginLeft: "m",
                  }}
                >
                  <Typography
                    color="--color-text-second"
                    variant="--font-body-text-9"
                  >
                    {sellerId}
                  </Typography>
                </Box>
              </Box>
              <Box
                flexDirection="column"
                marginTop="m"
                mXL={{
                  flexDirection: "row",
                }}
              >
                <Box minWidth="100%" mXL={{ minWidth: "110px" }}>
                  <Typography
                    color="--color-text-main"
                    variant="--font-body-text-9"
                  >
                    {l("Used in")}:
                  </Typography>
                </Box>
                <Box
                  marginLeft="0"
                  marginTop="s"
                  mXL={{
                    marginTop: "0",
                    marginLeft: "m",
                  }}
                >
                  <Typography
                    color="--color-text-second"
                    variant="--font-body-text-9"
                  >
                    {usedModules}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Grid>
          <Grid item mSM={12} mXL={6}>
            <Box
              flexDirection="column"
              marginLeft="0"
              marginTop="m"
              mXL={{
                marginTop: "0",
                marginLeft: "l",
              }}
            >
              <Box
                flexDirection="column"
                mXL={{
                  flexDirection: "row",
                }}
              >
                <Box minWidth="100%" mXL={{ minWidth: "110px" }}>
                  <Typography
                    color="--color-text-main"
                    variant="--font-body-text-9"
                  >
                    {l("Home marketplace")}:
                  </Typography>
                </Box>
                <Box
                  marginLeft="0"
                  marginTop="s"
                  mXL={{
                    marginTop: "0",
                    marginLeft: "m",
                  }}
                >
                  {homeMarketplace ? (
                    <Flag
                      borderRadius="--border-radius-circle"
                      locale={homeMarketplace.toLowerCase()}
                      size={24}
                    />
                  ) : null}
                </Box>
              </Box>
              <Box
                flexDirection="column"
                marginTop="m"
                mXL={{
                  flexDirection: "row",
                }}
              >
                <Box
                  maxWidth="100%"
                  minWidth="100%"
                  mXL={{
                    maxWidth: "110px",
                    minWidth: "110px",
                  }}
                >
                  <Typography
                    color="--color-text-main"
                    variant="--font-body-text-9"
                  >
                    {l("All marketplaces in the region")}:
                  </Typography>
                </Box>
                {checkIsArray(allRegionMarketplaces) ? (
                  <Box
                    flexWrap="wrap"
                    gap="s"
                    marginLeft="0"
                    marginTop="s"
                    mXL={{ marginTop: "0", marginLeft: "m" }}
                  >
                    {allRegionMarketplaces.map((marketplace) => {
                      return (
                        <Flag
                          borderRadius="--border-radius-circle"
                          locale={marketplace?.country.toLowerCase()}
                          size={24}
                        />
                      )
                    })}
                  </Box>
                ) : null}
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}
