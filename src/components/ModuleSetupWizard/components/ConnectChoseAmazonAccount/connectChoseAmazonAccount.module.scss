@import "assets/styles/variables.scss";

.header {
  align-items: center;
  display: flex;
}

.container {
  padding: 20px 20px 0;
}

.iconWrapper {
  align-items: center;
  background-color: $second_bg;
  border-radius: 68px;
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  height: 60px;
  margin-right: 20px;
  width: 60px;
}

.icon {
  color: $text_second;
  font-size: 24px;
}

.title.title {
  color: $text_main;
  font-size: 18px;
  font-weight: bold;
  padding-bottom: 10px;
}

.subtitle.subtitle {
  color: $text_second;
  font-size: 13px;
}

.footer {
  border-top: 1px solid $border_main;
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding: 10px 20px;
  transform: translateX(-20px);
  width: calc(100% + 40px);
}

.button {
  margin-right: 10px;

  &:last-child {
    margin-right: 0;
  }
}

.accounts {
  margin-top: 20px;
}

.renewSubtitle.renewSubtitle.renewSubtitle {
  color: $text_main;
  font-size: 13px;
}

.basConnectWrapper {
  height: 200px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.basConnectText {
  color: $text_link;
}

@media all and (max-width: $xs) {
  .container {
    padding: 20px 10px 0;
  }

  .title.title {
    font-size: 14px;
  }

  .footer {
    flex-wrap: wrap;
    margin-top: var(--margin-m);
    padding: 10px;
    transform: translateX(-10px);
    width: calc(100% + 20px);
  }

  .button {
    flex-basis: 100%;
    flex-grow: 1;
    margin-bottom: 10px;
    margin-right: 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .renew {
    .footer {
      flex-wrap: nowrap;
    }

    .button {
      flex-basis: unset;
      flex-grow: 0;
      margin-bottom: 0;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .renewSubtitle.renewSubtitle.renewSubtitle {
    margin: 0 10px 20px;
  }
}
