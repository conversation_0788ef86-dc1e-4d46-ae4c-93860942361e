import React, { useCallback, useEffect, useRef } from "react"
import { CreditCardOutlined } from "@ant-design/icons"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import cn from "classnames"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import { Button, PrimaryButton } from "components/shared/Buttons"
import EntryEditForm from "components/shared/EntryEditForm"
import Typography from "components/Typography"

import { getVirtualEventCategory, pushEvent } from "utils/gtmLoader"
import l from "utils/intl"

import { EVENTS_NAMES } from "consts/gtm"

import validationSchema from "validationSchemas/paymentSettingsBillingValidationSchema"

import styles from "./billingInformation.module.scss"
import formStyles from "./formStyles.module.scss"

const debitCountriesCodes = ["AT", "DE"]

const BillingInformationView = ({
  changeStep,
  displayModal,
  countries,
  customerId,
  getCountries,
  initialValues,
  initialValuesWithCustomerAdddress,
  setInitialValues,
  updateCustomer,
  useCC,
  useDebit,
  productNames,
  isAccountSettings,
  isConnectInAccount,
  connectAccountId,
  isBasModuleStarted,
  isBasActive,
  setValuesForValidateVatID,
  customerDefaultVatID,
  error = null,
  isSetupBas = true,
  isNeedSkipSelectAccountToNewSubscriptionStep = false,
  isSetupNewAccount,
}) => {
  const submitRef = useRef(null)

  const errors = !!error ? error : null

  const virtualEventCategory = getVirtualEventCategory({
    name: "modalWizardBillingInformation",
    productNames,
  })

  const closeHandler = useCallback(() => {
    changeStep(false, "billingInformation", productNames, {
      error: null,
      noTrack: true,
    })
    changeStep(false, undefined, productNames, undefined)
  }, [changeStep, productNames])

  const onSubmit = useCallback(
    (payload, failureCallback) => {
      const gtmHandler = (event) =>
        pushEvent({
          vHitNonInteraction: false,
          virtualEventAction: `click-${event}`,
          virtualEventCategory,
          event: EVENTS_NAMES.updEvents,
        })

      const { vat_id, ...billingAddressPayload } = payload
      const { eu, code } = countries.find(
        ({ id }) => id === payload.billingCountryId
      )

      const goToNextStep = () =>
        setInitialValues(payload, () => {
          if (
            useDebit ||
            (!useCC && debitCountriesCodes.indexOf(code) !== -1)
          ) {
            gtmHandler("next")
            changeStep(false, "billingInformation", productNames, {
              error: null,
              noTrack: true,
            })
            changeStep(true, "paymentDebit", productNames, {
              isAccountSettings,
              productNames,
              displayModal,
              isConnectInAccount,
              connectAccountId,
              isBasModuleStarted,
              isBasActive,
              isSetupBas,
              isNeedSkipSelectAccountToNewSubscriptionStep,
              isSetupNewAccount,
            })
          } else {
            gtmHandler("next")
            changeStep(false, "billingInformation", productNames, {
              error: null,
              noTrack: true,
            })
            changeStep(true, "paymentCC", productNames, {
              isAccountSettings,
              productNames,
              displayModal,
              isConnectInAccount,
              connectAccountId,
              isBasModuleStarted,
              isBasActive,
              isSetupBas,
              isNeedSkipSelectAccountToNewSubscriptionStep,
              isSetupNewAccount,
            })
          }
        })

      const isNeedUpdateAndValidateVatID = eu && code !== "DE"

      if (isNeedUpdateAndValidateVatID) {
        updateCustomer(
          {
            billingCountryId: billingAddressPayload.billingCountryId,
            id: customerId,
            vat_id: vat_id,
          },
          () => {
            goToNextStep()
          },
          failureCallback
        )
      } else {
        goToNextStep()
      }
    },
    [
      changeStep,
      countries,
      setInitialValues,
      useCC,
      useDebit,
      customerId,
      updateCustomer,
      productNames,
      isAccountSettings,
      displayModal,
      connectAccountId,
      isConnectInAccount,
      isBasModuleStarted,
      isBasActive,
      isSetupBas,
      virtualEventCategory,
      isNeedSkipSelectAccountToNewSubscriptionStep,
      isSetupNewAccount,
    ]
  )

  useEffect(() => {
    getCountries()
    setInitialValues(null, undefined)
  }, [getCountries, setInitialValues])

  useEffect(() => {
    setValuesForValidateVatID({
      countryID: initialValues?.billingCountryId,
      prevVatID: customerDefaultVatID,
    })
  }, [])

  /* This check is necessary because we can have our own vat_id and take it from the customer.
vat_id from the customer comes earlier than the rest of the data initialValues.
According to this, there may be a render.
To prevent this, we check that the object contains more than one key, that is, that not only
vat_id key, but also others. */
  const hasInitialValuesDataNotOnlyVatID =
    !!initialValues && getObjectKeys(initialValues).length > 1
  const canNotRenderBillingInfoForm =
    !initialValues || !hasInitialValuesDataNotOnlyVatID

  if (canNotRenderBillingInfoForm) {
    return null
  }

  /* If this the new account and this account never filled out a form before.
  On the first render, there will be no data, as there is none yet.
  */
  const initialValuesForBillingForm = !!initialValues?.billingAddressLine1
    ? initialValues
    : initialValuesWithCustomerAdddress

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.iconWrapper}>
          <CreditCardOutlined className={styles.icon} />
        </div>
        <div className={styles.titleWrapper}>
          <Typography className={styles.title} type="div" variant="textLarge">
            <FormattedMessage id="Billing address" />
          </Typography>
          <Typography className={styles.subtitle} type="div" variant="text">
            <FormattedMessage id="Please fill you billing address." />
          </Typography>
        </div>
      </div>
      <div>
        <EntryEditForm
          errors={errors}
          initialValues={initialValuesForBillingForm}
          styles={formStyles}
          submitRef={submitRef}
          validationSchema={validationSchema()}
          controls={[
            {
              name: "billingEmail",
              placeholder: "Email",
              type: "text",
              required: true,
            },
            {
              name: "useBillingAddress",
              type: "button",
              onClick: (values, setValues) => {
                setValues({
                  ...initialValuesWithCustomerAdddress,
                  useBillingAddress: 1,
                  billingEmail: values.billingEmail,
                })
              },
            },
            {
              name: "billingCompanyName",
              placeholder: "Company name",
              type: "text",
              required: true,
            },
            {
              name: "billingAddressLine1",
              placeholder: "Address 1",
              type: "text",
              required: true,
            },
            {
              name: "billingAddressLine2",
              placeholder: "Address 2",
              type: "text",
            },
            {
              name: "billingCity",
              placeholder: "City",
              type: "text",
              required: true,
            },
            {
              name: "billingZip",
              placeholder: "Postcode",
              type: "text",
              required: true,
            },
            {
              allowClear: false,
              name: "billingCountryId",
              options: countries.map(({ id, title }) => ({
                label: l(title),
                value: id,
              })),
              placeholder: "Country",
              type: "selectWithFilter",
              required: true,
            },
            {
              name: "vat_id",
              note: "If no valid VAT-ID is entered, your invoice will contain the currently valid German VAT.",
              placeholder: "VAT ID",
              required: false,
              textInputPlaceholder: "DE815528383",
              type: "text",
            },
          ]}
          onSubmit={onSubmit}
        />
      </div>
      <div className={cn(styles.footer, "wizard-footer")}>
        <Button className={styles.button} onClick={closeHandler}>
          <FormattedMessage id="Cancel" />
        </Button>
        <PrimaryButton
          className={styles.button}
          onClick={() => submitRef.current.click()}
        >
          <FormattedMessage id="Next" />
        </PrimaryButton>
      </div>
    </div>
  )
}

BillingInformationView.propTypes = {
  changeStep: PropTypes.func.isRequired,
  countries: PropTypes.array.isRequired,
  customerId: PropTypes.number.isRequired,
  getCountries: PropTypes.func.isRequired,
  initialValues: PropTypes.object.isRequired,
  initialValuesWithCustomerAdddress: PropTypes.object.isRequired,
  setInitialValues: PropTypes.func.isRequired,
  updateCustomer: PropTypes.func.isRequired,
  useCC: PropTypes.number.isRequired,
  useDebit: PropTypes.number.isRequired,
  productNames: PropTypes.string.isRequired,
  setValuesForValidateVatID: PropTypes.func,
  error: PropTypes.oneOfType([null, PropTypes.object]),
}

export default BillingInformationView
