import React from "react"
import { ButtonProps } from "@develop/fe-library"

import { ProductNamesTypes } from "types/ProductsTypes"

type FullServiceModalFooterParams = {
  okButtonProps: ButtonProps & {
    isVisibleOkButton?: boolean
    okButtonText?: string
  }
  cancelButtonProps?: ButtonProps & {
    isVisibleCancelButton?: boolean
    cancelButtonText?: string
  }
  supportButtonProps?:
    | ButtonProps & {
        isVisibleSupportButton?: boolean
        supportButtonText?: string
      }
  productNames?: ProductNamesTypes
}
export type FullServiceModalFooterTypes = (
  params: FullServiceModalFooterParams
) => React.ReactElement
