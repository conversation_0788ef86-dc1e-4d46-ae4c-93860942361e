export type CreateFullServiceAccountBlocksProps = {
  stepsData?: Record<string, unknown>
  modalInfoProps: {
    isDescriptionWithoutTranslation?: boolean
    title: string
    description: string
  }
  footerProps: {
    cancelButtonProps?: {
      cancelButtonText: string
      onClick: () => void
    }
    okButtonProps: {
      okButtonText: string
      onClick: () => void
      disabled?: boolean
    }
    supportButtonProps?: {
      isVisibleSupportButton?: boolean
      disabled?: boolean
    }
  }
}
