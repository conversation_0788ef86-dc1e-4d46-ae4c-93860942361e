import React, { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import customerActions from "actions/customerActions"
import fullServiceAction from "actions/fullServiceActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import {
  amazonAccountsSelector,
  currentNewAccountsSelector,
} from "selectors/amazonAccountsSelectors"
import { isVideoInstructionsWatchedSelector } from "selectors/userSettingSelectors"

import { getVirtualEventCategory, pushEvent } from "utils/gtmLoader"
import l from "utils/intl"
import { getItem, setItem } from "utils/storage"

import {
  CONNECT_ONLY_FULL_SERVICE_TYPES,
  FULL_SERVICE_CREDENTIAL_STATUS,
  FULL_SERVICE_STORAGE_KEYS,
} from "consts"
import { EVENTS_NAMES } from "consts/gtm"

import { CreateFullServiceAccountBlocks } from "../components"

import {
  ActivateHandlerTypes,
  CreateFullServiceAccountTypes,
} from "./CreateFullServiceAccountTypes"

const { toggleModal: changeStep } = moduleSetupWizardActions
const {
  clearFullServiceData,
  fullServiceUpdateCredentialsStatus,
  putFSWizardStatus,
  getFullServiceAccountData,
} = fullServiceAction
const { getAll: getAccounts } = amazonCustomerAccountsActions
const { getCurrentCustomer } = customerActions

// this Add SELLERLOGIC as a Limited User modal.
export const CreateFullServiceAccount: CreateFullServiceAccountTypes = ({
  productNames,
  fullServiceSellerId,
  connectOnlyFullServiceType,
  nextConnectingAccounts,
}) => {
  const dispatch = useDispatch()

  const isFullServiceOnlySetup = getItem("isFullServiceOnlySetup", true)

  const newAccountSellerId = useSelector(currentNewAccountsSelector)
  const amazonCustomerAccounts = useSelector(amazonAccountsSelector)
  const isVideoInstructionsWatched = useSelector(
    isVideoInstructionsWatchedSelector
  )

  const currentNewAccountSellerId: string =
    fullServiceSellerId || newAccountSellerId

  const virtualEventCategory = getVirtualEventCategory({
    name: "modalWizardCreateFullServiceAccount",
    productNames,
  })

  const currentAmazonAccount = amazonCustomerAccounts?.find(
    ({ sellerId }) => sellerId === currentNewAccountSellerId
  )

  const amazonCustomerAccountId = currentAmazonAccount?.id

  const currentAmazonAccountName = currentAmazonAccount?.customerAccount?.title

  useEffect(() => {
    if (currentNewAccountSellerId) {
      dispatch(
        getFullServiceAccountData({
          sellerId: currentNewAccountSellerId,
        })
      )
    }
  }, [currentNewAccountSellerId, dispatch])

  useEffect(() => {
    if (isFullServiceOnlySetup) {
      setItem(FULL_SERVICE_STORAGE_KEYS.IS_FULL_SERVICE_ONLY_SETUP, false, true)
      setItem(FULL_SERVICE_STORAGE_KEYS.FULL_SERVICE_SELLER_ID, false, true)
      setItem(
        FULL_SERVICE_STORAGE_KEYS.CONNECT_ONLY_FULL_SERVICE_TYPE,
        false,
        true
      )
      setItem(FULL_SERVICE_STORAGE_KEYS.NEXT_CONNECTING_ACCOUNT, false, true)
    }
  }, [isFullServiceOnlySetup])

  const activateAccountHandler: ActivateHandlerTypes = ({ successCallback }) =>
    dispatch(
      fullServiceUpdateCredentialsStatus({
        id: amazonCustomerAccountId,
        status: FULL_SERVICE_CREDENTIAL_STATUS.CORRECT,
        successCallback: (): void => {
          dispatch(
            putFSWizardStatus({
              id: amazonCustomerAccountId,
              value: 1,
              successCallback: (): void => {
                dispatch(
                  getAccounts(false, (): void => {
                    dispatch(getCurrentCustomer(false, successCallback))
                  })
                )
              },
            })
          )
        },
        failureCallback: (error: any): void => {
          console.error(error)
        },
      })
    )

  const onNextStep = (): void => {
    switch (connectOnlyFullServiceType) {
      case CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT:
      case CONNECT_ONLY_FULL_SERVICE_TYPES.MULTIPLE: {
        activateAccountHandler({
          successCallback: (): void => {
            dispatch(
              changeStep(true, "successConnectionFullService", productNames, {
                fullServiceSellerId: currentNewAccountSellerId,
                connectOnlyFullServiceType,
                nextConnectingAccounts,
              })
            )
          },
        })

        break
      }
      default: {
        pushEvent({
          vHitNonInteraction: false,
          virtualEventAction: "click-next",
          virtualEventCategory,
          event: EVENTS_NAMES.updEvents,
        })
        activateAccountHandler({
          successCallback: (): void =>
            dispatch(
              changeStep(true, "successConnectionFullService", productNames, {
                fullServiceSellerId: currentNewAccountSellerId,
                connectOnlyFullServiceType: undefined,
                nextConnectingAccounts: [],
              })
            ),
        })

        dispatch(clearFullServiceData())
      }
    }
  }

  return (
    <CreateFullServiceAccountBlocks
      footerProps={{
        okButtonProps: {
          okButtonText: l("Confirm"),
          onClick: onNextStep,
          disabled: !isVideoInstructionsWatched,
        },
        supportButtonProps: {
          disabled: !isVideoInstructionsWatched,
        },
      }}
      modalInfoProps={{
        title: "Add SELLERLOGIC as a Limited User",
        description:
          "Please, send a Limited User invite from your Seller Central account to the email provided below:",
      }}
      stepsData={{
        currentAmazonAccountName,
      }}
    />
  )
}
