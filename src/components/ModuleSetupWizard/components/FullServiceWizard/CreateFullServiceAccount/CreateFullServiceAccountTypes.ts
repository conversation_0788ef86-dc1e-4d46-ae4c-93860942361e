import { NextConnectingAccountsTypes } from "types"

import { ProductNamesTypes } from "types/ProductsTypes"

type CreateFullServiceAccountProps = {
  fullServiceSellerId?: string
  productNames: ProductNamesTypes
  connectOnlyFullServiceType?: string
  nextConnectingAccounts?: NextConnectingAccountsTypes
}

export type CreateFullServiceAccountTypes = (
  params: CreateFullServiceAccountProps
) => JSX.Element

type ActivateHandlerParams = {
  successCallback: () => any
}

export type ActivateHandlerTypes = (params: ActivateHandlerParams) => void
