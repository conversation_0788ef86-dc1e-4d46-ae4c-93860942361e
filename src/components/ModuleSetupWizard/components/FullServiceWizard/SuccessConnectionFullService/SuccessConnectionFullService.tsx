import React from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box } from "@develop/fe-library"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import fullServiceAction from "actions/fullServiceActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import {
  amazonAccountsSelector,
  currentNewAccountsSelector,
} from "selectors/amazonAccountsSelectors"

import { ModalInfo } from "components/ModuleSetupWizard/components/ModalInfo"

import { checkIsArray } from "utils/arrayHelpers"
import { getVirtualEventCategory, pushEvent } from "utils/gtmLoader"
import l from "utils/intl"

import { CONNECT_ONLY_FULL_SERVICE_TYPES } from "consts"
import { EVENTS_NAMES } from "consts/gtm"

import { FullServiceModalFooter } from "../components"

import { SuccessConnectionFullServiceTypes } from "./SuccessConnectionFullServiceTypes"

const { toggleModal: changeStep } = moduleSetupWizardActions
const { displayAmazonTokenRenewModal } = amazonCustomerAccountsActions
const { clearFullServiceData } = fullServiceAction

export const SuccessConnectionFullService: SuccessConnectionFullServiceTypes =
  ({
    fullServiceSellerId = "",
    productNames,
    connectOnlyFullServiceType,
    nextConnectingAccounts,
  }) => {
    const dispatch = useDispatch()

    const newAccountSellerId = useSelector(currentNewAccountsSelector)
    const amazonCustomerAccounts = useSelector(amazonAccountsSelector)

    const currentNewAccountSellerId: string =
      fullServiceSellerId || newAccountSellerId

    const currentAmazonAccount = amazonCustomerAccounts?.find(
      ({ sellerId }) => sellerId === currentNewAccountSellerId
    )
    const accountName = currentAmazonAccount?.customerAccount?.title

    const okButtonTextVariant = {
      [CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT]: "Close",
      [CONNECT_ONLY_FULL_SERVICE_TYPES.MULTIPLE]: "Next",
    }

    const okButtonText: string =
      // @ts-ignore
      okButtonTextVariant?.[connectOnlyFullServiceType] || "Next"

    const virtualEventCategory = getVirtualEventCategory({
      name: "successConnectionFullService",
      productNames,
    })

    const onNextStep = (): void => {
      pushEvent({
        vHitNonInteraction: false,
        virtualEventAction: "click-finish",
        virtualEventCategory,
        event: EVENTS_NAMES.updEvents,
      })
      switch (connectOnlyFullServiceType) {
        case CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT: {
          dispatch(changeStep(false, undefined, undefined, undefined))
          break
        }
        case CONNECT_ONLY_FULL_SERVICE_TYPES.MULTIPLE: {
          const [
            currentFullServiceAccountFromMultiple,
            ...nextActivateAccounts
          ] = nextConnectingAccounts

          const nextConnectType: string = checkIsArray(nextActivateAccounts)
            ? CONNECT_ONLY_FULL_SERVICE_TYPES.MULTIPLE
            : CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT

          if (!currentFullServiceAccountFromMultiple?.mwsAuthorization) {
            dispatch(
              displayAmazonTokenRenewModal({
                visible: true,
                accounts: [
                  {
                    sellerId: currentFullServiceAccountFromMultiple?.sellerId,
                    amazonMarketplaceId:
                      currentFullServiceAccountFromMultiple?.marketplaceId,
                    amazonZoneId:
                      currentFullServiceAccountFromMultiple?.accountZoneId,
                    title: currentFullServiceAccountFromMultiple?.name,
                    showIgnoreButton: false,
                  },
                ],
                closable: true,
                maskClosable: false,
                isFullServiceOnlySetup: true,
                connectOnlyFullServiceType: nextConnectType,
                nextConnectingAccounts: nextActivateAccounts,
              })
            )

            return
          }

          dispatch(
            changeStep(true, "createFullServiceAccount", productNames, {
              fullServiceSellerId:
                currentFullServiceAccountFromMultiple?.sellerId,
              connectOnlyFullServiceType: nextConnectType,
              nextConnectingAccounts: nextActivateAccounts,
            })
          )

          break
        }
        default: {
          dispatch(changeStep(true, "summary", productNames, {}))
        }
      }
      dispatch(clearFullServiceData())
    }

    return (
      <Box flexDirection="column">
        <ModalInfo
          isDescriptionWithoutTranslation
          isTitleWithoutTranslation
          description={l(
            "Once we have accepted your invitation, you will receive an email with further instructions. Please note, this process can take up to 3 days to complete. Related Amazon account: {accountName}",
            {
              accountName,
            }
          )}
          title={l(
            "Thank you! Please, expect the email with further setup instructions"
          )}
        />
        <FullServiceModalFooter
          cancelButtonProps={{
            isVisibleCancelButton: false,
          }}
          okButtonProps={{
            okButtonText: l(okButtonText),
            onClick: onNextStep,
          }}
          supportButtonProps={{
            isVisibleSupportButton: false,
          }}
        />
      </Box>
    )
  }
