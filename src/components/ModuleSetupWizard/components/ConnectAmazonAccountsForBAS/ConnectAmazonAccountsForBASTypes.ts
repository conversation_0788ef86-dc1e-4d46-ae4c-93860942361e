type ConnectAmazonAccountsForBasFooterProps = {
  onNextStep?: () => void
  onNextStepCallback?: () => void
  selectedAmazonAccountsData: Set<number>
  paymentModuleFlexiblePeriodId: number
  basCustomerAccountsWithLastSubscription: number[] | []
  connectAccountId?: number
}

export type ConnectAmazonAccountsForBasTypes = (
  params: Omit<
    ConnectAmazonAccountsForBasFooterProps,
    "selectedAmazonAccountsData" | "basCustomerAccountsWithLastSubscription"
  >
) => JSX.Element

export type ConnectAmazonAccountsForBasFooterTypes = (
  params: ConnectAmazonAccountsForBasFooterProps
) => JSX.Element
