import { connect } from "react-redux"

import SubscriptionUpdateView from "./SubscriptionUpdateView"

import { staffUserIdSelector } from "selectors/staffSelectors"
import { customerIdSelector } from "selectors/customerSelectors"

import moduleSetupWizardActions from "actions/moduleSetupWizardActions"
import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"

const { toggleModal } = moduleSetupWizardActions

const {
  cancelNextPlan,
  clearFlexibleItems,
  getAll: getAccountMarketplaces,
  getPlans,
  setPlan,
} = amazonCustomerAccountMarketplaceActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccountMarketplace: { flexibleItems, flexibleSize },
    moduleSetupWizard: { productNames },
    translations: { locale },
  } = state

  return {
    flexibleItems,
    flexibleSize,
    language: locale,
    productNames,
    user_id: staffUserIdSelector(state),
    customer_id: customerIdSelector(state),
  }
}

export default connect(mapStateToProps, {
  cancelNextPlan,
  changeStep: toggleModal,
  clearFlexibleItems,
  getAccountMarketplaces,
  getPlans,
  setPlan,
})(SubscriptionUpdateView)
