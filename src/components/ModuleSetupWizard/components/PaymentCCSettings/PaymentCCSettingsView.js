import React, { useCallback, useState } from "react"
import { Input } from "antd"
import { CreditCardOutlined } from "@ant-design/icons"
import { CardElement } from "@stripe/react-stripe-js"
import cn from "classnames"
import isEqual from "lodash/isEqual"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import { Button, PrimaryButton } from "components/shared/Buttons"
import IconPopover from "components/shared/IconPopover"
import Typography from "components/Typography"

import { checkIsArray } from "utils/arrayHelpers"
import { getVirtualEventCategory, pushEvent } from "utils/gtmLoader"
import l from "utils/intl"

import { EVENTS_NAMES } from "consts/gtm"
import { PRODUCTS } from "consts/product"

import styles from "./paymentCCSettings.module.scss"

const PaymentCCSettingsView = ({
  changeStep,
  displayModal,
  elements,
  initialValues,
  setInitialValues,
  stripe,
  updateCreditCard,
  productNames,
  countOfAmazonAccounts,
  isAccountSettings, // is account creating by "+" from accounts or subscriptions page
  isConnectInAccount,
  connectAccountId,
  isBasModuleStarted,
  isBasActive,
  validationDataForVatID,
  updateVatID,
  customerId,
  getCurrentCustomer,
  isSetupBas = true,
  isNeedSkipSelectAccountToNewSubscriptionStep,
  isSetupNewAccount,
  activeAmazonAccountsCount,
  amazonAccountsEnabledForRepricerCount,
  isRedirect = false,
}) => {
  const [errors, setErrors] = useState({})
  const [cardHolder, setCardHolder] = useState(initialValues.cardHolder)
  const [focusOnCard, setFocusOnCard] = useState(false)
  const [cardHasError, setCardHasError] = useState(false)
  const [cardValid, setCardValid] = useState(false)

  const isRepricerHasAdditionalAccounts =
    activeAmazonAccountsCount > 0 &&
    activeAmazonAccountsCount !== amazonAccountsEnabledForRepricerCount

  const nextStepForRepricerWithAccountsCheck = isRepricerHasAdditionalAccounts
    ? "connectChoseAmazonAccount"
    : "connectChoseRegion"

  const nextStep =
    countOfAmazonAccounts > 0
      ? "connectChoseAmazonAccount"
      : "connectChoseRegion"

  const nextStepWithCheckIsAccountSettings = isAccountSettings
    ? "connectChoseRegion"
    : nextStep

  const nextStepWithCheckRepricer = isAccountSettings
    ? "connectChoseRegion"
    : nextStepForRepricerWithAccountsCheck

  const virtualEventCategory = getVirtualEventCategory({
    name: "modalWizardPaymentCCSettings",
    productNames,
  })
  const closeHandler = useCallback(
    () => changeStep(false, undefined, productNames, undefined),
    [changeStep, productNames]
  )
  const onSubmit = useCallback(async () => {
    const {
      billingEmail,
      billingCompanyName,
      billingAddressLine1,
      billingAddressLine2,
      billingCity,
      billingZip,
      billingCountryId,
      useBillingAddress,
      vat_id,
    } = initialValues
    const cardElement = elements.getElement(CardElement)

    const { token: { id: stripeToken } = {}, error } = await stripe.createToken(
      cardElement
    )

    if (error) {
      setErrors({
        card: l(error.message),
      })

      return
    }

    const updateCreditCardHandler = (paramSuccessCallback = null) => {
      return updateCreditCard(
        {
          billingEmail,
          billingCompanyName,
          billingAddressLine1,
          billingAddressLine2,
          billingCity,
          billingZip,
          billingCountryId,
          cardHolder,
          stripeToken: stripeToken,
          useBillingAddress,
        },
        (errors = []) => {
          if (checkIsArray(errors)) {
            const errorsObject = errors.reduce((acc, { field, message }) => {
              acc[field] = message

              return acc
            }, {})

            setErrors(errorsObject)
          } else {
            setErrors(errors)
          }
        },
        () => {
          const canUpdateVatID = isNeedUpdateVatID && !!paramSuccessCallback

          if (!isNeedUpdateVatID) {
            pushEvent({
              vHitNonInteraction: false,
              virtualEventAction: "click-next",
              virtualEventCategory,
              event: EVENTS_NAMES.updEvents,
            })

            const isNewBasAccountWithoutSubscription =
              productNames === PRODUCTS.bas &&
              isBasModuleStarted &&
              !isBasActive &&
              !isSetupNewAccount

            if (isNewBasAccountWithoutSubscription) {
              changeStep(false, undefined, PRODUCTS.bas, { noTrack: true })
              displayModal(true, "basNextSubscription", {
                module: PRODUCTS.bas,
                isSetupBas,
                isRedirect,
                isAccountSettings,
                isConnectInAccount,
                connectAccountId,
                hideRemindLaterButton: true,
                showRemindButtons: false,
                isNeedSkipSelectAccountToNewSubscriptionStep,
              })
            } else if (productNames === PRODUCTS.repricer) {
              changeStep(true, nextStepWithCheckRepricer, productNames, {
                connectAccountId,
                isAccountSettings,
              })
            } else {
              changeStep(
                true,
                nextStepWithCheckIsAccountSettings,
                productNames,
                { connectAccountId }
              )
            }
            setInitialValues(
              {
                ...initialValues,
                cardHolder,
              },
              undefined
            )

            getCurrentCustomer(false, null)
          } else if (canUpdateVatID) {
            paramSuccessCallback()
          }
        }
      )
    }

    const hasCountryChanged = !isEqual(
      billingCountryId,
      validationDataForVatID?.countryID
    )
    const hasVatIDChanged = !isEqual(vat_id, validationDataForVatID?.prevVatID)

    const isNeedUpdateVatID = hasCountryChanged || hasVatIDChanged

    if (isNeedUpdateVatID) {
      updateCreditCardHandler(() => {
        updateVatID(
          { customerID: customerId, vatID: vat_id },
          () => {
            const isNewBasAccountWithoutSubscription =
              productNames === PRODUCTS.bas &&
              isBasModuleStarted &&
              !isBasActive &&
              !isSetupNewAccount

            if (isNewBasAccountWithoutSubscription) {
              changeStep(false, "billingInformation", productNames, {
                error: null,
                noTrack: true,
              })
              changeStep(false, undefined, PRODUCTS.bas, { noTrack: true })
              displayModal(true, "basNextSubscription", {
                module: PRODUCTS.bas,
                isSetupBas,
                isRedirect,
                isAccountSettings,
                isConnectInAccount,
                connectAccountId,
                hideRemindLaterButton: true,
                showRemindButtons: false,
                isNeedSkipSelectAccountToNewSubscriptionStep,
                isSetupNewAccount,
              })
            } else if (productNames === PRODUCTS.repricer) {
              changeStep(false, "billingInformation", productNames, {
                error: null,
                noTrack: true,
              })
              changeStep(true, nextStepWithCheckRepricer, productNames, {
                connectAccountId,
                isAccountSettings,
              })
            } else {
              changeStep(false, "billingInformation", productNames, {
                error: null,
                noTrack: true,
              })
              changeStep(
                true,
                nextStepWithCheckIsAccountSettings,
                productNames,
                { connectAccountId }
              )
            }
          },
          (errors = []) => {
            const errorsObject = errors.reduce((acc, { field, message }) => {
              acc[field] = message

              return acc
            }, {})

            const submitError = { vat_id: errorsObject?.vatId }

            setErrors(submitError)

            changeStep(true, "billingInformation", productNames, {
              error: submitError,
            })
          }
        )
      })
    } else {
      updateCreditCardHandler()
    }
  }, [
    cardHolder,
    changeStep,
    elements,
    initialValues,
    setInitialValues,
    stripe,
    updateCreditCard,
    productNames,
    isAccountSettings,
    countOfAmazonAccounts,
    displayModal,
    isConnectInAccount,
    connectAccountId,
    isBasActive,
    isBasModuleStarted,
    virtualEventCategory,
    isNeedSkipSelectAccountToNewSubscriptionStep,
    isSetupNewAccount,
    isRedirect,
  ])

  if (!initialValues) {
    return null
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.iconWrapper}>
          <CreditCardOutlined className={styles.icon} />
        </div>
        <div className={styles.titleWrapper}>
          <Typography className={styles.title} type="div" variant="textLarge">
            <FormattedMessage id="Payment settings" />
          </Typography>
          <Typography className={styles.subtitle} type="div" variant="text">
            <FormattedMessage id="Please fill your payment information." />
          </Typography>
        </div>
      </div>
      <div className={styles.controlsContainer}>
        <div className={styles.controlContainer}>
          <Typography
            className={cn(styles.label)}
            type="div"
            variant="textLarge"
          >
            <FormattedMessage id="Cardholder name" />
            {" *"}
          </Typography>
          <div className={styles.controlWrapper}>
            <Input
              placeholder={l("Cardholder name")}
              value={cardHolder}
              className={cn(styles.textField, {
                [styles.error]: errors.cardHolder,
              })}
              onChange={({ target: { value } }) => setCardHolder(value)}
            />
            {errors.cardHolder && (
              <>
                <IconPopover
                  className={styles.errorIcon}
                  content={errors.cardHolder}
                  theme="twoTone"
                  twoToneColor="#F89191"
                  type="exclamation-circle"
                />
                <Typography
                  className={styles.errorTextClass}
                  type="div"
                  variant="errorLabel"
                >
                  {errors.cardHolder}
                </Typography>
              </>
            )}
          </div>
        </div>
        <div className={styles.controlContainer}>
          <Typography
            className={cn(styles.label)}
            type="div"
            variant="textLarge"
          >
            <FormattedMessage id="Credit card" />
            {" *"}
          </Typography>
          <div className={styles.controlWrapper}>
            <div
              className={cn(styles.cardWrapper, {
                [styles.cardWrapperFocused]: focusOnCard,
                [styles.error]: cardHasError || errors.card,
              })}
            >
              <CardElement
                options={{
                  style: {
                    base: {
                      iconColor: "#666666",
                      color: "#000",
                      fontWeight: 400,
                      fontFamily: "Roboto, Open Sans, Segoe UI, sans-serif",
                      fontSize: "13px",
                      fontSmoothing: "antialiased",

                      ":-webkit-autofill": {
                        color: "#000",
                      },
                      "::placeholder": {
                        color: "#000",
                      },
                    },
                    invalid: {
                      iconColor: "#F89191",
                      color: "#F89191",
                    },
                  },
                }}
                onBlur={() => setFocusOnCard(false)}
                onFocus={() => setFocusOnCard(true)}
                onChange={({ complete, error }) => {
                  setCardHasError(!!error)
                  setCardValid(!error && complete)

                  if (error) {
                    setErrors({
                      ...errors,
                      card: error.message,
                    })
                  } else {
                    setErrors({
                      ...errors,
                      card: undefined,
                    })
                  }
                }}
              />
            </div>
            {errors.card && (
              <>
                <IconPopover
                  className={styles.errorIcon}
                  content={errors.card}
                  theme="twoTone"
                  twoToneColor="#F89191"
                  type="exclamation-circle"
                />
                <Typography
                  className={styles.errorTextClass}
                  type="div"
                  variant="errorLabel"
                >
                  {errors.card}
                </Typography>
              </>
            )}
          </div>
        </div>
      </div>
      <div className={cn(styles.footer, "wizard-footer")}>
        <Button className={styles.button} onClick={closeHandler}>
          <FormattedMessage id="Cancel" />
        </Button>
        <PrimaryButton
          className={styles.button}
          disabled={!(cardValid && cardHolder)}
          onClick={onSubmit}
        >
          <FormattedMessage id="Next" />
        </PrimaryButton>
      </div>
    </div>
  )
}

PaymentCCSettingsView.propTypes = {
  changeStep: PropTypes.func.isRequired,
  elements: PropTypes.object,
  initialValues: PropTypes.object.isRequired,
  setInitialValues: PropTypes.func.isRequired,
  stripe: PropTypes.object,
  updateCreditCard: PropTypes.func.isRequired,
  productNames: PropTypes.string.isRequired,
  validationDataForVatID: PropTypes.oneOfType([null, PropTypes.object]),
  updateVatID: PropTypes.func,
  customerId: PropTypes.number,
  getCurrentCustomer: PropTypes.func,
}

export default PaymentCCSettingsView
