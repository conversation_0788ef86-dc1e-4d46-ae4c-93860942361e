import { connect } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import amazonCustomerAccountStockActions from "actions/amazonCustomerAccountStockActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import customerActions from "actions/customerActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"
import repricerSubscriptionActions from "actions/repricerSubscriptionActions"

import { amazonAccountMarketplacesSelector } from "selectors/amazonAccountsSelectors"
import { repricerSubscriptionPlansSelector } from "selectors/repricerSubscriptionSelectors"

import CreateAccountView from "./CreateAccountView"

const { toggleModal } = moduleSetupWizardActions
const {
  createAccount,
  displayModal,
  displayAmazonFbaSettingsModal,
  getAll: getAmazonAccounts,
  activateBasAccount,
} = amazonCustomerAccountsActions
const { getCurrentCustomer } = customerActions
const { getBasPlan, getBasSubscriptions } = basCustomerPlanActions
const { getAll: getAmazonCustomerAccountStock } =
  amazonCustomerAccountStockActions
const { getRepricerCustomerPlanSubscriptions, setRepricerCustomerPlan } =
  repricerSubscriptionActions

const mapStateToProps = (state) => {
  const {
    amazonCustomerAccounts: { amazonCustomerAccounts },
    customer: {
      customer: { hasPaymentData },
    },
  } = state

  const amazonAccountMarketplaces = amazonAccountMarketplacesSelector(state)
  const repricerSubscriptionPlans = repricerSubscriptionPlansSelector(state)

  return {
    amazonCustomerAccounts,
    countOfAmazonAccounts: amazonCustomerAccounts.length,
    hasPaymentData,
    repricerSubscriptionPlans,
    amazonAccountMarketplaces,
  }
}

export default connect(mapStateToProps, {
  displayAmazonFbaSettingsModal,
  changeStep: toggleModal,
  createAccount,
  getAmazonAccounts,
  getCurrentCustomer,
  getBasPlan,
  displayModal,
  activateBasAccount,
  getBasSubscriptions,
  getAmazonCustomerAccountStock,
  getRepricerCustomerPlanSubscriptions,
  setRepricerCustomerPlan,
})(CreateAccountView)
