import React from "react"
import { useDispatch, useSelector } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { moduleSetupWizardProductNamesSelector } from "selectors/userSelectors"

import { RepricerSubscriptionSelection } from "components/RepricerSubscriptionSelection"

import { PRODUCTS } from "consts/product"

import { RepricerSubscriptionSelectionOnSuccessParams } from "components/RepricerSubscriptionSelection/RepricerSubscriptionSelectionTypes"

import { RepricerSubscriptionsParams } from "./RepricerSubscriptionsTypes"

const { setExcludeFromRepricer } = amazonCustomerAccountsActions
const { toggleModal: changeStep } = moduleSetupWizardActions

export const RepricerSubscriptions = ({
  accountId,
  connectAccountId,
  isAccountSettings,
  isOnlySubscriptionChoice = false,
  hasSomeIncludedForRepricerAccounts,
}: RepricerSubscriptionsParams) => {
  const dispatch = useDispatch()

  const productNames = useSelector(moduleSetupWizardProductNamesSelector)

  const excludeFromRepricerHandler = ({
    selectedSize,
    paymentModuleVersionId,
    localizedName,
    optimizationPrice,
    price,
    pricePerUnit,
    isFreemiumPlan,
    isYearlyPlan,
  }: RepricerSubscriptionSelectionOnSuccessParams): void => {
    const goToNextStep = () => {
      dispatch(
        changeStep(true, "summary", productNames, {
          accountId,
          connectAccountId,
          isAccountSettings,
          paymentModuleVersionId,
          selectedSize,
          localizedName,
          optimizationPrice,
          price,
          pricePerUnit,
          isFreemiumPlan,
          isYearlyPlan,
          isRepricerSubscriptionChange: productNames === PRODUCTS.repricer,
          repricerCheckedAccountsIds: [],
        })
      )
    }

    if (hasSomeIncludedForRepricerAccounts) {
      goToNextStep()

      return
    }

    dispatch(
      setExcludeFromRepricer({
        id: accountId,
        payload: { value: 0 },
        successCallback: goToNextStep,
      })
    )
  }

  const nextStepHandler = ({
    selectedSize,
    paymentModuleVersionId,
    localizedName,
    optimizationPrice,
    price,
    pricePerUnit,
    isFreemiumPlan,
    isYearlyPlan,
  }: RepricerSubscriptionSelectionOnSuccessParams): void => {
    isOnlySubscriptionChoice
      ? dispatch(changeStep(false, undefined, productNames, { noTrack: true }))
      : excludeFromRepricerHandler({
          selectedSize,
          paymentModuleVersionId,
          localizedName,
          optimizationPrice,
          price,
          pricePerUnit,
          isFreemiumPlan,
          isYearlyPlan,
        })
  }

  return <RepricerSubscriptionSelection isSetPlan onSuccess={nextStepHandler} />
}
