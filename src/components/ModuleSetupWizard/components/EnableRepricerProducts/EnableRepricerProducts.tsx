import React, { useCallback, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Box, Button, Icon, Typography } from "@develop/fe-library"
import cn from "classnames"

import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { countOfAmazonAccountsSelector } from "selectors/amazonAccountsSelectors"
import { customerProductsInfoSelector } from "selectors/customerSelectors"

import { WizardProductSelectBlock } from "components/WizardProductSelectBlock"
import { WizardProductSelectBlockGroup } from "components/WizardProductSelectBlockGroup"

import { pushEvent } from "utils/gtmLoader"
import l from "utils/intl"
import { setItem } from "utils/storage"

import { OFFER_TYPES, PRODUCT_NAMES, PRODUCTS } from "consts/product"

import { OfferType, ToggleModalProps } from "./EnableRepricerProductsTypes"

export const EnableRepricerProducts = () => {
  const dispatch = useDispatch()
  const countOfAmazonAccounts = useSelector(countOfAmazonAccountsSelector)
  const { isSelectedForNewPricing } = useSelector(customerProductsInfoSelector)

  const { toggleModal: changeStep }: ToggleModalProps = moduleSetupWizardActions

  const plainOptions = [OFFER_TYPES.b2c, OFFER_TYPES.b2b]
  const defaultCheckedList: OfferType[] = isSelectedForNewPricing
    ? []
    : [OFFER_TYPES.b2c, OFFER_TYPES.b2b]

  const [checkedRepricerList, setCheckedRepricerList] =
    useState<string[]>(defaultCheckedList)
  const [checkAllRepricers, setCheckAllRepricers] = useState(
    plainOptions.length === defaultCheckedList.length
  )

  const isB2CSelected = checkedRepricerList?.includes(OFFER_TYPES.b2c)
  const isB2BSelected = checkedRepricerList?.includes(OFFER_TYPES.b2b)

  const onChangeRepricerCheckboxHandler = (list: string[]): void => {
    setCheckedRepricerList(list)
    setCheckAllRepricers(!!list.length)
  }

  const onCheckAllRepricerHandler = (isChecked: boolean): void => {
    setCheckedRepricerList(isChecked ? plainOptions : [])
    setCheckAllRepricers(isChecked)
  }

  const repricerB2CConnect = isB2CSelected ? "-repricerB2C" : ""
  const repricerB2BConnect = isB2BSelected ? "-repricerB2B" : ""

  const connectModule = `${repricerB2CConnect}${repricerB2BConnect}`
  const virtualEventCategory = "modalEnableRepricerProducts"

  const closeHandler = useCallback((): void => {
    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: "click-cancel",
      virtualEventCategory,
      event: "updEvents",
    })

    dispatch(changeStep(false, undefined, null, { noTrack: true }))
  }, [changeStep])

  const nextStep = countOfAmazonAccounts
    ? "connectChoseAmazonAccount"
    : "connectChoseRegion"

  const onNext = useCallback((): void => {
    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: `click-next-with${
        connectModule ? connectModule : "-null"
      }`,
      virtualEventCategory,
      event: "updEvents",
    })

    if (isB2CSelected) {
      dispatch(
        changeStep(true, nextStep, PRODUCTS.repricerB2C, {
          selectedRegion: null,
          selectedMarketplaces: null,
          connectAccountId: "",
        })
      )
      setItem("setupRepricerB2B", isB2BSelected, true)
    } else if (isB2BSelected) {
      dispatch(
        changeStep(true, nextStep, PRODUCTS.repricerB2B, {
          selectedRegion: null,
          selectedMarketplaces: null,
          connectAccountId: "",
        })
      )
    } else {
      dispatch(
        changeStep(true, "connectChoseRegion", null, {
          withoutModule: true,
          selectedRegion: null,
          selectedMarketplaces: null,
          connectAccountId: "",
        })
      )
    }
  }, [
    changeStep,
    countOfAmazonAccounts,
    isB2CSelected,
    isB2BSelected,
    connectModule,
    nextStep,
    dispatch,
  ])

  const isButtonDisabled: boolean = !(isB2CSelected || isB2BSelected)

  return (
    <Box flexDirection="column">
      <Box align="start" mLG={{ padding: "l", align: "center" }} padding="m">
        <Box
          align="center"
          backgroundColor="--color-background-second"
          borderRadius="--border-radius-circle"
          justify="center"
          minHeight="60px"
          minWidth="60px"
        >
          <Icon name="icnTeam" size="--icon-size-6" />
        </Box>
        <Box
          justify="space-between"
          marginLeft="m"
          width="100%"
          mLG={{
            marginLeft: "l",
          }}
        >
          <Box flexDirection="column" marginRight="m">
            <Typography variant="--font-headline-3">
              {l("Module selection")}
            </Typography>
            <Box marginTop="m">
              <Typography
                color="--color-text-second"
                variant="--font-body-text-7"
              >
                {l(
                  "Please select SellerLogic module (-s) to connect your Amazon account to:"
                )}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      <WizardProductSelectBlock
        hasBorderTop
        iconName="icnRetweet"
        isChecked={checkAllRepricers}
        isDisabled={isSelectedForNewPricing}
        productName={PRODUCT_NAMES[PRODUCTS.repricer]}
        onChange={onCheckAllRepricerHandler}
      />

      <WizardProductSelectBlockGroup
        checkedValue={checkedRepricerList}
        isDisabled={isSelectedForNewPricing}
        options={plainOptions}
        onChange={onChangeRepricerCheckboxHandler}
      />

      <Box
        className={cn("wizard-footer")}
        padding="0 m m"
        mLG={{
          justify: "flex-end",
          display: "flex",
          flexDirection: "row",
          padding: "m l",
        }}
      >
        <Box
          marginTop="m"
          width="100%"
          mLG={{
            width: "min-content",
          }}
        >
          <Button fullWidth variant="secondary" onClick={closeHandler}>
            {l("Cancel")}
          </Button>
        </Box>

        <Box
          marginLeft="m"
          marginTop="m"
          width="100%"
          mLG={{
            width: "min-content",
          }}
        >
          <Button fullWidth disabled={isButtonDisabled} onClick={onNext}>
            {l("Next")}
          </Button>
        </Box>
      </Box>
    </Box>
  )
}
