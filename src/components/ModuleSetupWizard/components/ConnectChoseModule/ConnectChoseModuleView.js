import React, { use<PERSON><PERSON>back, useMemo, useState } from "react"
import { useSelector } from "react-redux"
import { <PERSON><PERSON>, Box, Button, Icon, Typography } from "@develop/fe-library"
import cn from "classnames"
import PropTypes from "prop-types"

import {
  customerProductsInfoSelector,
  isDemoAccountSelector,
} from "selectors/customerSelectors"

import { WizardProductSelectBlock } from "components/WizardProductSelectBlock"
import { WizardProductSelectBlockGroup } from "components/WizardProductSelectBlockGroup"

import { checkIsArray } from "utils/arrayHelpers"
import { pushEvent } from "utils/gtmLoader"
import l from "utils/intl"
import { setItem } from "utils/storage"

import { restrictPopoverMessages } from "consts"
import { EVENTS_NAMES, PRODUCT_ORDER } from "consts/gtm"
import { OFFER_TYPES, PRODUCT_NAMES, PRODUCTS } from "consts/product"

const ConnectChoseModuleView = ({
  changeStep,
  countOfAmazonAccounts,
  displayModal,
  lostModuleSignedFullService,
  hasPaymentData,
  customer_id,
  user_id,
  isBasModuleStarted,
  isBasActive,
  basCustomerAccounts,
  repricerSubscriptionPlans,
  amazonAccountMarketplaces,
  customerProductsInfo,
  // Delete this after Full Service MVP
  allowLostFullService,
  lostModuleSigned,
}) => {
  const { useNewPricing } = customerProductsInfo

  const isDemoAccount = useSelector(isDemoAccountSelector)
  const { isSelectedForNewPricing } = useSelector(customerProductsInfoSelector)

  const [isRepricerSelected, setIsRepricerSelected] = useState(useNewPricing)
  const [lostSelected, setLostSelected] = useState(true)
  const [basSelected, setBasSelected] = useState(true)

  const plainOptions = [OFFER_TYPES.b2c, OFFER_TYPES.b2b]
  const defaultCheckedList =
    isSelectedForNewPricing || useNewPricing
      ? []
      : [OFFER_TYPES.b2c, OFFER_TYPES.b2b]

  const [checkedRepricerList, setCheckedRepricerList] =
    useState(defaultCheckedList)
  const [checkAllRepricers, setCheckAllRepricers] = useState(
    plainOptions.length === defaultCheckedList.length
  )

  const isB2CSelected = checkedRepricerList?.includes(OFFER_TYPES.b2c)
  const isB2BSelected = checkedRepricerList?.includes(OFFER_TYPES.b2b)

  const isLastActiveSwitch = useMemo(() => {
    const switchers = useNewPricing
      ? [isRepricerSelected, lostSelected, basSelected]
      : [checkAllRepricers, lostSelected, basSelected]

    const activeCount = switchers.filter(Boolean).length

    return activeCount === 1
  }, [
    useNewPricing,
    isRepricerSelected,
    lostSelected,
    basSelected,
    checkAllRepricers,
  ])

  const buildSwitchHandler = useCallback(
    (handler) => (value) => {
      if (!value && isLastActiveSwitch) {
        return
      }

      handler(value)
    },
    [isLastActiveSwitch]
  )

  const onChangeRepricerSwitchHandler = useCallback(
    (list) => {
      if (list.length === 0 && isLastActiveSwitch) {
        return
      }

      setCheckedRepricerList(list)
      setCheckAllRepricers(!!list.length)
    },
    [isLastActiveSwitch]
  )

  const onCheckAllRepricerHandler = useCallback(
    (isChecked) => {
      if (!isChecked && isLastActiveSwitch) {
        return
      }

      setCheckedRepricerList(isChecked ? plainOptions : [])
      setCheckAllRepricers(isChecked)
    },
    [isLastActiveSwitch]
  )

  const onRepricerSwitchChanger = buildSwitchHandler(setIsRepricerSelected)
  const onLostSwitchChanger = buildSwitchHandler(setLostSelected)
  const onBasSwitchChanger = buildSwitchHandler(setBasSelected)

  const isLostFullServiceSetupWithContract =
    !!allowLostFullService && !!lostModuleSignedFullService
  // Delete this  after FULL SERVICE MVP
  const isOldLostWithContract = !allowLostFullService && lostModuleSigned
  const isOldLostWithoutContract = !allowLostFullService && !lostModuleSigned

  const { current, last } = repricerSubscriptionPlans

  const isRepricerTrial =
    !checkIsArray(amazonAccountMarketplaces) && last === null

  const isRepricerHasPlan = isRepricerTrial || current !== null

  const nextRepricerStep =
    !hasPaymentData && !isRepricerHasPlan
      ? "billingInformation"
      : "connectChoseRegion"

  const defaultGTMValue = {
    originalGTMItemName: "",
    newGTMItemName: null,
    newGTMItemId: null,
  }

  const repricerConnect = isRepricerSelected
    ? {
        originalGTMItemName: `${PRODUCTS.repricer}`,
        item_id: PRODUCT_ORDER[PRODUCTS.repricer],
        item_name: PRODUCT_NAMES[PRODUCTS.repricer],
        quantity: 1,
      }
    : defaultGTMValue

  const repricerB2CConnect = isB2CSelected
    ? {
        originalGTMItemName: "-repricerB2C",
        item_id: PRODUCT_ORDER[PRODUCTS.repricerB2C],
        item_name: PRODUCT_NAMES[PRODUCTS.repricerB2C],
        quantity: 1,
      }
    : defaultGTMValue

  const repricerB2BConnect = isB2BSelected
    ? {
        originalGTMItemName: "-repricerB2B",
        item_id: PRODUCT_ORDER[PRODUCTS.repricerB2B],
        item_name: PRODUCT_NAMES[PRODUCTS.repricerB2B],
        quantity: 1,
      }
    : defaultGTMValue

  const lostConnect = lostSelected
    ? {
        originalGTMItemName: "-lost",
        item_id: PRODUCT_ORDER[PRODUCTS.lost],
        item_name: PRODUCT_NAMES[PRODUCTS.lost],
        quantity: 1,
      }
    : defaultGTMValue

  const basConnect = basSelected
    ? {
        originalGTMItemName: "-bas",
        item_id: PRODUCT_ORDER[PRODUCTS.bas],
        item_name: PRODUCT_NAMES[PRODUCTS.bas],
        quantity: 1,
      }
    : defaultGTMValue

  const isConnectAnyProduct =
    isRepricerSelected ||
    isB2CSelected ||
    isB2BSelected ||
    lostSelected ||
    basSelected

  const connectModule = `${repricerConnect.originalGTMItemName}${repricerB2CConnect.originalGTMItemName}${repricerB2BConnect.originalGTMItemName}${lostConnect.originalGTMItemName}${basConnect.originalGTMItemName}`

  const connectModuleForGTM = [
    repricerB2CConnect,
    repricerB2BConnect,
    lostConnect,
    basConnect,
  ]

  const readyGTMData = () => {
    let index = 0

    return connectModuleForGTM?.reduce((acc, product) => {
      const { originalGTMItemName, ...productWitOutOriginalGTMItemName } =
        product

      if (product.item_name) {
        acc = [...acc, { index, ...productWitOutOriginalGTMItemName }]
        index++
      }

      return acc
    }, [])
  }

  const virtualEventCategory = "modalWizardProductSelection"

  const isLostWithoutPaymentButSigned =
    !!allowLostFullService && !hasPaymentData && !!lostModuleSignedFullService

  const closeHandler = useCallback(() => {
    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: "click-cancel",
      virtualEventCategory,
      event: EVENTS_NAMES.updEvents,
    })

    changeStep(false, undefined, null, { noTrack: true })
  }, [changeStep])

  const basCustomerAccountsIncludeInPreviousSubscription = checkIsArray(
    basCustomerAccounts
  )
    ? basCustomerAccounts.filter(
        ({ isIncludeInPreviousSubscription }) => isIncludeInPreviousSubscription
      )
    : null

  const isBasPlanExpiredAndHavePreviousSubscription =
    basSelected &&
    isBasModuleStarted &&
    !isBasActive &&
    checkIsArray(basCustomerAccountsIncludeInPreviousSubscription)

  const onNext = useCallback(() => {
    if (isConnectAnyProduct) {
      pushEvent({ ecommerce: null })

      pushEvent({
        event: EVENTS_NAMES.addToCart,
        customer_id,
        user_id,
        ecommerce: {
          items: readyGTMData(),
        },
      })
    }

    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: `click-next-with${
        connectModule ? connectModule : "-null"
      }`,
      virtualEventCategory,
      event: EVENTS_NAMES.updEvents,
    })

    if (isRepricerSelected) {
      setItem("setupLost", lostSelected, true)
      setItem("setupBas", basSelected, true)
      changeStep(true, nextRepricerStep, PRODUCTS.repricer, {
        selectedRegion: null,
        selectedMarketplaces: null,
        connectAccountId: "",
        isAccountSettings: true,
      })
    } else if (isB2CSelected) {
      setItem("setupRepricerB2B", isB2BSelected, true)
      setItem("setupLost", lostSelected, true)
      setItem("setupBas", basSelected, true)
      changeStep(true, "connectChoseRegion", PRODUCTS.repricerB2C, {
        selectedRegion: null,
        selectedMarketplaces: null,
        connectAccountId: "",
      })
    } else if (isB2BSelected) {
      setItem("setupLost", lostSelected, true)
      setItem("setupBas", basSelected, true)

      changeStep(true, "connectChoseRegion", PRODUCTS.repricerB2B, {
        selectedRegion: null,
        selectedMarketplaces: null,
        connectAccountId: "",
      })
    } else if (lostSelected) {
      setItem("setupRepricerB2B", isB2BSelected, true)
      setItem("setupBas", basSelected, true)

      // isOldLostWithContract should be deleted after Full-Service MVP
      if (isLostFullServiceSetupWithContract || isOldLostWithContract) {
        changeStep(
          true,
          !hasPaymentData ? "billingInformation" : "connectChoseRegion",
          PRODUCTS.lost,
          {
            selectedRegion: null,
            selectedMarketplaces: null,
            isAccountSettings: true,
          }
        )
      } else {
        if (isLostWithoutPaymentButSigned) {
          changeStep(true, "billingInformation", PRODUCTS.lost, {
            selectedRegion: null,
            selectedMarketplaces: null,
            isAccountSettings: true,
          })
        } else if (isOldLostWithoutContract) {
          // Delete this  after FULL SERVICE MVP
          changeStep(false, undefined, PRODUCTS.lost, { noTrack: true })
          displayModal(true, "lostFoundProductInformationMVP", {
            countOfAmazonAccounts,
            enable: true,
            isAccountSettings: true,
          })
        } else {
          changeStep(false, undefined, PRODUCTS.lost, { noTrack: true })
          displayModal(true, "lostFoundProductInformation", {
            countOfAmazonAccounts,
            enable: true,
            isAccountSettings: true,
          })
        }
        setItem("setupBas", basSelected, true)
      }
    } else if (basSelected) {
      setItem("setupRepricerB2B", isB2BSelected, true)
      setItem("setupLost", lostSelected, true)

      changeStep(false, undefined, PRODUCTS.bas, { noTrack: true })
      displayModal(true, "basProductInformation", {
        countOfAmazonAccounts,
        enable: true,
        isAccountSettings: true,
        isNeedSkipSelectAccountToNewSubscriptionStep: true,
        isSetupNewAccount: true,
      })
    } else {
      setItem("setupRepricerB2B", isB2BSelected, true)
      setItem("setupLost", lostSelected, true)
      setItem("setupBas", basSelected, true)

      changeStep(true, "connectChoseRegion", null, {
        withoutModule: true,
        selectedRegion: null,
        selectedMarketplaces: null,
        connectAccountId: "",
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    changeStep,
    countOfAmazonAccounts,
    displayModal,
    lostModuleSignedFullService,
    isRepricerSelected,
    lostSelected,
    isB2CSelected,
    isB2BSelected,
    basSelected,
    hasPaymentData,
    isBasPlanExpiredAndHavePreviousSubscription,
  ])

  const isAlertVisible =
    !isRepricerSelected &&
    !lostSelected &&
    !isB2CSelected &&
    !isB2BSelected &&
    !basSelected

  const onNextHandler = () => {
    isBasPlanExpiredAndHavePreviousSubscription
      ? changeStep(true, "connectAmazonAccountsForBASOnlyInfo", PRODUCTS.bas, {
          onNextStep: onNext,
          onCancel: () =>
            changeStep(true, "connectChoseModule", undefined, undefined),
        })
      : onNext()
  }

  return (
    <Box flexDirection="column">
      <Box align="start" mLG={{ padding: "l", align: "center" }} padding="m">
        <Box
          align="center"
          backgroundColor="--color-background-second"
          borderRadius="--border-radius-circle"
          justify="center"
          minHeight="60px"
          minWidth="60px"
        >
          <Icon name="icnTeam" size="--icon-size-6" />
        </Box>
        <Box
          justify="space-between"
          marginLeft="m"
          width="100%"
          mLG={{
            marginLeft: "l",
          }}
        >
          <Box flexDirection="column" marginRight="m">
            <Typography variant="--font-headline-3">
              {l("Module selection")}
            </Typography>
            <Box marginTop="m">
              <Typography
                color="--color-text-second"
                variant="--font-body-text-7"
              >
                {l(
                  "Please select SellerLogic module (-s) to connect your Amazon account to:"
                )}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>

      {useNewPricing ? (
        <WizardProductSelectBlock
          hasBorderTop
          iconName="icnRetweet"
          isChecked={isRepricerSelected}
          productName={PRODUCT_NAMES[PRODUCTS.repricer]}
          onChange={onRepricerSwitchChanger}
        />
      ) : (
        <Box flexDirection="column">
          <WizardProductSelectBlock
            hasBorderTop
            iconName="icnRetweet"
            isChecked={checkAllRepricers}
            isDisabled={isSelectedForNewPricing}
            productName={PRODUCT_NAMES[PRODUCTS.repricer]}
            restrictedSwitchMessage={restrictPopoverMessages.transferToNewPrice}
            onChange={onCheckAllRepricerHandler}
          />
          <WizardProductSelectBlockGroup
            checkedValue={checkedRepricerList}
            isDisabled={isSelectedForNewPricing}
            options={plainOptions}
            restrictedSwitchMessage={restrictPopoverMessages.transferToNewPrice}
            onChange={onChangeRepricerSwitchHandler}
          />
        </Box>
      )}
      <WizardProductSelectBlock
        iconName="icnFileSearch"
        isChecked={lostSelected}
        productName={PRODUCT_NAMES[PRODUCTS.lost]}
        onChange={onLostSwitchChanger}
      />
      {!isDemoAccount ? (
        <WizardProductSelectBlock
          iconName="icnPieChart"
          isChecked={basSelected}
          productName={PRODUCT_NAMES[PRODUCTS.bas]}
          onChange={onBasSwitchChanger}
        />
      ) : null}
      {isAlertVisible ? (
        <Box display="block">
          <Alert
            alertType="warning"
            message={l(
              "No SellerLogic module will be connected to your Amazon account."
            )}
          />
        </Box>
      ) : null}
      <Box
        className={cn("wizard-footer")}
        hasBorder={{ top: isAlertVisible }}
        padding="0 m m"
        mLG={{
          justify: "flex-end",
          display: "flex",
          flexDirection: "row",
          padding: "m l",
        }}
      >
        <Box
          marginTop="m"
          width="100%"
          mLG={{
            width: "min-content",
          }}
        >
          <Button fullWidth variant="secondary" onClick={closeHandler}>
            {l("Cancel")}
          </Button>
        </Box>

        <Box
          marginLeft="m"
          marginTop="m"
          width="100%"
          mLG={{
            width: "min-content",
          }}
        >
          <Button fullWidth onClick={onNextHandler}>
            {l("Next")}
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

ConnectChoseModuleView.propTypes = {
  changeStep: PropTypes.func.isRequired,
  countOfAmazonAccounts: PropTypes.number.isRequired,
  displayModal: PropTypes.func.isRequired,
  lostModuleSignedFullService: PropTypes.bool.isRequired,
}

export default ConnectChoseModuleView
