import { connect } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import amazonMarketplacesActions from "actions/amazonMarketplacesActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { amazonAccountItemsSelector } from "selectors/amazonAccountsSelectors"
import { customerIdSelector } from "selectors/customerSelectors"
import { staffUserIdSelector } from "selectors/staffSelectors"

import SummaryView from "./SummaryView"

const { toggleModal } = moduleSetupWizardActions
const { displayModal, clearNewAccounts } = amazonCustomerAccountsActions

const { getAll } = amazonMarketplacesActions

const mapStateToProps = (state) => {
  const {
    customer: {
      customer: {
        allow_lost_full_service,
        //   Delete this after FULL SERVICE MVP
        lost_module_signed,
        //
        lost_module_signed_full_service,
        hasPaymentData,
      },
    },
    moduleSetupWizard: { productNames },
    amazonCustomerAccounts: { newAccounts },
  } = state

  return {
    accounts: amazonAccountItemsSelector(state),
    newAccounts,
    productNames,
    //   Delete this after FULL SERVICE MVP
    lostModuleSigned: lost_module_signed,
    //
    lostModuleSignedFullService: lost_module_signed_full_service,
    hasPaymentData,
    allowLostFullService: allow_lost_full_service,
    user_id: staffUserIdSelector(state),
    customer_id: customerIdSelector(state),
  }
}

export default connect(mapStateToProps, {
  changeStep: toggleModal,
  getMarketplaces: getAll,
  displayModal,
  clearNewAccounts,
})(SummaryView)
