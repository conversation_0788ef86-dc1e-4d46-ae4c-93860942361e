import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Badge, Box, Flag, Typography } from "@develop/fe-library"
import { checkIsArray } from "@develop/fe-library/dist/utils"

import { subscriptionActiveAmazonAccountsSelector } from "selectors/amazonAccountsSelectors"
import { moduleSetupWizardProductNamesSelector } from "selectors/userSelectors"

import l from "utils/intl"

import { PRODUCT_NAMES } from "consts/product"

import { RepricerAccountsListProps } from "./RepricerAccountsListTypes"
import { ProductType } from "types/ProductsTypes"
import {
  SubscriptionActiveAmazonAccounts,
  SubscriptionAmazonAccount,
} from "types/SubscriptionActiveAmazonAccountsTypes"

export const RepricerAccountsList = ({
  repricerCheckedAccountsIds = [],
  newConnectedAccountId,
}: RepricerAccountsListProps) => {
  const subscriptionActiveAmazonAccounts: SubscriptionActiveAmazonAccounts =
    useSelector(subscriptionActiveAmazonAccountsSelector)
  const productName: ProductType = useSelector(
    moduleSetupWizardProductNamesSelector
  )

  const currentProductAccounts = useMemo(() => {
    return subscriptionActiveAmazonAccounts[productName]
  }, [subscriptionActiveAmazonAccounts, productName])

  const newConnectedAccount = useMemo(
    () => currentProductAccounts.find(({ id }) => newConnectedAccountId === id),
    [currentProductAccounts, newConnectedAccountId]
  )

  const oldConnectedAccounts = useMemo(
    () =>
      currentProductAccounts.filter(({ id }) => newConnectedAccountId !== id),
    [currentProductAccounts, newConnectedAccountId]
  )

  const newAddedAccounts = useMemo(
    () =>
      currentProductAccounts.filter(
        ({ id }) =>
          repricerCheckedAccountsIds.includes(id) &&
          newConnectedAccountId !== id
      ),
    [currentProductAccounts, repricerCheckedAccountsIds, newConnectedAccountId]
  )

  const oldAddedAccounts = useMemo(
    () =>
      currentProductAccounts.filter(
        ({ id }) =>
          !repricerCheckedAccountsIds.includes(id) &&
          newConnectedAccountId !== id
      ),
    [currentProductAccounts, repricerCheckedAccountsIds, newConnectedAccountId]
  )

  const accountsList: SubscriptionAmazonAccount[] = useMemo(() => {
    const connectedAccountArray = newConnectedAccount
      ? [newConnectedAccount]
      : []

    return checkIsArray(repricerCheckedAccountsIds)
      ? [...newAddedAccounts, ...oldAddedAccounts]
      : [...connectedAccountArray, ...oldConnectedAccounts]
  }, [
    newConnectedAccount,
    repricerCheckedAccountsIds,
    newAddedAccounts,
    oldAddedAccounts,
    oldConnectedAccounts,
  ])

  return (
    <Box flexDirection="column" gap="m" width="100%">
      <Typography color="--color-text-main" variant="--font-body-text-7">
        {l("{productName} is enabled for the following accounts", {
          productName: PRODUCT_NAMES[productName],
        })}
        :
      </Typography>

      <Box flexDirection="column" width="100%">
        {checkIsArray(accountsList)
          ? accountsList.map(
              ({
                id,
                homeMarketplace,
                allowedMarketplaces,
                accountName,
                sellerId,
              }) => {
                return (
                  <Box
                    key={accountName}
                    flexDirection="column"
                    gap="m"
                    padding="m"
                    hasBorder={{
                      top: true,
                    }}
                    tb={{
                      padding: "l",
                    }}
                  >
                    <Box
                      flexDirection="column"
                      gap="m"
                      tb={{
                        flexDirection: "row",
                        align: "center",
                      }}
                    >
                      <Box tb={{ width: 180 }} width="100%">
                        <Typography variant="--font-body-text-7">
                          {l("Account name")}:
                        </Typography>
                      </Box>

                      <Box align="center" flexDirection="row" gap="m">
                        <Typography
                          color="--color-text-second"
                          variant="--font-body-text-9"
                        >
                          {accountName}
                        </Typography>

                        {repricerCheckedAccountsIds.includes(id) ? (
                          <Badge content={l("Added account")} size="m" />
                        ) : null}

                        {newConnectedAccountId === id ? (
                          <Badge content={l("New account")} size="m" />
                        ) : null}
                      </Box>
                    </Box>

                    <Box
                      flexDirection="column"
                      gap="m"
                      tb={{
                        flexDirection: "row",
                      }}
                    >
                      <Box tb={{ width: 180 }} width="100%">
                        <Typography variant="--font-body-text-7">
                          {l("SellerID")}:
                        </Typography>
                      </Box>
                      <Typography
                        color="--color-text-second"
                        variant="--font-body-text-9"
                      >
                        {sellerId}
                      </Typography>
                    </Box>
                    <Box
                      flexDirection="column"
                      gap="m"
                      tb={{
                        flexDirection: "row",
                      }}
                    >
                      <Box tb={{ width: 180 }} width="100%">
                        <Typography variant="--font-body-text-7">
                          {l("Home Marketplace")}:
                        </Typography>
                      </Box>

                      <Flag
                        key={homeMarketplace?.extension}
                        borderRadius="--border-radius-circle"
                        locale={homeMarketplace?.extension}
                        size={20}
                      />
                    </Box>

                    <Box
                      flexDirection="column"
                      gap="m"
                      tb={{
                        flexDirection: "row",
                      }}
                    >
                      <Box tb={{ width: 180 }} width="100%">
                        <Typography variant="--font-body-text-7">
                          {l("Marketplaces in {productName}", {
                            productName: PRODUCT_NAMES[productName],
                          })}
                          :
                        </Typography>
                      </Box>

                      <Box flexWrap="wrap" gap="s">
                        {allowedMarketplaces?.map(({ extension }) => (
                          <Flag
                            key={extension}
                            borderRadius="--border-radius-circle"
                            locale={extension}
                            size={20}
                          />
                        )) ?? null}
                      </Box>
                    </Box>
                  </Box>
                )
              }
            )
          : null}
      </Box>
    </Box>
  )
}
