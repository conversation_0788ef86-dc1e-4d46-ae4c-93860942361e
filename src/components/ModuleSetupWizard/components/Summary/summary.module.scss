@import "assets/styles/variables.scss";

.container {
  padding: var(--padding-l) var(--padding-l) 0;
}

.footer {
  border-top: var(--border-main);
  display: flex;
  justify-content: flex-end;
  margin-top: var(--margin-l);
  padding: var(--padding-m) var(--padding-l);
  transform: translateX(-20px);
  width: calc(100% + 40px);
}

.button {
  margin-right: var(--margin-m);

  &:last-child {
    margin-right: 0;
  }
}

.warningIcon {
  margin-right: var(--margin-s);
}

.warningNotification {
  align-items: center;
  background-color: var(--color-alert-background-warning);
  border-radius: var(--border-radius);
  display: flex;
  margin-top: var(--margin-l);
  padding: var(--padding-m);

  .warningIcon {
    margin-right: var(--margin-m);
  }
}

@media all and (max-width: $xs) {
  .container {
    padding: var(--padding-m) var(--padding-m) 0;
  }

  .footer {
    flex-wrap: wrap;
    margin-top: var(--margin-m);
    padding: var(--padding-m);
    transform: translateX(-10px);
    width: calc(100% + 20px);
  }

  .button {
    flex-basis: 100%;
    flex-grow: 1;
    margin-bottom: var(--margin-m);
    margin-right: 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .warningNotification {
    align-items: flex-start;
    margin-top: var(--margin-m);
  }
}
