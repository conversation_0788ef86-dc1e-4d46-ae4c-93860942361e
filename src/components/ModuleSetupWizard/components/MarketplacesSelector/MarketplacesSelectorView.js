import React from "react"
import { Checkbox } from "antd"
import { Box, Flag, Grid, Popover, Typography } from "@develop/fe-library"
import cn from "classnames"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"

import { PRODUCTS, REVERSE_REPRICER_FIND_OFFER_TYPE } from "consts/product"

import styles from "./marketplacesSelector.module.scss"

const MarketplacesSelectorView = ({
  canSelectRegion,
  disabled,
  marketplaces = [],
  onSelectMarketplace,
  onSelectRegion,
  region,
  regionLabel,
  selectedMarketplaces,
  selectedRegion,
  productNames,
  repricerFoundMarketplace = [],
}) => {
  const selectedRegionHandler = () => onSelectRegion(region)
  const selectedMarketplaceHandler =
    ({ id }) =>
    () =>
      onSelectMarketplace(region, id)

  const isOldRepricer =
    productNames === PRODUCTS.repricerB2C ||
    productNames === PRODUCTS.repricerB2B

  return (
    <div className={styles.container}>
      <Box align="center">
        {canSelectRegion ? (
          <Checkbox
            checked={selectedRegion === region}
            className={styles.checkbox}
            onChange={selectedRegionHandler}
          />
        ) : null}
        <Typography variant="--font-body-text-2">
          <FormattedMessage id={regionLabel} />
        </Typography>
      </Box>
      <Box flexDirection="column" marginTop="m">
        <Grid container columnGap="m" rowGap="m">
          {marketplaces.map(({ id, country }) => {
            const isMarketplaceSelectable = selectedMarketplaces?.includes(id)

            const isRepricerProductEnabledInAnotherProduct =
              repricerFoundMarketplace.includes(id)

            const isLightAnotherProductInRepricer =
              isOldRepricer && isRepricerProductEnabledInAnotherProduct

            return (
              <Grid
                key={country}
                item
                mLG={4}
                mSM={6}
                tb={3}
                onClick={selectedMarketplaceHandler({ id })}
              >
                <Box
                  align="center"
                  justify={"space-between"}
                  padding="m"
                  className={cn(styles.marketplaceItem, {
                    [styles.disabled]: disabled,
                    [styles.selected]: isMarketplaceSelectable,
                  })}
                >
                  <Box align="center">
                    <Flag
                      borderRadius="--border-radius-circle"
                      locale={country.toLowerCase()}
                      size={24}
                      className={cn(styles.flagIcon, {
                        [styles.selectedFlagIcon]: isMarketplaceSelectable,
                      })}
                    />
                    <Typography
                      variant="--font-body-text-7"
                      className={cn({
                        [styles.selectedText]: isMarketplaceSelectable,
                        [styles.disabledText]: disabled,
                      })}
                    >
                      {country}
                    </Typography>
                  </Box>
                  {isLightAnotherProductInRepricer ? (
                    <Popover
                      content={`This marketplace is connected to ${REVERSE_REPRICER_FIND_OFFER_TYPE[productNames]}`}
                      maxWidth={176}
                      placement="bottom"
                    >
                      <Box
                        align="center"
                        justify="center"
                        className={cn(styles.productMark, {
                          [styles.selectedProductMark]: isMarketplaceSelectable,
                          [styles.disabledProductMark]: disabled,
                        })}
                      >
                        <Typography
                          color="--color-text-white"
                          variant="--font-body-text-11"
                          className={cn({
                            [styles.selectedProductMarkText]:
                              isMarketplaceSelectable,
                            [styles.disabledProductMarkText]: disabled,
                          })}
                        >
                          {REVERSE_REPRICER_FIND_OFFER_TYPE[productNames]}
                        </Typography>
                      </Box>
                    </Popover>
                  ) : null}
                </Box>
              </Grid>
            )
          })}
        </Grid>
      </Box>
    </div>
  )
}

MarketplacesSelectorView.propTypes = {
  canSelectRegion: PropTypes.bool,
  disabled: PropTypes.bool,
  marketplaces: PropTypes.array.isRequired,
  onSelectMarketplace: PropTypes.func.isRequired,
  onSelectRegion: PropTypes.func,
  region: PropTypes.string.isRequired,
  regionLabel: PropTypes.string.isRequired,
  selectedMarketplaces: PropTypes.array.isRequired,
  selectedRegion: PropTypes.string,
}

export default MarketplacesSelectorView
