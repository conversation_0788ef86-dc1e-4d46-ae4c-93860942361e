@import "assets/styles/variables.scss";

.container {
  margin-top: var(--margin-l);

  &:first-child {
    margin-top: var(--margin-xl);
  }
}

.checkbox {
  margin-right: var(--margin-m);
}

.marketplaceItem {
  border: var(--border-main);
  border-radius: var(--border-radius);
  cursor: pointer;

  &:hover {
    border-color: var(--color-int-on-active);
  }

  &.selected {
    background-color: var(--color-int-on-active);
    border-color: var(--color-int-on-active);
  }

  &.disabled {
    background-color: var(--color-background-disable);
  }
}

.disabledText.disabledText {
  color: var(--color-text-disable);
}

.productMark {
  height: 19px;
  width: 27px;
  border-radius: var(--border-radius);
  background-color: var(--color-badge-1);
}

.selectedProductMark {
  background-color: var(--color-int-background-second);
}

.disabledProductMark {
  background-color: var(--color-badge-6);
}

.selectedProductMarkText.selectedProductMarkText {
  color: var(--color-text-second);
}

.selectedText.selectedText {
  color: var(--color-text-white);
}

.disabledProductMarkText.disabledProductMarkText {
  color: var(--color-text-second);
}

.flagIcon {
  margin-right: var(--margin-m);
}

.selectedFlagIcon {
  border: solid 1px var(--color-int-off-disable);
}

@media all and (max-width: $xs) {
  .container {
    margin-top: var(--margin-l);
  }
}
