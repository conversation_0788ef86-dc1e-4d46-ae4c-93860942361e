import React, { useCallback, useEffect, useRef, useState } from "react"
import { with<PERSON>out<PERSON> } from "react-router-dom"
import { ArrowLeftOutlined } from "@ant-design/icons"
import { Alert, Box, Button, Modal, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import {
  getObjectKeys,
  getUrlSearchParams,
} from "@develop/fe-library/dist/utils"
import cn from "classnames"
import moment from "moment"
import PropTypes from "prop-types"

import config from "config"

import { ValueNotifier } from "components/Dashboard/components/valueNotifier/ValueNotifier"
import styles from "components/Dashboard/dashboard.module.scss"
import { DashboardLayout } from "components/DashboardLayout"
import ErrorNotification from "components/ErrorNotification"
import FormattedMessage from "components/FormattedMessage"
import { RestrictedPrimaryButton } from "components/shared/Buttons"
import { ConfirmModal } from "components/shared/Modal"

import useAccess from "hooks/useAccess"
import { useStartSetupWizard } from "hooks/useStartSetupWizard"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"
import ln from "utils/localeNumber"
import { checkReminderDate } from "utils/paymentMethodVerification"
import { replaceLanguageCode } from "utils/replaceLanguageCode"

import {
  ALERT_KEYS_FROM_USER_SETTINGS,
  INVITE_MODAL_STATUS_VALUES,
  permissionKeys,
} from "consts"
import { NAV_LINKS_IDS, NAV_MODULES_LINKS_TYPES } from "consts/navigation"
import { PERMISSIONS_CONTENT } from "consts/permissions"
import { PRODUCTS } from "consts/product"
import { USER_ROLE_SIDES } from "consts/user"

import { useDemoAccountConfirmModal } from "./hooks"

import { ADS_ACCOUNT_REMINDER_MODAL_ZINDEX, ReminderKeys } from "./constants"

import DashboardPanel from "./components/DashboardPanel"

import { InviteUserModal } from "../InviteUserModal"

const { isCrmDisabled } = config

const {
  REPRICER_ROUTES: { PATH_REPRICER_DASHBOARD },
  LOST_ROUTES: { PATH_LOST_DASHBOARD, PATH_LOST_DATA_IS_GATHERED_FOR_ACCOUNT },
  BAS_ROUTES: { PATH_BAS_DASHBOARD },
  CRM_ROUTES: { PATH_CRM_CONTACTS },
  SERVICE_DESK_ROUTES: { PATH_SERVICE_DESK_TASKS },
  GENERAL_ROUTES: {
    PATH_PAYMENT_SETTINGS_BILLING_INFORMATION,
    PATH_PAYMENT_SETTINGS,
    PATH_AMAZON_ADS_ACCOUNTS,
  },
} = ROUTES

const DashboardView = ({
  badges,
  countOfAmazonAccounts,
  displayModal,
  hasError,
  useRepricerModule,
  useRepricerModuleB2C,
  useRepricerModuleB2B,
  useLostModule,
  useBasModule,
  navLinks,
  onClose,
  showModuleSetupWizard,
  totalLostCaseCount,
  lostModuleSigned,
  lostModuleSignedFullService,
  allowLostFullService,
  ready,
  nonLostCaseCount,
  nonLostCaseEstimatedAmount,
  history,
  sellerUser,
  viewMode,
  productLoaded,
  isNeedToUpdateBillingSettings,
  getUserSettings,
  updateUserSettings,
  paymentMethodReminder,
  expiredCCReminder = { value: "" },
  amazonAccountConnectionReminder = { value: "" },
  isCCDataCloseToExpired,
  isLoadingFinished,
  isCCPayment,
  roleSide,
  canManage,
  status,
  hasPaymentData,
  getCurrentCustomer,
  isDemoAccount,
  isAllowToSetup,
  canStaffManage,
  saveUserSettings,
  inviteModalStatus,
  locale,
  isLostCustomerCounterLoaded,
  nonLostCaseCurrency = "",
  reimbursementTransactionCount,
  user_id,
  customer_id,
  bas_module_started,
  isBasSubscriptionExpired,
  hasConnectedAmazonAdAccounts,
  areWizardsModalsVisible,
  isFreemiumBasEnabled,
  isSelectedForNewPricing,
  customerProductsInfo,
  getRepricerCustomerPlanSubscriptions,
}) => {
  const startSetupWizard = useStartSetupWizard()
  const { useNewPricing } = customerProductsInfo

  const isRepricerConnected = useNewPricing
    ? useRepricerModule
    : useRepricerModuleB2C || useRepricerModuleB2B

  const showBASBlock = navLinks.BusinessAnalytics && !isDemoAccount
  const isLostReimbursementEnabled = !useLostModule && nonLostCaseCount > 0
  const isLostSetupModalOpened = useRef(false)

  const day = moment().add(24, "hours")
  const [isInviteModalVisible, setInviteModalVisible] = useState(true)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isShowPaymentConfirmation, setIsShowPaymentConfirmation] =
    useState(true)
  const [isShowCCExpiresModal, setIsShowCCExpiresModal] = useState(true)

  const hasAmazonAdsAccountManagePermission = useAccess(
    permissionKeys.amazonAdsAccountManage
  )

  const urlSearchParams = getUrlSearchParams({
    locationSearch: document.location.search,
  })
  const { openWizardSetupModal } = urlSearchParams

  const hasBasLinks = !!navLinks[NAV_MODULES_LINKS_TYPES.BusinessAnalytics]

  const closePaymentConfirmationHandler = () =>
    setIsShowPaymentConfirmation(false)

  const closeCCExpiresModalHandler = () => setIsShowCCExpiresModal(false)

  useDemoAccountConfirmModal()
  useEffect(() => {
    getUserSettings(
      { key: ReminderKeys.expiredCC, defaultSettings: "", cache: false },
      undefined
    )
    getUserSettings(
      {
        key: ReminderKeys.verifyPaymentMethod,
        defaultSettings: "",
        cache: false,
      },
      undefined
    )
    getUserSettings(
      {
        key: ReminderKeys.amazonAccountConnection,
        defaultSettings: "",
        cache: false,
      },
      undefined
    )
    getUserSettings(
      {
        key: ALERT_KEYS_FROM_USER_SETTINGS.ALERTS_MAIN_KEY,
        defaultSettings: null,
        cache: false,
      },
      undefined
    )
  }, [getUserSettings])

  useEffect(() => {
    if (isLoadingFinished) {
      setIsLoaded(isLoadingFinished)
    }
  }, [isLoadingFinished])

  useEffect(() => {
    switch (openWizardSetupModal) {
      case PRODUCTS.repricer: {
        useNewPricing
          ? getRepricerCustomerPlanSubscriptions({
              successCallback: () =>
                startSetupWizard({
                  productName: PRODUCTS.repricer,
                  deleteUrlParam: "openWizardSetupModal",
                }),
            })
          : startSetupWizard({
              productName: PRODUCTS.repricer,
              deleteUrlParam: "openWizardSetupModal",
            })
        break
      }
      case PRODUCTS.lost: {
        startSetupWizard({
          productName: PRODUCTS.lost,
          deleteUrlParam: "openWizardSetupModal",
        })
        break
      }
      case PRODUCTS.LOST_FULL_SERVICE_SETUP: {
        startSetupWizard({
          productName: PRODUCTS.LOST_FULL_SERVICE_SETUP,
          deleteUrlParam: "openWizardSetupModal",
        })
        break
      }
      case PRODUCTS.bas: {
        startSetupWizard({
          productName: PRODUCTS.bas,
          deleteUrlParam: "openWizardSetupModal",
        })
        break
      }
      default: {
        if (openWizardSetupModal) {
          const urlParams = new URLSearchParams(document.location.search)

          urlParams.delete("openWizardSetupModal")

          history.replace({
            search: urlParams.toString(),
          })
        }
      }
    }
  }, [history, openWizardSetupModal, startSetupWizard])

  const lostCountText =
    nonLostCaseCount === 1
      ? "We have noticed that you currently have a total of <strong>1 case</strong> with an expected and unused reimbursement potential of"
      : "We have noticed that you currently have a total of <strong>{cases} cases</strong> with an expected and unused reimbursement potential of"

  const onBlockLinkClick = useCallback(() => {
    if (!isAllowToSetup) {
      return
    }

    if ((sellerUser && viewMode !== "user") || status === "soft_block") {
      history.push(PATH_LOST_DASHBOARD)

      return
    }
    const routesWizard =
      countOfAmazonAccounts > 0
        ? "connectChoseAmazonAccount"
        : "connectChoseRegion"

    const isLostFullServiceSetupWithoutContract =
      allowLostFullService && !lostModuleSignedFullService
    //   Delete this after FULL SERVICE MVP
    const isOldLostWithoutContract = !allowLostFullService && !lostModuleSigned

    if (isLostFullServiceSetupWithoutContract) {
      displayModal(true, "lostFoundProductInformation", {
        countOfAmazonAccounts,
        enable: true,
      })
    } else if (isOldLostWithoutContract) {
      //   Delete this after FULL SERVICE MVP
      displayModal(true, "lostFoundProductInformationMVP", {
        countOfAmazonAccounts,
        enable: true,
      })
    } else {
      showModuleSetupWizard(true, routesWizard, PRODUCTS.lost, {
        selectedRegion: null,
        selectedMarketplaces: null,
        connectAccountId: "",
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    lostModuleSignedFullService,
    countOfAmazonAccounts,
    displayModal,
    history,
    lostModuleSigned,
    lostModuleSignedFullService,
    allowLostFullService,
    sellerUser,
    showModuleSetupWizard,
    viewMode,
    status,
  ])

  const shouldOpenLostSetupModal =
    isLoaded &&
    isLostReimbursementEnabled &&
    history.location.hash === "#shouldOpenLostSetupModal" &&
    isLostSetupModalOpened.current === false

  useEffect(() => {
    if (shouldOpenLostSetupModal) {
      onBlockLinkClick()
      isLostSetupModalOpened.current = true
    }
  }, [shouldOpenLostSetupModal, onBlockLinkClick])

  if (!isLostCustomerCounterLoaded) {
    return null
  }

  const redirectToPaymentSettings = () => {
    window.location.href = PATH_PAYMENT_SETTINGS
  }

  const handleVerifyPaymentReminder = () => {
    const reminderId = paymentMethodReminder?.id
    const action = reminderId ? updateUserSettings : saveUserSettings
    const payload = reminderId
      ? {
          id: reminderId,
          settings: day,
        }
      : {
          key: ReminderKeys.verifyPaymentMethod,
          settings: day,
        }

    action(payload, null, null)
  }

  const handleVerifyPayment = () => {
    isCCPayment && canManage
      ? (window.location.href = PATH_PAYMENT_SETTINGS_BILLING_INFORMATION)
      : (window.location.href = PATH_PAYMENT_SETTINGS)
  }

  const handleCCReminder = () => {
    const reminderId = expiredCCReminder?.id
    const action = reminderId ? updateUserSettings : saveUserSettings
    const payload = reminderId
      ? {
          id: reminderId,
          settings: day,
        }
      : { key: ReminderKeys.expiredCC, settings: day }

    action(payload, null, null)
  }

  const buildAdsConnectionReminderHandler =
    (isForever = false) =>
    () => {
      const day = isForever
        ? moment().add(100, "years")
        : moment().add(24, "hours")

      const reminderId = amazonAccountConnectionReminder?.id

      const action = reminderId ? updateUserSettings : saveUserSettings

      const payload = reminderId
        ? { id: reminderId, settings: day }
        : { key: ReminderKeys.amazonAccountConnection, settings: day }

      const successCallback = () => {
        getUserSettings(
          {
            key: ReminderKeys.amazonAccountConnection,
            defaultSettings: "",
            cache: false,
          },
          undefined
        )
      }

      action(payload, successCallback, null)
    }

  const handleAdsConnectionOk = () => {
    window.location.href = PATH_AMAZON_ADS_ACCOUNTS
  }

  const isLostDisabled =
    !useLostModule && (lostModuleSigned || lostModuleSignedFullService)
  const isLostWithoutCases = useLostModule && totalLostCaseCount === 0

  if (hasError || (ready && getObjectKeys(navLinks).length === 0)) {
    return (
      <div
        className={cn(styles.errorContainer, { [styles.closable]: onClose })}
      >
        {onClose && (
          <div className={styles.closeWrapper} onClick={onClose}>
            <ArrowLeftOutlined className={styles.backIcon} />
            <Typography
              uppercase
              color="--color-text-link"
              variant="--font-body-text-6"
            >
              <FormattedMessage id="Close" />
            </Typography>
          </div>
        )}
        <ErrorNotification
          description="Home page can not be loaded"
          showHomeLink={false}
        />
      </div>
    )
  }

  const isCustomer = roleSide === USER_ROLE_SIDES.customer

  const canShowVerifyPaymentModal =
    isLoadingFinished &&
    isNeedToUpdateBillingSettings &&
    isCustomer &&
    checkReminderDate(paymentMethodReminder)

  const canShowCCExpiresModal =
    !canShowVerifyPaymentModal &&
    isCustomer &&
    isLoadingFinished &&
    isCCDataCloseToExpired &&
    checkReminderDate(expiredCCReminder)

  const canShowInviteUserModal =
    isLoaded &&
    isCustomer &&
    canStaffManage &&
    inviteModalStatus?.value === INVITE_MODAL_STATUS_VALUES.visible

  const canShowAdsConnectionModal =
    !areWizardsModalsVisible &&
    !hasConnectedAmazonAdAccounts &&
    !!useBasModule &&
    !isBasSubscriptionExpired &&
    isCustomer &&
    isLoadingFinished &&
    checkReminderDate(amazonAccountConnectionReminder) &&
    !isDemoAccount

  const buildInviteModalStatusChangeHandler = (status) => () => {
    if (!inviteModalStatus?.id) {
      return
    }

    updateUserSettings(
      {
        id: inviteModalStatus?.id,
        settings: status,
      },
      null,
      null
    )
  }

  const getLinks = (type) => {
    const linksItems = navLinks?.[type]?.items || []
    const isShowTransactionButton =
      reimbursementTransactionCount > 0 && !!isLostDisabled
    const isRepricerModule = type === NAV_MODULES_LINKS_TYPES.Repricer
    const isLostModule = type === NAV_MODULES_LINKS_TYPES.LostFound
    const isBasModule = type === NAV_MODULES_LINKS_TYPES.BusinessAnalytics
    const isLostTransactionButtonShow = isShowTransactionButton && isLostModule
    const isLostKnowledgeBaseButtonShow = isLostWithoutCases && isLostModule

    const shouldCheckWithRepricerVersion =
      !useRepricerModuleB2C && !useRepricerModuleB2B

    const isRepricerSetup = shouldCheckWithRepricerVersion && isRepricerModule
    const isLostSetup =
      !useLostModule && isLostModule && !isLostTransactionButtonShow
    const isBasSetup = !useBasModule && isBasModule

    if (!checkIsArray(linksItems)) {
      return []
    }

    const getKnowledgeBaseLink = () => {
      return linksItems.filter(({ id }) => id === NAV_LINKS_IDS.knowledgeBase)
    }

    if (isRepricerSetup) {
      return getKnowledgeBaseLink()
    }

    if (isLostSetup) {
      return getKnowledgeBaseLink()
    }

    if (isBasSetup) {
      // freemium_bas_enabled (isFreemiumBasEnabled) - This key is temporary and will be removed later.
      const isFreemiumBasModelActive = useLostModule && isFreemiumBasEnabled

      const linkToLeft =
        useRepricerModule || isFreemiumBasModelActive
          ? [NAV_LINKS_IDS.dashboard, NAV_LINKS_IDS.knowledgeBase]
          : [NAV_LINKS_IDS.knowledgeBase]

      return linksItems.filter(({ id }) => linkToLeft.includes(id))
    }

    if (isLostTransactionButtonShow) {
      return linksItems.filter(({ id }) =>
        [NAV_LINKS_IDS.transactions, NAV_LINKS_IDS.knowledgeBase].includes(id)
      )
    }

    if (isLostKnowledgeBaseButtonShow) {
      return getKnowledgeBaseLink()
    }

    return linksItems?.length > 6
      ? linksItems.filter(({ id }) => id !== NAV_LINKS_IDS.knowledgeBase)
      : linksItems
  }

  const lostExtraButtons = isLostReimbursementEnabled
    ? [
        <RestrictedPrimaryButton
          managePermission={isAllowToSetup}
          popoverMessage={l(PERMISSIONS_CONTENT.allowSetupAccount)}
          size="large"
          onClick={onBlockLinkClick}
        >
          {l("Request reimbursement now")}
        </RestrictedPrimaryButton>,
      ]
    : []

  const lostExtraContent = isLostReimbursementEnabled ? (
    <ValueNotifier
      cases={nonLostCaseCount}
      isAllowToSetup={isAllowToSetup}
      title={lostCountText}
      value={ln(nonLostCaseEstimatedAmount, 2, {
        currency: nonLostCaseCurrency,
      })}
      onClick={onBlockLinkClick}
    />
  ) : null

  const isLostNavToPending =
    totalLostCaseCount === 0 && roleSide !== USER_ROLE_SIDES.sellerLogic
  const lostNoteText = isLostWithoutCases
    ? "You will automatically receive an email notification as soon as new errors are discovered."
    : "The tool for automated monitoring of Amazon FBA processes, for finding and reporting FBA errors of all kinds."
  const lostNoteTitle = isLostWithoutCases
    ? "We currently looking for new cases"
    : null
  const lostTitleLink = isLostNavToPending
    ? PATH_LOST_DATA_IS_GATHERED_FOR_ACCOUNT
    : PATH_LOST_DASHBOARD

  const isServiceDeskPanelVisible =
    !isDemoAccount &&
    navLinks[NAV_MODULES_LINKS_TYPES.ServiceDesk] &&
    roleSide === USER_ROLE_SIDES.sellerLogic

  const isCrmPanelVisible =
    !isCrmDisabled &&
    !isDemoAccount &&
    navLinks[NAV_MODULES_LINKS_TYPES.CRM] &&
    roleSide === USER_ROLE_SIDES.sellerLogic

  const adsConnectionModalFooter = (
    <Box gap="m" justify="flex-end" width="100%">
      {hasAmazonAdsAccountManagePermission ? (
        <>
          <Button
            variant="secondary"
            onClick={buildAdsConnectionReminderHandler(true)}
          >
            {l("Cancel")}
          </Button>
          <Button
            variant="secondary"
            onClick={buildAdsConnectionReminderHandler()}
          >
            {l("Remind me later")}
          </Button>
          <Button variant="primary" onClick={handleAdsConnectionOk}>
            {l("Connect")}
          </Button>
        </>
      ) : (
        <Button
          variant="secondary"
          onClick={buildAdsConnectionReminderHandler(true)}
        >
          {l("Close")}
        </Button>
      )}
    </Box>
  )

  return (
    <>
      <DashboardLayout>
        <Box
          align="center"
          flexDirection="column"
          marginLeft="l"
          marginRight="l"
          marginTop="l"
          width="100%"
        >
          <DashboardPanel
            countOfAmazonAccounts={countOfAmazonAccounts}
            customer_id={customer_id}
            getCurrentCustomer={getCurrentCustomer}
            isAllowToSetup={isAllowToSetup}
            isSelectedForNewPricing={isSelectedForNewPricing}
            links={getLinks(NAV_MODULES_LINKS_TYPES.Repricer)}
            note="Intelligent price control for Amazon, extensive functions for every automation requirement."
            product={PRODUCTS.repricer}
            productLoaded={productLoaded}
            roleSide={roleSide}
            setUpModule={!isRepricerConnected}
            setUpModuleIcon="retweet"
            setUpModuleLabel="Setup Repricer"
            showModuleSetupWizard={showModuleSetupWizard}
            status={status}
            title="Repricer"
            titleLink={PATH_REPRICER_DASHBOARD}
            titlePermission={permissionKeys.repricerDashboardView}
            user_id={user_id}
            viewMode={viewMode}
          />

          <DashboardPanel
            allowLostFullService={allowLostFullService}
            badges={badges}
            countOfAmazonAccounts={countOfAmazonAccounts}
            customer_id={customer_id}
            displayModal={displayModal}
            extraButtons={lostExtraButtons}
            extraContent={lostExtraContent}
            getCurrentCustomer={getCurrentCustomer}
            hasPaymentData={hasPaymentData}
            isAllowToSetup={isAllowToSetup}
            isLostDisabled={isLostDisabled}
            isLostReimbursementEnabled={isLostReimbursementEnabled}
            links={getLinks(NAV_MODULES_LINKS_TYPES.LostFound)}
            lostModuleSigned={lostModuleSigned}
            lostModuleSignedFullService={lostModuleSignedFullService}
            note={lostNoteText}
            noteTitle={lostNoteTitle}
            product={PRODUCTS.lost}
            roleSide={roleSide}
            setUpModule={!useLostModule}
            setUpModuleIcon="file-search"
            setUpModuleLabel="Setup Lost & Found"
            showModuleSetupWizard={showModuleSetupWizard}
            status={status}
            title="Lost & Found"
            titleLink={lostTitleLink}
            titlePermission={permissionKeys.lfDashboardView}
            totalLostCaseCount={totalLostCaseCount}
            user_id={user_id}
            viewMode={viewMode}
            onBlockLinkClick={onBlockLinkClick}
          />

          {hasBasLinks ? (
            <DashboardPanel
              bas_module_started={bas_module_started}
              countOfAmazonAccounts={countOfAmazonAccounts}
              customer_id={customer_id}
              displayModal={displayModal}
              getCurrentCustomer={getCurrentCustomer}
              hasPaymentData={hasPaymentData}
              isAllowToSetup={isAllowToSetup}
              isBasSubscriptionExpired={isBasSubscriptionExpired}
              isBeta={false}
              links={getLinks(NAV_MODULES_LINKS_TYPES.BusinessAnalytics)}
              note="Analyze and measure your performance to generate insights and keep business processes under control."
              product={PRODUCTS.bas}
              productLoaded={productLoaded}
              roleSide={roleSide}
              setUpModule={!useBasModule}
              setUpModuleIcon="retweet"
              setUpModuleLabel="Setup Business Analytics"
              showModuleSetupWizard={showModuleSetupWizard}
              status={status}
              title="Business Analytics"
              titleLink={PATH_BAS_DASHBOARD}
              titlePermission={permissionKeys.basDashboardView}
              user_id={user_id}
              viewMode={viewMode}
            />
          ) : null}

          {isServiceDeskPanelVisible ? (
            <DashboardPanel
              titlePermission
              countOfAmazonAccounts={countOfAmazonAccounts}
              displayModal={displayModal}
              getCurrentCustomer={getCurrentCustomer}
              hasPaymentData={hasPaymentData}
              isAllowToSetup={isAllowToSetup}
              isBeta={false}
              links={getLinks(NAV_MODULES_LINKS_TYPES.ServiceDesk)}
              note="Track, manage, and solve your customer requests."
              product={PRODUCTS.serviceDesk}
              productLoaded={productLoaded}
              roleSide={roleSide}
              setUpModule={false}
              setUpModuleIcon="retweet"
              setUpModuleLabel="Setup Service desk"
              showModuleSetupWizard={showModuleSetupWizard}
              status={status}
              title="Service desk"
              titleLink={PATH_SERVICE_DESK_TASKS}
              viewMode={viewMode}
            />
          ) : null}

          {isCrmPanelVisible ? (
            <DashboardPanel
              titlePermission
              isBeta={false}
              links={getLinks(NAV_MODULES_LINKS_TYPES.CRM)}
              note="Manage, track, and store information related to customers"
              product={PRODUCTS.crm}
              roleSide={roleSide}
              title="CRM"
              titleLink={PATH_CRM_CONTACTS}
            />
          ) : null}
        </Box>
      </DashboardLayout>

      {/*Card will expire soon*/}
      {canShowCCExpiresModal ? (
        canManage ? (
          <ConfirmModal
            visible
            cancelText={l("Remind me later")}
            closable={false}
            draggable={false}
            maskClosable={false}
            okText={l("Update")}
            title={l("Card will expire soon")}
            message={l(
              "Your card will expire soon. Please update your payment profile."
            )}
            onCancel={handleCCReminder}
            onOk={redirectToPaymentSettings}
          />
        ) : (
          <ConfirmModal
            cancelText={l("Remind me later")}
            closable={false}
            draggable={false}
            maskClosable={false}
            okText={l("Ok")}
            title={l("Card will expire soon")}
            visible={isShowCCExpiresModal}
            message={l(
              "Your card will expire soon. Please contact a user with administrator rights and ask to update the payment profile."
            )}
            onCancel={handleCCReminder}
            onOk={closeCCExpiresModalHandler}
          />
        )
      ) : null}

      {/* Ads connection alert */}
      {canShowAdsConnectionModal ? (
        <Modal
          closeAfterOk
          fitToTop
          visible
          footer={adsConnectionModalFooter}
          isClosable={false}
          isDraggable={false}
          title={l("Connect Amazon Ads account")}
          zIndex={ADS_ACCOUNT_REMINDER_MODAL_ZINDEX}
          onCancel={buildAdsConnectionReminderHandler()}
          onOk={handleAdsConnectionOk}
        >
          <Typography color="--color-text-second" variant="--font-body-text-7">
            {l(
              "Connect Amazon Ads accounts to get a Ads (PPC) cost breakdown for Business Analytics. Activation does not affect your Business Analytics subscription."
            )}
          </Typography>
          {hasAmazonAdsAccountManagePermission ? null : (
            <Box marginTop="m">
              <Alert
                alertType="warning"
                message={l(
                  "You are not authorized to perform this action. Please, contact the account administrator directly for assistance."
                )}
              />
            </Box>
          )}
        </Modal>
      ) : null}

      {/*Payment method confirmation required*/}
      {canShowVerifyPaymentModal ? (
        canManage ? (
          <ConfirmModal
            visible
            cancelText={l("Remind me later")}
            closable={false}
            maskClosable={false}
            okText={l("Confirm")}
            title={l("Payment method confirmation required")}
            width={400}
            message={
              <FormattedMessage
                id="Your payment method requires confirmation. You will be redirected to the Payment page for payment method confirmation. Otherwise, some SellerLogic products may be unavailable. If you experiencing any issues with payment method confirmation - please <a>contact support</a>."
                values={{
                  a: (...chunks) => (
                    <a
                      className="main-link"
                      rel="noreferrer noopener"
                      target="_blank"
                      href={replaceLanguageCode({
                        url: "https://www.sellerlogic.com/{languageCode}/contact-us/",
                        language: locale,
                      })}
                    >
                      {chunks}
                    </a>
                  ),
                }}
              />
            }
            onCancel={handleVerifyPaymentReminder}
            onOk={handleVerifyPayment}
          />
        ) : (
          <ConfirmModal
            cancelText={l("Remind me later")}
            closable={false}
            maskClosable={false}
            okText={l("Confirm")}
            title={l("Payment method confirmation required")}
            visible={isShowPaymentConfirmation}
            width={400}
            message={l(
              "Your payment method requires confirmation. Please contact a user with administrator rights and ask to confirm the payment method. Otherwise, some SellerLogic products may be unavailable."
            )}
            onCancel={handleVerifyPaymentReminder}
            onOk={closePaymentConfirmationHandler}
          />
        )
      ) : null}
      {canShowInviteUserModal ? (
        <InviteUserModal
          cancelText={l("Remind me later")}
          okText={l("Invite")}
          setVisible={setInviteModalVisible}
          visible={isInviteModalVisible}
          onCancel={buildInviteModalStatusChangeHandler(
            INVITE_MODAL_STATUS_VALUES.none
          )}
          onRemindNever={buildInviteModalStatusChangeHandler(
            INVITE_MODAL_STATUS_VALUES.hidden
          )}
          onSubmit={buildInviteModalStatusChangeHandler(
            INVITE_MODAL_STATUS_VALUES.hidden
          )}
        />
      ) : null}
    </>
  )
}

DashboardView.propTypes = {
  badges: PropTypes.object.isRequired,
  countOfAmazonAccounts: PropTypes.number.isRequired,
  displayModal: PropTypes.func.isRequired,
  hasError: PropTypes.bool.isRequired,
  lostModuleSignedFullService: PropTypes.number.isRequired,
  navLinks: PropTypes.object.isRequired,
  ready: PropTypes.bool.isRequired,
  sellerUser: PropTypes.bool.isRequired,
  viewMode: PropTypes.string.isRequired,
  nonLostCaseEstimatedAmount: PropTypes.string,
  nonLostCaseCount: PropTypes.number,
  showModuleSetupWizard: PropTypes.func.isRequired,
  totalLostCaseCount: PropTypes.number,
  useLostModule: PropTypes.bool.isRequired,
  useRepricerModule: PropTypes.bool.isRequired,
  useBasModule: PropTypes.bool.isRequired,
  roleSide: PropTypes.string,
  canManage: PropTypes.bool,
  isAllowToSetup: PropTypes.bool,
  expiredCCReminder: PropTypes.object,
  isCCDataCloseToExpired: PropTypes.bool,
  isLoadingFinished: PropTypes.bool,
  locale: PropTypes.string.isRequired,
  isLostCustomerCounterLoaded: PropTypes.bool.isRequired,
  nonLostCaseCurrency: PropTypes.string,
  reimbursementTransactionCount: PropTypes.number,
}

export default withRouter(DashboardView)
