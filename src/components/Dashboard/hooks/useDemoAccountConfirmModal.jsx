import React, { useCallback, useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import Typography from "components/Typography"

import userSettingsActions from "actions/userSettingsActions"
import { demoAccountConfirmModalSelector } from "selectors/userSettingSelectors"
import { isDemoAccountSelector } from "selectors/customerSelectors"
import l from "utils/intl"
import { setConfirm } from "utils/confirm"
import { demoAccountConstants } from "components/Dashboard/constants"

import styles from "./useDashboardView.module.scss"

const { getUserSettings, updateUserSettings, saveUserSettings } =
  userSettingsActions

export const useDemoAccountConfirmModal = () => {
  const headerModal = l("Welcome to the SellerLogic demo account")

  const textItems = [
    l(
      "This demo account will introduce you to what SellerLogic provides for its customers and give a glimpse of how they can help you with the optimization of your sales activities on Amazon. "
    ),
    l("Please note that the following applies to the demo account:"),
  ]
  const listLtems = [
    l(
      "Data for all products are pre-generated to provide an understanding of how SellerLogic product works"
    ),
    l("Some features & actions may be restricted to a demo account"),
    l(
      "The demo account will be deleted after 10 days, but no worries, you can always transfer your profile to a standard account at any time, or just use your credentials to register an account after the demo is deleted"
    ),
  ]

  const dispatch = useDispatch()

  const [isDemoAccountModalVisible, setIsDemoAccountModalVisible] =
    useState(false)

  const demoAccountConfirmModal = useSelector(demoAccountConfirmModalSelector)
  const isDemoAccount = useSelector(isDemoAccountSelector)

  const closeModalWindowHandler = useCallback(() => {
    if (!demoAccountConfirmModal?.id) {
      return
    }

    dispatch(
      updateUserSettings(
        {
          id: demoAccountConfirmModal?.id,
          settings: false,
        },
        () => {
          setIsDemoAccountModalVisible(false)
        },
        null
      )
    )
  }, [demoAccountConfirmModal?.id, dispatch])

  const checkDemoConfirmModalSettings = (data) => {
    const confirmModalValue = data?.settings?.value
    const isNeedToCreateDemoConfirmModal = confirmModalValue === 1
    const isDemoAccountModalFormModalVisible =
      confirmModalValue && confirmModalValue !== 1

    if (isDemoAccountModalFormModalVisible) {
      setIsDemoAccountModalVisible(true)

      return
    }

    if (isNeedToCreateDemoConfirmModal) {
      dispatch(
        saveUserSettings(
          {
            key: demoAccountConstants.DEMO_ACCOUNT_CONFIRM_MODAL,
            settings: true,
          },
          () => {
            dispatch(
              getUserSettings(
                {
                  key: demoAccountConstants.DEMO_ACCOUNT_CONFIRM_MODAL,
                },
                null
              )
            )

            setIsDemoAccountModalVisible(true)
          },
          null
        )
      )
    }
  }

  useEffect(() => {
    if (isDemoAccount) {
      dispatch(
        getUserSettings(
          {
            key: demoAccountConstants.DEMO_ACCOUNT_CONFIRM_MODAL,
            defaultSettings: 1,
          },
          checkDemoConfirmModalSettings
        )
      )
    }
  }, [isDemoAccount])

  useEffect(() => {
    if (isDemoAccountModalVisible) {
      setConfirm({
        title: headerModal,
        message: (
          <>
            {textItems.map((text) => {
              return <Typography type="p">{text}</Typography>
            })}

            <ul className={styles.rootList}>
              {listLtems.map((text) => {
                return <li className={styles.item}>{text}</li>
              })}
            </ul>
          </>
        ),
        onOk: closeModalWindowHandler,
        onCancel: closeModalWindowHandler,
      })
    }
  }, [closeModalWindowHandler, isDemoAccountModalVisible])
}
