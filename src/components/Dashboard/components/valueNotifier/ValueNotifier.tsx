import React, { ReactNode } from "react"
import { Box, Typography } from "@develop/fe-library"

import FormattedMessage from "components/FormattedMessage"

interface ValueNotifierProps {
  title: ReactNode
  value: string | number
  cases: string
}

export const ValueNotifier: React.FC<ValueNotifierProps> = ({
  title,
  value,
  cases,
}) => {
  return (
    <Box flexDirection="column" gap="m" marginTop="m">
      <Typography
        color="--color-text-link"
        variant="--font-body-text-1"
        tb={{
          variant: "--font-body-text-4",
          color: "--color-text-link",
        }}
      >
        <FormattedMessage
          defaultMessage={title}
          id={title}
          values={{
            cases,
            strong: (...chunks: any[]) => <strong>{chunks}</strong>,
          }}
        />
      </Typography>

      <Typography color="--color-text-error" variant="--font-headline-2">
        {value}
      </Typography>
    </Box>
  )
}
