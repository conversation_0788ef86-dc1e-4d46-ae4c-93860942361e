import React, { useCallback } from "react"
import { useDispatch } from "react-redux"
import { useHistory } from "react-router-dom"
import { Badge } from "antd"
import { Icon } from "@ant-design/compatible"
import { Box, Popover, Typography } from "@develop/fe-library"
import cn from "classnames"
import PropTypes from "prop-types"

import customerPaymentSettingsActions from "actions/customerPaymentSettingsActions"

import FormattedMessage from "components/FormattedMessage"
import { FullServiceBlock } from "components/FullServiceBlock"
import LinkAdapter from "components/hocs/LinkAdapter"
import { TransferToNewPricingInfo } from "components/TransferToNewPricing"

import { useStartSetupWizard } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"
import { pushEvent } from "utils/gtmLoader"
import l from "utils/intl"
import { removeWizardSessionStorage } from "utils/wizard/removeWizardSessionStorage"

import { EVENTS_NAMES, PRODUCT_ORDER } from "consts/gtm"
import { PERMISSIONS_CONTENT } from "consts/permissions"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import { DashboardPanelTitle } from "./components"

import styles from "./dashboardPanel.module.scss"

const { setInitialValues } = customerPaymentSettingsActions

const DashboardPanelView = ({
  badges,
  countOfAmazonAccounts,
  links = [],
  note,
  title,
  extraContent,
  titleLink,
  titlePermission,
  shouldBlockLinks,
  onBlockLinkClick,
  noteTitle,
  setUpModule,
  setUpModuleIcon,
  setUpModuleLabel,
  showModuleSetupWizard,
  lostModuleSigned,
  lostModuleSignedFullService,
  allowLostFullService,
  displayModal,
  productLoaded,
  status,
  roleSide,
  isBeta = false,
  product,
  hasPaymentData,
  getCurrentCustomer,
  isAllowToSetup,
  extraButtons = [],
  user_id,
  customer_id,
  bas_module_started,
  isBasSubscriptionExpired,
  isSelectedForNewPricing,
}) => {
  const history = useHistory()

  const dispatch = useDispatch()

  const launchWizard = useStartSetupWizard()

  const onShowModuleSetupWizard = useCallback(() => {
    if (product === "service-desk" || !isAllowToSetup) {
      return
    }

    // REMOVE USELESS PRODUCTS IN THIS CASE

    removeWizardSessionStorage()

    pushEvent({ ecommerce: null })

    pushEvent({
      event: EVENTS_NAMES.addToCart,
      user_id,
      customer_id,
      ecommerce: {
        items: [
          {
            item_id: PRODUCT_ORDER[product],
            item_name: PRODUCT_NAMES[title],
            index: 0,
            quantity: 1,
          },
        ],
      },
    })

    pushEvent({
      vHitNonInteraction: false,
      virtualEventAction: `click-setup-${product}`,
      virtualEventCategory: "homepage",
      event: EVENTS_NAMES.updEvents,
    })

    if (status === "soft_block") {
      history.push(titleLink)

      return
    }
    getCurrentCustomer(false, () => {
      launchWizard({ productName: product })
    })

    /* This request is required to update the initialValues data.
   Since we have cached data, we use this request to update them. */
    dispatch(setInitialValues(null, undefined))
  }, [
    countOfAmazonAccounts,
    showModuleSetupWizard,
    lostModuleSignedFullService,
    displayModal,
    status,
    titleLink,
    history,
    product,
    getCurrentCustomer,
    hasPaymentData,
    isAllowToSetup,
    allowLostFullService,
    launchWizard,
    //   Delete this after FULL SERVICE MVP
    lostModuleSigned,
  ])

  const isNotSellerLogicUser =
    roleSide !== "SELLERLOGIC" && status !== "soft_block"

  const isShowWizardForRepricerAndLost =
    shouldBlockLinks ||
    extraContent ||
    (setUpModule &&
      !(
        lostModuleSignedFullService ||
        ((product === PRODUCTS.repricer || product === PRODUCTS.repricerB2C) &&
          productLoaded)
      ))

  const isShowWizardOrBlock =
    isNotSellerLogicUser && isShowWizardForRepricerAndLost

  const isShowWizardForBas =
    (!bas_module_started || isBasSubscriptionExpired) && isNotSellerLogicUser

  const isShowWizardMap = {
    [PRODUCTS.repricer]: isShowWizardOrBlock,
    [PRODUCTS.repricerB2C]: isShowWizardOrBlock,
    [PRODUCTS.lost]: isShowWizardOrBlock,
    [PRODUCTS.bas]: isShowWizardForBas,
    [PRODUCTS.serviceDesk]: isShowWizardOrBlock,
  }

  const isShowWizard = isShowWizardMap[product]

  const canViewFullServiceBlock =
    product === PRODUCTS.lost &&
    !lostModuleSignedFullService &&
    allowLostFullService &&
    !setUpModule

  const isSetupButtonShow = !extraContent && setUpModule

  return (
    <Box
      hasBorder
      borderColor="--color-border-main"
      borderRadius="--border-radius"
      flexDirection="column"
      margin="0 0 m"
      width="100%"
      className={cn({
        [styles.extraContainerDirection]: extraContent,
      })}
      dMD={{
        maxWidth: "984px",
        margin: "0 l l l",
      }}
      dSM={{
        margin: "0 auto l",
        maxWidth: "100%",
      }}
      tb={{
        minHeight: 186,
      }}
    >
      <Box
        align="flex-start"
        className={styles.mainContainer}
        justify="space-between"
        padding="m"
        mXL={{
          padding: "l",
        }}
      >
        <div className={styles.description}>
          <DashboardPanelTitle
            extraContent={extraContent}
            isAllowToSetup={isAllowToSetup}
            isBeta={isBeta}
            isShowWizard={isShowWizard}
            setUpModule={setUpModule}
            title={title}
            titleLink={titleLink}
            titlePermission={titlePermission}
            onBlockLinkClick={onBlockLinkClick}
            onShowModuleSetupWizard={onShowModuleSetupWizard}
          />

          {noteTitle && (
            <Typography
              className={styles.noteTitle}
              color="--color-text-second"
              variant="--font-headline-3"
            >
              <FormattedMessage id={noteTitle} />
            </Typography>
          )}
          <Typography
            className={styles.note}
            color="--color-text-second"
            variant="--font-body-text-7"
          >
            <FormattedMessage id={note} />
          </Typography>
          {extraContent || null}
        </div>

        <ul
          className={cn(styles.links, {
            [styles.setupWithLinks]: setUpModule,
            [styles.withExtraContainer]: !!extraContent,
          })}
        >
          {links?.map(({ icon, internal, url, id }) => {
            const isNotKnowledgeBase =
              shouldBlockLinks && id !== "Knowledge base"
            const LinkWrapper = isNotKnowledgeBase ? "div" : LinkAdapter

            return (
              <li key={id} className={styles.linkWrapper}>
                <LinkWrapper
                  className={styles.link}
                  internal={internal}
                  url={url}
                  onClick={isNotKnowledgeBase ? onBlockLinkClick : undefined}
                >
                  <span className={styles.relative}>
                    <Icon className={styles.icon} type={icon} />
                    <Badge className={styles.badge} count={badges[id]} />
                  </span>
                  <Typography
                    className={styles.linkLabel}
                    color="--color-text-main"
                    variant="--font-body-text-3"
                  >
                    <FormattedMessage id={id} />
                  </Typography>
                </LinkWrapper>
              </li>
            )
          })}

          {isSetupButtonShow ? (
            <li className={`${styles.linkWrapper} setup-button-${product}`}>
              <Popover
                content={
                  !isAllowToSetup
                    ? l(PERMISSIONS_CONTENT.allowSetupAccount)
                    : ""
                }
              >
                <div
                  className={cn(styles.link, {
                    [styles.disabled]: !isAllowToSetup,
                  })}
                  onClick={onShowModuleSetupWizard}
                >
                  <span className={styles.relative}>
                    <Icon className={styles.icon} type={setUpModuleIcon} />
                  </span>

                  <Typography
                    className={styles.linkLabel}
                    variant="--font-body-text-3"
                  >
                    <FormattedMessage id={setUpModuleLabel} />
                  </Typography>
                </div>
              </Popover>
            </li>
          ) : null}

          {!checkIsArray(extraButtons)
            ? null
            : extraButtons.map((button) => {
                return <li className={styles.linkWrapper}>{button}</li>
              })}
        </ul>
      </Box>

      {canViewFullServiceBlock ? <FullServiceBlock /> : null}

      {isSelectedForNewPricing ? <TransferToNewPricingInfo /> : null}
    </Box>
  )
}

DashboardPanelView.propTypes = {
  isBeta: PropTypes.bool,
  badges: PropTypes.object,
  countOfAmazonAccounts: PropTypes.number.isRequired,
  links: PropTypes.array.isRequired,
  note: PropTypes.string,
  title: PropTypes.string,
  titlePermission: PropTypes.string,
  extraContent: PropTypes.any,
  shouldBlockLinks: PropTypes.bool,
  onBlockLinkClick: PropTypes.func,
  noteTitle: PropTypes.string,
  setUpModule: PropTypes.bool.isRequired,
  setUpModuleIcon: PropTypes.string.isRequired,
  setUpModuleLabel: PropTypes.string.isRequired,
  lostModuleSignedFullService: PropTypes.number,
  displayModal: PropTypes.func,
  isAllowToSetup: PropTypes.bool.isRequired,
}

DashboardPanelView.defaultProps = {
  badges: {},
}

export default DashboardPanelView
