import React from "react"
import { Badge, Box, Typography } from "@develop/fe-library"
import cn from "classnames"

import Link from "components/shared/Link"

import useAccess from "hooks/useAccess"

import l from "utils/intl"

import { DashboardPanelTitleProps } from "./DashboardPanelTitleTypes"

import styles from "./dashboardPanelTitle.module.scss"

export const DashboardPanelTitle = ({
  isShowWizard,
  title,
  titleLink,
  titlePermission,
  isAllowToSetup,
  setUpModule,
  extraContent,
  onShowModuleSetupWizard,
  onBlockLinkClick,
  isBeta,
}: DashboardPanelTitleProps) => {
  const hasTitleClickPermission: boolean =
    useAccess(titlePermission) || titlePermission === true
  const isLabel: boolean = isShowWizard || !hasTitleClickPermission

  const isLabelDisabled: boolean = !isAllowToSetup && isLabel

  const onClick = (): void => {
    const isBlocked: boolean = !hasTitleClickPermission || !isShowWizard

    if (isBlocked) {
      return
    }

    const isWizardShown: boolean = setUpModule && !extraContent

    if (isWizardShown) {
      onShowModuleSetupWizard()
    }

    onBlockLinkClick()
  }

  return (
    <Box align="center" display="inline-flex" gap="m">
      <Box cursor="pointer" onClick={onClick}>
        {isLabel ? (
          <Typography
            className={cn(styles.title, { [styles.disabled]: isLabelDisabled })}
            variant="--font-headline-5"
          >
            {l(title)}
          </Typography>
        ) : (
          // @ts-ignore
          <Link
            internal
            label={title}
            styleType="dashboard"
            type="span"
            url={titleLink}
            variant="header"
          />
        )}
      </Box>
      {isBeta ? <Badge content={l("Beta")} size="m" /> : null}
    </Box>
  )
}
