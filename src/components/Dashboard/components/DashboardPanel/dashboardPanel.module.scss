@import "assets/styles/variables.scss";

.relative {
  position: relative;
}

.extraContainerDirection {
  flex-wrap: wrap;
}

.note.note {
  margin-top: var(--margin-m);
}

.noteTitle.noteTitle {
  margin-top: var(--margin-m);
  font-weight: bold;
}

.description {
  margin-right: 50px;
}

.links {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: flex-start;
  list-style: none;
  margin: 0;
  min-width: 430px;
  padding: 0;
  width: 430px;
  gap: var(--gap-m);

  &.setupWithLinks.withExtraContainer {
    row-gap: var(--gap-m);
    column-gap: 0;
    justify-content: space-between;
  }

  &.setupWithLinks {
    gap: var(--gap-m);
    align-content: flex-end;
    flex-direction: column;
    flex-wrap: nowrap;

    .linkWrapper {
      flex-grow: 1;
      width: 100%;
      max-width: 250px;
      align-self: flex-end;
      margin-bottom: 0;
    }
  }
}

.linkWrapper {
  height: 40px;
  width: 210px;
}

.link {
  align-items: center;
  border: var(--border-main);
  border-radius: var(--border-radius);
  display: flex;
  height: 100%;
  justify-content: flex-start;
  position: relative;
  width: 100%;
  cursor: pointer;

  & .icon {
    color: $import-icon-color;
  }

  &:not(.disabled):hover {
    border-color: $border_active;

    .icon {
      color: $icon_active;
    }

    .linkLabel.linkLabel,
    & > div {
      color: $text_link;
    }
  }

  &.disabled,
  &.disabled > div {
    cursor: not-allowed;
    color: $text_disable;
    background-color: $disable_bg;
  }

  &.disabled:hover > div {
    color: $text_disable;
  }
}

.mainContainer.mainContainer {
  height: 100%;
  .linkLabel {
    line-height: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.withBadge {
      padding-right: var(--padding-xl);
    }

    &:hover {
      color: $text_link;
    }
  }
}

.icon {
  font-size: 20px;
  margin: 0 18px;
}

.badge.badge {
  left: 53%;
  top: -15%;
  position: absolute;

  sup {
    background-color: #f5222d;
    border-radius: 32px;
    color: $white_text;
    font-size: 10px;
    height: 15px;
    line-height: 15px;
    padding: 0;
    text-align: center;
    width: 20px;
  }
}

.title {
  &:not(.disabled):hover {
    color: $text_link;
  }

  &.disabled {
    cursor: auto;
  }
}

.blockLink {
  cursor: pointer;
}

@media (max-width: $md) {
  .mainContainer.mainContainer {
    flex-wrap: wrap;
  }

  .links {
    margin-top: var(--margin-l);
    min-width: unset;
    width: 100%;

    &.setupWithLinks {
      flex-direction: row;
      flex-wrap: wrap;
      row-gap: var(--gap-m);
      column-gap: 0;

      .linkWrapper {
        width: 100%;
        max-width: calc(50% - var(--gap-s));
      }
    }
  }

  .linkWrapper {
    width: 100%;
    max-width: calc(50% - var(--gap-s));
  }

  .description {
    margin-right: 10px;
    width: calc(100% - 20px);
  }

  .noteTitle {
    font-size: 14px;
  }
}

@media (max-width: $xs) {
  .mainContainer.mainContainer {
    align-items: center;
    flex-direction: column;
  }

  .description {
    margin-right: 0;
    width: 100%;
  }

  .linkWrapper {
    width: 100%;
    max-width: 100%;
  }

  .links {
    margin-top: var(--margin-m);

    &.setupWithLinks {
      flex-direction: row;
      flex-wrap: wrap;

      .linkWrapper {
        max-width: unset;
        width: 100%;
      }
    }
  }
}

.withExtraContainer {
  flex-direction: column;
  gap: var(--gap-m);
  width: 100%;
  min-width: unset;
  justify-content: flex-start;
  align-items: flex-end;

  li,
  button {
    width: 100%;
    margin: 0;
  }

  @media (min-width: $sm) and (max-width: $md) {
    flex-direction: row;
    flex-wrap: wrap;
  }

  @media (min-width: $md) {
    width: 430px;
  }
}
