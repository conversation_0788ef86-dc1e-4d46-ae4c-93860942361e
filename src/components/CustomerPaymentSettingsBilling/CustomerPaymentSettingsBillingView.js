import React, { useEffect } from "react"
import { useForm } from "react-hook-form"
import { Button, FormItems } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import PropTypes from "prop-types"

import { CustomerPaymentSettingsFormLayout } from "components/CustomerPaymentSettingsFormLayout"

import { checkIsArray } from "utils/arrayHelpers"
import { buildErrorMessagesMapper } from "utils/formHelpers/buildErrorMessagesMapper"
import l from "utils/intl"

const CustomerPaymentSettingsBillingView = ({
  countries,
  errors,
  initialValues,
  initialValuesWithCustomerAdddress,
  onSubmit,
}) => {
  const form = useForm({
    defaultValues: {
      ...initialValues,
      billingEmail: initialValues.billingEmail.split(","),
    },
    mode: "onChange",
  })

  const { setError, handleSubmit: formHandleSubmit } = form

  useEffect(() => {
    const hasErrors = !!errors && getObjectKeys(errors).length > 0

    if (!hasErrors) {
      return
    }

    getObjectKeys(errors).forEach((key) => {
      setError(key, {
        type: "custom",
        message: errors[key],
      })
    })
  }, [errors, setError])

  if (!countries?.length) {
    return null
  }

  const handleUseCompanyAddress = () => {
    const { billingEmail } = form.getValues()

    form.reset({
      ...initialValuesWithCustomerAdddress,
      useBillingAddress: 1,
      billingEmail: billingEmail,
    })
  }

  const handleSubmit = formHandleSubmit((values) => {
    const billingEmail = values.billingEmail.join(",")

    onSubmit(
      {
        ...values,
        billingEmail,
      },
      (submitErrors) => {
        if (checkIsArray(submitErrors)) {
          submitErrors.forEach((error) => {
            setError(error.field, {
              type: "custom",
              message: error.message,
            })
          })
        }
      }
    )
  })

  return (
    <CustomerPaymentSettingsFormLayout
      activeStep={0}
      buttons={
        <Button fullWidth variant="primary" onClick={handleSubmit}>
          {l("Continue")}
        </Button>
      }
    >
      <FormItems
        form={form}
        validationMessagesMapper={buildErrorMessagesMapper()}
        gridContainerProps={{
          gapMSM: "m",
          gapTb: "l",
        }}
        items={[
          {
            type: "email",
            name: "billingEmail",
            inputProps: {
              label: l("Email"),
              isRequired: true,
              separator: ", ",
              errorMessagesMapper: {
                duplicate: l("Duplicate"),
                pattern: l("Invalid"),
              },
            },
            gridItemProps: {
              mSM: 12,
            },
            rules: {
              required: true,
            },
          },
          {
            type: "action",
            label: l("Use company address"),
            actionProps: {
              fullWidth: true,
              onClick: handleUseCompanyAddress,
            },
            gridItemProps: {
              mSM: 12,
              tb: "min-content",
            },
          },
        ]}
      />

      <FormItems
        form={form}
        gridContainerProps={{
          gap: "m",
          align: "end",
        }}
        items={[
          {
            type: "text",
            name: "billingCompanyName",
            inputProps: {
              label: l("Company name"),
              isRequired: true,
            },
            gridItemProps: {
              mSM: 12,
            },
            rules: {
              required: true,
            },
          },
          {
            type: "text",
            name: "billingAddressLine1",
            inputProps: {
              label: l("Address 1"),
              isRequired: true,
            },
            gridItemProps: {
              mSM: 12,
            },
            rules: {
              required: true,
            },
          },
          {
            type: "text",
            name: "billingAddressLine2",
            inputProps: {
              label: l("Address 2"),
            },
            gridItemProps: {
              mSM: 12,
            },
          },
          {
            type: "text",
            name: "billingZip",
            inputProps: {
              label: l("Postcode"),
              isRequired: true,
            },
            gridItemProps: {
              mSM: 12,
              tb: 6,
            },
            rules: {
              required: true,
            },
          },
          {
            type: "text",
            name: "billingCity",
            inputProps: {
              label: l("City"),
              isRequired: true,
            },
            gridItemProps: {
              mSM: 12,
              tb: 6,
            },
            rules: {
              required: true,
            },
          },
          {
            type: "select",
            name: "billingCountryId",
            inputProps: {
              label: l("Country"),
              isRequired: true,
              hasSearch: true,
              options: countries.map(({ id, title }) => ({
                label: l(title),
                value: id,
              })),
            },
            gridItemProps: {
              mSM: 12,
            },
            rules: {
              required: true,
            },
          },
          {
            type: "text",
            name: "vat_id",
            inputProps: {
              label: l("VAT ID"),
              placeholder: "DE815528383",
              suffixIcons: [
                {
                  name: "icnQuestionCircle",
                  color: "--color-icon-active",
                  content: l(
                    "If no valid VAT-ID is entered, your invoice will contain the currently valid German VAT."
                  ),
                },
              ],
            },
            gridItemProps: {
              mSM: 12,
            },
          },
        ]}
      />
    </CustomerPaymentSettingsFormLayout>
  )
}

CustomerPaymentSettingsBillingView.propTypes = {
  countries: PropTypes.array.isRequired,
  errors: PropTypes.object,
  initialValues: PropTypes.object.isRequired,
  initialValuesWithCustomerAdddress: PropTypes.object.isRequired,
  onSubmit: PropTypes.func.isRequired,
}

export { CustomerPaymentSettingsBillingView }
