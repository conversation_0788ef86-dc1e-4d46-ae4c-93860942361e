import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory, useRouteMatch } from "react-router-dom"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import authActions from "actions/authActions"
import languagesActions from "actions/languagesActions"
import selectedRoleActions from "actions/selectedRoleActions"
import staffActions from "actions/staffActions"
import timezoneActions from "actions/timezoneActions"

import {
  isDemoAccountSelector,
  isUpdatingCustomerSelector,
} from "selectors/customerSelectors"
import { selectOptionsWithCustomUserSelector } from "selectors/selectedRoleSelectors"
import {
  getStaffData,
  staffCurrentUserSelector,
  staffTotalCountSelector,
} from "selectors/staffSelectors"
import { customerSelector, permissionsSelector } from "selectors/userSelectors"

import l from "utils/intl"

import { USER_ROLE_SIDES } from "consts/user"

const { customer } = USER_ROLE_SIDES
// @ts-ignore
const {
  getAllStaff,
  deleteStaff,
  update,
  disable2FA,
  resendInviteToEmail,
  clearAllStaff,
} = staffActions
// @ts-ignore
const { getRoles } = selectedRoleActions
// @ts-ignore
const { getAll: getLanguages } = languagesActions
// @ts-ignore
const { getAll: getTimezones } = timezoneActions
// @ts-ignore
const { authorize } = authActions

const titleTransformHandler = ({
  title,
  isDemoAccount,
}: {
  title: string
  isDemoAccount: boolean | undefined
}) => {
  return isDemoAccount
    ? l("This feature is available in full version.")
    : l(title)
}

export const useStaffList = () => {
  const [confirmDisable2FA, setConfirmDisable2FA] = useState({})
  const [visible, setVisible] = useState(false)

  const accessRolesSelectOptions = useSelector(
    selectOptionsWithCustomUserSelector(customer)
  )
  const dataSource = useSelector(getStaffData)
  const isDemoAccount = useSelector(isDemoAccountSelector)
  const { staffView } = useSelector(permissionsSelector)

  const isUpdatingCustomer = useSelector(isUpdatingCustomerSelector)

  const totalCount = useSelector(staffTotalCountSelector)
  const { id: customerID } = useSelector(customerSelector)
  const {
    id: userID,
    user: { role },
  } = useSelector(staffCurrentUserSelector)

  const dispatch = useDispatch()
  const { path } = useRouteMatch()
  const history = useHistory()

  useEffect(() => {
    if (isUpdatingCustomer) {
      dispatch(clearAllStaff())
    }
  }, [isUpdatingCustomer])

  const searchOptions = getUrlSearchParams({
    locationSearch: document.location.search,
  })
  const { page, pageSize, ...restSearchOptions } = searchOptions

  const filtersOptions = {
    role: accessRolesSelectOptions,
    allow_app_login: [
      { label: l("Allow"), value: "1" },
      { label: l("Deny"), value: "0" },
    ],
    is_enabled_2fa: [
      { label: l("Active"), value: "1" },
      { label: l("Not active"), value: "0" },
    ],
  }

  const getAllStaffHandler = <Type,>(params: Type) => {
    return dispatch(getAllStaff({ ...params, customer_id: customerID }))
  }

  const getAccessRoles = () => {
    return dispatch(getRoles(false, { side: customer }, false))
  }

  const getAdditionalData = () => {
    getAccessRoles()
    dispatch(getLanguages())
    dispatch(getTimezones())
  }

  const deleteItem = (id: string | number, callback?: () => void) => {
    return dispatch(deleteStaff(id, callback))
  }

  const deleteIconHandler = ({ id }: { id: string | number }) => {
    return deleteItem(id, () => getAllStaffHandler(searchOptions))
  }

  // @ts-ignore
  const saveCell = ({ params: queryParams, payload }, success, failure) => {
    return dispatch(update({ queryParams, payload }, success, failure))
  }

  const saveCellHandler = <Type,>(
    payload: Type,
    failureCallback?: () => void,
    successCallback?: () => void
  ) => {
    // @ts-ignore
    const { id, allow_app_login } = payload
    const params = { id }
    const data = { allow_app_login }
    const success = () => {
      successCallback && successCallback()
    }

    return saveCell({ params, payload: data }, success, failureCallback)
  }

  // @ts-ignore
  const updateIconHandler = ({ user: { id } }) => {
    return history.push(`${path}/${id}`)
  }
  // @ts-ignore
  const loginHandler = ({ user: { id } }) => {
    dispatch(
      authorize(
        id,
        () => (window.location.href = ROUTES.GENERAL_ROUTES.PATH_HOME)
      )
    )
  }

  const onDisable2FA = (id: number | string, successCallback?: () => void) => {
    return dispatch(disable2FA(id, successCallback))
  }

  const okConfirmModalHandler = () => {
    // @ts-ignore
    onDisable2FA(confirmDisable2FA.userId, () =>
      getAllStaffHandler(searchOptions)
    )
    setConfirmDisable2FA({})
  }

  const cancelConfirmModalHandler = () => setConfirmDisable2FA({})

  const resentInviteToEmailHandler = ({ id }: { id: string | number }) => {
    return dispatch(resendInviteToEmail(id))
  }

  const createButtonHandler = () => setVisible(true)

  const buildDisableTwoFactorIconHandler = <Type,>(user: Type) => {
    return () => {
      return setConfirmDisable2FA({
        visible: true,
        // @ts-ignore
        userId: user.id,
      })
    }
  }

  const loadDataHandler = () => {
    return getAllStaffHandler(searchOptions)
  }

  return {
    canEditRole: staffView,
    dataSource,
    visible,
    totalCount,
    confirmDisable2FA,
    userID,
    role,
    isDemoAccount,
    searchOptions,
    restSearchOptions,
    filtersOptions,
    getAllStaffHandler,
    getAdditionalData,
    setVisible,
    updateIconHandler,
    loginHandler,
    okConfirmModalHandler,
    cancelConfirmModalHandler,
    resentInviteToEmailHandler,
    titleTransformHandler,
    saveCellHandler,
    deleteIconHandler,
    createButtonHandler,
    buildDisableTwoFactorIconHandler,
    loadDataHandler,
  }
}
