import React from "react"
import { ROUTES } from "@develop/fe-library/dist/routes"

import Typography from "components/Typography"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import Link from "components/shared/Link"

import { USER_ROLES } from "consts/user"

import l from "utils/intl"

export default ({ canEditRole }) => [
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
    sorter: true,
    type: "input",
    width: 50,
    align: "left",
  },

  {
    title: "First name",
    dataIndex: "firstname",
    key: "firstname",
    sorter: true,
    type: "input",
    width: 200,
    align: "left",
  },

  {
    title: "Last Name",
    dataIndex: "lastname",
    key: "lastname",
    sorter: true,
    type: "input",
    width: 200,
    align: "left",
  },

  {
    title: "Phone",
    dataIndex: "phone",
    key: "phone",
    sorter: true,
    type: "input",
    width: 200,
    render: (text) => (
      <Typography type="span" variant="textSmall">
        <ExportValue>
          <Link
            internal={false}
            url={`tel:${text}`}
            text={text}
            variant="textSmall"
            styleType="primary"
          />
        </ExportValue>
      </Typography>
    ),
    align: "left",
  },

  {
    title: "Email",
    dataIndex: "email",
    key: "email",
    sorter: true,
    type: "input",
    width: 200,
    align: "left",
  },
  {
    title: "Role",
    dataIndex: ["user", "role"],
    key: "role",
    sorter: true,
    type: "selectWithFilter",
    width: 200,
    render: (text, item) => {
      const { id, user: { email_confirmed, roleCode } = {} } = item || {}
      const isInvitedUser =
        roleCode === USER_ROLES.invitedUser ||
        roleCode === USER_ROLES.invitedSlUser ||
        roleCode === USER_ROLES.invitedTranslatorUser

      const roleContent = canEditRole ? (
        <Link
          type="span"
          url={`${ROUTES.GENERAL_ROUTES.PATH_STAFF}/${id}#roleAdministration`}
          text={text}
          variant="textSmall"
          styleType="primary"
        />
      ) : (
        text
      )

      if (isInvitedUser) {
        return (
          <>
            <ExportValue>{roleContent}</ExportValue>
            <br />
            <ExportValue>
              {l(!email_confirmed ? "Invitation sent" : "Accepted")}
            </ExportValue>
          </>
        )
      }

      return <ExportValue>{roleContent}</ExportValue>
    },
  },
]
