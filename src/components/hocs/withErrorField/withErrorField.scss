@import "assets/styles/variables.scss";

@mixin errorStyle() {
  border-radius: 2px;
  border: 1px solid $error_border;
}

.with-error-field-wraper {
  .ant-input {
    @include errorStyle;
  }
  .phone-input {
    border: none;
  }
}

.with-error-field {
  border-radius: 2px;
  border: 1px solid $error_border;

  input {
    border-color: transparent;

    &:focus {
      border-color: transparent;
      box-shadow: none;
    }
  }

  &.ant-input-group-wrapper,
  &.ant-input-affix-wrapper,
  &.ant-select-multiple {
    border: none;
    .ant-input {
      border: 1px solid $error_border;
    }
    .ant-input-group-addon {
      border-color: $error_border;
    }
    .ant-select-selector {
      border-color: $error_border !important;
    }
  }

  &.phone-input {
    border: none;
    .ant-input {
      border: 1px solid $error_border;
    }
    .ant-input-group-addon {
      border-color: $error_border;
      span {
        color: $error_text;
      }
    }
    .anticon-down {
      color: $icon_error;
    }
  }

  &.date-picker-dropdown {
    border: none;
  }

  &:hover {
    @include errorStyle;

    input {
      border: none;
    }
    &.date-picker-dropdown {
      border: none;
      .ant-input {
        @include errorStyle;
      }
    }
    &.ant-input-group-wrapper,
    &.ant-input-affix-wrapper,
    &.ant-select-multiple {
      border: none;
    }

    &.ant-input-affix-wrapper {
      .ant-input {
        border: 1px solid $error_border;
      }
    }
    &.phone-input {
      border: none;
      .ant-input {
        border: 1px solid $error_border;
        border-left: solid 1px $error_border !important;
      }
      .ant-input-group-addon {
        border-color: $error_border;
      }
    }
  }

  &:focus {
    @include errorStyle;
    box-shadow: none;
  }

  .ant-input-suffix,
  .ant-calendar-picker-icon {
    display: none;
  }
}
