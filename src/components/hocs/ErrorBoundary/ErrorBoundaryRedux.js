import { connect } from "react-redux"

import ErrorBoundaryHoc from "components/hocs/ErrorBoundary/ErrorBoundaryHoc"
import errorActions from "actions/errorActions"

const { setError } = errorActions

const mapStateToProps = ({ error: { hasError } }) => ({
  hasError,
})

const mapDispatchToProps = (dispatch) => ({
  setError: () => dispatch(setError(true, null)),
})

export default connect(mapStateToProps, mapDispatchToProps)(ErrorBoundaryHoc)
