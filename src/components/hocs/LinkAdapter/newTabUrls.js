import { replaceLanguageCode } from "utils/replaceLanguageCode"

const urlsInNewTab = [
  "https://support.sellerlogic.com/{localeCode}/home",
  "https://www.sellerlogic.com/{languageCode}/contact-us/",
  "https://support.sellerlogic.com/{localeCode}/lost-found",
  "https://support.sellerlogic.com/{localeCode}/repricer",
  "https://support.sellerlogic.com/{localeCode}/business-analytics",
  "https://support.sellerlogic.com/{localeCode}",
  "https://roadmap.sellerlogic.com/",
]

export const shouldOpenInNewTab = ({ url, language }) => {
  const adjustedUrls = urlsInNewTab.map((urlItem) =>
    replaceLanguageCode({ url: urlItem, language })
  )

  return adjustedUrls.includes(url)
}
