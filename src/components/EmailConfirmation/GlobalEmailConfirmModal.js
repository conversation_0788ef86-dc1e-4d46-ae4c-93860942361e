import React, { PureComponent } from "react"
import EmailConfirmModal from "./EmailConfirmRedux"

let _okCallbecks = []
let _cancelCallbecks = []
let _setState
let _isVisible = false

const initialState = {
  visible: false,
  title: undefined,
  isSelectedUser: undefined,
}

export default class GlobaleConfirmModal extends PureComponent {
  state = { ...initialState }

  componentDidMount() {
    _setState = ({ visible, title, isSelectedUser }) => {
      this.setState({
        visible: visible !== undefined ? visible : true,
        title,
        isSelectedUser,
      })
    }
  }

  onClear = () => {
    _isVisible = false
    _okCallbecks = []
    _cancelCallbecks = []
    this.setState({ ...initialState })
  }

  onOk = () => {
    _okCallbecks.forEach((callback) => {
      callback()
    })
    this.onClear()
  }

  onCancel = () => {
    _cancelCallbecks.forEach((callback) => {
      callback()
    })
    this.onClear()
  }

  render() {
    const { visible, title, isSelectedUser } = this.state

    return (
      <>
        {visible && (
          <EmailConfirmModal
            title={title}
            isSelectedUser={isSelectedUser}
            onClose={() => this.setState({ visible: false })}
          />
        )}
      </>
    )
  }
}

const emailConfirmIsVisible = () => _isVisible

const showEmailConfirm = ({ visible, title, isSelectedUser }) => {
  _setState && _setState({ visible, title, isSelectedUser })
}

export { emailConfirmIsVisible, showEmailConfirm }
