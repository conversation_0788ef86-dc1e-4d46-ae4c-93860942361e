import React from "react"
import Typography from "components/Typography"
import l from "utils/intl"
import { setConfirm } from "utils/confirm"
import styles from "./emailConfirmModal.module.scss"

export const confirmEmailSent = (newEmail, isCurrentUser) =>
  setConfirm({
    title: l("Confirm email"),
    width: 415,
    message: (
      <>
        <Typography className={styles.modalText} type="div" variant="text">
          {l("We have sent an email to ")}
          <span className={styles.email}>{newEmail}</span>
        </Typography>
        <Typography className={styles.modalText} type="div" variant="text">
          {isCurrentUser
            ? l(
                "Please follow the link in the email to complete the change of your email address."
              )
            : l(
                "To complete the change of an email, please ask a user to follow the link in the email."
              )}
        </Typography>
      </>
    ),
    cancelButtonProps: { style: { display: "none" } },
  })
