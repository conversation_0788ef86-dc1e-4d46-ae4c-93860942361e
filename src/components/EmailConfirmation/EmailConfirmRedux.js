import { connect } from "react-redux"

import actions from "actions/userSettingActions"
import staffActions from "actions/staffActions"

import { currentUserInformationSelector } from "selectors/informationSelector"
import { selectedUser } from "selectors/staffSelectors"

import EmailConfirmModal from "./EmailConfirmModal"

const mapStateToProps = (state, props) => {
  const { currentUser } = currentUserInformationSelector(state)
  const user = selectedUser(state)

  return {
    user: props.isSelectedUser ? user : currentUser,
  }
}

const mapDispatchToProps = (dispatch, props) => ({
  updateUser: (values, successCallback, failureCallback) =>
    dispatch(actions.updateInfo(values, successCallback, failureCallback)),
  sync: () => {
    if (props.isSelectedUser) {
      return
    }
    return dispatch(staffActions.syncCurrent())
  },
})

export default connect(mapStateToProps, mapDispatchToProps)(EmailConfirmModal)
