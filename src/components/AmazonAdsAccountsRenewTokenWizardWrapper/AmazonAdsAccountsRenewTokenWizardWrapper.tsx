import React, { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

import amazonAdsAccountsRenewTokenWizardActions from "actions/amazonAdsAccountsRenewTokenWizardActions"

import {
  currentStepSelector,
  isRedirectingSelector,
} from "selectors/amazonAdsAccountsRenewTokenWizardSelectors"

import { AmazonAdsAccountsRenewTokenWizard } from "components/AmazonAdsAccountsRenewTokenWizard"

import { useIsDevMode, useSearchParams } from "hooks"

import { SearchParams } from "types/AmazonAdsAccountTypes"

const { loadState, endRedirecting } = amazonAdsAccountsRenewTokenWizardActions

export const AmazonAdsAccountsRenewTokenWizardWrapper = () => {
  const dispatch = useDispatch()

  const currentStep = useSelector(currentStepSelector)

  const isRedirecting = useSelector(isRedirectingSelector)

  const { code } = useSearchParams<SearchParams>(["code"])

  const isDevMode = useIsDevMode()

  useEffect(() => {
    const isCodeReady: boolean = !!code && !isDevMode && !isRedirecting

    if (isCodeReady) {
      dispatch(loadState())
      dispatch(endRedirecting())
    }
  }, [isDevMode, code])

  if (!currentStep) {
    return null
  }

  return <AmazonAdsAccountsRenewTokenWizard />
}
