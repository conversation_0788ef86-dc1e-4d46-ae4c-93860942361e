import { useDispatch, useSelector } from "react-redux"

import gridActions from "actions/gridActions"
import languagesActions from "actions/languagesActions"
import translationsActions from "actions/translationsActions"

import {
  filtersOptionsSelector,
  messagesSelector,
  translationsSearchOptionsSelector,
  translationsTotalCountSelector,
} from "selectors/translationsSelectors"

const {
  // @ts-ignore
  deleteTranslation,
  // @ts-ignore
  get: getMessagesData,
  // @ts-ignore
  getCategories,
} = translationsActions
//   @ts-ignore
const { getAll: getLanguages } = languagesActions
//   @ts-ignore
const { pushUrl } = gridActions

export const useTranslations = () => {
  const dataSource = useSelector(messagesSelector)
  const searchOptions = useSelector(translationsSearchOptionsSelector)
  const totalCount = useSelector(translationsTotalCountSelector)
  const selectFiltersOptions = useSelector(filtersOptionsSelector)

  const dispatch = useDispatch()

  const getMessages = <Type,>(searchOptions: Type): void => {
    dispatch(getMessagesData(searchOptions))
  }

  const getAdditionalData = (): void => {
    dispatch(getLanguages())
    dispatch(getCategories())
  }

  const deleteTranslationHandler = ({
    id,
    language,
  }: {
    id: number | string
    language: string
  }): void => {
    dispatch(
      deleteTranslation(`${id},${language}`, () => {
        dispatch(
          pushUrl({
            page: 1,
            sort: searchOptions.sort,
          })
        )
      })
    )
  }

  return {
    dataSource,
    searchOptions,
    totalCount,
    selectFiltersOptions,
    getMessages,
    getAdditionalData,
    deleteTranslationHandler,
  }
}
