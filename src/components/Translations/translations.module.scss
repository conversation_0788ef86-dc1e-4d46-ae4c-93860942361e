@import "assets/styles/variables.scss";

.button {
  align-items: center;
  display: inline-flex;
  justify-content: flex-start;
  padding-right: 15px;
  width: 100%;
  min-width: 135px;
  max-width: 135px;
  border-radius: 2px;
  height: 32px;
  padding: 4.8px 15px;

  .statusLabel.statusLabel {
    font-weight: 500;
    margin: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .icon {
    margin-right: 12px;
    position: relative;
  }

  &.Translated {
    background-color: rgba(0, 85, 204, 0.2);
    border: solid 1px rgba(0, 85, 204, 0.3);
    color: $text_main;
    cursor: default;

    .statusLabel {
      color: $text_main;
    }
  }

  &.Approved {
    background-color: rgba(139, 195, 74, 0.2);
    border: solid 1px rgba(139, 195, 74, 0.3);
    color: $text_main;
    cursor: default;

    .statusLabel {
      color: $text_main;
    }
  }

  &.Pending {
    background-color: rgba(0, 85, 204, 0.2);
    border: solid 1px rgba(0, 85, 204, 0.3);
    color: $text_main;
    cursor: default;

    .statusLabel {
      color: $text_main;
    }
  }
}

.statusColumn {
  min-width: 145px;
}
