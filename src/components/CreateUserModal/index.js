import React, { useCallback, useState } from "react"
import PropTypes from "prop-types"
import Modal from "components/shared/Modal"

import l from "utils/intl"
import CreateUser from "components/CreateUser"
import { setConfirm, FORM_IS_CHANGED } from "utils/confirm"

import styles from "./createUserModal.module.scss"

export default function CreateUserModal({
  setVisible,
  userRoleSides,
  visible,
  defaultRole,
  reloadUsers,
}) {
  const [formIsChenged, setFormIsChenged] = useState()

  const onCancel = useCallback(() => {
    if (formIsChenged) {
      setConfirm({
        template: FORM_IS_CHANGED,
        onOk: () => {
          setFormIsChenged(false)
          setVisible(false)
        },
      })
      return
    }
    setVisible(false)
  }, [setFormIsChenged, setVisible, formIsChenged])

  return (
    <>
      {visible && (
        <Modal
          className={styles.modal}
          footer={null}
          okText={l("Create")}
          onCancel={onCancel}
          title={l("Create User")}
          visible={true}
          width={580}
          keyboard={false}
        >
          <CreateUser
            closeAction={setVisible}
            userRoleSides={userRoleSides}
            defaultRole={defaultRole}
            onSuccess={reloadUsers}
            onCancel={onCancel}
            onFormChange={setFormIsChenged}
          />
        </Modal>
      )}
    </>
  )
}

CreateUserModal.defaultValues = {
  userRoleSides: ["CUSTOMER"],
  visible: false,
  defaultRole: "user",
}

CreateUserModal.defaultProps = {
  reloadUsers: () => {},
}

CreateUserModal.propTypes = {
  setVisible: PropTypes.func.isRequired,
  userRoleSide: PropTypes.string,
  visible: PropTypes.bool,
}
