import type {
  RenderTableRowActions,
  TableBaseData,
  TableEntryParams,
} from "@develop/fe-library"
import type {
  FilterPaginationValues,
  FilterSortValues,
} from "@develop/fe-library/dist/lib/types"

import type { AmazonSellerCentralAccount } from "types/Models/AmazonSellerCentralAccount"
import { GetAmazonSellerCentralAccountsRequestParams } from "types/RequestParams/GetAmazonSellerCentralAccountsRequestParams"

export type AmazonSellerCentralAccountsTableData = AmazonSellerCentralAccount &
  TableBaseData

export type AmazonSellerCentralAccountTableEntryParams = TableEntryParams<
  AmazonSellerCentralAccount & TableBaseData
>

export type RenderProductsTableRowActions =
  RenderTableRowActions<AmazonSellerCentralAccount>

export type AmazonSellerCentralAccountsTableFilterValues = FilterSortValues &
  FilterPaginationValues &
  Pick<
    GetAmazonSellerCentralAccountsRequestParams,
    "all" | "id" | "email" | "is_active" | "invites_count"
  >

export type AmazonSellerCentralAccountFormModalCommonProps = {
  isVisible: boolean
  setVisible: (visible: boolean) => void
  onReloadPage: () => void
}
