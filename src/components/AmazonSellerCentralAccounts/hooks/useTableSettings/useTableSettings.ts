import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import type { OnChangeTableSettingsParams } from "@develop/fe-library"

import amazonSellerCentralAccountActions from "actions/amazonSellerCentralAccountActions"

import { amazonSellerCentralAccountsTableSettingsSelector } from "selectors/amazonSellerCentralAccountsSelectors"

import { checkIsFunction } from "utils/validationHelper"

const { getTableSettings, updateTableSettings, updateDefaultTableSettings } =
  amazonSellerCentralAccountActions

export const useTableSettings = () => {
  const dispatch = useDispatch()

  const { settings, pageSize } = useSelector(
    amazonSellerCentralAccountsTableSettingsSelector
  )

  const handleChangeSettings = useCallback(
    ({
      actionType,
      closeModal,
      settings: settingsNew,
    }: OnChangeTableSettingsParams): void => {
      const payload = {
        settings: settingsNew,
        pageSize,
      }

      const successCallback = () => {
        dispatch(getTableSettings({}))

        if (!!closeModal && checkIsFunction(closeModal)) {
          closeModal()
        }
      }

      switch (actionType) {
        case "saveSettingsAsDefault": {
          dispatch(updateDefaultTableSettings({ payload, successCallback }))
          break
        }
        case "resetSettingsToDefault": {
          dispatch(updateTableSettings({ payload, successCallback }))
          break
        }
        case "saveContent": {
          dispatch(
            updateTableSettings({ payload, successCallback: closeModal })
          )
          break
        }
        case "saveSettings": {
          dispatch(updateTableSettings({ payload }))
          break
        }
        default:
          break
      }
    },
    [pageSize]
  )

  const handleChangePageSize = useCallback(
    (pageSizeNew: number) => {
      if (pageSizeNew === pageSize) {
        return
      }

      const payload = { pageSize: pageSizeNew }

      dispatch(
        updateTableSettings({
          payload,
        })
      )
    },
    [pageSize]
  )

  return {
    settings,
    handleChangeSettings,
    pageSize,
    handleChangePageSize,
  }
}
