import { connect } from "react-redux"

import staffActions from "actions/staffActions"
import cacheAPIActions from "actions/cacheAPIActions"

import { selectedUser } from "selectors/staffSelectors"

import PhoneConfirmationContainer from "./PhoneConfirmationContainer"

const { purgeCache } = cacheAPIActions

const {
  checkCode,
  sendCode,
  syncCurrent: syncCurrentUser,
  update: updateUser,
  getUserById,
} = staffActions

const mapStateToProps = (state, props) => {
  const {
    customer: { customer },
    staff: { currentUser },
  } = state
  const user = selectedUser(state)

  const { code = "de" } = customer?.country || {}
  const { id: userId, phone } =
    (props.isSelectedUser ? user : currentUser) || {}

  return {
    defaultCountryCode: code,
    phone,
    userId,
  }
}

const mapDispatchToProps = (dispatch, props) => ({
  checkCode: (...args) => dispatch(checkCode(...args)),
  sendCode: (...args) => dispatch(sendCode(...args)),
  syncCurrentUser: (...args) => dispatch(syncCurrentUser(...args)),
  updateUser: (...args) => dispatch(updateUser(...args)),
  purgeCache: (...args) => {
    if (props.isSelectedUser) {
      return
    }
    return dispatch(purgeCache(...args))
  },
  getUserById: (id) => {
    if (!props.isSelectedUser) {
      return
    }
    return dispatch(getUserById({ params: { id } }))
  },
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(PhoneConfirmationContainer)
