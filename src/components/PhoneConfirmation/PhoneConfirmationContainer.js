import React, { Component } from "react"
import PropTypes from "prop-types"
import PhoneConfirmationView from "./PhoneConfirmationView"
import RegisterPhoneView from "./RegisterPhoneView"

import { checkIsArray } from "utils/arrayHelpers"
import { pushEvent } from "utils/gtmLoader"

import { EVENTS_NAMES } from "consts/gtm"

class PhoneConfirmationContainer extends Component {
  state = {
    phoneIsSaved: false,
    codeError: undefined,
    errorMessage: undefined,
    successMessage: undefined,
    phoneErrorMessage: undefined,
  }

  sendCode = async (phone, channel, withVerification) => {
    const { sendCode, updateUser, userId, purgeCache, getUserById } = this.props

    this.setState({
      errorMessage: undefined,
      successMessage: undefined,
      phoneErrorMessage: undefined,
    })

    updateUser(
      {
        queryParams: {
          id: userId,
        },
        payload: {
          phone,
        },
      },
      () => {
        if (withVerification) {
          pushEvent({
            event: EVENTS_NAMES.requestVerification,
            phone_nr: phone || "",
          })

          sendCode(
            { channel },
            ({ message }) => {
              this.setState({ successMessage: message })
            },
            ({ message }) => this.setState({ errorMessage: message })
          )
        } else {
          this.setState({ phoneIsSaved: true })
          purgeCache(true, undefined)
          getUserById(userId)
        }
      },
      (errors) => {
        const phoneField = checkIsArray(errors)
          ? errors?.find(({ field }) => field === "phone")
          : null

        if (phoneField) {
          this.setState({
            phoneErrorMessage: phoneField.message,
          })
        }
      }
    )
  }

  checkCode = (code) => {
    const { checkCode, syncCurrentUser } = this.props

    this.setState({
      phoneIsSaved: false,
      codeError: undefined,
      successMessage: undefined,
    })

    checkCode(
      { code },
      ({ message }) => {
        syncCurrentUser()
        this.setState({
          phoneIsSaved: true,
          successMessage: message,
        })
      },
      (errors) => {
        const [{ message: codeError }] = errors
        this.setState({
          codeError,
        })
      }
    )
  }

  componentDidUpdate(prevProps, prevState, _) {
    if (this.props.codeError !== prevState.codeError) {
      this.setState({ codeError: this.props.codeError })
    }
  }

  render() {
    const {
      codeError,
      errorMessage,
      phoneIsSaved,
      successMessage,
      phoneErrorMessage,
    } = this.state

    const { isRegistration, savePhoneError } = this.props

    if (isRegistration) {
      return (
        <RegisterPhoneView
          {...this.props}
          phoneIsSaved={phoneIsSaved}
          codeError={codeError}
          errorMessage={errorMessage}
          clearErrors={() => {
            this.setState({
              codeError: null,
              errorMessage: null,
              successMessage: null,
              phoneErrorMessage: null,
            })
          }}
          savePhoneError={savePhoneError}
          onCancel={this.onCancel}
          onConfirm={this.checkCode}
          onSendCode={this.sendCode}
          successMessage={successMessage}
          phoneErrorMessage={phoneErrorMessage}
        />
      )
    }

    return (
      <PhoneConfirmationView
        {...this.props}
        phoneIsSaved={phoneIsSaved}
        codeError={codeError}
        errorMessage={errorMessage}
        onCancel={this.onCancel}
        onConfirm={this.checkCode}
        onSendCode={this.sendCode}
        successMessage={successMessage}
        phoneErrorMessage={phoneErrorMessage}
      />
    )
  }
}

PhoneConfirmationContainer.propTypes = {
  onCodeChange: PropTypes.func,
  withConfirm: PropTypes.bool,
  codeError: PropTypes.string,
  savePhoneError: PropTypes.string,
  changeView: PropTypes.func.isRequired,
  checkCode: PropTypes.func.isRequired,
  sendCode: PropTypes.func.isRequired,
  syncCurrentUser: PropTypes.func.isRequired,
  twoFAEnabled: PropTypes.number.isRequired,
  updateUser: PropTypes.func.isRequired,
  userId: PropTypes.number.isRequired,
  purgeCache: PropTypes.func.isRequired,
  isRegistration: PropTypes.bool,
  getUserById: PropTypes.func.isRequired,
}

export default PhoneConfirmationContainer
