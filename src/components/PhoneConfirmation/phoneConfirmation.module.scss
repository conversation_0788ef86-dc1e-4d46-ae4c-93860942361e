@import "assets/styles/variables.scss";

.button,
.sendButton {
  margin-bottom: 20px;
}

.savePhoneButton {
  margin-top: 10px;
  margin-bottom: 0;
}

.verificationButton,
.savePhoneInput {
  margin-bottom: 10px;
}

.radioGroup {
  margin-top: 5px;
  margin-bottom: 10px;
}

.alert.alert {
  margin-top: 10px;
}

.radioLabel.radioLabel {
  color: $text_second;
  font-size: 12px;
}

.radioGroup {
  display: block;
}

.button.button.button {
  min-width: 120px;
}

.button.button.button {
  margin-right: 10px;

  &:last-child {
    margin-right: 0;
  }
}

.codeInput {
  margin-bottom: 10px;
}

.confirmButton {
  margin-bottom: 0;
}

@media (max-width: $sm) {
  .sendButton {
    margin-bottom: 10px;
  }

  .radioGroup {
    padding-top: 5px;
  }

  .button.button.button {
    width: 100%;
  }

  .sendButton {
    margin-bottom: 20px;
  }
}
