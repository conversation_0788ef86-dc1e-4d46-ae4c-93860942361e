import React, { PureComponent } from "react"
import l from "utils/intl"
import Modal from "components/shared/Modal"
import PhoneConfirmation from "./index"
import styles from "./phoneConfirmation.module.scss"

let _okCallbecks = []
let _cancelCallbecks = []
let _setState
let _isVisible = false

const initialState = {
  visible: false,
  title: undefined,
  withVerification: undefined,
  isSelectedUser: undefined,
}

export default class GlobaleConfirmModal extends PureComponent {
  state = { ...initialState }

  componentDidMount() {
    _setState = ({ visible, title, withVerification, isSelectedUser }) => {
      this.setState({
        visible: visible !== undefined ? visible : true,
        title: title || l("Phone validation"),
        withVerification,
        isSelectedUser,
      })
    }
  }

  onClear = () => {
    _isVisible = false
    _okCallbecks = []
    _cancelCallbecks = []
    this.setState({ ...initialState })
  }

  onOk = () => {
    _okCallbecks.forEach((callback) => {
      callback()
    })
    this.onClear()
  }

  onCancel = () => {
    _cancelCallbecks.forEach((callback) => {
      callback()
    })
    this.onClear()
  }

  render() {
    const { visible, title, withVerification, isSelectedUser } = this.state

    return (
      <>
        {visible && (
          <Modal
            title={title || l("Phone validation")}
            visible
            width={728}
            className={styles.modal}
            onCancel={() =>
              this.setState({
                visible: false,
              })
            }
          >
            <PhoneConfirmation
              withVerification={withVerification}
              isSelectedUser={isSelectedUser}
            />
          </Modal>
        )}
      </>
    )
  }
}

const phoneConfirmIsVisible = () => _isVisible

const showPhoneConfirm = ({
  visible,
  title,
  withVerification,
  isSelectedUser,
}) => {
  _setState && _setState({ visible, title, withVerification, isSelectedUser })
}

export { phoneConfirmIsVisible, showPhoneConfirm }
