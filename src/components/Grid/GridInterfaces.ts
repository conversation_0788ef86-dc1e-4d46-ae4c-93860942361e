export interface AmazonProductOffer {
  id: number
  price: string
  start_price: string
  buy_price: string
  listing_price: string
  shipping_price: string
  landed_price: string
  min_landed_price: string
  buy_box_price: string
  buy_box_shipping_price: string
  is_buy_box: number
  count_beat_item: number
  is_updated: number
  price_updated: string
  last_price_check: string
  buy_box_count_winners: number
  buy_box_winner_prices: string
  buy_box_count_fba_winners: number
  buy_box_fba_winner_prices: string
  buy_box_count_fba_winner_prices?: any
  buy_box_count_fbm_winners: number
  buy_box_fbm_winner_prices: string
  standard_price: string
  is_prime: number | boolean
  is_national_prime: number | boolean
  competitive_price_threshold: string
  is_featured_merchant: number | boolean
}

export interface AmazonProductStock {
  id: number
  sku: string
  stock: number
  stock_type: string
  local_stock: number
  fba_stock_updated: number
  local_stock_updated: number
  local_stock_last_check: number
}

export interface IOptimization {
  id: number
  customer_id: number
  min_price?: any
  max_price: string
  start_price?: any
  step_price?: any
  step_point: string
  min_seller_rate?: any
  min_number_estimates?: any
  use_seller: string
  unique_price?: any
  round_price: number
  round_step: string
  strategy: string
  step_price2?: any
  position?: any
  condition?: any
  subcondition?: any
  min_delivery_days?: any
  delivery_from: string
  availability_type?: any
  seller_list_type: string
  ignore_shipping: number
  step_price_based_on: string
  ignore_less_min_price: number
  reset_time?: any
  reset_type?: any
  reset_value?: any
  keep_buy_box: number
  skip_min_price_jump: number
  buybox_settings?: any
  buybox_settings_type?: any
  calculate_order_type?: any
  day_in_week: string
  optimizationSellers: any[]
  optimizationLimits: any[]
}

export interface ProductSetting {
  id: number
  profit_type: string
  vendor_price: string
  amazon_calculation_group_id: number
  package_dimension_height: string
  package_dimension_length: string
  package_dimension_width: string
  package_dimension_weight: string
  calculated_order_fee: string
  manual_order_fee: string
  calculated_fulfilment_fee: string
  manual_fulfilment_fee: string
  other_fee: string
  auto_calculated_order_fee: number
  auto_calculated_fulfilment_fee: number
}

export interface AmazonCustomerAccountClear {
  ignore_amazon_shipping: number
  global_shipping: string
  sellerId: string
}

export interface ProductInfo {
  id: number
  sku: string
  title: string
  asin: string
  date_inserted: string
  date_updated: string
  amazon_customer_account_id: number
  deleted: number
  image: string
  group?: string
  type?: string
  product_group_id: number
  optimization_template_id?: string
  optimization_id: number
  comments: string
  min_shipping_hours?: string
  max_shipping_hours?: string
  optimization_active: number
  other_fee_max_fee?: number
  current_profit: string
  hash?: string
  prices_hash?: string
  stock_type: string
  zero_price: string
  fulfilment_fee: string
  fulfilment_non_pan_eu_fee: string
  other_fee: string
  vendor_price: string
  order_fee: string
  tax_amount: string
  min_price: string | number
  max_price: string | number
  condition: string
  min_price_type: string
  min_price_auto_type: string
  min_price_auto_value: string
  max_price_type: string
  max_price_auto_type: string
  max_price_auto_value: string
  used: number
  min_price_auto_field: string
  max_price_auto_field: string
  marketplace_id: string
  local_fulfilment_fee_updated: number
  shipment_tax_amount: string
  date_last_merchant_listing: string
  use_strategy_logger: number
  last_ping_time: number
  stand_alone_price: string
  old_optimization_active: number
  use_sale_price: number
  recommended_retail_price: string
  amazonProductOffer: AmazonProductOffer
  amazonProductStock: AmazonProductStock
  optimization: IOptimization
  optimizationTemplate: any
  amazonProductInfo: AmazonProductInfo | null
  productGroup: any
  productSetting: ProductSetting
  amazonCustomerAccountClear: AmazonCustomerAccountClear
}

export interface AmazonProductInfo {
  id: number
  last_buy_box_price: string
  last_buy_box_date: string
  last_buy_box_step: string
  order_count_last_30: number
  order_last_date: string
  order_last_check_date: string
  sales_rank_1: number
  sales_rank_category_1: string
  sales_rank_2: number
  sales_rank_category_2: string
  bb_last_24h_count: number
  bb_last_24h_with_duration: number
  bb_last_24h_without_duration: number
  bb_last_24h_percent_with: string
  bb_last_24h_last_update: string
}

export interface IOptimizationTemplate {
  id: number
  title: string
  optimization_id: number
  optimization: IOptimization
}
