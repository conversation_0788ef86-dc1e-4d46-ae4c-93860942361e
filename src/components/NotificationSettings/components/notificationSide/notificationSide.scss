@import "assets/styles/variables.scss";
.notification-side {
  &__title {
    margin-bottom: 10px !important;
  }

  &__body {
    display: grid;
    grid-template-columns: 1fr;

    @media all and (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      column-gap: 20px;
    }

    @media all and (min-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
      column-gap: 20px;
    }

    @media all and (min-width: 1366px) {
      grid-template-columns: repeat(4, 1fr);
      column-gap: 20px;
    }

    @media all and (min-width: 1920px) {
      grid-template-columns: repeat(5, 1fr);
      column-gap: 20px;
    }
  }
}
