@import "assets/styles/variables.scss";

@mixin checkbox {
  margin-right: 10px;
}

.notification-block {
  padding: 20px;
  border-radius: 5px;
  border: solid 1px $border_main;
  background-color: $main_bg;
  margin-bottom: 10px;

  @media all and (min-width: 728px) {
    margin-bottom: 20px;
  }

  @media (max-width: $xs) {
    padding: 10px;

    &:last-child {
      margin-bottom: 20px;
    }
  }

  &__title {
    display: flex;
    align-items: center;
    font-family: Roboto, sans-serif;
    margin-bottom: 20px;

    &.hasError {
      margin-bottom: 10px;
    }

    @media (max-width: $xs) {
      margin-bottom: 15px;
    }

    .checkbox {
      @include checkbox;
    }
  }
}
