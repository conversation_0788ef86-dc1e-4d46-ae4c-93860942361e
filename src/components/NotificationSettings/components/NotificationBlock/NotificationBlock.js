import React from "react"
import { useDispatch, useSelector } from "react-redux"
import { Checkbox } from "antd"
import { Popover, Typography } from "@develop/fe-library"
import cn from "classnames"
import PropTypes from "prop-types"

import notificationActions from "actions/notificationActions"

import { currentCustomersSelector } from "selectors/customerSelectors"

import NotificationUser from "components/NotificationSettings/components/notificationUser/NotificationUser"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"
import { NOTIFICATIONS_TYPES } from "utils/notificationConstants"

import NotificationErrorMessage from "../NotificationErrorMessage/NotificationErrorMessage"

import styles from "./notificationBlock.module.scss"

import "./notificationBlock.scss"

const renderBlock = ({ component, isNeedPopover }) => {
  return isNeedPopover ? (
    <Popover
      placement="top"
      content={l(
        "Notifications are unavailable. Enabled product to enable its notifications."
      )}
    >
      {component}
    </Popover>
  ) : (
    component
  )
}

const NotificationBlock = ({
  title,
  selection,
  all,
  users,
  id,
  hasError,
  errorMessage,
  hasCaseHandler,
  side,
}) => {
  const customer = useSelector(currentCustomersSelector)
  const { use_lost_module } = customer
  const hasSetupLostAndFound = !!use_lost_module
  const canDisabled =
    !hasSetupLostAndFound && side !== NOTIFICATIONS_TYPES.GENERAL

  const dispatch = useDispatch()

  const updateUsers = (userIds, isActive) => {
    dispatch(notificationActions.selectUsers(id, userIds, isActive))
  }

  const checkboxHandler = () => {
    if (canDisabled) {
      return
    }

    return updateUsers(
      users.map(({ id }) => id),
      !all
    )
  }

  const builderNotificationHandler = ({ userID, isActive }) => {
    return () => {
      if (canDisabled) {
        return
      }

      return updateUsers([userID], !isActive)
    }
  }

  const isNeedDisable = hasCaseHandler || canDisabled

  return renderBlock({
    component: (
      <div
        className={cn("notification-block", {
          [styles.isDisabled]: canDisabled,
        })}
      >
        <div className={cn("notification-block__title", { hasError })}>
          <Checkbox
            checked={all}
            className={"checkbox"}
            disabled={canDisabled}
            indeterminate={selection}
            onClick={checkboxHandler}
          />
          <Typography variant="--font-body-text-5">{title}</Typography>
        </div>
        <div className="notification-block__users">
          {hasCaseHandler ? (
            <NotificationUser
              disabled={isNeedDisable}
              email={""}
              firstName={"Case manager"}
              is_active={1}
              is_new={false}
              user_id={0}
            />
          ) : null}
          {hasError && <NotificationErrorMessage errorMessage={errorMessage} />}
          {checkIsArray(users)
            ? users.map(
                ({ id: user_id, is_active, firstName, email, is_new }) => (
                  <NotificationUser
                    key={user_id}
                    block_id={id}
                    disabled={canDisabled}
                    email={email}
                    firstName={firstName}
                    hasError={hasError}
                    is_active={is_active}
                    is_new={is_new}
                    user_id={user_id}
                    onClick={builderNotificationHandler({
                      userID: user_id,
                      isActive: is_active,
                    })}
                  />
                )
              )
            : null}
        </div>
      </div>
    ),
    isNeedPopover: canDisabled,
  })
}

NotificationBlock.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  title: PropTypes.string.isRequired,
  selection: PropTypes.bool,
  all: PropTypes.bool,
  hasError: PropTypes.bool,
  hasCaseHandler: PropTypes.bool,
  errorMessage: PropTypes.string,
  users: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
      firstName: PropTypes.string.isRequired,
      email: PropTypes.string.isRequired,
      is_active: PropTypes.number.isRequired,
    })
  ),
}

NotificationBlock.defaultProps = {
  hasError: false,
}

export default NotificationBlock
