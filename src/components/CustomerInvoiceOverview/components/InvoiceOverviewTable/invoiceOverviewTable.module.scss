@import "assets/styles/variables.scss";

.container {
  :global(.ant-table-placeholder) {
    border-left: 1px solid $table-border-color;
    border-right: 1px solid $table-border-color;
  }
}

.tableHeader {
  display: flex;
  align-items: center;
  width: 100%;
  height: 52px;
  padding: 0 20px;
  border-left: 1px solid $table-border-color;
  border-right: 1px solid $table-border-color;
  justify-content: space-between;

  @media (max-width: $xs) {
    flex-direction: column;
    height: auto;
    align-items: flex-start;
    padding: 10px;
  }

  &.roundedHeader {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-top: 1px solid $table-border-color;

    @media (max-width: $xs) {
      border-top-right-radius: 0;
    }
  }
}

.headerStats {
  display: flex;

  @media (max-width: $xs) {
    width: 100%;
  }
}

.headerStat {
  font-size: 13px;

  @media (max-width: $xs) {
    width: 100%;
    flex-direction: row;
  }

  &:not(:last-child) {
    margin-right: 50px;

    @media (max-width: $xs) {
      margin-right: 0;
    }
  }
}

.footerStat {
  &:not(:last-child) {
    margin-right: 50px;

    @media (max-width: $xs) {
      margin-right: 0;
      margin-bottom: 10px;
    }
  }

  @media (max-width: $xs) {
    flex-grow: 1;
    display: flex;
    align-items: center;
  }
}

.headerStat {
  color: $input-icon-color;

  @media (max-width: $xs) {
    flex-direction: column;
  }
}
.footerStat {
  color: $black;
  font-size: 13px;
}

.statTitle,
.footerStatTitle {
  margin-right: 20px;
  display: inline-block;

  @media (max-width: $xs) {
    margin-right: 5px;
  }
}

.statTitle {
  color: $black;
}

.footerStatTitle {
  color: $input-icon-color;
}

.tableFooter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 52px;
  padding: 0 20px;
  background-color: $disabled-color;
  border-left: 1px solid $table-border-color;
  border-right: 1px solid $table-border-color;
  border-bottom: 1px solid $table-border-color;

  @media (max-width: $xs) {
    flex-direction: column;
    height: auto;
    padding: 10px;
  }

  &.roundedFooter {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;

    @media (max-width: $xs) {
      border-bottom-right-radius: 0;
    }
  }
}

.footerStats {
  display: flex;

  @media (max-width: $xs) {
    width: 100%;
    flex-direction: column;
  }
}

.exportButton {
  @media (max-width: $xs) {
    width: 100%;
    margin-top: 10px;
  }
}
