import { connect } from "react-redux"
import CustomerInvoiceOverviewContainer from "components/CustomerInvoiceOverview/CustomerInvoiceOverviewContainer"
import customerInvoiceOverviewActions from "actions/customerInvoiceOverviewActions"

const { get: getCustomerInvoiceList } = customerInvoiceOverviewActions

const mapStateToProps = (state) => {
  return {
    invoiceGroups: state.customerInvoiceOverview.invoiceGroups,
    language: state.translations.locale,
  }
}

const mapDispatchToProps = (dispatch) => ({
  getCustomerInvoiceList: (year, month) =>
    dispatch(getCustomerInvoiceList(year, month)),
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(CustomerInvoiceOverviewContainer)
