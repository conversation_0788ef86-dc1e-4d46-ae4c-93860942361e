import React, { PureComponent } from "react"
import { notification } from "antd"
import { Title, Description } from "./TextContainers"
import activeTabSetInterval from "utils/activeTabSetInterval"

import styles from "./importNotifications.module.scss"
class ImportNotificationsContainer extends PureComponent {
  constructor(props) {
    super(props)
    this.closeList = []
  }

  componentDidUpdate(prevProps) {
    const { allProductImports, history, processingId, customerId, path } =
      this.props
    const processingIsChanged = processingId !== prevProps?.processingId
    const importIsUpdated = allProductImports !== prevProps?.allProductImports
    const pathIsUpdated = path !== prevProps?.path
    if (customerId !== prevProps?.customerId) {
      allProductImports?.forEach(({ id }) => {
        notification.close(`import-${id}`)
      })
      this.closeList = []
    }
    if (importIsUpdated || processingIsChanged || pathIsUpdated) {
      allProductImports?.forEach(({ id, status, type, count_products }) => {
        const key = `import-${id}`
        const prev = prevProps.allProductImports.find(
          ({ id: _id }) => id === _id
        )
        const statusIsChanged = status !== prev?.status
        if (statusIsChanged || processingIsChanged || pathIsUpdated) {
          if (prev?.status && statusIsChanged && status === "DONE") {
            notification.close(key)
          }
          if (
            processingId !== id &&
            (status === "IN_PROGRESS" || status === "NEW") &&
            !this.closeList.includes(id)
          ) {
            notification.open({
              key,
              className: styles.processing,
              message: <Title type={type} status={status} />,
              description: (
                <Description
                  id={id}
                  type={type}
                  history={history}
                  path={path}
                  status={status}
                  countProducts={count_products}
                />
              ),
              duration: 0,
              onClose: () => this.closeList.push(id),
            })
          }
        }
      })
    }
  }

  componentDidMount() {
    if (!document.hidden) {
      setTimeout(() => {
        const { getAllImports, canUseImport } = this.props
        this.activeImportsInterval = activeTabSetInterval.setInterval(
          () => canUseImport && getAllImports(),
          10000
        )
      }, 2000)
    }
  }

  componentWillUnmount() {
    this.activeImportsInterval && clearInterval(this.activeImportsInterval)
  }

  render() {
    return null
  }
}

export default ImportNotificationsContainer
