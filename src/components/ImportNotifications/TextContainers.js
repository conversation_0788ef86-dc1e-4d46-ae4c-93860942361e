import React from "react"
import { But<PERSON> } from "antd"
import { CheckCircleOutlined } from "@ant-design/icons"
import { ROUTES } from "@develop/fe-library/dist/routes"

import Typography from "components/Typography"
import CircularSpinner from "components/shared/CircularSpinner"

import l from "utils/intl"

import styles from "./importNotifications.module.scss"

const {
  REPRICER_ROUTES: { PATH_REPRICER_IMPORT },
} = ROUTES

const getTitle = {
  NEW: {
    edit: () => l("Pending"),
    import: () => l("Pending"),
  },
  IN_PROGRESS: {
    edit: () => l("Processing"),
    import: () => l("Processing"),
  },
}

const getDescription = {
  NEW: {
    edit: () =>
      l(
        "Please wait. Your request is pending execution. More details are available on the import page."
      ),
    import: () =>
      l(
        "Please wait. Your request is pending execution. More details are available on the import page."
      ),
  },
  IN_PROGRESS: {
    edit: (count_products) =>
      l(
        "{count_products} products will be changed. More details are available on the Import page.",
        { count_products }
      ),
    import: () =>
      l(
        "Product import is in progress. More details are available on the Import page"
      ),
  },
}

export const Title = ({ status, type }) => {
  const isLoaded = status === "IN_PROGRESS" || status === "NEW"
  return (
    <div className={styles.titleBox}>
      <div className={styles.iconBox}>
        {isLoaded ? (
          <CircularSpinner />
        ) : (
          <CheckCircleOutlined className={styles.iconDone} />
        )}
      </div>
      <Typography className={styles.title} type="div" variant="text">
        {getTitle?.[status]?.[type]()}
      </Typography>
    </div>
  )
}

export const Description = ({
  id,
  history,
  type,
  countProducts,
  status,
  path,
}) => {
  const changeUrlHandler = () => {
    const url = `${PATH_REPRICER_IMPORT}?&id=${id}&`

    return path !== PATH_REPRICER_IMPORT ? window.open(url) : history.push(url)
  }

  return (
    <>
      <Typography className={styles.description} type="div" variant="text">
        {getDescription?.[status]?.[type](countProducts)}
      </Typography>
      <Button className={styles.btn} onClick={changeUrlHandler}>
        {l("Details")}
      </Button>
    </>
  )
}
