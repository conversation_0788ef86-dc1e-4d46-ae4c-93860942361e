@import "assets/styles/variables.scss";

.processing,
.done {
  background-color: $info_bg_alert;
  padding: 10px;
  max-width: 300px;
  :global(.ant-notification-notice-message) {
    margin-bottom: 0;
  }
  :global(.ant-notification-notice-close) {
    right: 10px;
    top: 10px;
  }
  :global(.anticon-close) {
    font-size: 16px;
  }
}

.titleBox {
  .iconBox {
    position: absolute;
    :global(.anticon) {
      font-size: 20px;
    }
    .iconDone {
      color: $icon_done;
    }
  }
}

.title {
  margin-left: 30px;
  line-height: 1.5;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
}

.done {
  background-color: $done_bg_alert;
}

.description {
  line-height: 1.5;
  font-size: 12px;
  margin-bottom: 10px;
  margin-left: 30px;
}

.btn {
  width: 100%;
  font-size: 14px;
  font-weight: 500;
}
