import { connect } from "react-redux"
import productImportActions from "actions/productImportActions"
import ImportNotificationsContainer from "./ImportNotificationsContainer"
import { permissionsSelector } from "selectors/userSelectors"

const { getAllImports } = productImportActions

const mapStateToProps = (state) => {
  const {
    router: { location },
    productImport: { allProductImports },
    productGroups: { processingId },
    customer: { customer },
  } = state

  const { productImportList } = permissionsSelector(state)

  return {
    path: location?.pathname,
    allProductImports,
    processingId,
    customerId: customer?.id,
    canUseImport: !!productImportList && !!customer?.id,
  }
}

const mapDispatchToProps = (dispatch) => ({
  getAllImports: () => dispatch(getAllImports()),
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(ImportNotificationsContainer)
