import { connect } from "react-redux"
import CreateR<PERSON>View from "./CreateRoleView"

import selectedRoleActions from "actions/selectedRoleActions"
import { permissionsSelector } from "selectors/userSelectors"

const { getController, createRole } = selectedRoleActions

const mapStateToProps = (state) => {
  const { rbacRoleManage } = permissionsSelector(state)
  return {
    canManage: !!rbacRoleManage,
  }
}

const mapDispatchToProps = {
  getController,
  createRole,
}

export default connect(mapStateToProps, mapDispatchToProps)(CreateRoleView)
