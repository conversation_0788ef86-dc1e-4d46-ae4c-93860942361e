import React from "react"
import PropTypes from "prop-types"
import { Box, Checkbox, Grid, Typography } from "@develop/fe-library"
import FormattedMessage from "components/FormattedMessage"
import l from "utils/intl"

import styles from "./roleValidateMessageView.module.scss"

const RoleValidateMessageView = ({
  controllers,
  warningType,
  controllerTitle,
  permissionTitle,
}) => {
  const isEnable = warningType === "required"

  const title = isEnable
    ? "You are about to enable <span>{controller}: {permission}</span> permission. This requires all of the following permissions to be enabled:"
    : "You are about to disable <span>{controller}: {permission}</span> permission. This requires all of the following permissions to be disabled:"

  const message = isEnable
    ? "These permissions will be enabled as well. Do you want to proceed?"
    : "These permissions will be disabled as well. Do you want to proceed?"

  return (
    <Grid container flexDirection="column" gap="l" className={styles.container}>
      <Grid item>
        <FormattedMessage
          id={title}
          defaultMessage={title}
          values={{
            controller: l(controllerTitle),
            permission: l(permissionTitle),
            span: (...chunks) => (
              <span className={styles.highlightedSpan}>{chunks}</span>
            ),
          }}
        />
      </Grid>

      <Grid item>
        <Typography variant="--font-body-text-5">
          <FormattedMessage id={message} />
        </Typography>
      </Grid>

      <Grid item>
        <Grid container flexDirection="column">
          {controllers.map(({ title, children }) => (
            <Grid item>
              <Grid container gap="m" flexWrap="nowrap">
                <Grid item always="168px">
                  <FormattedMessage id={title} />
                </Grid>
                <Grid item>
                  <Grid container gap="m">
                    {children.map(({ title: permissionTitle }) => (
                      <Grid item always="168px">
                        <Grid container gap="m" align="start" flexWrap="nowrap">
                          <Grid item>
                            <Box className={styles.checkbox} align="center">
                              <Checkbox checked={isEnable} disabled={true} />
                            </Box>
                          </Grid>

                          <Grid item>
                            <FormattedMessage id={permissionTitle} />
                          </Grid>
                        </Grid>
                      </Grid>
                    ))}
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          ))}
        </Grid>
      </Grid>
    </Grid>
  )
}

export default RoleValidateMessageView

RoleValidateMessageView.defaultValue = {
  controllers: [],
}

RoleValidateMessageView.propTypes = {
  controllers: PropTypes.array,
  warningType: PropTypes.string,
  controllerTitle: PropTypes.string,
  permissionTitle: PropTypes.string,
}
