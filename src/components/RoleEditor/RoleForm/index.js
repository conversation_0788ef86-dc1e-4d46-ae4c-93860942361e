import React from "react"
import cn from "classnames"
import PropTypes from "prop-types"
import { Form, Field } from "formik"
import { Select } from "antd"
import isEmpty from "lodash/isEmpty"
import { Alert, AlertGroup, Box, Grid } from "@develop/fe-library"

import FormikWithRoleValidation from "../FormikWithRoleValidation"
import l from "utils/intl"

import styles from "./roleForm.module.scss"

import {
  SwitchField,
  SelectField as DefaultSelect,
  with<PERSON>rror<PERSON>ield,
  TextField as DefaultTextField,
} from "components/shared/FormFields"
import FormattedMessage from "components/FormattedMessage"
import { Button, PrimaryButton } from "components/shared/Buttons"
import TypographyView from "components/Typography"
import RoleOperations from "components/shared/RoleOperations"
import withOutlineLabel from "components/hocs/withOutlineLabel"

const SelectField = withErrorField(
  withOutlineLabel(DefaultSelect),
  false,
  false
)

const TextField = withError<PERSON>ield(
  withOutlineLabel(DefaultTextField),
  true,
  false
)

export default function RoleForm(props) {
  const {
    controller,
    disabledCode,
    disabledType,
    errors,
    handleReset,
    handleSubmit,
    initialValues,
    isOperationsAvailable,
    sides,
    isAdminRole,
    canManage,
  } = props

  return (
    <>
      <FormikWithRoleValidation
        controller={controller}
        validateOnBlur={false}
        initialValues={initialValues}
        onSubmit={handleSubmit}
        children={({ isSubmitting }) => {
          const isSaveButtonDisabled = isSubmitting || !canManage

          return (
            <Form noValidate>
              <Box className={cn(styles.fields, styles.block)} display="block">
                <Grid container gap="m">
                  <Grid item mSM={12}>
                    <Field
                      name="title"
                      component={TextField}
                      placeholder={l("Title")}
                    />
                  </Grid>

                  <Grid item mSM={12}>
                    <Field
                      name="id"
                      component={TextField}
                      placeholder={l("Code")}
                      disabled={disabledCode}
                    />
                  </Grid>

                  <Grid item mSM={12}>
                    <Field
                      name="type"
                      component={SelectField}
                      placeholder={l("Type")}
                      disabled={disabledType}
                      className={styles.roleSelector}
                    >
                      {sides.map(({ value, label }) => (
                        <Select.Option value={value} key={value}>
                          {label}
                        </Select.Option>
                      ))}
                    </Field>
                  </Grid>

                  <Grid item mSM={12}>
                    <Grid container gap="m">
                      <Grid item>
                        <Field name={"active"} component={SwitchField} />
                      </Grid>
                      <Grid item>{l("Active")}</Grid>
                    </Grid>
                  </Grid>

                  <Grid item mSM={12}>
                    <Box className={styles.errorAlerts} display="block">
                      {!isEmpty(errors) ? (
                        <AlertGroup>
                          {errors.map((error) => (
                            <Alert alertType="error" message={error.message} />
                          ))}
                        </AlertGroup>
                      ) : null}
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              {isOperationsAvailable ? (
                <RoleOperations options={controller} disabled={isAdminRole} />
              ) : null}

              <Box className={styles.block} display="block">
                <Grid container gap="m" justify="end">
                  <Grid item mSM={6} mLG="auto">
                    <Button
                      disabled={isSubmitting}
                      onClick={handleReset}
                      htmlType="reset"
                      fullWidth
                    >
                      <TypographyView variant="controlLabel">
                        <FormattedMessage id="Cancel" />
                      </TypographyView>
                    </Button>
                  </Grid>

                  <Grid item mSM={6} mLG="auto">
                    <PrimaryButton
                      disabled={isSaveButtonDisabled}
                      htmlType="submit"
                      fullWidth
                    >
                      <TypographyView variant="controlLabel">
                        <FormattedMessage id="Save" />
                      </TypographyView>
                    </PrimaryButton>
                  </Grid>
                </Grid>
              </Box>
            </Form>
          )
        }}
      />
    </>
  )
}

RoleForm.defaultValue = {
  controller: [],
  disabledCode: false,
  disabledType: false,
  isOperationsAvailable: false,
  errors: [],
}

RoleForm.propTypes = {
  controller: PropTypes.array,
  disabledCode: PropTypes.bool,
  disabledType: PropTypes.bool,
  handleReset: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  initialValues: PropTypes.object.isRequired,
  isOperationsAvailable: PropTypes.bool,
  sides: PropTypes.array.isRequired,
  errors: PropTypes.array,
}
