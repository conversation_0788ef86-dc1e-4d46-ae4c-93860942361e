import React from "react"
import { Typography, Box } from "@develop/fe-library"
import FormattedMessage from "components/FormattedMessage"

import l from "utils/intl"
import ln from "utils/localeNumber"

import styles from "./baRevenuePreviewTableHeader.module.scss"

export const BARevenuePreviewTableHeader = ({
  avgPaidContractValue,
  avgPaidContractValueDiscounted,
  avgPaidCustomerValue,
  avgPaidCustomerValueDiscounted,
  countPaidCustomers,
  countTrialCustomers,
  totalAmount,
  totalAmountDiscounted,
}) => {
  return (
    <Box
      flexDirection="column"
      padding="m 0 m s"
      marginTop="m"
      className={styles.wrapper}
    >
      <div className={styles.line}>
        <Typography variant="--font-body-text-8" className={styles.label}>
          <FormattedMessage id="Trial:" />
        </Typography>
        <Typography variant="--font-body-text-9" className={styles.entry}>
          <FormattedMessage id="Customer quantity:" />
          <span className={styles.amount}>{countTrialCustomers}</span>
        </Typography>
      </div>
      <div className={styles.line}>
        <Typography variant="--font-body-text-8" className={styles.label}>
          <FormattedMessage id="Paid:" />
        </Typography>
        <Typography variant="--font-body-text-9" className={styles.entry}>
          <FormattedMessage id="Customer quantity:" />
          <span className={styles.amount}>{countPaidCustomers}</span>
        </Typography>
        <Typography variant="--font-body-text-9" className={styles.entry}>
          <FormattedMessage
            defaultMessage="Average customer value:"
            id="Average customer value:"
          />
          <span className={styles.amount}>
            {ln(avgPaidCustomerValueDiscounted, 2)} (
            {ln(avgPaidCustomerValue, 2)})
          </span>
        </Typography>
        <Typography variant="--font-body-text-9" className={styles.entry}>
          <FormattedMessage
            defaultMessage="Average contract value:"
            id="Average contract value:"
          />
          <span className={styles.amount}>
            {ln(avgPaidContractValueDiscounted, 2)} (
            {ln(avgPaidContractValue, 2)})
          </span>
        </Typography>
      </div>
      <div className={styles.line}>
        <Typography
          type="div"
          variant="--font-body-text-8"
          className={styles.label}
        >
          <FormattedMessage id="Invoice amount:" />
          <span className={styles.strikethrough}>{ln(totalAmount, 2)}</span>
          {ln(totalAmountDiscounted, 2)} {l("€/Month")}
        </Typography>
      </div>
    </Box>
  )
}
