import { useDispatch, useSelector } from "react-redux"

import customerSubscriptionActions from "actions/customerSubscriptionActions"

import {
  customerBasSubscriptionInfo,
  customerBasSubscriptionStatisticSelector,
  customerSubscriptionPageOptionsSelector,
  customerSubscriptionSearchOptionsSelector,
  filtersOptionsCustomerBasSubscriptionSelector,
} from "selectors/customerSubscriptionSelectors"

import { PRODUCTS } from "consts/product"

const {
  // @ts-ignore
  getCustomerBasSubscription,
  // @ts-ignore
  getCustomerBasSubscriptionStatistic,
  // @ts-ignore
  getSubscriptionTypes,
} = customerSubscriptionActions

export const useBARevenuePreview = () => {
  const dataSource = useSelector(customerBasSubscriptionInfo)
  const customerBasSubscriptionStatistic = useSelector(
    customerBasSubscriptionStatisticSelector
  )
  const selectFiltersOptions = useSelector(
    filtersOptionsCustomerBasSubscriptionSelector
  )
  const searchOptions = useSelector(customerSubscriptionSearchOptionsSelector)
  const { totalCount } = useSelector(customerSubscriptionPageOptionsSelector)

  const dispatch = useDispatch()

  const getCustomerBasSubscriptionHandler = <Type,>(
    searchOptions: Type
  ): void => {
    dispatch(getCustomerBasSubscription(searchOptions))
  }
  const getAdditionalData = (): void => {
    dispatch(getCustomerBasSubscriptionStatistic())
    dispatch(getSubscriptionTypes(PRODUCTS.bas))
  }

  return {
    dataSource,
    totalCount,
    customerBasSubscriptionStatistic,
    selectFiltersOptions,
    searchOptions,
    getCustomerBasSubscriptionHandler,
    getAdditionalData,
  }
}
