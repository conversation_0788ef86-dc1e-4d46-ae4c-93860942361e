import React from "react"

import { STATUSES, VISIBLE_STATUSES } from "consts"

import { useAmazonAdsAccountsWizard } from "./hooks"

import { componentsMap, preloadErrorComponentsMap } from "./componentsMap"

export const AmazonAdsAccountsWizardModal = () => {
  const { currentStep, currentStepStatus, onSubmitCurrentStep, onReset } =
    useAmazonAdsAccountsWizard()

  if (currentStepStatus === STATUSES.preloadError) {
    const PreloadErrorComponent = preloadErrorComponentsMap[currentStep]

    return <PreloadErrorComponent onReset={onReset} />
  }

  if (!VISIBLE_STATUSES[currentStepStatus]) {
    return null
  }

  const Component = componentsMap[currentStep]

  return <Component onReset={onReset} onSubmit={onSubmitCurrentStep} />
}
