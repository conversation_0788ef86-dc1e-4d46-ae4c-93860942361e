import React, { useMemo } from "react"
import { Alert, Box, FormItems, Modal } from "@develop/fe-library"

import type { StepModalProps } from "components/AmazonAdsAccountsWizardModal/AmazonAdsAccountsWizardModalTypes"
import { ZONES_LIST } from "components/AmazonAdsAccountsWizardModal/constants"

import l from "utils/intl"

import { useSelectZoneModal } from "./hooks"

export const SelectZoneModal = ({
  onSubmit: onSubmitProp,
  onReset,
}: StepModalProps) => {
  const { form, onSubmit, zoneErrorMessage, isSubmitButtonDisabled } =
    useSelectZoneModal({
      onSubmit: onSubmitProp,
    })

  const zones = useMemo(() => {
    return ZONES_LIST.map((zone) => {
      return {
        label: l(zone.label),
        value: zone.value,
      }
    })
  }, [])

  return (
    <Modal
      visible
      cancelButtonText={l("Cancel")}
      okButtonText={l("Next")}
      title={l("Select zone of Amazon Ads account")}
      width="--modal-size-m"
      okButtonProps={{
        disabled: isSubmitButtonDisabled,
      }}
      onCancel={onReset}
      onOk={onSubmit}
    >
      <Box flexDirection="column" gap="m">
        {zoneErrorMessage ? (
          <Alert alertType="error" message={zoneErrorMessage} />
        ) : null}

        <FormItems
          form={form}
          items={[
            {
              type: "radioGroup",
              name: "zone",
              inputProps: {
                labelVariant: "--font-body-text-2",
                items: zones,
                gridItemTemplateProps: {
                  mSM: 12,
                },
                gap: "m",
              },
              gridItemProps: {
                always: 12,
              },
            },
          ]}
        />
      </Box>
    </Modal>
  )
}
