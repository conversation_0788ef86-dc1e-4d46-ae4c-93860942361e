import React from "react"
import { <PERSON><PERSON>, <PERSON>, But<PERSON> } from "@develop/fe-library"

import l from "utils/intl"

export const ExpiredRepricerSubscription = ({ offerType, expiredDate }) => {
  return (
    <Box flexDirection="column" width="100%">
      <Alert
        alertType="warning"
        action={
          <Button
            variant="secondary"
            onClick={() =>
              alert(
                `it's ${offerType} from src/components/Subscriptions/components/Alerts/ExpiredRepricerSubscription/ExpiredRepricerSubscription.tsx`
              )
            }
          >
            {l("Renew")}
          </Button>
        }
        description={l("Expiration date: {expiredDate}", {
          expiredDate,
        })}
        message={l("Some of {offerType} subscriptions will expire soon!", {
          offerType,
        })}
      />
    </Box>
  )
}
