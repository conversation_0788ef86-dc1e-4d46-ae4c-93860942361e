import React from "react"
import { useSelector } from "react-redux"
import { Button } from "@develop/fe-library"

import { amazonAccountsInfoSelector } from "selectors/amazonAccountsSelectors"
import { repricerSubscriptionPlansSelector } from "selectors/repricerSubscriptionSelectors"

import { useStartSetupWizard } from "hooks"

import l from "utils/intl"

import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import { RepricerAlertButtonAction } from "./useNewRepricerAlertDataTypes"

export const useNewRepricerAlertData = () => {
  const { current } = useSelector(repricerSubscriptionPlansSelector)

  const { activeAmazonAccountsCount, amazonAccountsEnabledForRepricerCount } =
    useSelector(amazonAccountsInfoSelector)

  const startSetupWizard = useStartSetupWizard()

  const setupRepricerWizardHandler = (): void => {
    startSetupWizard({ productName: PRODUCTS.repricer })
  }

  const isRepricerHasPlan: boolean = current !== null

  const isRepricerWithoutAccounts: boolean = activeAmazonAccountsCount === 0
  const isRepricerHasAnyActiveAccounts: boolean =
    amazonAccountsEnabledForRepricerCount >= 1

  const isShowRepricerInfoAlert: boolean =
    isRepricerHasPlan &&
    (isRepricerWithoutAccounts || !isRepricerHasAnyActiveAccounts)

  const repricerAlertButtonAction: RepricerAlertButtonAction =
    isRepricerWithoutAccounts ? (
      <Button onClick={setupRepricerWizardHandler}>
        {l("Connect account")}
      </Button>
    ) : undefined

  const alertDescriptionText: string = isRepricerWithoutAccounts
    ? l(
        "You have an active {productName} subscription, but your Amazon account is not connected. Please link your Amazon account.",
        {
          productName: PRODUCT_NAMES[PRODUCTS.repricer],
        }
      )
    : l(
        "You have an active {productName} subscription but at least one Amazon account is not linked to {productName}. Please connect your Amazon account.",
        {
          productName: PRODUCT_NAMES[PRODUCTS.repricer],
        }
      )

  return {
    isShowRepricerInfoAlert,
    repricerAlertButtonAction,
    alertDescriptionText,
  }
}
