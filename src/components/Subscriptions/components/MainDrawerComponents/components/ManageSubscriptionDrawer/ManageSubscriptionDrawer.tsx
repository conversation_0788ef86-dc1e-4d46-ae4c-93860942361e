import React, { useContext, useState } from "react"

import { MainDrawerContext } from "pages/SubscriptionsPage/context"

import l from "utils/intl"

import { MAIN_DRAWER_COMPONENT_TITLE } from "consts"

import {
  ManageSubscriptionMainViewStep,
  SubscriptionHistoryStep,
} from "./components"

import { MANAGE_SUBSCRIPTION_DRAWER_VIEWS } from "./constants"

import type {
  ManageSubscriptionDrawerProps,
  ManageSubscriptionDrawerViews,
} from "./ManageSubscriptionDrawerTypes"

export const ManageSubscriptionDrawer = ({ data, onClose }) => {
  const { openedModal, drawerView: initialDrawerView } =
    data as ManageSubscriptionDrawerProps

  const [drawerView, setDrawerView] = useState<ManageSubscriptionDrawerViews>(
    initialDrawerView || MANAGE_SUBSCRIPTION_DRAWER_VIEWS.MAIN_VIEW
  )

  const { updateMainDrawerData, mainDrawerData } = useContext(MainDrawerContext)

  const showInitialDrawerStep = (): void => {
    setDrawerView(MANAGE_SUBSCRIPTION_DRAWER_VIEWS.MAIN_VIEW)

    updateMainDrawerData({
      ...mainDrawerData,
      titleName: l(MAIN_DRAWER_COMPONENT_TITLE.MANAGE_SUBSCRIPTION),
      onBack: undefined,
    })
  }

  const showSubscriptionHistoryStep = (): void => {
    setDrawerView(MANAGE_SUBSCRIPTION_DRAWER_VIEWS.SUBSCRIPTION_HISTORY)

    if (!mainDrawerData.open) {
      return
    }

    updateMainDrawerData({
      ...mainDrawerData,
      titleName: l("Subscription history"),
      onBack: showInitialDrawerStep,
    })
  }

  const drawerViewElementsMap: Record<
    ManageSubscriptionDrawerViews,
    JSX.Element
  > = {
    [MANAGE_SUBSCRIPTION_DRAWER_VIEWS.MAIN_VIEW]: (
      <ManageSubscriptionMainViewStep
        closeDrawer={onClose}
        openedModal={openedModal}
        showSubscriptionHistoryStep={showSubscriptionHistoryStep}
      />
    ),
    [MANAGE_SUBSCRIPTION_DRAWER_VIEWS.SUBSCRIPTION_HISTORY]: (
      <SubscriptionHistoryStep />
    ),
  }

  return drawerViewElementsMap[drawerView]
}
