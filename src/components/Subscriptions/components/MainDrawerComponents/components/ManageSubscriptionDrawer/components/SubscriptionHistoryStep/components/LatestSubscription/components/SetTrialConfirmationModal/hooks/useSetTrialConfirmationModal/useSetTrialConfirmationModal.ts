import { useMemo } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { FormItem } from "@develop/fe-library"
import { DATE_OUTPUT_FORMATS } from "@develop/fe-library/dist/consts"
import { buildFormSubmitFailureCallback } from "@develop/fe-library/dist/utils"
import { yupResolver } from "@hookform/resolvers/yup"
import { format } from "date-fns"

import customerActions from "actions/customerActions"
import repricerSubscriptionActions from "actions/repricerSubscriptionActions"

import { currentCustomersSelector } from "selectors/customerSelectors"
import { repricerCurrentSubscriptionPlanSelector } from "selectors/repricerSubscriptionSelectors"
import { staffCurrentUserLocaleSelector } from "selectors/staffSelectors"
import { languageSelector } from "selectors/translationsSelectors"

import { getDateTimePickerLabels } from "utils/datePicker"
import l from "utils/intl"
import { translateOptions } from "utils/translateOptions"

import { LOCALES } from "consts/locales"
import { SUBSCRIPTION_IDS } from "consts/repricerSubscription"

import { buildSchema } from "./utils"

import {
  DURATION_TYPE_OPTIONS,
  DURATION_TYPES,
  FORM_INITIAL_VALUES,
} from "./constants"

import type {
  DurationTypesValue,
  SetTrialConfirmationModalFormValues,
  UseSetTrialConfirmationModalProps,
} from "./UseSetTrialConfirmationModalTypes"

const { setTrialRepricerCustomerPlan } = repricerSubscriptionActions
const { getCurrentCustomer } = customerActions

export const useSetTrialConfirmationModal = ({
  onCancel,
}: UseSetTrialConfirmationModalProps) => {
  const dispatch = useDispatch()

  const language = useSelector(languageSelector)
  const locale = useSelector(staffCurrentUserLocaleSelector)
  const customer = useSelector(currentCustomersSelector)
  const currentPlan = useSelector(repricerCurrentSubscriptionPlanSelector)

  const isFreemiumPlan: boolean =
    currentPlan?.payment_module_version_id === SUBSCRIPTION_IDS.FREEMIUM

  const isTrialPlan: boolean =
    currentPlan?.payment_module_version_id === SUBSCRIPTION_IDS.TRIAL

  const isPaidSubscription: boolean = !!currentPlan?.is_paid

  const isSubscriptionInactive: boolean = !currentPlan

  const isChangeCurrentPlan: boolean =
    (isSubscriptionInactive || isFreemiumPlan) &&
    !isTrialPlan &&
    !isPaidSubscription

  const customerTitle: string = customer?.title || ""

  const form = useForm<SetTrialConfirmationModalFormValues>({
    mode: "onChange",
    defaultValues: FORM_INITIAL_VALUES,
    resolver: yupResolver<SetTrialConfirmationModalFormValues>(buildSchema()),
  })

  const { handleSubmit: formHandleSubmit, reset, watch, setError } = form

  const durationType: DurationTypesValue = watch("durationType")

  const items = useMemo<FormItem[]>(
    () => [
      {
        type: "select",
        name: "durationType",
        inputProps: {
          label: l("Trial duration"),
          options: translateOptions(DURATION_TYPE_OPTIONS),
        },
        gridItemProps: { always: 12 },
      },
      {
        type: "numeric",
        name: "duration",
        inputProps: {
          label: l("Duration in days"),
          isNegativeAllowed: false,
          isDecimalAllowed: false,
          isZeroAllowed: false,
        },
        gridItemProps: { always: 12 },
        isVisible: durationType === DURATION_TYPES.DURATION,
      },
      {
        type: "date",
        name: "dateFinish",
        inputProps: {
          label: l("Select end date"),
          isGlobal: true,
          fromDate: new Date(),
          language: LOCALES[language],
          locale: LOCALES[locale],
          labels: getDateTimePickerLabels({}),
        },
        gridItemProps: { always: 12 },
        isVisible: durationType === DURATION_TYPES.DATE_FINISH,
      },
    ],
    [durationType, language, locale]
  )

  const handleClose = (): void => {
    reset()
    onCancel()
  }

  const handleSubmit = formHandleSubmit(
    async ({
      dateFinish,
      duration,
      durationType,
    }: SetTrialConfirmationModalFormValues): Promise<void> => {
      const handleSuccess = (): void => {
        dispatch(getCurrentCustomer(false, handleClose))
      }

      await dispatch(
        setTrialRepricerCustomerPlan({
          payload: {
            dateFinish:
              durationType === DURATION_TYPES.DATE_FINISH && !!dateFinish
                ? format(dateFinish, DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT)
                : undefined,
            duration:
              durationType === DURATION_TYPES.DURATION ? duration : undefined,
          },
          successCallback: handleSuccess,
          failureCallback:
            buildFormSubmitFailureCallback<SetTrialConfirmationModalFormValues>(
              { setError }
            ),
        })
      )
    }
  )

  return {
    customerTitle,
    form,
    isChangeCurrentPlan,
    items,
    handleSubmit,
    handleClose,
  }
}
