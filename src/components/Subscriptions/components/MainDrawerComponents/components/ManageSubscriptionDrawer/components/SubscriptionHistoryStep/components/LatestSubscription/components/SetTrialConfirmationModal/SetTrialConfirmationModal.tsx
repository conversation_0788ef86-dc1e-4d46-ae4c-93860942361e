import React from "react"
import { <PERSON>, FormItems, Modal, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import { useSetTrialConfirmationModal } from "./hooks"

import type { SetTrialConfirmationModalProps } from "./SetTrialConfirmationModalTypes"

export const SetTrialConfirmationModal = ({
  onCancel,
}: SetTrialConfirmationModalProps) => {
  const {
    isChangeCurrentPlan,
    customerTitle,
    form,
    items,
    handleSubmit,
    handleClose,
  } = useSetTrialConfirmationModal({ onCancel })

  return (
    <Modal
      visible
      cancelButtonText={l("Back")}
      okButtonText={l("Confirm")}
      title={l("Set Trial subscription")}
      width="--modal-size-m"
      onCancel={handleClose}
      onOk={handleSubmit}
    >
      <Box flexDirection="column" gap="l">
        <Box flexDirection="column" gap="m">
          <Typography color="--color-text-main" variant="--font-body-text-7">
            {isChangeCurrentPlan
              ? l(
                  "Upon confirmation, the {productName} “Trial” subscription will be set for the customer <b>{customerTitle}</b>.",
                  {
                    productName: PRODUCT_NAMES[PRODUCTS.repricer],
                    customerTitle,
                    b: (...chunks) => <b>{chunks}</b>,
                  }
                )
              : l(
                  "Upon confirmation, the {productName} “Trial” subscription will be set as “Next subscription” for the customer <b>{customerTitle}</b>.",
                  {
                    productName: PRODUCT_NAMES[PRODUCTS.repricer],
                    customerTitle,
                    b: (...chunks) => <b>{chunks}</b>,
                  }
                )}
          </Typography>

          <Typography color="--color-text-main" variant="--font-body-text-7">
            {l(
              "The Trial subscription will be set according to the following settings:"
            )}
          </Typography>
        </Box>

        <FormItems
          // @ts-expect-error
          form={form}
          gridContainerProps={{ gap: "m" }}
          items={items}
        />
      </Box>
    </Modal>
  )
}
