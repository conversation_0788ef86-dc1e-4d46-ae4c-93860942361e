import React from "react"
import { SelectPlanModal } from ".."
import { Box, Pagination, Typography } from "@develop/fe-library"
import { checkIsArray, getObjectKeys } from "@develop/fe-library/dist/utils"

import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"

import { DEFAULT_VALUES } from "consts"

import { LatestSubscription, SubscriptionItem } from "./components"

import { useSubscriptionHistoryStep } from "./hooks"

export const SubscriptionHistoryStep = () => {
  const {
    currentPage,
    currentPlan,
    isDowngradePlanModalVisible,
    pageSize,
    plansHistory,
    totalCount,
    handleClickDowngradeSubscription,
    handleCloseDowngradePlanModal,
    handlePageChange,
  } = useSubscriptionHistoryStep()

  return (
    <>
      <Box flexDirection="column" height="100%">
        <LatestSubscription
          onClickDowngradeSubscription={handleClickDowngradeSubscription}
        />

        {getObjectKeys(plansHistory).length !== 0 ? (
          <Box display="block">
            {getObjectKeys(plansHistory).map((date) => (
              <Box key={date} display="block">
                <Box
                  backgroundColor="--color-background-second"
                  hasBorder={{ bottom: true }}
                  mLG={{ padding: "m l" }}
                  padding="m"
                >
                  <Typography variant="--font-body-text-8">{date}</Typography>
                </Box>

                {checkIsArray(plansHistory[date])
                  ? plansHistory[date].map(
                      ({
                        id,
                        subscription_end_date,
                        subscription_start_date,
                        size,
                        units_limit,
                        units_used,
                        repricerSubscriptionInfo: { localizedName } = {},
                      }) => {
                        const localStartDate: string = subscription_start_date
                          ? convertToLocalDate(subscription_start_date)
                          : l(DEFAULT_VALUES.NA)

                        const localEndDate: string = subscription_end_date
                          ? convertToLocalDate(subscription_end_date)
                          : l(DEFAULT_VALUES.NA)

                        return (
                          <SubscriptionItem
                            key={id}
                            endDate={localEndDate}
                            size={size}
                            startDate={localStartDate}
                            subscriptionTitle={localizedName}
                            unitsLimit={units_limit}
                            unitsUsed={units_used}
                          />
                        )
                      }
                    )
                  : null}
              </Box>
            ))}
          </Box>
        ) : null}

        <Box
          hasBorder={{ top: true }}
          justify="center"
          marginTop="auto"
          padding="m"
          width="100%"
        >
          <Pagination
            currentPage={currentPage}
            mobileViewBreakpoint="mXL"
            pageSizeLimit={pageSize}
            total={totalCount}
            onPageChange={handlePageChange}
          />
        </Box>
      </Box>

      {isDowngradePlanModalVisible ? (
        <SelectPlanModal
          isDowngradePlan
          duration={currentPlan?.duration}
          level={currentPlan?.repricerSubscriptionInfo?.level}
          size={currentPlan?.size}
          title={l("Repricer subscription downgrade")}
          onClose={handleCloseDowngradePlanModal}
        />
      ) : null}
    </>
  )
}
