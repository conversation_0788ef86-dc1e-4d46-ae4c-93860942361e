import { Option } from "@develop/fe-library"

import { SetTrialConfirmationModalFormValues } from "./UseSetTrialConfirmationModalTypes"

export const DURATION_TYPES = {
  DURATION: "duration",
  DATE_FINISH: "dateFinish",
} as const

export const DURATION_TYPE_OPTIONS: Option[] = [
  { value: DURATION_TYPES.DURATION, label: "Duration in days" },
  { value: DURATION_TYPES.DATE_FINISH, label: "Select end date" },
]

export const DEFAULT_DURATION = 14

export const FORM_INITIAL_VALUES: SetTrialConfirmationModalFormValues = {
  durationType: DURATION_TYPES.DURATION,
  duration: DEFAULT_DURATION,
  dateFinish: null,
} as const
