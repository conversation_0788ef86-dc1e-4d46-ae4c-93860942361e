import React, { useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import customerActions from "actions/customerActions"
import repricerSubscriptionActions from "actions/repricerSubscriptionActions"

import {
  isRepricerOverdraftDisabledSelector,
  isRepricerPlanAutoUpgradeEnabledSelector,
  setUseRepricerOverdraftStatusSelector,
  setUseRepricerPlanAutoUpgradeStatusSelector,
} from "selectors/customerSelectors"
import {
  repricerCurrentSubscriptionPlanSelector,
  repricerSubscriptionPlansSelector,
} from "selectors/repricerSubscriptionSelectors"
import { languageSelector } from "selectors/translationsSelectors"
import { isCustomerOrUserModeSelector } from "selectors/userSelectors"

import { SUBSCRIPTION_MODAL_IDS } from "components/Subscriptions/constants"

import setConfirm from "utils/confirm"
import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"
import { replaceLanguageCode } from "utils/replaceLanguageCode"

import { DEFAULT_VALUES } from "consts"
import { ASYNC_STATUSES } from "consts/async"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"
import { SUBSCRIPTION_IDS } from "consts/repricerSubscription"

import { CancelSubscriptionConfirmationMessage } from "../../components"

import { RepricerCustomerPlan } from "types/Models"

import { UseManageSubscriptionMainViewStepProps } from "./UseManageSubscriptionMainViewStepTypes"

const {
  cancelRepricerCustomerPlan,
  renewCurrentRepricerCustomerPlan,
  cancelNextRepricerCustomerPlan,
} = repricerSubscriptionActions
const {
  setUseRepricerOverdraft,
  setUseRepricerPlanAutoUpgrade,
  getCurrentCustomer,
} = customerActions

export const useManageSubscriptionMainViewStep = ({
  openedModal,
  closeDrawer,
}: UseManageSubscriptionMainViewStepProps) => {
  const dispatch = useDispatch()

  const currentPlan = useSelector(repricerCurrentSubscriptionPlanSelector)

  const [isSetNextPlanModalVisible, setIsSetNextPlanModalVisible] =
    useState<boolean>(openedModal === SUBSCRIPTION_MODAL_IDS.SET_NEXT_PLAN)
  const [isUpgradePlanModalVisible, setIsUpgradePlanModalVisible] =
    useState<boolean>(openedModal === SUBSCRIPTION_MODAL_IDS.UPGRADE_PLAN)
  const [isChangeDurationModalVisible, setIsChangeDurationModalVisible] =
    useState(false)

  const language = useSelector(languageSelector)
  const isCustomerOrUserMode = useSelector(isCustomerOrUserModeSelector)
  const { next } = useSelector(repricerSubscriptionPlansSelector)
  const setUseRepricerOverdraftStatus = useSelector(
    setUseRepricerOverdraftStatusSelector
  )
  const setUseRepricerPlanAutoUpgradeStatus = useSelector(
    setUseRepricerPlanAutoUpgradeStatusSelector
  )
  const isRepricerOverdraftDisabled = useSelector(
    isRepricerOverdraftDisabledSelector
  )
  const isRepricerPlanAutoUpgradeEnabled = useSelector(
    isRepricerPlanAutoUpgradeEnabledSelector
  )

  const {
    auto_renew,
    duration,
    payment_module_version_id,
    subscription_end_date,
    subscription_start_date,
    is_paid,
    repricerSubscriptionInfo: {
      localizedName: currentSubscriptionLocalizedName,
    } = {},
  } = currentPlan || ({} as Partial<RepricerCustomerPlan>)

  const isDatesAvailable: boolean =
    !!subscription_end_date && !!subscription_start_date

  const isChangeDurationAvailable = false // !isCustomerOrUserMode && isDatesAvailable // TODO: Finish this feature when BE is ready

  const isSubscriptionHistoryButtonVisible: boolean = !isCustomerOrUserMode

  const isSetUseRepricerOverdraftLoading: boolean =
    setUseRepricerOverdraftStatus === ASYNC_STATUSES.PENDING

  const isSetUseRepricerPlanAutoUpgradeStatusLoading: boolean =
    setUseRepricerPlanAutoUpgradeStatus === ASYNC_STATUSES.PENDING

  const hasNextSubscription: boolean = !!next

  const nextSubscriptionStart: string = next?.subscription_start_date
    ? convertToLocalDate(next?.subscription_start_date)
    : l(DEFAULT_VALUES.NA)

  const nextSubscriptionFinish: string = next?.subscription_end_date
    ? convertToLocalDate(next?.subscription_end_date)
    : l(DEFAULT_VALUES.NA)

  const nextSubscriptionLocalizedName: string | undefined =
    next?.repricerSubscriptionInfo?.localizedName

  const nextSubscriptionSize = next?.size

  const isNextSubscriptionFreemium: boolean =
    !!next && next.payment_module_version_id === SUBSCRIPTION_IDS.FREEMIUM

  const currentSubscriptionLocalStartDate: string = subscription_start_date
    ? convertToLocalDate(subscription_start_date)
    : l(DEFAULT_VALUES.NA)

  const currentSubscriptionLocalEndDate: string = subscription_end_date
    ? convertToLocalDate(subscription_end_date)
    : l(DEFAULT_VALUES.NA)

  const isYearlySubscription: boolean = duration === 12

  const isTrialPlan: boolean =
    payment_module_version_id === SUBSCRIPTION_IDS.TRIAL

  const isFreemiumPlan: boolean =
    payment_module_version_id === SUBSCRIPTION_IDS.FREEMIUM

  const isFreePlan: boolean = !is_paid

  const isSubscriptionCancelled: boolean = auto_renew === 0 && !next

  const isAutomaticUpgradeBlockVisible: boolean =
    !isFreePlan && !isYearlySubscription && !isRepricerOverdraftDisabled

  const moreInfoLink: string = replaceLanguageCode({
    url: "https://support.sellerlogic.com/{localeCode}/general/subscriptions-overview",
    language,
  })

  const handleCancelSubscription = (): void => {
    setConfirm({
      title: isFreePlan
        ? l("Warning. Subscription cancellation.")
        : l("Cancel subscription"),
      message: (
        <CancelSubscriptionConfirmationMessage
          isFreePlan={isFreePlan}
          subscriptionLocalizedName={
            currentSubscriptionLocalizedName || l(DEFAULT_VALUES.NA)
          }
        />
      ),
      onOk: () => {
        dispatch(
          cancelRepricerCustomerPlan({
            successCallback: () => {
              dispatch(getCurrentCustomer(false, undefined))

              if (isFreePlan) {
                closeDrawer?.()
              }
            },
          })
        )
      },
      okText: l("Confirm"),
      onCancel: () => {},
    })
  }

  const handleRenewSubscription = (): void => {
    setConfirm({
      title: l("Renew current subscription"),
      message: l(
        "Currently selected {repricer} subscription will be restored and renewed at its end. Are you sure you want to proceed?",
        { repricer: PRODUCT_NAMES[PRODUCTS.repricer] }
      ),
      onOk: () => {
        dispatch(
          renewCurrentRepricerCustomerPlan({
            successCallback: () => {
              dispatch(getCurrentCustomer(false, undefined))
            },
          })
        )
      },
      okText: l("Confirm"),
      onCancel: () => {},
    })
  }

  const handleToggleRepricerOverdraftSwitch = (): void => {
    dispatch(
      setUseRepricerOverdraft({
        value: isRepricerOverdraftDisabled ? 1 : 0,
        successCallback: () => {
          dispatch(getCurrentCustomer(false, undefined))
        },
      })
    )
  }

  const handleToggleRepricerPlanAutoUpgradeSwitch = (): void => {
    dispatch(
      setUseRepricerPlanAutoUpgrade({
        value: isRepricerPlanAutoUpgradeEnabled ? 0 : 1,
        successCallback: () => {
          dispatch(getCurrentCustomer(false, undefined))
        },
      })
    )
  }

  const handleClickSetNextPlanButton = (): void => {
    setIsSetNextPlanModalVisible(true)
  }

  const handleClickCancelNextPlanButton = (): void => {
    dispatch(cancelNextRepricerCustomerPlan())
  }

  const handleCloseSetNextPlanModal = (): void => {
    setIsSetNextPlanModalVisible(false)
  }

  const handleClickUpgradePlanButton = (): void => {
    setIsUpgradePlanModalVisible(true)
  }

  const handleCloseUpgradePlanModal = (): void => {
    setIsUpgradePlanModalVisible(false)
  }

  const handleOpenChangeDurationModal = (): void => {
    if (!isChangeDurationAvailable) {
      return
    }

    setIsChangeDurationModalVisible(true)
  }

  const handleCloseChangeDurationModal = (): void => {
    setIsChangeDurationModalVisible(false)
  }

  return {
    currentPlan,
    currentSubscriptionLocalEndDate,
    currentSubscriptionLocalStartDate,
    currentSubscriptionLocalizedName,
    hasNextSubscription,
    isAutomaticUpgradeBlockVisible,
    isChangeDurationAvailable,
    isChangeDurationModalVisible,
    isFreePlan,
    isFreemiumPlan,
    isNextSubscriptionFreemium,
    isRepricerOverdraftDisabled,
    isRepricerPlanAutoUpgradeEnabled,
    isSetNextPlanModalVisible,
    isSetUseRepricerOverdraftLoading,
    isSetUseRepricerPlanAutoUpgradeStatusLoading,
    isSubscriptionCancelled,
    isSubscriptionHistoryButtonVisible,
    isTrialPlan,
    isUpgradePlanModalVisible,
    isYearlySubscription,
    moreInfoLink,
    nextSubscriptionFinish,
    nextSubscriptionLocalizedName,
    nextSubscriptionSize,
    nextSubscriptionStart,
    handleCancelSubscription,
    handleClickCancelNextPlanButton,
    handleClickSetNextPlanButton,
    handleClickUpgradePlanButton,
    handleCloseChangeDurationModal,
    handleCloseSetNextPlanModal,
    handleCloseUpgradePlanModal,
    handleOpenChangeDurationModal,
    handleRenewSubscription,
    handleToggleRepricerOverdraftSwitch,
    handleToggleRepricerPlanAutoUpgradeSwitch,
  }
}
