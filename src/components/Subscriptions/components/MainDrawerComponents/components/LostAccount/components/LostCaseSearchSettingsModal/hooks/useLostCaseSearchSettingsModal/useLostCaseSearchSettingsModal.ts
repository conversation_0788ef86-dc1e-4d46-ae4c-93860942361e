import { useDispatch, useSelector } from "react-redux"
import groupBy from "lodash/groupBy"

import lostCaseSearchLockActions from "actions/lostCaseSearchLockActions"

import {
  lostAmazonAccountsSelector,
  memoizedLostCaseTypesSelector,
} from "selectors/amazonAccountsSelectors"

import { checkIsArray } from "utils/arrayHelpers"

import { LostCaseType } from "types/Models/LostCaseType"

import { UseLostCaseSearchSettingsModalProps } from "./useLostCaseSearchSettingsModalTypes"
import { LostTypesForGlobalManage } from "../../LostCaseSearchSettingsModalTypes"
import {
  LostAmazonAccount,
  LostAmazonAccountType,
} from "types/LostAmazonAccountTypes"

// @ts-expect-error
const { get: getLostCaseSearchLocks, update: updateLostCaseSearchLock } =
  lostCaseSearchLockActions

export const useLostCaseSearchSettingsModal = ({
  accountId,
}: UseLostCaseSearchSettingsModalProps) => {
  const dispatch = useDispatch()

  const lostCaseType: LostCaseType[] = useSelector(
    memoizedLostCaseTypesSelector
  )
  const lostAmazonAccounts: LostAmazonAccount[] | undefined = useSelector(
    lostAmazonAccountsSelector
  )
  const {
    lostMarketplaces: marketplaces,
    lostMarketplacesLocksTypesForGlobalFilter,
  } = lostAmazonAccounts?.find((account) => account.id === accountId) || {}

  const activeMarketplaces =
    marketplaces?.filter(({ active }) => active === 1) || []

  const allLostTypeInMarketplace =
    activeMarketplaces?.reduce<LostAmazonAccountType[]>((acc, marketplace) => {
      return [...acc, ...marketplace.types]
    }, []) || []

  const structuredTypes = checkIsArray(allLostTypeInMarketplace)
    ? groupBy(allLostTypeInMarketplace, ({ id }) => id)
    : {}

  const handleLostCaseLockUpdate = (data) => {
    dispatch(
      updateLostCaseSearchLock(data, () => {
        dispatch(getLostCaseSearchLocks())
      })
    )
  }

  const handleLostCaseLockAllMarketplacesChange = (data) => {
    const { marketplace_id, ...caseSearchLock } = data

    dispatch(
      updateLostCaseSearchLock(caseSearchLock, () => {
        dispatch(getLostCaseSearchLocks())
      })
    )
  }

  const lostTypesForGlobalManage: LostTypesForGlobalManage[] =
    lostCaseType?.reduce<LostTypesForGlobalManage[]>((acc, caseType) => {
      const isActive = lostMarketplacesLocksTypesForGlobalFilter?.includes(
        caseType.id
      )
        ? false
        : structuredTypes[caseType.id]?.some(({ active }) => active)

      const data: LostTypesForGlobalManage = {
        title: caseType.title,
        id: caseType.id,
        active: isActive,
      }

      return [...acc, data]
    }, [])

  return {
    activeMarketplaces,
    lostMarketplacesLocksTypesForGlobalFilter,
    marketplaces,
    lostTypesForGlobalManage,
    handleLostCaseLockUpdate,
    handleLostCaseLockAllMarketplacesChange,
  }
}
