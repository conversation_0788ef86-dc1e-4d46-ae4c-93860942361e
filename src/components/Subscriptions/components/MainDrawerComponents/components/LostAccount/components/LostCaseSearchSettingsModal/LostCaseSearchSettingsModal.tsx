import React from "react"
import { Box, Modal, Typography } from "@develop/fe-library"
import { checkIsArray } from "@develop/fe-library/dist/utils"

import { RestrictedCheckbox } from "components/Restrict"
import Link from "components/shared/Link"

import l from "utils/intl"
import { getAmazonAccountLink } from "utils/links"

import { permissionKeys } from "consts"

import { useLostCaseSearchSettingsModal } from "./hooks"

import { LostCaseSearchSetting } from "../LostCaseSearchSetting"

import { LostCaseSearchSettingsModalProps } from "./LostCaseSearchSettingsModalTypes"

export const LostCaseSearchSettingsModal = ({
  accountId,
  onModalClose,
}: LostCaseSearchSettingsModalProps) => {
  const {
    activeMarketplaces,
    lostMarketplacesLocksTypesForGlobalFilter,
    marketplaces,
    lostTypesForGlobalManage,
    handleLostCaseLockUpdate,
    handleLostCaseLockAllMarketplacesChange,
  } = useLostCaseSearchSettingsModal({ accountId })

  return (
    <Modal
      isWithoutBodyPadding
      visible
      cancelButtonText={l("Cancel")}
      title={l("Manage case search")}
      width="--modal-size-l"
      onCancel={onModalClose}
      onClose={onModalClose}
    >
      <Box
        align="center"
        gap="m"
        hasBorder={{ bottom: true }}
        minHeight={63}
        padding="m l"
        width="100%"
      >
        <Box
          minWidth="94px"
          mXL={{
            minWidth: "126px",
          }}
        >
          <Typography color="--color-text-main" variant="--font-body-text-7">
            {l("Marketplace")}
          </Typography>
        </Box>

        {checkIsArray(activeMarketplaces) ? (
          <LostCaseSearchSetting
            isActive
            isGlobal
            accountId={accountId}
            id={activeMarketplaces[0].id}
            types={lostTypesForGlobalManage}
            updateLostCaseLock={handleLostCaseLockAllMarketplacesChange}
            lostMarketplacesLocksTypesForGlobalFilter={
              lostMarketplacesLocksTypesForGlobalFilter
            }
          />
        ) : null}
      </Box>

      {marketplaces && checkIsArray(marketplaces)
        ? marketplaces.map(
            (
              {
                active,
                amazonDomain,
                id,
                lostActive,
                sellerId,
                title,
                types,
                updatingLost,
              },
              index
            ) => (
              <Box
                key={id}
                align="center"
                gap="l"
                hasBorder={{ bottom: index < marketplaces.length - 1 }}
                minHeight={63}
                padding="m l"
                width="100%"
              >
                <RestrictedCheckbox
                  checked={active === 1}
                  disabled={!lostActive || updatingLost}
                  viewPermission={permissionKeys.sellerLogicManager}
                  onChange={(checked) =>
                    handleLostCaseLockUpdate({
                      amazon_customer_account_id: accountId,
                      marketplace_id: id,
                      case_type: null,
                      disable: checked ? 0 : 1,
                    })
                  }
                />

                <Box
                  flexDirection="column"
                  flexShrink={1}
                  width="80px"
                  mXL={{
                    flexShrink: 0,
                  }}
                >
                  <Link
                    internal={false}
                    rel="noopener noreferrer"
                    styleType="primary"
                    target="_blank"
                    text={title}
                    type="span"
                    url={getAmazonAccountLink(amazonDomain, id, sellerId)}
                    variant="text"
                  />

                  <Typography
                    variant="--font-body-text-9"
                    color={
                      active ? "--color-text-second" : "--color-text-disable"
                    }
                  >
                    {active ? l("Active") : l("Inactive")}
                  </Typography>
                </Box>

                <LostCaseSearchSetting
                  accountId={accountId}
                  id={id}
                  isActive={active === 1}
                  isGlobal={false}
                  types={types}
                  updateLostCaseLock={handleLostCaseLockUpdate}
                  lostMarketplacesLocksTypesForGlobalFilter={
                    lostMarketplacesLocksTypesForGlobalFilter
                  }
                />
              </Box>
            )
          )
        : null}
    </Modal>
  )
}
