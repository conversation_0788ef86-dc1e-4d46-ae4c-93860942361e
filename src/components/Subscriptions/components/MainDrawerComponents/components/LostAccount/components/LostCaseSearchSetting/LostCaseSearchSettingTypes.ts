import { LostAmazonAccountType } from "types/LostAmazonAccountTypes"

type UpdateLostCaseLockParams = {
  amazon_customer_account_id: number
  marketplace_id: string
  case_type: string
  disable: number
}

export type LostCaseSearchSettingProps = {
  id: string
  types: LostAmazonAccountType[]
  isActive: boolean
  accountId: number
  updateLostCaseLock: (params: UpdateLostCaseLockParams) => void
  isGlobal: boolean
  lostMarketplacesLocksTypesForGlobalFilter?: string[]
}
