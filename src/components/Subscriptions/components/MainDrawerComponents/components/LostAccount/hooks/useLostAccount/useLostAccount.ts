import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import lostCaseSearchLockActions from "actions/lostCaseSearchLockActions"
import lostCaseTypeActions from "actions/lostCaseTypeActions"

import { lostAmazonAccountsSelector } from "selectors/amazonAccountsSelectors"
import {
  currentCustomersSelector,
  isDemoAccountSelector,
  lostModuleSignedFullServiceSelector,
} from "selectors/customerSelectors"
import {
  isCustomerOrUserModeSelector,
  permissionsSelector,
} from "selectors/userSelectors"
import { modeSelector } from "selectors/viewModesSelector"

import { FULL_SERVICE_CREDENTIAL_STATUS, LOST_SERVICE_TYPE } from "consts"
import { VIEW_MODE } from "consts/viewMode"

import { UseLostAccountParams } from "./useLostAccountTypes"

// @ts-expect-error
const { get: getLostCaseSearchLocks, update: updateLostCaseSearchLock } =
  lostCaseSearchLockActions
const { get: getLostCaseTypes } = lostCaseTypeActions

export const useLostAccount = ({ accountId }: UseLostAccountParams) => {
  const [
    isLostCaseSearchSettingsModalVisible,
    setIsLostCaseSearchSettingsModalVisible,
  ] = useState(false)

  const dispatch = useDispatch()
  const lostAmazonAccounts = useSelector(lostAmazonAccountsSelector)
  const lostModuleSignedFullService = useSelector(
    lostModuleSignedFullServiceSelector
  )

  const {
    allow_lost_full_service: allowLostFullService,
    lost_service_type: lostServiceType,
  } = useSelector(currentCustomersSelector)

  const { sellerLogicManager } = useSelector(permissionsSelector)
  const viewMode = useSelector(modeSelector)
  const isDemoAccount: boolean = !!useSelector(isDemoAccountSelector)

  const canManageSL: boolean =
    sellerLogicManager && viewMode === VIEW_MODE.admin

  const {
    isNeedHideFullService,
    fsIsWizardFinished,
    merchantId,
    fs_credentials_status: fsCredentialsStatus,
    lostActive,
    updatingLost,
  } = lostAmazonAccounts.find((account) => account.id === accountId)

  const isCustomerOrUserMode = useSelector(isCustomerOrUserModeSelector)

  const isFullServiceActive: boolean =
    !!lostModuleSignedFullService &&
    !!allowLostFullService &&
    lostServiceType === LOST_SERVICE_TYPE.FULL

  const isShowFullServiceBlock: boolean =
    (!isNeedHideFullService && isFullServiceActive) || !!fsIsWizardFinished

  const isInvitationAccepted: boolean = !!merchantId

  const isWizardFinished: boolean =
    lostServiceType === LOST_SERVICE_TYPE.FULL && !!fsIsWizardFinished

  const isPermissionsStatusVisible: boolean =
    isWizardFinished && isInvitationAccepted

  const isCorrectCredentials: boolean =
    fsCredentialsStatus === FULL_SERVICE_CREDENTIAL_STATUS.CORRECT

  const isInvitationAlertVisible: boolean =
    isWizardFinished && !isInvitationAccepted

  const isPermissionsAlertVisible: boolean =
    isWizardFinished && isInvitationAccepted && !isCorrectCredentials

  const handleCaseSearchManage = (): void => {
    setIsLostCaseSearchSettingsModalVisible(true)
  }

  const handleLostCaseSearchSettingsModalClose = (): void => {
    setIsLostCaseSearchSettingsModalVisible(false)
  }

  useEffect(() => {
    if (isLostCaseSearchSettingsModalVisible) {
      dispatch(getLostCaseSearchLocks())
      dispatch(getLostCaseTypes())
    }
  }, [dispatch, isLostCaseSearchSettingsModalVisible])

  const isManageCaseSearchButtonVisible: boolean =
    canManageSL && lostActive && !updatingLost

  return {
    isInvitationAccepted,
    isCorrectCredentials,
    isCustomerOrUserMode,
    isShowFullServiceBlock,
    isPermissionsStatusVisible,
    isInvitationAlertVisible,
    isPermissionsAlertVisible,
    isDemoAccount,
    isManageCaseSearchButtonVisible,
    isLostCaseSearchSettingsModalVisible,
    handleCaseSearchManage,
    handleLostCaseSearchSettingsModalClose,
    updateLostCaseSearchLock,
  }
}
