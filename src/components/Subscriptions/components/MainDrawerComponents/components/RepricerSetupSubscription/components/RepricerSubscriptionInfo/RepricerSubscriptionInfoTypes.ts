import { CommonRepricerSectionParams } from "components/Subscriptions/components/CommonRepricerSection/CommonRepricerSectionTypes"
import { RepricerAccountMarketplaces } from "components/Subscriptions/types"

import { AutoRenewHandler } from "../../RepricerSetupSubscriptionTypes"

export type RepricerSubscriptionInfoProps = Pick<
  RepricerAccountMarketplaces,
  | "active"
  | "currentSubscriptionTitle"
  | "title"
  | "amazonMarketplaceId"
  | "amazonDomain"
  | "sellerId"
  | "autoRenew"
  | "canManageDuration"
  | "deleted"
  | "id"
  | "customerAccountId"
> & {
  accountId: CommonRepricerSectionParams["accountId"]
  productName: CommonRepricerSectionParams["productName"]
  trial: RepricerAccountMarketplaces["canBeDeleted"]
  nextPlan: boolean
  onOpenChangeDuration: () => void
  onOpenCurrentSubscription: () => void
  onAutoRenew: AutoRenewHandler
  countyFlag: string
  currentPlanPeriod: string
  autoRenewSwitchedDisabled: boolean
  language: string
  isDemoAccount: boolean
}
