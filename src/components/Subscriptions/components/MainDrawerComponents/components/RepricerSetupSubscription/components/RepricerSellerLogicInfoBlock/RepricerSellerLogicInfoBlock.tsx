import React from "react"
import { Box, Typography } from "@develop/fe-library"

import { Restrict } from "components/Restrict"

import { basicRestrictionAndAdminMode } from "utils/customRestrictions"
import l from "utils/intl"

import { permissionKeys } from "consts"

import { RepricerSellerLogicInfoBlockParams } from "./RepricerSellerLogicInfoBlockTypes"

export const RepricerSellerLogicInfoBlock = ({
  subscriptionId,
  accountId,
}: RepricerSellerLogicInfoBlockParams) => {
  return (
    <Restrict
      content=""
      customViewRestriction={basicRestrictionAndAdminMode}
      placement="top"
      viewPermission={permissionKeys.sellerLogicManager}
    >
      <Box
        flexDirection="column"
        gap="m"
        padding=" 0 m m"
        hasBorder={{
          bottom: true,
        }}
        mLG={{
          padding: "m l l",
        }}
      >
        <Restrict
          content=""
          customViewRestriction={basicRestrictionAndAdminMode}
          placement="top"
          viewPermission={permissionKeys.sellerLogicManager}
        >
          <Box gap="s" marginTop="m">
            <Typography variant="--font-body-text-7">
              {l("Account ID")}:
            </Typography>
            <Typography
              color="--color-text-second"
              variant="--font-body-text-7"
            >
              {accountId}
            </Typography>
          </Box>
        </Restrict>

        <Restrict
          content=""
          customViewRestriction={basicRestrictionAndAdminMode}
          placement="top"
          viewPermission={permissionKeys.sellerLogicManager}
        >
          <Box gap="s">
            <Typography variant="--font-body-text-7">
              {l("Subscription ID")}:
            </Typography>
            <Typography
              color="--color-text-second"
              variant="--font-body-text-7"
            >
              {subscriptionId}
            </Typography>
          </Box>
        </Restrict>
      </Box>
    </Restrict>
  )
}

export default RepricerSellerLogicInfoBlock
