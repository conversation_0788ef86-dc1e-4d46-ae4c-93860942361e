import React from "react"
import { <PERSON>, But<PERSON>, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { RepricerRevertSubscriptionParams } from "./RepricerRevertSubscriptionTypes"

export const RepricerRevertSubscription = ({
  isShowRevertBlock,
  openRevertSubscriptionHandler,
}: RepricerRevertSubscriptionParams) => {
  if (!isShowRevertBlock) return null

  return (
    <Box
      flexDirection="column"
      flexWrap="nowrap"
      padding="m"
      mLG={{
        padding: "l",
      }}
    >
      <Typography variant="--font-body-text-5">
        {l("Revert subscription")}:
      </Typography>
      <Box
        marginTop="m"
        width="100%"
        mLG={{
          width: "max-content",
        }}
      >
        <Button
          fullWidth
          variant="secondary"
          onClick={openRevertSubscriptionHandler}
        >
          {l("Revert Subscription")}
        </Button>
      </Box>
    </Box>
  )
}
