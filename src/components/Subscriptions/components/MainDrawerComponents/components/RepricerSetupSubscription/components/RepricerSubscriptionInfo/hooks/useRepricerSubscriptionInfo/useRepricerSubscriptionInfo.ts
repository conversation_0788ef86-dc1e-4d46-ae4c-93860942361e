import { useDispatch } from "react-redux"

import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"

import { REPRICER_OFFER_TYPE } from "consts/product"

import {
  AddMarketplaceHandler,
  DeleteMarketplaceHandler,
  UseRepricerSubscriptionInfoTypes,
} from "./useRepricerSubscriptionInfoTypes"

const {
  add: addAccountMarketplace,
  delete: deleteAccountMarketplace,
  getAll: getAccountMarketplaces,
} = amazonCustomerAccountMarketplaceActions

export const useRepricerSubscriptionInfo = ({
  trial,
  id,
  customerAccountId,
  amazonMarketplaceId,
  language,
  productName,
}: UseRepricerSubscriptionInfoTypes) => {
  const dispatch = useDispatch()
  const deleteMarketplaceHandler: DeleteMarketplaceHandler = (
    marketplaceId
  ) => {
    dispatch(
      deleteAccountMarketplace(marketplaceId, () =>
        dispatch(getAccountMarketplaces(false))
      )
    )
  }

  const addMarketplaceHandler: AddMarketplaceHandler = ({
    customerAccountId,
    amazonMarketplaceId,
    language,
    offerType,
  }) => {
    dispatch(
      addAccountMarketplace(
        customerAccountId,
        { amazonMarketplaceId, language, offerType },
        () => dispatch(getAccountMarketplaces(false))
      )
    )
  }

  const isDisabledCheckboxHandler = ({ trial, deleted, isDemoAccount }) => {
    return !(trial || deleted) || isDemoAccount
  }

  const deleteTrialSubscriptionHandler = (checked: boolean): void => {
    if (!checked) {
      trial && deleteMarketplaceHandler(id)
    } else {
      addMarketplaceHandler({
        customerAccountId,
        amazonMarketplaceId,
        language,
        offerType: REPRICER_OFFER_TYPE[productName],
      })
    }
  }

  return {
    isDisabledCheckboxHandler,
    deleteTrialSubscriptionHandler,
  }
}
