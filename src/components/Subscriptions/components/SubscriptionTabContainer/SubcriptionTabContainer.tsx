import React, { useState } from "react"
import { Box, Drawer } from "@develop/fe-library"

import l from "utils/intl"

import { DetailsSection } from "../DetailsSection"
import { SideBarContent } from "../SideBarContent"

const isNeedAndLogicImplemented = false

export const SubscriptionTabContainer = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false)

  const handleCloseDrawer = () => {
    setIsOpen(false)
  }

  const handleOpenDrawer = () => {
    setIsOpen(true)
  }

  return (
    <Box flexDirection="column" height="100%" width="100%">
      {isNeedAndLogicImplemented ? (
        // dev-note: This is a temporary solution for the sidebar. It will be removed after the implementation of the new sidebar.
        // TODO: DELETE REACT FRAGMENT AFTER IMPLEMENTATION THE SIDE BAR LOGIC.
        <>
          <Drawer
            isOpen={isOpen}
            title={l("Important information")}
            width="440px"
            zIndex={1001}
            onClose={handleCloseDrawer}
          >
            <SideBarContent />
          </Drawer>
          <DetailsSection
            notificationsCount={45}
            onDrawerOpen={handleOpenDrawer}
          />
        </>
      ) : null}

      <Box height="100%" width="100%">
        {children}
        {/*dev-note: This is a temporary solution for the sidebar. It will be removed after the implementation of the new sidebar.*/}
        {/*TODO: DELETE TERNARY OPERATOR AFTER IMPLEMENTATION THE SIDE BAR LOGIC.*/}
        {isNeedAndLogicImplemented ? (
          <Box
            display="none"
            dSM={{
              display: "flex",
            }}
          >
            <SideBarContent />
          </Box>
        ) : null}
      </Box>
    </Box>
  )
}
