import React from "react"
import { <PERSON> } from "react-router-dom"
import { Box, Icon, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"

import { Restrict, RestrictedSwitch } from "components/Restrict"
import LinkComponent from "components/shared/Link"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import { useAmazonAdsAccounts } from "./hooks"

const {
  GENERAL_ROUTES: { PATH_AMAZON_ADS_ACCOUNTS },
} = ROUTES

export const AmazonAdsAccounts = () => {
  const {
    amazonAdsAccounts,
    isAdsAccountsConnected,
    isLoading,
    buildHandleChange,
    handleStartAmazonAdsAccountsWizard,
  } = useAmazonAdsAccounts()

  if (isLoading) {
    return null
  }

  return (
    <Box
      flexDirection="column"
      gap="m"
      mXL={{ padding: "l", gap: "l" }}
      padding="m"
      width="100%"
    >
      <Box flexDirection="column" gap="m">
        <Typography variant="--font-body-text-2">
          {l("Amazon Ads accounts")}
        </Typography>

        {isAdsAccountsConnected ? (
          <Box display="block">
            <Typography variant="--font-body-text-9">
              {l(
                "Connect <a>Amazon Ads accounts</a> to get an Ads (PPC) cost breakdown for {productName}. Activation does not affect your {productName} subscription.",
                {
                  productName: PRODUCT_NAMES[PRODUCTS.bas],
                  a: (text) => (
                    <Restrict
                      blockedEventHandlerKeys={["onClick"]}
                      managePermission={permissionKeys.amazonAdsAccountList}
                      popoverMessage={restrictPopoverMessages.view}
                      popoverPlacement="top"
                    >
                      <LinkComponent
                        internal
                        styleType="primary"
                        text={text}
                        type="span"
                        url={PATH_AMAZON_ADS_ACCOUNTS}
                        variant="text"
                      />
                    </Restrict>
                  ),
                }
              )}
            </Typography>

            <Typography variant="--font-body-text-9">
              {l(
                "When “Off”, the data collection for the Ads (PPC) cost breakdown in {productName} will stop.",
                { productName: PRODUCT_NAMES[PRODUCTS.bas] }
              )}
            </Typography>
          </Box>
        ) : null}
      </Box>

      {isAdsAccountsConnected ? (
        <Box
          display="grid"
          dLG={{ gridTemplateColumns: "repeat(4, 1fr)" }}
          dMD={{ gridTemplateColumns: "repeat(3, 1fr)" }}
          gap="m"
          gridTemplateColumns="1fr"
          mXL={{ gridTemplateColumns: "1fr 1fr", gap: "l" }}
        >
          {amazonAdsAccounts.map(({ id, name, is_ba_active }) => (
            <Box
              key={id}
              hasBorder
              align="center"
              gap="m"
              justify="space-between"
              mXL={{ padding: "l" }}
              padding="m"
            >
              <Typography variant="--font-body-text-9">
                <Link to={`${PATH_AMAZON_ADS_ACCOUNTS}#/${id}`}>{name}</Link>
              </Typography>

              <RestrictedSwitch
                isNumeric
                isChecked={is_ba_active}
                managePermission={permissionKeys.amazonAdsAccountManage}
                popoverMessage={restrictPopoverMessages.alter}
                onChange={buildHandleChange(id)}
              />
            </Box>
          ))}
        </Box>
      ) : (
        <Box
          flexDirection="column"
          justify="center"
          mSM={{
            gap: "m",
            padding: "m",
          }}
          mXL={{
            gap: "l",
            padding: "l",
          }}
        >
          <Icon
            color="--color-icon-static"
            name="icnWarning"
            size="--icon-size-8"
          />

          <Typography textAlign="center" variant="--font-headline-3">
            {l("No Amazon Ads account is connected.")}
          </Typography>

          <Typography textAlign="center" variant="--font-body-text-7">
            {l(
              "Ads (PPC) costs breakdown is unavailable. You can add a new account on the <a>Amazon Ads account management</a> page.",
              {
                a: (text) => (
                  <Restrict
                    blockedEventHandlerKeys={["onClick"]}
                    managePermission={permissionKeys.amazonAdsAccountList}
                    popoverMessage={restrictPopoverMessages.view}
                    popoverPlacement="top"
                  >
                    <Link
                      to={PATH_AMAZON_ADS_ACCOUNTS}
                      onClick={handleStartAmazonAdsAccountsWizard}
                    >
                      {text}
                    </Link>
                  </Restrict>
                ),
              }
            )}
          </Typography>
        </Box>
      )}
    </Box>
  )
}
