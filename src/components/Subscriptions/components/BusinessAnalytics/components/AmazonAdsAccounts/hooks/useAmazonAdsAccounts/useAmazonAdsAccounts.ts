import { useDispatch, useSelector } from "react-redux"

import amazonAdsAccountsActions from "actions/amazonAdsAccountsActions"
import amazonAdsAccountsWizardActions from "actions/amazonAdsAccountsWizardActions"

import {
  amazonAdsAccountsSelector,
  isAmazonAdsAccountsLoadingSelector,
} from "selectors/amazonAdsAccountsSelectors"

import { checkIsArray } from "utils/arrayHelpers"

import { STATUSES } from "consts"

const { start } = amazonAdsAccountsWizardActions
const { updateAmazonAdsAccount } = amazonAdsAccountsActions

export const useAmazonAdsAccounts = () => {
  const dispatch = useDispatch()

  const amazonAdsAccounts = useSelector(amazonAdsAccountsSelector)
  const isLoading = useSelector(isAmazonAdsAccountsLoadingSelector)

  const isAdsAccountsConnected: boolean = checkIsArray(amazonAdsAccounts)

  const handleStartAmazonAdsAccountsWizard = (): void => {
    dispatch(start(STATUSES.initialized))
  }

  const buildHandleChange =
    (id: number) =>
    (isActive: number): void => {
      dispatch(updateAmazonAdsAccount(id, { is_ba_active: isActive }))
    }

  return {
    amazonAdsAccounts,
    isAdsAccountsConnected,
    isLoading,
    buildHandleChange,
    handleStartAmazonAdsAccountsWizard,
  }
}
