import { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import {
  amazonCustomerAccountsWithBasOrdersSelector,
  countOfAmazonAccountsSelector,
} from "selectors/amazonAccountsSelectors"
import { amazonMarketplaceRegionsSelector } from "selectors/amazonMarketplaceSelectors"
import {
  currentBasPaymentPeriod as currentBasPaymentPeriodSelector,
  includeInNextBasSubscriptionItemsSelector,
  isBasSubscriptionError as isBasSubscriptionErrorSelector,
  nextBasPaymentPlan as nextBasPaymentPlanSelector,
} from "selectors/basCustomerPlanSelector"

import { setConfirm } from "utils/confirm"
import l from "utils/intl"
import { removeWizardSessionStorage } from "utils/wizard/removeWizardSessionStorage"

import { PAYMENT_MODULE_FLEXIBLE_PERIOD_IDS } from "consts/basCustomerPlan"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import type { HandleChangeIncludeSubscription } from "./UseAmazonSellerAccountsTypes"

const { getAll: getAccounts } = amazonCustomerAccountsActions
const { displayModal, setIncludeNextBasSubscription } =
  amazonCustomerAccountsActions
const { toggleModal } = moduleSetupWizardActions
const { getBasPlan, getBasSubscriptions, setAutoRenew, cancelBasNextPlan } =
  basCustomerPlanActions

export const useAmazonSellerAccounts = () => {
  const dispatch = useDispatch()

  const accounts = useSelector(amazonCustomerAccountsWithBasOrdersSelector)
  const marketplaceRegions = useSelector(amazonMarketplaceRegionsSelector)
  const countOfAmazonAccounts = useSelector(countOfAmazonAccountsSelector)
  const currentBasPaymentPeriod = useSelector(currentBasPaymentPeriodSelector)
  const nextBasPaymentPlan = useSelector(nextBasPaymentPlanSelector)
  const isBasSubscriptionError = useSelector(isBasSubscriptionErrorSelector)
  const includeInNextBasSubscriptionItems = useSelector(
    includeInNextBasSubscriptionItemsSelector
  )

  const handleChangeIncludeSubscription: HandleChangeIncludeSubscription =
    useCallback(
      ({ isChecked, id, includeInNextBasSubscription }): void => {
        const isTrialPlan: boolean =
          currentBasPaymentPeriod === PAYMENT_MODULE_FLEXIBLE_PERIOD_IDS.TRIAL

        const isConfirmationNeeded: boolean =
          includeInNextBasSubscriptionItems.length === 1 && !isChecked

        if (isConfirmationNeeded) {
          setConfirm({
            title: l("Turning subscription off for {productName}", {
              productName: PRODUCT_NAMES[PRODUCTS.bas],
            }),
            message: l(
              "You are disconnecting the last account from the active subscription. Auto-renew and the next subscription selection won't be available. If any account will be added or switched back payment auto-renew will be restored."
            ),
            onOk: () => {
              dispatch(
                setIncludeNextBasSubscription(id, { value: 0 }, () => {
                  dispatch(getBasSubscriptions())
                  dispatch(getAccounts(false))

                  if (nextBasPaymentPlan) {
                    dispatch(
                      cancelBasNextPlan("", () => {
                        dispatch(
                          getBasPlan({ cache: false, successCallback: null })
                        )
                      })
                    )
                  }

                  if (!isTrialPlan) {
                    dispatch(
                      setAutoRenew({ value: 0 }, () => {
                        dispatch(
                          getBasPlan({ cache: false, successCallback: null })
                        )
                      })
                    )
                  }
                })
              )
            },
            onCancel: () => {},
          })

          return
        }

        dispatch(
          setIncludeNextBasSubscription(
            id,
            { value: includeInNextBasSubscription ? 0 : 1 },
            () => {
              dispatch(getBasSubscriptions())
              dispatch(getAccounts(false))

              const shouldEnableAutoRenew: boolean =
                includeInNextBasSubscriptionItems.length === 0 && !isTrialPlan

              if (shouldEnableAutoRenew) {
                dispatch(
                  setAutoRenew({ value: 1 }, () => {
                    dispatch(
                      getBasPlan({ cache: false, successCallback: null })
                    )
                  })
                )
              }
            }
          )
        )
      },
      [
        dispatch,
        currentBasPaymentPeriod,
        includeInNextBasSubscriptionItems.length,
        nextBasPaymentPlan,
      ]
    )

  const buildHandleConnectBasAccount = useCallback(
    (accountId: number) => () => {
      removeWizardSessionStorage()

      dispatch(toggleModal(false, undefined, PRODUCTS.bas, { noTrack: true }))

      dispatch(
        displayModal(true, "basProductInformation", {
          countOfAmazonAccounts,
          enable: true,
          isConnectInAccount: true,
          connectAccountId: accountId,
        })
      )
    },
    [countOfAmazonAccounts, dispatch]
  )

  return {
    accounts,
    isBasSubscriptionError,
    marketplaceRegions,
    buildHandleConnectBasAccount,
    handleChangeIncludeSubscription,
  }
}
