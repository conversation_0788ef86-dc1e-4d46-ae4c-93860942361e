import { useDispatch, useSelector } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"

import {
  activeSubscriptionId as activeSubscriptionIdSelector,
  basPaymentsPeriodSelector,
  currentBasPaymentPeriod as currentBasPaymentPeriodSelector,
  dateBasFinish as dateBasFinishSelector,
  dateBasStart as dateBasStartSelector,
  oldCurrentBasPaymentPeriod as oldCurrentBasPaymentPeriodSelector,
  oldDateBasFinish as oldDateBasFinishSelector,
  oldDateBasStart as oldDateBasStartSelector,
  oldSubscriptionId as oldSubscriptionIdSelector,
} from "selectors/basCustomerPlanSelector"
import { permissionsSelector } from "selectors/userSelectors"

import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"

import { PAYMENT_MODULE_FLEXIBLE_PERIOD_IDS } from "consts/basCustomerPlan"
import { PRODUCTS } from "consts/product"

const { displayModal } = amazonCustomerAccountsActions

export const useCurrentSubscription = () => {
  const dispatch = useDispatch()

  const activeSubscriptionId = useSelector(activeSubscriptionIdSelector)
  const basPaymentsPeriod = useSelector(basPaymentsPeriodSelector)
  const currentBasPaymentPeriod = useSelector(currentBasPaymentPeriodSelector)
  const dateBasFinish = useSelector(dateBasFinishSelector)
  const dateBasStart = useSelector(dateBasStartSelector)
  const oldCurrentBasPaymentPeriod = useSelector(
    oldCurrentBasPaymentPeriodSelector
  )
  const oldDateBasFinish = useSelector(oldDateBasFinishSelector)
  const oldDateBasStart = useSelector(oldDateBasStartSelector)
  const oldSubscriptionId = useSelector(oldSubscriptionIdSelector)
  const permissions = useSelector(permissionsSelector)

  const mainStartDateBAS = dateBasStart || oldDateBasStart
  const mainEndDateBAS = dateBasFinish || oldDateBasFinish

  const period: string = basPaymentsPeriod.filter(
    (item) => item.id === currentBasPaymentPeriod
  )?.[0]?.title

  const oldPeriod: string = basPaymentsPeriod.filter(
    (item) => item.id === oldCurrentBasPaymentPeriod
  )?.[0]?.title

  const titleCurrentPaymentPeriod: string =
    currentBasPaymentPeriod === PAYMENT_MODULE_FLEXIBLE_PERIOD_IDS.TRIAL
      ? l("Trial")
      : l(period)

  const titleOldPaymentPeriod: string =
    oldCurrentBasPaymentPeriod === PAYMENT_MODULE_FLEXIBLE_PERIOD_IDS.TRIAL
      ? l("Trial")
      : l(oldPeriod)

  const currentSubscriptionPeriod: string = currentBasPaymentPeriod
    ? titleCurrentPaymentPeriod
    : titleOldPaymentPeriod

  const currentSubscriptionDateRange: string = `${convertToLocalDate(
    mainStartDateBAS
  )} - ${convertToLocalDate(mainEndDateBAS)}`

  const canManageDuration: boolean =
    !!permissions.amazonCustomerAccountEditSubscriptionDuration

  const handleOpenChangeDuration = (): void => {
    if (canManageDuration) {
      dispatch(
        displayModal(true, "changeDuration", {
          product: PRODUCTS.bas,
          initialValues: {
            date_start: dateBasStart,
            date_finish: dateBasFinish,
          },
        })
      )
    }
  }

  return {
    activeSubscriptionId,
    canManageDuration,
    currentSubscriptionDateRange,
    currentSubscriptionPeriod,
    oldSubscriptionId,
    handleOpenChangeDuration,
  }
}
