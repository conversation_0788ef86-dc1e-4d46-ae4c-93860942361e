import { useMemo } from "react"
import { useDispatch, useSelector } from "react-redux"

import basCustomerPlanActions from "actions/basCustomerPlanActions"

import {
  basDateFinish as basDateFinishSelector,
  currentBasPaymentPeriod as currentBasPaymentPeriodSelector,
  isAutoRenewActive as isAutoRenewActiveSelector,
  isIncludeInNextBasSubscriptionSelector,
  nextBasPaymentPlan as nextBasPaymentPlanSelector,
} from "selectors/basCustomerPlanSelector"

import l from "utils/intl"

import { PAYMENT_MODULE_FLEXIBLE_PERIOD_IDS } from "consts/basCustomerPlan"

const { getBasPlan, setAutoRenew } = basCustomerPlanActions

export const useAutoRenew = () => {
  const dispatch = useDispatch()

  const currentBasPaymentPeriod = useSelector(currentBasPaymentPeriodSelector)
  const hasNextBasPaymentPlan = useSelector(nextBasPaymentPlanSelector)
  const isAutoRenewActive = useSelector(isAutoRenewActiveSelector)
  const isBasCustomerPlanExpired = useSelector(basDateFinishSelector)
  const isIncludeInNextBasSubscription = useSelector(
    isIncludeInNextBasSubscriptionSelector
  )

  const isTrial: boolean =
    currentBasPaymentPeriod === PAYMENT_MODULE_FLEXIBLE_PERIOD_IDS.TRIAL

  const isAutoRenewalAvailable: boolean =
    !isTrial && isIncludeInNextBasSubscription && !hasNextBasPaymentPlan

  const infoIconPopoverText: string = useMemo(() => {
    if (!isIncludeInNextBasSubscription) {
      return l(
        "Auto-renewal is unavailable due to the absence of active Amazon seller accounts linked to this subscription. At least one active account needs to be included."
      )
    }

    const isAutoRenewalUnavailable: boolean =
      !isTrial && isIncludeInNextBasSubscription && hasNextBasPaymentPlan

    if (isAutoRenewalUnavailable) {
      return l("Next plan is set, auto renewal is not available")
    }

    const isSubscriptionExpired: boolean = isTrial && isBasCustomerPlanExpired

    if (isSubscriptionExpired) {
      return l("Your subscription expired.")
    }

    const isSubscriptionInTrial: boolean = isTrial && !isBasCustomerPlanExpired

    if (isSubscriptionInTrial) {
      return l("Your subscription is in trial period.")
    }

    return l(
      "Your subscription will be automatically renewed after the expiration date unless an extension is selected or an automatic renewal is disabled."
    )
  }, [
    isTrial,
    isIncludeInNextBasSubscription,
    hasNextBasPaymentPlan,
    isBasCustomerPlanExpired,
  ])

  const handleAutoRenew = (): void => {
    if (!isAutoRenewalAvailable) {
      return
    }

    dispatch(
      setAutoRenew({ value: isAutoRenewActive ? 0 : 1 }, () => {
        dispatch(getBasPlan({ cache: false, successCallback: null }))
      })
    )
  }

  return {
    isAutoRenewActive,
    isAutoRenewalAvailable,
    infoIconPopoverText,
    handleAutoRenew,
  }
}
