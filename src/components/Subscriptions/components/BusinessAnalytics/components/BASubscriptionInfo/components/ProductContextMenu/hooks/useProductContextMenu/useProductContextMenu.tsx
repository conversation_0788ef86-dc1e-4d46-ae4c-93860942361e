import React, { useCallback, useMemo } from "react"
import { useDispatch } from "react-redux"
import { Box, Typography } from "@develop/fe-library"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import customerActions from "actions/customerActions"

import { useRestrict } from "hooks/useRestrict"

import setConfirm from "utils/confirm"
import l from "utils/intl"

import { permissionKeys } from "consts"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import { PRODUCT_MENU_OPTIONS } from "./constants"

import type {
  DropdownMenuOptions,
  UseProductContextMenuProps,
} from "./UseProductContextMenuTypes"

const { displayModal } = amazonCustomerAccountsActions
const { getCurrentCustomer } = customerActions

const { getBasPlan, deleteBasPlan, getBasSubscriptions } =
  basCustomerPlanActions

export const useProductContextMenu = ({
  product,
  isDisabled,
  isRevertSubscriptionDisabled,
}: UseProductContextMenuProps) => {
  const dispatch = useDispatch()

  const { canView } = useRestrict({
    viewPermission: permissionKeys.rootManager,
  })

  const handleDeleteBasPlan = useCallback(() => {
    setConfirm({
      title: l("Delete current subscription for {productName}", {
        productName: PRODUCT_NAMES[PRODUCTS.bas],
      }),

      message: (
        <Box flexDirection="column" gap="m">
          <Typography color="--color-text-main" variant="--font-body-text-7">
            {l(
              "You are about to delete your current subscription for {productName}. This action cannot be reverted. If the Trial subscription is currently running, the customer will be able to launch the Trial subscription again.",
              { productName: PRODUCT_NAMES[PRODUCTS.bas] }
            )}
          </Typography>
          <Typography color="--color-text-main" variant="--font-body-text-7">
            {l("Are you sure you want to proceed?")}
          </Typography>
        </Box>
      ),

      okText: l("Confirm"),

      onCancel: () => {},

      onOk: () => {
        dispatch(
          deleteBasPlan(null, () => {
            dispatch(
              getCurrentCustomer(false, () => {
                dispatch(displayModal(false, "subscriptionBasSetting"))
                dispatch(getBasPlan({ cache: false, successCallback: null }))
                dispatch(getBasSubscriptions())
              })
            )
          })
        )
      },
    })
  }, [dispatch])

  const handleRevertSubscription = useCallback(() => {
    displayModal(true, "revertPlan", { product })
  }, [product])

  const options = useMemo<DropdownMenuOptions>(() => {
    const result: DropdownMenuOptions = []

    if (!isRevertSubscriptionDisabled) {
      result.push({
        value: PRODUCT_MENU_OPTIONS.REVERT_PLAN,
        iconLeft: "icnStop",
        label: l("Revert subscription"),
      })
    }

    if (product === PRODUCTS.bas) {
      result.push({
        value: PRODUCT_MENU_OPTIONS.DELETE_PLAN,
        iconLeft: "icnDeleteOutlined",
        label: l("Delete current subscription"),
      })
    }

    return result
  }, [isRevertSubscriptionDisabled, product])

  const handleSelect = useCallback(
    (selectedIndex: number): void => {
      if (isDisabled) {
        return
      }

      const { value } = options[selectedIndex] || {}

      if (value === PRODUCT_MENU_OPTIONS.DELETE_PLAN) {
        handleDeleteBasPlan()
      }

      if (value === PRODUCT_MENU_OPTIONS.REVERT_PLAN) {
        handleRevertSubscription()
      }
    },
    [handleDeleteBasPlan, handleRevertSubscription, isDisabled, options]
  )

  return { canView, options, handleSelect }
}
