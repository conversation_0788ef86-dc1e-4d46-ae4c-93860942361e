import React from "react"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { PRODUCTS } from "consts/product"

import {
  AutoRenew,
  CurrentSubscription,
  NextSubscription,
  ProductContextMenu,
  TotalOrders,
} from "./components"

export const BASubscriptionInfo = () => {
  return (
    <Box
      flexDirection="column"
      gap="m"
      hasBorder={{ bottom: true }}
      mXL={{ padding: "l", gap: "l" }}
      padding="m"
      width="100%"
    >
      <Typography variant="--font-headline-5">
        {l("Your subscription plan")}
      </Typography>

      <Box gap="m" mXL={{ gap: "l" }} width="100%">
        <Box
          dMD={{ gridTemplateColumns: "repeat(4, 1fr)" }}
          flexDirection="column"
          flexGrow="1"
          gap="m"
          mXL={{
            display: "grid",
            align: "start",
            gridTemplateColumns: "repeat(2, 1fr)",
            gap: "l",
          }}
        >
          <CurrentSubscription />
          <TotalOrders />
          <AutoRenew />
          <NextSubscription />
        </Box>

        <ProductContextMenu
          isRevertSubscriptionDisabled
          product={PRODUCTS.bas}
        />
      </Box>
    </Box>
  )
}
