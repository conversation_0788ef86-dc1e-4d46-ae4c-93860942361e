import React from "react"
import { Box, Button, Icon, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { DetailsSectionParams } from "./DetailsSectionTypes"

export const DetailsSection = ({
  notificationsCount,
  onDrawerOpen,
}: DetailsSectionParams) => {
  return (
    <Box
      flexDirection="column"
      mLG={{ padding: "l", flexDirection: "row" }}
      padding="m"
      dSM={{
        display: "none",
        hasBorder: {
          bottom: true,
          top: false,
        },
      }}
      hasBorder={{
        top: true,
        bottom: true,
      }}
    >
      <Box justify="space-between" width="100%">
        <Box>
          <Icon name="icnWarning" />
          <Box flexDirection="column" marginLeft="m">
            <Typography variant="--font-headline-5">
              {l("Important information")}
            </Typography>
            <Box marginTop="s">
              <Typography
                color="--color-text-second"
                variant="--font-body-text-9"
              >
                {l(
                  "{notificationsCount} notifications require your attention",
                  {
                    notificationsCount,
                  }
                )}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
      <Box
        marginTop="m"
        mLG={{ width: "min-content", marginTop: "0" }}
        width="100%"
      >
        <Button fullWidth onClick={onDrawerOpen}>
          {l("Details")}
        </Button>
      </Box>
    </Box>
  )
}
