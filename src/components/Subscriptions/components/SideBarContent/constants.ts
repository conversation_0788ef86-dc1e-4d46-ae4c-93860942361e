import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

export const SELECT_VALUES = {
  ALL_PRODUCTS: {
    VALUE: "allProducts",
    LABEL: "All Products",
  },
  [PRODUCTS.repricerB2C]: {
    VALUE: PRODUCTS.repricerB2C,
    LABEL: PRODUCT_NAMES[PRODUCTS.repricerB2C],
  },
  [PRODUCTS.repricerB2B]: {
    VALUE: PRODUCTS.repricerB2B,
    LABEL: PRODUCT_NAMES[PRODUCTS.repricerB2B],
  },
  [PRODUCTS.lost]: {
    VALUE: PRODUCTS.lost,
    LABEL: PRODUCT_NAMES[PRODUCTS.lost],
  },
  [PRODUCTS.bas]: {
    VALUE: PRODUCTS.bas,
    LABEL: PRODUCT_NAMES[PRODUCTS.bas],
  },
} as const
