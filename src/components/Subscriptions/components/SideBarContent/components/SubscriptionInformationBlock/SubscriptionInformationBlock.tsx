import React from "react"
import { Box, Icon, Typography } from "@develop/fe-library"

import { ICON_STATUSES_CONFIGURATION } from "./constants"

import { SubscriptionInformationBlockParams } from "./SubscriptionInformationBlockTypes"

export const SubscriptionInformationBlock = ({
  status = "warning",
  productName,
  description,
}: SubscriptionInformationBlockParams) => {
  return (
    <Box marginTop="l">
      <Box>
        <Icon {...ICON_STATUSES_CONFIGURATION[status]} />
      </Box>
      <Box justify="space-between" width="100%">
        <Box flexDirection="column" marginLeft="m">
          <Box>
            <Typography variant="--font-body-text-5">{productName}</Typography>
          </Box>
          <Box>
            <Typography variant="--font-body-text-9">{description}</Typography>
          </Box>
        </Box>
        <Box>
          <Icon
            name="icnMore"
            size="--icon-size-5"
            onClick={() => alert("<PERSON><PERSON><PERSON> the best")}
          />
        </Box>
      </Box>
    </Box>
  )
}
