import { CommonRepricerSectionParams } from "components/Subscriptions/components/CommonRepricerSection/CommonRepricerSectionTypes"

export type RepricerCardParams = Pick<
  CommonRepricerSectionParams["repricerAccountMarketplaces"][0],
  | "dateFinish"
  | "dateStart"
  | "currentSubscriptionTitle"
  | "nextSubscriptionTitle"
  | "title"
  | "amazonDomain"
  | "amazonMarketplaceId"
  | "sellerId"
  | "autoRenew"
  | "active"
  | "id"
> &
  Pick<
    CommonRepricerSectionParams,
    "accountId" | "productName" | "isSelectedForNewPricing"
  >
