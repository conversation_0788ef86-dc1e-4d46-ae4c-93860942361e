import React from "react"
import { Box, Icon } from "@develop/fe-library"

import { TagParams } from "./TagTypes"

export const Tag = ({ tagColor, iconName }: TagParams) => {
  return (
    <Box align="center" height="var(--tag-height-m)" padding="0 m">
      <Box
        backgroundColor={tagColor}
        borderRadius="--border-radius"
        bottom="0"
        left="0"
        opacity="--box-opacity-2"
        position="absolute"
        right="0"
        top="0"
      />
      <Box zIndex="1">
        <Icon color={tagColor} name={iconName} size="--icon-size-3" />
      </Box>
    </Box>
  )
}
