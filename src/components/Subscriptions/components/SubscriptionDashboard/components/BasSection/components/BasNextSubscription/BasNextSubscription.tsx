import React from "react"
import { Box, Icon, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { AutoRenewStatus } from "./components"

import { BAS_BLOCK_COMMON_WRAPPER_STYLES } from "../constants"

import { BasNextSubscriptionParams } from "./BasNextSubscriptionTypes"

export const BasNextSubscription = ({
  currentSubscription,
  nextSubscription,
  isAutoRenewActive,
  hasNextSubscription,
}: BasNextSubscriptionParams) => {
  const canShowAutoRenewStatus = isAutoRenewActive || hasNextSubscription

  return (
    <Box
      {...BAS_BLOCK_COMMON_WRAPPER_STYLES}
      align="flex-start"
      flexDirection="column"
      justify="center"
      mLG={{
        ...BAS_BLOCK_COMMON_WRAPPER_STYLES.mLG,
        flexDirection: "row",
        align: "center",
        justify: "space-between",
      }}
    >
      <Box flexDirection="column">
        <Box>
          <Icon
            color="--color-icon-static"
            name="icnTimer"
            size="--icon-size-6"
          />
        </Box>
        <Box marginTop="m">
          <Typography
            variant="--font-body-text-2"
            style={{
              whiteSpace: "nowrap",
            }}
          >
            {l("Next subscription")}
          </Typography>
        </Box>
        <Box cursor="pointer" marginTop="m">
          <Typography variant="--font-body-text-7">
            {nextSubscription || currentSubscription}
          </Typography>
        </Box>
      </Box>
      {canShowAutoRenewStatus ? (
        <AutoRenewStatus isAutoRenewActive={isAutoRenewActive} />
      ) : null}
    </Box>
  )
}
