import React from "react"
import { Box, StatusTag } from "@develop/fe-library"

import l from "utils/intl"

import { AutoRenewStatusParams } from "./AutoRenewStatusTypes"

export const AutoRenewStatus = ({
  isAutoRenewActive,
}: AutoRenewStatusParams) => {
  return (
    <Box
      marginLeft="0"
      marginTop="m"
      mLG={{
        marginTop: "0",
        marginLeft: "m",
      }}
    >
      {isAutoRenewActive ? (
        <StatusTag
          colorStatus="--color-status-5"
          iconName="icnCheck"
          name={l("Auto-renew")}
        />
      ) : (
        <StatusTag
          colorStatus="--color-status-7"
          iconName="icnWarning"
          name={l("Auto-renew Off")}
        />
      )}
    </Box>
  )
}
