import React from "react"
import { Box, Typography } from "@develop/fe-library"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts"

import { CELL_COLORS } from "./constants"

import { BasPieParams } from "./BasPieTypes"

export const BasPie = ({ data, maxValue, value }: BasPieParams) => {
  return (
    <Box align="center" height="110px" width="110px">
      <ResponsiveContainer>
        <PieChart>
          <Pie
            blendStroke
            data={data}
            dataKey="value"
            endAngle={-270} // Конец прогресса (сверху по часовой стрелке)
            innerRadius="90%" // Внутренний радиус для создания кольца
            outerRadius="100%" // Внешний радиус кольца
            paddingAngle={0}
            startAngle={90} // Начало прогресса сверху
          >
            {data?.map((_, index) => (
              <Cell key={`cell-bas-${index}`} fill={CELL_COLORS[index]} />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
      <Box
        left="50%"
        position="absolute"
        top="50%"
        transform="translate(-50%, -50%)"
      >
        <Box>
          <Typography variant="--font-headline-2">{value}</Typography>
        </Box>

        <Box transform="translateY(15px)">
          <Typography
            color="--color-text-placeholders"
            variant="--font-body-text-6"
            style={{
              whiteSpace: "nowrap",
            }}
          >{`/${maxValue}`}</Typography>
        </Box>
      </Box>
    </Box>
  )
}
