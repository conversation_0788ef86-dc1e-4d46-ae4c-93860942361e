import { useSelector } from "react-redux"

import { countOfAmazonAccountsAndConnectedProductsSelector } from "selectors/amazonAccountsSelectors"

export const useBasConnectedAmazonAccountsData = () => {
  const { connectedBasAccountsCounter, totalActiveAccounts } = useSelector(
    countOfAmazonAccountsAndConnectedProductsSelector
  )

  return {
    connectedBasAccountsCounter,
    totalActiveAccounts,
  }
}
