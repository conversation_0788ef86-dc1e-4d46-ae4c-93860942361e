import { useSelector } from "react-redux"

import { BasNextSubscriptionDataSelector } from "selectors/basCustomerPlanSelector"

import l from "utils/intl"

export const useBasNextSubscriptionData = () => {
  const { nextPeriod, nextBasPaymentPlan, isAutoRenewActive } = useSelector(
    BasNextSubscriptionDataSelector
  )

  const subscriptionWithoutNextPeriod: string | undefined = isAutoRenewActive
    ? undefined
    : l("No subscription")

  const nextSubscription: string = !nextBasPaymentPlan
    ? subscriptionWithoutNextPeriod
    : l(nextPeriod)

  const hasNextSubscription: boolean = !!(
    isAutoRenewActive || nextBasPaymentPlan
  )

  return {
    isAutoRenewActive,
    nextSubscription,
    hasNextSubscription,
  }
}
