import React from "react"
import { Box, But<PERSON>, Typography } from "@develop/fe-library"

import { NotEnabledAccount } from "components/Subscriptions/components/NotEnabledAccount"
import {
  useBasCurrentSubscriptionData,
  useBasData,
} from "components/Subscriptions/hooks"

import l from "utils/intl"

import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import {
  BasConnectedAmazonAccounts,
  BasCurrentSubscription,
  BasNextSubscription,
} from "./components"

import {
  useBasConnectedAmazonAccountsData,
  useBasNextSubscriptionData,
} from "./hooks"

import { BasSectionParams } from "./BasSectionTypes"

export const BasSection = ({ onChangeTab }: BasSectionParams) => {
  const {
    currentSubscription,
    useBasModule,
    startBasWizardHandler,
    showLastSubscriptionModalHandler,
    isBasActive,
    isBasModuleStarted,
    handleChangeTab,
  } = useBasData({ onChangeTab })

  const { totalActiveAccounts, connectedBasAccountsCounter } =
    useBasConnectedAmazonAccountsData()

  const { currentDateSubscription, currentSubscriptionPopover } =
    useBasCurrentSubscriptionData()

  const { isAutoRenewActive, nextSubscription, hasNextSubscription } =
    useBasNextSubscriptionData()

  return (
    <Box flexDirection="column" marginTop="xl">
      <Box
        flexDirection="column"
        gap="m"
        width="100%"
        mLG={{
          flexDirection: "row",
          align: "center",
          justify: "space-between",
          width: "auto",
        }}
      >
        <Typography variant="--font-headline-5">
          {PRODUCT_NAMES[PRODUCTS.bas]}
        </Typography>

        {useBasModule ? (
          <Button onClick={handleChangeTab}>{l("Details")}</Button>
        ) : null}
      </Box>

      <Box
        flexWrap="wrap"
        gap="m"
        marginTop="m"
        mLG={{
          gap: "l",
        }}
      >
        {!useBasModule ? (
          <NotEnabledAccount
            isSecondButtonVisible={!isBasActive && isBasModuleStarted}
            primaryButtonText={l("Enable")}
            secondButtonText={l("Last subscription")}
            description={l(
              "Analyze and measure your performance to generate insights and keep business processes under control."
            )}
            title={l("{productName} is not enabled", {
              productName: PRODUCT_NAMES[PRODUCTS.bas],
            })}
            onPrimaryButtonClick={startBasWizardHandler}
            onSecondButtonClick={showLastSubscriptionModalHandler}
          />
        ) : (
          <>
            <BasConnectedAmazonAccounts
              connectedBasAccountsCounter={connectedBasAccountsCounter}
              totalActiveAccounts={totalActiveAccounts}
            />
            <BasCurrentSubscription
              currentDateSubscription={currentDateSubscription}
              currentSubscription={currentSubscription}
              currentSubscriptionPopover={currentSubscriptionPopover}
            />
            <BasNextSubscription
              currentSubscription={currentSubscription}
              hasNextSubscription={hasNextSubscription}
              isAutoRenewActive={isAutoRenewActive}
              nextSubscription={nextSubscription}
            />
          </>
        )}
      </Box>
    </Box>
  )
}
