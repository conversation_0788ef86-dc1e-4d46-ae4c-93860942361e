import { useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { ROUTES } from "@develop/fe-library/dist/routes"

import { customerProductsInfoSelector } from "selectors/customerSelectors"
import { lostCaseInfoSelector } from "selectors/lostCaseSelectors"

import { TAB_KEYS } from "components/Subscriptions/constants"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { PRODUCTS } from "consts/product"

import { UseLostDataParams } from "./useLostDataTypes"
import { PreparedLostCardsData } from "../../LostAndFoundSectionTypes"

const {
  LOST_ROUTES: { PATH_LOST_ACTION_REQUIRED },
} = ROUTES

export const useLostData = ({ onChangeTab }: UseLostDataParams) => {
  const {
    totalCases,
    reimbursedItemsCount,
    totalReimbursementAmount,
    actionRequired,
  } = useSelector(lostCaseInfoSelector)
  const { useLostModule } = useSelector(customerProductsInfoSelector)

  const history = useHistory()

  const preparedLostCardsData: PreparedLostCardsData = [
    {
      iconName: "icnFileSearch",
      mainValue: ln(totalCases),
      description: l("Total cases"),
      key: "searchItem",
    },
    {
      iconName: "icnInbox",
      mainValue: ln(reimbursedItemsCount),
      description: l("Reimbursed items"),
      key: "reimbursementItems",
    },
    {
      iconName: "icnWallet",
      mainValue: ln(totalReimbursementAmount, 0, {
        currency: "EUR",
      }),
      description: l("Total reimbursement"),
      key: "totalReimbursement",
    },
  ]

  const handleActionRequiredClick = () => {
    history.push(PATH_LOST_ACTION_REQUIRED)
  }

  const handleChangeTab = (): void => {
    onChangeTab(TAB_KEYS[PRODUCTS.lost])
  }

  return {
    actionRequiredData: {
      title: ln(actionRequired),
      description: l("Action required"),
      buttonValue: l("Show more"),
      onClick: handleActionRequiredClick,
    },
    useLostModule,
    preparedLostCardsData,
    handleChangeTab,
  }
}
