import React from "react"
import { <PERSON>, Button, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

import {
  InactiveLostBlock,
  LostAndFoundBlock,
  LostBlockWithButton,
} from "./components"

import { useLostData } from "./hooks"

import { LostSectionParams } from "./LostAndFoundSectionTypes"

export const LostAndFoundSection = ({ onChangeTab }: LostSectionParams) => {
  const {
    actionRequiredData,
    preparedLostCardsData,
    useLostModule,
    handleChangeTab,
  } = useLostData({ onChangeTab })

  return (
    <Box flexDirection="column" marginTop="xl">
      <Box
        flexDirection="column"
        gap="m"
        width="100%"
        mLG={{
          flexDirection: "row",
          align: "center",
          justify: "space-between",
          width: "auto",
        }}
      >
        <Typography variant="--font-headline-5">
          {PRODUCT_NAMES[PRODUCTS.lost]}
        </Typography>

        {useLostModule ? (
          <Button onClick={handleChangeTab}>{l("Details")}</Button>
        ) : null}
      </Box>

      <Box
        flexWrap="wrap"
        gap="m"
        marginTop="m"
        mLG={{
          gap: "l",
        }}
      >
        {!useLostModule ? (
          <InactiveLostBlock />
        ) : (
          <>
            <LostBlockWithButton {...actionRequiredData} />
            {preparedLostCardsData.map(
              ({ iconName, mainValue, description, key }) => {
                return (
                  <LostAndFoundBlock
                    key={key}
                    description={description}
                    iconName={iconName}
                    mainValue={mainValue}
                  />
                )
              }
            )}
          </>
        )}
      </Box>
    </Box>
  )
}
