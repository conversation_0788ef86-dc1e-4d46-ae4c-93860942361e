import React from "react"
import { Box, Icon, Typography } from "@develop/fe-library"

import FormattedMessage from "components/FormattedMessage"
import { RestrictedPrimaryButton } from "components/shared/Buttons"

import l from "utils/intl"

import { PERMISSIONS_CONTENT } from "consts/permissions"

import { useReimbursementData, useReimbursementWizardStart } from "./hooks"

export const ReimbursementBlock = () => {
  const reimbursementWizardStart = useReimbursementWizardStart()
  const {
    isAllowToSetup,
    lostCountText,
    reimbursementValue,
    nonLostCaseCount,
  } = useReimbursementData()

  return (
    <Box
      align="center"
      backgroundColor="--color-row-select"
      marginTop="m"
      mLG={{ padding: "l" }}
      padding="m"
      width="100%"
    >
      <Box
        flexDirection="column"
        gap="m"
        tb={{ flexDirection: "row", justify: "space-between", gap: "l" }}
        width="100%"
        dMD={{
          flexWrap: "nowrap",
          flexDirection: "row",
          justify: "space-between",
        }}
        dSM={{
          flexWrap: "wrap",
          justify: "flex-start",
          align: "center",
        }}
      >
        {/*left section*/}
        <Box gap="m" tb={{ gap: "l" }}>
          <Box>
            <Icon
              color="--color-icon-active"
              name="icnWarning"
              size="--icon-size-5"
            />
          </Box>
          <Box align="flex-end" flexWrap="nowrap" gap="m">
            <Typography
              color="--color-text-link"
              variant="--font-service-text-2"
              style={{
                whiteSpace: "nowrap",
              }}
            >
              {reimbursementValue}
            </Typography>
            <Typography
              color="--color-text-link"
              variant="--font-headline-2"
              style={{
                whiteSpace: "nowrap",
              }}
            >
              €
            </Typography>
          </Box>
        </Box>
        {/*center section*/}
        <Box>
          <Typography color="--color-text-second" variant="--font-body-text-9">
            <FormattedMessage
              defaultMessage={lostCountText}
              id={lostCountText}
              values={{
                cases: nonLostCaseCount,
                strong: (...chunks: any[]) => <strong>{chunks}</strong>,
              }}
            />
          </Typography>
        </Box>
        {/*right section*/}
        <Box dMD={{ width: "auto" }} dSM={{ width: "100%" }}>
          <Box tb={{ width: "min-content" }} width="100%">
            <RestrictedPrimaryButton
              fullWidth
              managePermission={isAllowToSetup}
              popoverMessage={l(PERMISSIONS_CONTENT.allowSetupAccount)}
              onClick={reimbursementWizardStart}
            >
              {l("Request reimbursement now")}
            </RestrictedPrimaryButton>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}
