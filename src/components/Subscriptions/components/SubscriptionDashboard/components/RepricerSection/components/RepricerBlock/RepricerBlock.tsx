import React from "react"

import { ActiveRepricerBlock, InactiveRepricerBlock } from "./components"

import { RepricerBlockParams } from "./RepricerBlockTypes"

export const RepricerBlock = ({
  connectedProductsCounter,
  totalActiveAccounts,
  productName,
  onActive,
  active,
}: RepricerBlockParams) => {
  return active ? (
    <ActiveRepricerBlock
      connectedProductsCounter={connectedProductsCounter}
      productName={productName}
      totalActiveAccounts={totalActiveAccounts}
    />
  ) : (
    <InactiveRepricerBlock productName={productName} onActive={onActive} />
  )
}
