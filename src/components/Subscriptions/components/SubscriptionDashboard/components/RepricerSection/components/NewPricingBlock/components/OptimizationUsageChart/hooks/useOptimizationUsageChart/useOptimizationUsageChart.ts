import { useMemo, useState } from "react"
import { useSelector } from "react-redux"

import { optimizationUsageChartDataSelector } from "selectors/repricerSubscriptionSelectors"

import ln from "utils/localeNumber"

import { CHART_YAXIS_GAP, YAXIS_WIDTHS } from "../../constants"

export const useOptimizationUsageChart = () => {
  const [maxViewsNumber, setMaxViewsNumber] = useState(0)

  const data = useSelector(optimizationUsageChartDataSelector)

  const computedYAxisWidth = useMemo(() => {
    const digitsCount: number = ln(Math.abs(maxViewsNumber)).length

    const width = YAXIS_WIDTHS[digitsCount]

    return (width ?? 0) + CHART_YAXIS_GAP
  }, [maxViewsNumber])

  const yAxisTickFormatter = (tickNumber: number): string => {
    const isNextGreater: boolean = tickNumber > maxViewsNumber && tickNumber > 0

    if (isNextGreater) {
      setMaxViewsNumber(tickNumber)
    }

    return ln(tickNumber, 0)
  }

  const getTickCount = (): number | undefined => {
    if (!maxViewsNumber || maxViewsNumber === 1) {
      return 2
    }

    return maxViewsNumber < 5 ? maxViewsNumber + 1 : undefined
  }

  return {
    computedYAxisWidth,
    data,
    maxViewsNumber,
    getTickCount,
    yAxisTickFormatter,
  }
}
