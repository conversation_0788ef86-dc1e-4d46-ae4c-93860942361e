import React from "react"
import { Box, Icon, Typography } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { useRepricerCurrentSubscription } from "./hooks"

import type { RepricerCurrentSubscriptionProps } from "./RepricerCurrentSubscriptionTypes"

export const RepricerCurrentSubscription = ({
  currentPlan,
}: RepricerCurrentSubscriptionProps) => {
  const { isFreePlan, localizedName, localStartDate, localFinishDate } =
    useRepricerCurrentSubscription({ currentPlan })

  return (
    <Box
      hasBorder
      align="flex-start"
      flexDirection="column"
      gap="m"
      justify="center"
      mLG={{ gap: "l" }}
      padding="l"
    >
      <Icon
        color="--color-icon-static"
        name="icnCalendar"
        size="--icon-size-6"
      />

      <Box flexDirection="column" gap="s">
        <Typography variant="--font-body-text-2">
          {l("Current subscription")}
        </Typography>

        <Box gap="s">
          <Typography variant="--font-body-text-9">
            {l("{planName} plan", { planName: localizedName })}
          </Typography>
          {isFreePlan ? null : (
            <Typography variant="--font-body-text-9">
              {ln(currentPlan.size)}
            </Typography>
          )}
        </Box>
        <Typography variant="--font-body-text-9">
          {localStartDate} - {localFinishDate}
        </Typography>
      </Box>
    </Box>
  )
}
