import React from "react"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { PRODUCT_NAMES } from "consts/product"

import { REPRICER_BLOCK_COMMON_WRAPPER_STYLES } from "../../constants"

import { ActiveRepricerBlockParams } from "./ActiveRepricerBlockTypes"

export const ActiveRepricerBlock = ({
  connectedProductsCounter,
  totalActiveAccounts,
  productName,
}: ActiveRepricerBlockParams) => {
  return (
    <Box
      hasBorder
      flexDirection="column"
      height={180}
      justify="center"
      {...REPRICER_BLOCK_COMMON_WRAPPER_STYLES}
    >
      <Box flexDirection="column" justify="center">
        <Typography variant="--font-headline-5">
          {PRODUCT_NAMES[productName]}
        </Typography>
        <Box align="center" marginTop="m">
          <Typography variant="--font-headline-2">
            {ln(connectedProductsCounter)}
          </Typography>
          <Box marginLeft="s">
            <Typography variant="--font-body-text-7">/</Typography>
          </Box>
          <Box marginLeft="s">
            <Typography variant="--font-body-text-7">
              {ln(totalActiveAccounts)}
            </Typography>
          </Box>
        </Box>
        <Box marginTop="s">
          <Typography
            color="--color-text-second"
            style={{ whiteSpace: "nowrap" }}
            variant="--font-body-text-9"
          >
            {l("Enabled accounts")}
          </Typography>
        </Box>
      </Box>
    </Box>
  )
}
