import { SVGProps } from "react"
import { DotProps } from "recharts"

export const CHART_YAXIS_GAP = 10
export const YAXIS_X_OFFSET = -5

export const GRADIENT_ID = "customGradient"

export const YAXIS_WIDTHS: { [key: number]: number } = {
  1: 9.19,
  2: 12.37,
  3: 18.55,
  4: 24.73,
  5: 30.91,
  6: 37.09,
  7: 43.27,
  8: 49.45,
  9: 55.63,
  10: 61.81,
} as const

export const AREA_DOT_STYLES: DotProps = {
  r: 4,
  stroke: "#c2d0e6",
  strokeWidth: 2,
  fill: "#0055cc",
} as const

export const YAXIS_TICK_STYLES: SVGProps<SVGTextElement> = {
  fontSize: 11,
  fill: "#666666",
} as const
