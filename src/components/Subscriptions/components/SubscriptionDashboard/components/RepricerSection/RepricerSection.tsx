import React from "react"
import { <PERSON>, But<PERSON>, Typography } from "@develop/fe-library"

import { NotEnabledAccount } from "components/Subscriptions/components/NotEnabledAccount"
import { TransferToNewPricingAlert } from "components/TransferToNewPricing"

import l from "utils/intl"

import { PRODUCT_NAMES, PRODUCTS, REPRICER_OFFER_TYPE } from "consts/product"

import { NewPricingBlock, RepricerBlock, RepricerInfo } from "./components"

import { useRepricerData } from "./hooks"

import { RepricerSectionProps } from "./RepricerSectionTypes"

export const RepricerSection = ({ onChangeTab }: RepricerSectionProps) => {
  const {
    repricerBlockData,
    connectedRepricerAccountsCounter,
    totalActiveAccounts,
    startRepricerWizardHandler,
    handleClickRepricerDetails,
    useRepricer,
    useNewPricing,
    isSelectedForNewPricing,
  } = useRepricerData({ onChangeTab })

  if (useNewPricing) {
    return (
      <NewPricingBlock
        onClickRepricerDetails={handleClickRepricerDetails({
          productName: PRODUCTS.repricer,
        })}
      />
    )
  }

  return (
    <Box flexDirection="column" width="100%">
      <Box
        flexDirection="column"
        rowGap="m"
        mLG={{
          flexDirection: "row",
          align: "center",
          justify: "space-between",
        }}
      >
        <Typography variant="--font-headline-5">
          {PRODUCT_NAMES[PRODUCTS.repricer]}
        </Typography>
        {useRepricer ? (
          <Box
            flexDirection="column"
            gap="m"
            width="100%"
            mLG={{
              flexDirection: "row",
              width: "auto",
            }}
          >
            <Button
              onClick={handleClickRepricerDetails({
                productName: PRODUCTS.repricerB2C,
              })}
            >
              {l(`{productName} Details`, {
                productName: REPRICER_OFFER_TYPE[PRODUCTS.repricerB2C],
              })}
            </Button>
            <Button
              onClick={handleClickRepricerDetails({
                productName: PRODUCTS.repricerB2B,
              })}
            >
              {l(`{productName} Details`, {
                productName: REPRICER_OFFER_TYPE[PRODUCTS.repricerB2B],
              })}
            </Button>
          </Box>
        ) : null}
      </Box>

      {isSelectedForNewPricing ? (
        <Box display="block" margin="m 0" width="100%">
          <TransferToNewPricingAlert />
        </Box>
      ) : null}

      <Box
        flexWrap="wrap"
        gap="m"
        marginTop="m"
        mLG={{
          gap: "l",
        }}
      >
        {!useRepricer ? (
          <NotEnabledAccount
            isSelectedForNewPricing={isSelectedForNewPricing}
            primaryButtonText={l("Enable")}
            description={l(
              "Intelligent price control for Amazon. Extensive functions for every automation requirement."
            )}
            title={l("{productName} is not enabled", {
              productName: PRODUCT_NAMES[PRODUCTS.repricer],
            })}
            onPrimaryButtonClick={startRepricerWizardHandler({
              productName: PRODUCTS.repricer,
            })}
          />
        ) : (
          <>
            <RepricerInfo
              totalActiveAccounts={totalActiveAccounts}
              connectedRepricerAccountsCounter={
                connectedRepricerAccountsCounter
              }
            />
            {repricerBlockData.map(
              ({ connectedProductsCounter, productName, active, onActive }) => (
                <RepricerBlock
                  active={active}
                  connectedProductsCounter={connectedProductsCounter}
                  productName={productName}
                  totalActiveAccounts={totalActiveAccounts}
                  onActive={onActive}
                />
              )
            )}
          </>
        )}
      </Box>
    </Box>
  )
}
