import React from "react"
import { Box, Modal, Typography } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { OFFER_TYPES } from "consts/product"

import type {
  DetailedOptimizationsOverviewModalProps,
  OverviewItem,
} from "./DetailedOptimizationsOverviewModalTypes"

export const DetailedOptimizationsOverviewModal = ({
  onClose,
  b2b,
  b2c,
  total,
  accountName,
}: DetailedOptimizationsOverviewModalProps) => {
  const items: OverviewItem[] = [
    {
      label: l("Total Product Optimizations used"),
      value: total,
    },
    {
      label: l("{b2c} Product Optimizations used", { b2c: OFFER_TYPES.b2c }),
      value: b2c,
    },
    {
      label: l("{b2b} Product Optimizations used", { b2b: OFFER_TYPES.b2b }),
      value: b2b,
    },
  ]

  return (
    <Modal
      visible
      width="--modal-size-s"
      title={l("{accountName} Product Optimizations breakdown", {
        accountName,
      })}
      onClose={onClose}
    >
      <Box hasBorder display="block">
        {items.map(({ label, value }, index) => (
          <Box
            key={label}
            flexWrap="wrap"
            gap="s"
            hasBorder={{ top: index !== 0 }}
            justify="space-between"
            mXL={{ padding: "m l" }}
            padding="m"
          >
            <Typography variant="--font-body-text-9">{label}:</Typography>
            <Typography variant="--font-body-text-9">{ln(value)}</Typography>
          </Box>
        ))}
      </Box>
    </Modal>
  )
}
