import React from "react"
import { useSelector } from "react-redux"
import { Box, Typography } from "@develop/fe-library"
import { checkIsArray } from "@develop/fe-library/dist/utils"

import {
  activeRepricerAccountsSelector,
  repricerSubscriptionAmazonAccountsSelector,
} from "selectors/repricerSubscriptionSelectors"

import l from "utils/intl"

import { AccountCard } from "./components"

export const RepricerAmazonAccounts = () => {
  const accounts = useSelector(repricerSubscriptionAmazonAccountsSelector)
  const activeAccounts = useSelector(activeRepricerAccountsSelector)

  return (
    <Box
      flexDirection="column"
      gap="m"
      hasBorder={{ top: true }}
      mXL={{ padding: "l", gap: "l" }}
      padding="m"
    >
      <Typography variant="--font-body-text-2">
        {l("Amazon seller accounts")}
      </Typography>

      {checkIsArray(accounts) ? (
        <Box
          display="grid"
          dSM={{ gridTemplateColumns: "repeat(3, 1fr)" }}
          gap="m"
          gridTemplateColumns="1fr"
          mXL={{ gap: "l", gridTemplateColumns: "1fr 1fr" }}
        >
          {accounts.map(
            ({
              id,
              use_repricer_module,
              accountName,
              accountMarketplaces,
              allowedMarketplaces,
              optimizations,
            }) => {
              const isLastEnabledAccount: boolean =
                !!use_repricer_module && activeAccounts.length === 1

              return (
                <AccountCard
                  key={id}
                  accountMarketplaces={accountMarketplaces}
                  active={!!use_repricer_module}
                  allowedMarketplaces={allowedMarketplaces}
                  id={id}
                  isLastEnabledAccount={isLastEnabledAccount}
                  optimizations={optimizations}
                  title={accountName}
                />
              )
            }
          )}
        </Box>
      ) : null}
    </Box>
  )
}
