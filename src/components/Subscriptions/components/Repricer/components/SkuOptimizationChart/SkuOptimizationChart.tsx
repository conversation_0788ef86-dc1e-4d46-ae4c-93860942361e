import React from "react"
import { useSelector } from "react-redux"
import { Box, Icon, IconPopover, Typography } from "@develop/fe-library"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON> } from "recharts"

import { priceOptimizationChartDataSelector } from "selectors/repricerSubscriptionSelectors"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { OFFER_TYPES } from "consts/product"

import { OptimizationChartTooltip } from "./components"

import { getChartColor } from "./utils"

import styles from "./skuOptimizationChart.module.scss"

export const SkuOptimizationChart = () => {
  const {
    data,
    used,
    total,
    percent,
    isTrialPlan,
    isFreemiumPlan,
    offersWithActiveOptimizationWithStock,
  } = useSelector(priceOptimizationChartDataSelector)

  const renderCustomizedLabel: PieLabel = ({ cy, index }) => {
    if (index !== 0) {
      return null
    }

    const roundedPercent: number = Math.floor(percent * 100)

    if (isTrialPlan) {
      return (
        <foreignObject
          height={86}
          pointerEvents="none"
          width="100%"
          x={0}
          y={cy - 43}
        >
          <Box align="center" flexDirection="column" padding="0 xl">
            <Icon name="icnInfinity" size="--icon-size-9" />
            <Typography
              className={styles.labelText}
              textAlign="center"
              variant="--font-body-text-9"
            >
              {l("Unlimited Product Optimizations")}
            </Typography>
          </Box>
        </foreignObject>
      )
    }

    if (isFreemiumPlan) {
      return (
        <foreignObject
          height={50}
          pointerEvents="none"
          width="100%"
          x={0}
          y={cy - 30}
        >
          <Box align="center" flexDirection="column" padding="0 xl">
            <Typography
              className={styles.labelText}
              textAlign="center"
              variant="--font-headline-1"
            >
              {ln(offersWithActiveOptimizationWithStock)}
            </Typography>
          </Box>
        </foreignObject>
      )
    }

    return (
      <foreignObject
        height={86}
        pointerEvents="none"
        width="100%"
        x={0}
        y={cy - 43}
      >
        <Box align="center" flexDirection="column" padding="0 xl">
          <Typography
            className={styles.labelText}
            textAlign="center"
            variant="--font-headline-1"
          >
            {roundedPercent}%
          </Typography>
          <Typography className={styles.labelText} variant="--font-body-text-7">
            {ln(used, 0)} / {ln(total, 0)}
          </Typography>
          <Typography
            className={styles.labelText}
            color="--color-text-second"
            variant="--font-body-text-9"
          >
            {l("Used")} / {l("Total")}
          </Typography>
        </Box>
      </foreignObject>
    )
  }

  return (
    <Box
      align="center"
      flexDirection="column"
      mXL={{ width: "50%" }}
      padding="s"
      width="100%"
    >
      <PieChart height={168} width={224}>
        <Pie
          cx="50%"
          cy="68%"
          data={data}
          dataKey="value"
          endAngle={-390}
          innerRadius={82}
          label={renderCustomizedLabel}
          labelLine={false}
          outerRadius={112}
          startAngle={-150}
        >
          {data.map((entry, index) => (
            <Cell key={entry.key} fill={getChartColor({ index, percent })} />
          ))}
        </Pie>

        <Tooltip
          content={<OptimizationChartTooltip percent={percent} total={total} />}
        />
      </PieChart>
      <Box align="center" gap="m" marginTop="m">
        <Typography variant="--font-headline-5">
          {isFreemiumPlan ? l("Optimized products") : l("Product Optimization")}
        </Typography>
        <IconPopover
          color="--color-icon-active"
          maxWidth={350}
          name="icnInfoCircle"
          size="--icon-size-3"
          content={l(
            "Product Optimization for {b2c} and {b2b} products are counted separately. If Product Optimization happens for the same product but for its {b2c} and {b2b} offers - it would be counted as 2 Product Optimizations",
            {
              b2c: OFFER_TYPES.b2c,
              b2b: OFFER_TYPES.b2b,
            }
          )}
        />
      </Box>
    </Box>
  )
}
