import React from "react"
import { Box } from "@develop/fe-library"
import { checkIsNumber } from "@develop/fe-library/dist/utils"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { OPTIMIZATION_CHART_DATA_KEY } from "consts/repricerSubscription"

import type { OptimizationChartTooltipProps } from "./OptimizationChartTooltipTypes"

export const OptimizationChartTooltip = ({
  percent,
  total,
  active,
  payload = [],
}: OptimizationChartTooltipProps) => {
  const data = payload[0]?.payload?.payload

  const isTooltipVisible: boolean =
    !!active &&
    data?.key === OPTIMIZATION_CHART_DATA_KEY.USED &&
    checkIsNumber(data?.value) &&
    percent >= 1

  if (!isTooltipVisible) {
    return null
  }

  return (
    <Box
      backgroundColor="--color-main-background"
      borderRadius="--border-radius"
      boxShadow="--box-shadow"
      flexDirection="column"
      gap="m"
      padding="m"
    >
      {l("On-Demand Product Optimizations: {value}", {
        value: ln(data.value - total, 0),
      })}
    </Box>
  )
}
