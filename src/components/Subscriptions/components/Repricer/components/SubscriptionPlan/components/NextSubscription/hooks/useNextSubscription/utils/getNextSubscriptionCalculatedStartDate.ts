import { addDays, format, isValid, parse } from "date-fns"

import { convertToLocalDate } from "utils/dateConverter"

import { DATE_FNS_FORMATS } from "consts/dateTime"

export const getNextSubscriptionCalculatedStartDate = (
  currentSubscriptionEndDate: string | undefined
): string | null => {
  if (!currentSubscriptionEndDate) {
    return null
  }

  const parsedEndDate: Date = parse(
    currentSubscriptionEndDate,
    DATE_FNS_FORMATS.SERVER,
    new Date()
  )

  if (!isValid(parsedEndDate)) {
    return null
  }

  const nextStartDate: Date = addDays(parsedEndDate, 1)

  return convertToLocalDate(format(nextStartDate, DATE_FNS_FORMATS.SERVER))
}
