import { useSelector } from "react-redux"
import { addDays, format, parseISO } from "date-fns"

import { repricerSubscriptionPlansSelector } from "selectors/repricerSubscriptionSelectors"

import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"

import { DEFAULT_VALUES } from "consts"
import { DATE_FNS_FORMATS } from "consts/dateTime"
import { SUBSCRIPTION_IDS } from "consts/repricerSubscription"

import { getNextSubscriptionCalculatedStartDate } from "./utils"

export const useNextSubscription = () => {
  const { current, next } = useSelector(repricerSubscriptionPlansSelector)

  const isSubscriptionRenewal: boolean =
    !!current && !next && current?.auto_renew === 1

  const renewalDate: string = current?.subscription_end_date
    ? convertToLocalDate(
        format(
          addDays(parseISO(current.subscription_end_date), 1),
          DATE_FNS_FORMATS.SERVER
        )
      )
    : l(DEFAULT_VALUES.NA)

  const isNextSubscription: boolean = !!next

  const isNextSubscriptionFreemium: boolean =
    !!next && next.payment_module_version_id === SUBSCRIPTION_IDS.FREEMIUM

  const isNextSubscriptionTrial: boolean =
    !!next && next.payment_module_version_id === SUBSCRIPTION_IDS.TRIAL

  const isCurrentSubscriptionTrial: boolean =
    !!current && current.payment_module_version_id === SUBSCRIPTION_IDS.TRIAL

  const isTrialWithoutNextSubscription: boolean =
    isCurrentSubscriptionTrial && !next

  const nextSubscriptionStart: string = next?.subscription_start_date
    ? convertToLocalDate(next?.subscription_start_date)
    : l(DEFAULT_VALUES.NA)

  const nextSubscriptionFinish: string = next?.subscription_end_date
    ? convertToLocalDate(next?.subscription_end_date)
    : l(DEFAULT_VALUES.NA)

  const nextSubscriptionStartCalculated: string =
    getNextSubscriptionCalculatedStartDate(current?.subscription_end_date) ||
    l(DEFAULT_VALUES.NA)

  const nextSubscriptionLocalizedName: string | undefined =
    next?.repricerSubscriptionInfo?.localizedName

  const nextSubscriptionSize = next?.size

  const isSubscriptionCancelled: boolean =
    !!current && current?.auto_renew === 0 && !next

  const currentSubscriptionEndDate: string = current?.subscription_end_date
    ? convertToLocalDate(current?.subscription_end_date)
    : l(DEFAULT_VALUES.NA)

  return {
    currentSubscriptionEndDate,
    isNextSubscription,
    isNextSubscriptionFreemium,
    isNextSubscriptionTrial,
    isSubscriptionCancelled,
    isSubscriptionRenewal,
    isTrialWithoutNextSubscription,
    nextSubscriptionFinish,
    nextSubscriptionLocalizedName,
    nextSubscriptionSize,
    nextSubscriptionStart,
    nextSubscriptionStartCalculated,
    renewalDate,
  }
}
