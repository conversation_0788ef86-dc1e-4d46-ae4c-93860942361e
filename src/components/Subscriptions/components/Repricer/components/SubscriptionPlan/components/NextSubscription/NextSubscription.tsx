import React from "react"
import { Box, IconPopover, Typography } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { PRODUCT_NAMES, PRODUCTS } from "consts/product"
import {
  DEFAULT_FREEMIUM_SIZE,
  SUBSCRIPTION_TYPES,
} from "consts/repricerSubscription"

import { NextFreemiumSubscriptionWarningContent } from "./components"

import { useNextSubscription } from "./hooks"

export const NextSubscription = () => {
  const {
    currentSubscriptionEndDate,
    isNextSubscription,
    isNextSubscriptionFreemium,
    isNextSubscriptionTrial,
    isSubscriptionCancelled,
    isSubscriptionRenewal,
    isTrialWithoutNextSubscription,
    nextSubscriptionFinish,
    nextSubscriptionLocalizedName,
    nextSubscriptionSize,
    nextSubscriptionStart,
    nextSubscriptionStartCalculated,
    renewalDate,
  } = useNextSubscription()

  if (isSubscriptionCancelled) {
    return (
      <Box flexDirection="column" gap="s">
        <Box align="center" gap="s">
          <Typography variant="--font-body-text-2">
            {l("Subscription cancelled")}
          </Typography>
          <IconPopover
            color="--color-icon-active"
            maxWidth={300}
            name="icnInfoCircle"
            size="--icon-size-3"
            content={l(
              "{repricer} subscription has been canceled. You may use {repricer} until {endDate}. You may restore, upgrade or select the next subscription to continue to use {repricer}.",
              {
                endDate: currentSubscriptionEndDate,
                repricer: PRODUCT_NAMES[PRODUCTS.repricer],
              }
            )}
          />
        </Box>

        <Typography variant="--font-body-text-7">
          {l("Subscription end-date")}: {currentSubscriptionEndDate}
        </Typography>
      </Box>
    )
  }

  if (isNextSubscription) {
    return (
      <Box flexDirection="column" gap="s">
        <Typography variant="--font-body-text-2">
          {l("Next subscription")}
        </Typography>

        <Box align="center" flexWrap="wrap" gap="s">
          <Typography variant="--font-body-text-5">
            {l("{planName} plan", {
              planName: nextSubscriptionLocalizedName,
            })}
          </Typography>
          <Typography variant="--font-body-text-7">
            (
            {isNextSubscriptionFreemium
              ? l("From: {startDate}", { startDate: nextSubscriptionStart })
              : `${nextSubscriptionStart} - ${nextSubscriptionFinish}`}
            )
          </Typography>

          {isNextSubscriptionFreemium ? (
            <IconPopover
              color="--color-icon-warning"
              content={<NextFreemiumSubscriptionWarningContent />}
              maxWidth={500}
              name="icnWarning"
              size="--icon-size-3"
            />
          ) : null}
        </Box>

        {isNextSubscriptionTrial ? null : (
          <Typography variant="--font-body-text-7">
            {isNextSubscriptionFreemium
              ? l("{number} optimized products", {
                  number: DEFAULT_FREEMIUM_SIZE,
                })
              : l("Product Optimization limit: {limit}", {
                  limit: ln(nextSubscriptionSize, 0),
                })}
          </Typography>
        )}
      </Box>
    )
  }

  if (isTrialWithoutNextSubscription) {
    return (
      <Box flexDirection="column" gap="s">
        <Typography variant="--font-body-text-2">
          {l("Next subscription")}
        </Typography>

        <Box align="center" flexWrap="wrap" gap="s">
          <Typography variant="--font-body-text-5">
            {l("{planName} plan", { planName: l(SUBSCRIPTION_TYPES.FREEMIUM) })}
          </Typography>

          <Typography variant="--font-body-text-7">
            (
            {l("From: {startDate}", {
              startDate: nextSubscriptionStartCalculated,
            })}
            )
          </Typography>

          <IconPopover
            color="--color-icon-warning"
            content={<NextFreemiumSubscriptionWarningContent />}
            maxWidth={500}
            name="icnWarning"
            size="--icon-size-3"
          />
        </Box>

        <Typography variant="--font-body-text-7">
          {l("{number} optimized products", { number: DEFAULT_FREEMIUM_SIZE })}
        </Typography>
      </Box>
    )
  }

  if (isSubscriptionRenewal) {
    return (
      <Box flexDirection="column" gap="s">
        <Typography variant="--font-body-text-2">
          {l("Renewal subscription")}
        </Typography>
        <Typography variant="--font-body-text-7">
          {l("Renewal date")}: {renewalDate}
        </Typography>
      </Box>
    )
  }

  return null
}
