import { SVGProps } from "react"
import { DotProps } from "recharts"

export const CHART_YAXIS_GAP = 20
export const YAXIS_X_OFFSET = -15

export const GRADIENT_ID = "customGradient"

export const YAXIS_WIDTHS: { [key: number]: number } = {
  1: 7.3,
  2: 14.61,
  3: 21.91,
  4: 29.21,
  5: 36.5,
  6: 40.43,
  7: 47.17,
  8: 53.91,
  9: 60.66,
  10: 67.39,
} as const

export const AREA_DOT_STYLES: DotProps = {
  r: 4,
  stroke: "#c2d0e6",
  strokeWidth: 2,
  fill: "#0055cc",
} as const

export const YAXIS_TICK_STYLES: SVGProps<SVGTextElement> = {
  fontSize: 11,
  fill: "#666666",
} as const

export const XAXIS_TICK_STYLES: SVGProps<SVGTextElement> = {
  fontSize: 11,
  fill: "#bdbdbd",
} as const
