import React, { useContext } from "react"
import { <PERSON>, Drawer, TabsProps, TitleBar } from "@develop/fe-library"

import { MainDrawerContext } from "pages/SubscriptionsPage/context"

import { RestrictedButtonPopover } from "components/shared/Buttons"
import {
  BusinessAnalytics,
  ExpiredRepricerSubscription,
  LostAndFound,
  RepricerB2B,
  RepricerB2C,
  SubscriptionDashboard,
  SubscriptionTabContainer,
} from "components/Subscriptions/components"
import { DRAWER_COMPONENTS_MAP } from "components/Subscriptions/components/MainDrawerComponents"
import { SUBSCRIPTION_DASHBOARD_KEY } from "components/Subscriptions/constants"
import TabsComponent from "components/TabsComponent/TabsComponent"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import {
  MAIN_DRAWER_INITIAL_STATE,
  permissionKeys,
  restrictPopoverMessages,
} from "consts"
import { PRODUCT_NAMES, PRODUCTS, REPRICER_OFFER_TYPE } from "consts/product"

import { useHeaderData, useLostData, useSubscriptionsData } from "./hooks"

import { Repricer } from "./components/Repricer"

import { Tab } from "@develop/fe-library/dist/lib/components/Tabs/TabsTypes"

// Delete this after implementing the real data
const expiredDate = "28.09.24"
const mockDataForAlert = [
  {
    offerType: REPRICER_OFFER_TYPE[PRODUCTS.repricerB2C],
    expiredDate,
  },
  {
    offerType: REPRICER_OFFER_TYPE[PRODUCTS.repricerB2B],
    expiredDate,
  },
]

const isNeedAndLogicImplemented = false

export const Subscriptions = () => {
  const { backHandler, activeTab, useNewPricing, handleChangeTab, TAB_KEYS } =
    useSubscriptionsData()
  const { isDemoAccount, iconHoverTextForAddNewProduct, addNewProductHandler } =
    useHeaderData()

  useLostData()
  const { updateMainDrawerData, mainDrawerData } = useContext(MainDrawerContext)

  const { titleName, open, component, data, onBack } = mainDrawerData

  const subscriptionsDashboard: Tab = {
    key: TAB_KEYS[SUBSCRIPTION_DASHBOARD_KEY],
    label: l("Subscription dashboard"),
    icon: "icnBarChart",
    contents: (
      <SubscriptionTabContainer>
        <SubscriptionDashboard handleChangeTab={handleChangeTab} />
      </SubscriptionTabContainer>
    ),
  }

  const repricerB2C: Tab = {
    key: TAB_KEYS[PRODUCTS.repricerB2C],
    label: PRODUCT_NAMES[PRODUCTS.repricerB2C],
    icon: "icnB2CRepricer",
    contents: (
      <SubscriptionTabContainer>
        <RepricerB2C />
      </SubscriptionTabContainer>
    ),
  }

  const repricerB2B: Tab = {
    key: TAB_KEYS[PRODUCTS.repricerB2B],
    label: PRODUCT_NAMES[PRODUCTS.repricerB2B],
    icon: "icnB2BRepricer",
    contents: (
      <SubscriptionTabContainer>
        <RepricerB2B />
      </SubscriptionTabContainer>
    ),
  }

  const repricer: Tab = {
    key: TAB_KEYS[PRODUCTS.repricer],
    label: PRODUCT_NAMES[PRODUCTS.repricer],
    icon: "icnRetweet",
    contents: (
      <SubscriptionTabContainer>
        <Repricer />
      </SubscriptionTabContainer>
    ),
  }

  const lostAndFound: Tab = {
    key: TAB_KEYS[PRODUCTS.lost],
    label: PRODUCT_NAMES[PRODUCTS.lost],
    icon: "icnFileSearch",
    contents: (
      <SubscriptionTabContainer>
        <LostAndFound />
      </SubscriptionTabContainer>
    ),
  }

  const businessAnalytics: Tab = {
    key: TAB_KEYS[PRODUCTS.bas],
    label: PRODUCT_NAMES[PRODUCTS.bas],
    icon: "icnPieChart",
    contents: (
      <SubscriptionTabContainer>
        <BusinessAnalytics onChangeTab={handleChangeTab} />
      </SubscriptionTabContainer>
    ),
  } as const

  const tabs: TabsProps["items"] = [
    subscriptionsDashboard,
    ...(useNewPricing ? [repricer] : [repricerB2C, repricerB2B]),
    businessAnalytics,
    lostAndFound,
  ]

  const isComponentExist: boolean =
    !!component && component in DRAWER_COMPONENTS_MAP

  const DrawerComponent =
    DRAWER_COMPONENTS_MAP[component as keyof typeof DRAWER_COMPONENTS_MAP]

  const drawerCloseHandler = () => {
    updateMainDrawerData(MAIN_DRAWER_INITIAL_STATE)
  }

  return (
    <Box flexDirection="column" height="100%" width="100%">
      <Drawer
        hasHeaderBottomMargin={false}
        hasPadding={false}
        isOpen={open}
        title={titleName}
        width="440px"
        zIndex={1001}
        onBack={onBack}
        onClose={drawerCloseHandler}
      >
        {isComponentExist ? (
          <DrawerComponent data={data} onClose={drawerCloseHandler} />
        ) : null}
      </Drawer>
      {checkIsArray(mockDataForAlert) && isNeedAndLogicImplemented ? (
        <Box flexDirection="column" margin="0 m m" mLG={{ margin: "0 l l" }}>
          {mockDataForAlert.map(({ offerType, expiredDate }) => {
            return (
              <Box
                key={offerType}
                flexDirection="column"
                marginTop="m"
                mLG={{ marginTop: "l" }}
              >
                <ExpiredRepricerSubscription
                  expiredDate={expiredDate}
                  offerType={offerType}
                />
              </Box>
            )
          })}
        </Box>
      ) : null}
      <TitleBar
        hasBorderBottom={false}
        hasBorderTop={false}
        contents={[
          l("Subscriptions"),
          null,
          <RestrictedButtonPopover
            iconOnly
            content={iconHoverTextForAddNewProduct}
            disabled={isDemoAccount}
            icon="icnPlus"
            managePermission={permissionKeys.amazonCustomerAccountManage}
            placement="topRight"
            popoverMessage={restrictPopoverMessages.setupAccount}
            size="small"
            variant="primary"
            onClick={addNewProductHandler}
          />,
        ]}
        onBack={backHandler}
      />
      <Box display="block" height="100%">
        <TabsComponent
          // @ts-ignore
          hideIconsOnMobile
          activeTab={activeTab}
          tabs={tabs}
          onChangeTab={handleChangeTab}
        />
      </Box>
    </Box>
  )
}
