import { useCallback, useEffect, useMemo, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import {
  getUrlSearchParams,
  removeUrlSearchParam,
} from "@develop/fe-library/dist/utils"

import amazonAdsAccountsActions from "actions/amazonAdsAccountsActions"
import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import amazonMarketplacesActions from "actions/amazonMarketplacesActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"
import basPaymentsActions from "actions/basPaymentsActions"
import customerActions from "actions/customerActions"
import lostCaseCustomerCounterActions from "actions/lostCaseCustomerCounterActions"
import repricerSubscriptionActions from "actions/repricerSubscriptionActions"

import {
  isDemoAccountSelector,
  useNewPricingSelector,
} from "selectors/customerSelectors"
import {
  isProductNotLoadedSelector,
  permissionsSelector,
} from "selectors/userSelectors"

import { buildGetRepricerEventGroupedByDateRequestParams } from "./utils"

import {
  SUBSCRIPTION_DASHBOARD_KEY,
  SUBSCRIPTION_MODAL_IDS,
  TAB_KEYS,
} from "../../constants"

import { PlansInfo } from "types/Models"

import {
  SubscriptionPageUrlParams,
  SubscriptionsTabTypes,
} from "../../SubscriptionsTypes"

const { getAmazonAdsAccounts } = amazonAdsAccountsActions
const { setIsTransferToNewPricingModalVisible } = customerActions
const { getAll: getAccounts } = amazonCustomerAccountsActions
const { getAll: getAmazonMarketplaces } = amazonMarketplacesActions
const { get: getLostCaseCustomerCounters } = lostCaseCustomerCounterActions
const { getBasPaymentPlans } = basPaymentsActions
const { getBasPlan, getBasSubscriptions } = basCustomerPlanActions
const {
  getRepricerCustomerPlanSubscriptions,
  getRepricerEventTotal,
  getRepricerEventGroupedByDate,
  getRepricerCustomerPlanStatisticWithStock,
} = repricerSubscriptionActions

export const useSubscriptionsData = () => {
  const dispatch = useDispatch()
  const history = useHistory()

  const [activeTab, setActiveTab] = useState<SubscriptionsTabTypes>(
    TAB_KEYS[SUBSCRIPTION_DASHBOARD_KEY]
  )

  const useNewPricing = useSelector(useNewPricingSelector)
  const isProductNotLoaded = useSelector(isProductNotLoadedSelector)
  const permissions = useSelector(permissionsSelector)
  const isDemoAccount = useSelector(isDemoAccountSelector)

  const { hash, search, pathname } = history.location

  const currentHash = hash.slice(1)

  const urlSearchParams = getUrlSearchParams<SubscriptionPageUrlParams>({
    locationSearch: search,
  })

  const hashParams = useMemo(
    () => new URLSearchParams(currentHash),
    [currentHash]
  )

  const onChangeHash = useCallback(
    ({ tabKey }) => {
      hashParams.set("tab", tabKey)

      history.replace({
        search,
        pathname,
        hash: hashParams.toString(),
      })
    },
    [hashParams, history, pathname, search]
  )

  const handleChangeTab = useCallback(
    (tabKey: SubscriptionsTabTypes): void => {
      setActiveTab(tabKey)
      onChangeHash({ tabKey })
    },
    [onChangeHash]
  )

  useEffect(() => {
    if (activeTab !== hashParams.get("tab")) {
      const tabFromHash =
        (hashParams.get("tab") as SubscriptionsTabTypes) ||
        SUBSCRIPTION_DASHBOARD_KEY

      handleChangeTab(tabFromHash)
    }
  }, [activeTab, handleChangeTab, hash, hashParams])

  useEffect(() => {
    dispatch(getAccounts(false))
    dispatch(getLostCaseCustomerCounters())
    dispatch(getBasPlan({}))
    dispatch(getBasSubscriptions())
    dispatch(getBasPaymentPlans({}))
    dispatch(getAmazonMarketplaces(undefined))

    const shouldGetAmazonAdsAccounts =
      permissions.amazonAdsAccountList && !isDemoAccount

    if (shouldGetAmazonAdsAccounts) {
      dispatch(getAmazonAdsAccounts({ is_deleted: "0" }, false, () => {}))
    }
  }, [])

  useEffect(() => {
    if (useNewPricing) {
      const getRepricerCustomerPlanSubscriptionsSuccessCallback = (
        data: PlansInfo
      ) => {
        dispatch(getRepricerCustomerPlanStatisticWithStock())

        if (!isProductNotLoaded) {
          if (data?.current) {
            dispatch(
              getRepricerEventGroupedByDate({
                params: buildGetRepricerEventGroupedByDateRequestParams(),
              })
            )
          }

          const currentSubscriptionMonthlyStartDate: string | undefined =
            data?.current?.date_start

          const currentSubscriptionMonthlyEndDate: string | undefined =
            data?.current?.date_finish

          if (
            currentSubscriptionMonthlyStartDate &&
            currentSubscriptionMonthlyEndDate
          ) {
            dispatch(
              getRepricerEventTotal({
                params: {
                  dateStart: currentSubscriptionMonthlyStartDate,
                  dateEnd: currentSubscriptionMonthlyEndDate,
                },
              })
            )
          }
        }
      }

      dispatch(
        getRepricerCustomerPlanSubscriptions({
          cache: false,
          successCallback: getRepricerCustomerPlanSubscriptionsSuccessCallback,
        })
      )
    }
  }, [dispatch, useNewPricing, isProductNotLoaded])

  useEffect(() => {
    if (
      urlSearchParams.openedModal ===
      SUBSCRIPTION_MODAL_IDS.TRANSFER_TO_NEW_PRICING
    ) {
      const newLocationSearch = removeUrlSearchParam({
        paramName: "openedModal",
        locationSearch: search,
      })

      history.replace({
        ...history.location,
        search: newLocationSearch.toString(),
      })

      dispatch(setIsTransferToNewPricingModalVisible(true))
    }
  }, [dispatch, history, search, urlSearchParams])

  const backHandler = (): void => {
    history.goBack()
  }

  return {
    backHandler,
    activeTab,
    useNewPricing,
    handleChangeTab,
    TAB_KEYS,
  }
}
