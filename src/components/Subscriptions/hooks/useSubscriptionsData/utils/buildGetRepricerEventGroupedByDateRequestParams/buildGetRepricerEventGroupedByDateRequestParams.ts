import { DATE_OUTPUT_FORMATS } from "@develop/fe-library/dist/consts"
import { addDays, format, lastDayOfMonth, subMonths, subYears } from "date-fns"

import type { BuildGetRepricerEventGroupedByDateRequestParams } from "./BuildGetRepricerEventGroupedByDateRequestParamsTypes"

export const buildGetRepricerEventGroupedByDateRequestParams: BuildGetRepricerEventGroupedByDateRequestParams =
  () => {
    const endDate: Date = lastDayOfMonth(subMonths(new Date(), 1))
    const startDate: Date = addDays(subYears(endDate, 1), 1)

    const endServerDate: string = format(
      endDate,
      DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT
    )
    const startServerDate: string = format(
      startDate,
      DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT
    )

    return {
      dateStart: startServerDate,
      dateEnd: endServerDate,
    }
  }
