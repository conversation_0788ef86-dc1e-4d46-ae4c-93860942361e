import { useDispatch, useSelector } from "react-redux"

import amazonCustomerAccountMarketplaceActions from "actions/amazonCustomerAccountMarketplaceActions"

import { languageSelector } from "selectors/translationsSelectors"

import { checkIsArray } from "utils/arrayHelpers"

import { REPRICER_OFFER_TYPE } from "consts/product"

import { RepricerAccountMarketplaces } from "components/Subscriptions/types"

import { UseRepricerEnableDisableLogicParams } from "./useRepricerEnableDisableLogicTypes"

const {
  add: addAccountMarketplace,
  delete: deleteAccountMarketplace,
  getAll: getAccountMarketplaces,
} = amazonCustomerAccountMarketplaceActions

export const useRepricerEnableDisableLogic = ({
  productName,
  repricerAccountMarketplaces,
  deleted,
}: UseRepricerEnableDisableLogicParams) => {
  const dispatch = useDispatch()
  const language = useSelector(languageSelector)

  const trialRepricerMarketplaces = repricerAccountMarketplaces?.filter(
    ({ canBeDeleted, deleted }) => canBeDeleted || deleted
  )

  const enableRepricerMarketplaces = trialRepricerMarketplaces.some(
    ({ deleted }) => deleted
  )

  const currentRepricerTrial = trialRepricerMarketplaces

  const isToggleRepricerMarketplacesButtonVisible =
    checkIsArray(trialRepricerMarketplaces) && !deleted

  const toggleMarketplacesButtonText: string = enableRepricerMarketplaces
    ? "Enable all marketplaces"
    : "Disable all marketplaces"

  const enableMarketplacesHandler = ({
    enableMarketplaces,
    marketplacesList,
    offerType,
  }) => {
    const marketplacesListCopy: RepricerAccountMarketplaces[] = [
      ...marketplacesList,
    ]

    const updateMarketplace = (marketplaces) => {
      const marketplace = marketplaces.shift()

      if (!marketplace) {
        dispatch(getAccountMarketplaces(false))

        return
      }

      const { amazonMarketplaceId, customerAccountId, id } = marketplace

      enableMarketplaces
        ? dispatch(
            addAccountMarketplace(
              customerAccountId,
              { amazonMarketplaceId, language, offerType },
              () => updateMarketplace(marketplaces)
            )
          )
        : dispatch(
            deleteAccountMarketplace(id, () => updateMarketplace(marketplaces))
          )
    }

    updateMarketplace(marketplacesListCopy)
  }

  const handleToggleMarketplaces = ({ target }) => {
    target.blur()

    const filteredTrialMarketplaces = enableRepricerMarketplaces
      ? currentRepricerTrial?.filter(({ deleted }) => deleted)
      : currentRepricerTrial

    enableMarketplacesHandler({
      enableMarketplaces: enableRepricerMarketplaces,
      marketplacesList: filteredTrialMarketplaces,
      offerType: REPRICER_OFFER_TYPE[productName],
    })
  }

  return {
    toggleMarketplacesButtonText,
    handleToggleMarketplaces,
    isToggleRepricerMarketplacesButtonVisible,
  }
}
