@import "assets/styles/variables.scss";

.legend {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.cardList {
  list-style: none;
  margin: 0;
  max-height: 430px;
  overflow: auto;
  padding: 0;
  padding-right: 15px;
  width: calc(100% + 15px);
}

.paginationContainer {
  display: flex;
  justify-content: center;
  pointer-events: none;

  :global(.ant-pagination) {
    cursor: pointer;
    pointer-events: auto;
  }
}

.pagination {
  padding-bottom: 0;
}

.titleLabel {
  font-size: 14px;
}

.countLabel {
  color: $text_second;
  font-size: 13px;
}

.modalContainer.modalContainer {
  z-index: 1000;
}

@media (max-width: $xs) {
  .legend {
    flex-direction: column;
  }

  .cardList {
    flex-basis: 1px;
    flex-grow: 1;
    max-height: unset;
  }
}
