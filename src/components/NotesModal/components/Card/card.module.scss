@import "assets/styles/variables.scss";

.card {
  background-color: $main_bg;
  border: 1px solid $border_main;
  border-radius: 2px;
  padding: 20px;
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 0;
  }
}

.cardTitle {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.dateWrapper {
  align-items: center;
  display: flex;
  justify-content: flex-start;
}

.iconsContainer {
  display: flex;
  margin-left: 20px;
}

.icon {
  cursor: pointer;
  font-size: 18px;
  margin-right: 10px;

  &:last-child {
    margin-right: 0;
  }
}

.textArea.textArea.textArea {
  height: 107px;
}

.authorLabel.authorLabel,
.label.label {
  color: $text_second;
  font-size: 13px;
}

.content {
  color: $text_main;
  white-space: pre-wrap;
}

@media (max-width: $xs) {
  .cardTitle {
  }

  .card {
    margin-bottom: 5px;
    padding: 10px;
  }

  .iconsContainer {
    margin-left: 5px;
  }

  .icon {
    font-size: 15px;
  }

  .authorLabel.authorLabel {
    max-width: 88px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .card {
    &:last-child {
      margin-bottom: 10px;
    }
  }
}
