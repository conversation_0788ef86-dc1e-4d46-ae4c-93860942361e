import { connect } from "react-redux"
import get from "lodash/get"

import staffActions from "actions/staffActions"

import { languagesSelectOptions } from "selectors/languagesSelectors"
import { timeZoneSelectorAlphabetOrder } from "selectors/timezoneSelectors"
import { rolesOptionsForTableSelector } from "selectors/selectedRoleSelectors"
import { currentCustomersSelector } from "selectors/customerSelectors"

import CreateUserForm from "./CreateUserForm"

const { create: createUser } = staffActions

const mapStateToProps = (state) => {
  return {
    customerId: get(currentCustomersSelector(state), "id"),
    timezones: timeZoneSelectorAlphabetOrder(state),
    languages: languagesSelectOptions(state),
    rolesOptions: rolesOptionsForTableSelector(state),
  }
}

const mapDispatchToProps = {
  createUser,
}

export default connect(mapStateToProps, mapDispatchToProps)(CreateUserForm)
