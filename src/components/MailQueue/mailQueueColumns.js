import React from "react"

import ExportValue from "components/TableGridLayout/components/ExportValue"
import {
  COLUMN_INPUT_TYPE_DATE_RANGE,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT,
} from "components/TableGridLayout/TableGridLayoutView"
import Typography from "components/Typography"

import { avaiableStatuses } from "utils/intl"

import { UserLink } from "./components"

export const tableColumns = [
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: true,
    width: 80,
    cellTextStyles: { textAlign: "left" },
  },
  {
    title: "To",
    dataIndex: "to",
    key: "to",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: false,
    width: 180,
    cellTextStyles: { textAlign: "left" },
    render: (value, { to, user_id }) => {
      return <UserLink to={to} user_id={user_id} />
    },
  },
  {
    title: "Reply to",
    dataIndex: "reply_to",
    key: "reply_to",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: true,
    width: 160,
    cellTextStyles: { textAlign: "left" },
  },
  {
    title: "CC",
    dataIndex: "cc",
    key: "cc",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: false,
    width: 90,
    cellTextStyles: { textAlign: "left" },
  },
  {
    title: "Bcc",
    dataIndex: "bcc",
    key: "bcc",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: false,
    width: 90,
    cellTextStyles: { textAlign: "left" },
  },
  {
    title: "Subject",
    dataIndex: "subject",
    key: "subject",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: false,
    width: 350,
    cellTextStyles: { textAlign: "left" },
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT,
    ellipsis: true,
    width: 120,
    render: (statusId) => (
      <ExportValue>
        {statusId === -1 ? (
          <Typography type={"div"} variant="textSmall">
            {"Error"}
          </Typography>
        ) : (
          <Typography type={"div"} variant="textSmall">
            {avaiableStatuses[statusId]}
          </Typography>
        )}
      </ExportValue>
    ),
  },
  {
    title: "Create time",
    dataIndex: "create_time",
    key: "create_time",
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE_RANGE,
    ellipsis: true,
    width: 160,
  },
  {
    title: "Update time",
    dataIndex: "update_time",
    key: "update_time",
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE_RANGE,
    ellipsis: true,
    width: 160,
  },
  {
    title: "Error",
    dataIndex: "error",
    key: "error",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: false,
    cellTextStyles: { textAlign: "left", paddingTop: 5, paddingBottom: 5 },
    width: 90,
  },
  {
    title: "Mail user",
    dataIndex: "mail_user",
    key: "mail_user",
    sorter: true,
    type: COLUMN_INPUT_TYPE_INPUT,
    ellipsis: true,
    width: 180,
    cellTextStyles: { textAlign: "left" },
  },
]
