import React from "react"

import { MailQueueTableSettings } from "initialState/tableSettings"

import { TableWrapper } from "components/shared/TableWrapper"

import { useMailQueue } from "./hooks"

import { tableColumns } from "./mailQueueColumns"

import "./mailQueueContainer.scss"

const PAGE_NAME = "Mail queue"

export const MailQueue = () => {
  const {
    mailQueues,
    searchOptions,
    selectFiltersOptions,
    restSearchOptions,
    totalCount,
    getMailQueues,
  } = useMailQueue()

  return (
    <TableWrapper
      // @ts-ignore
      isNeedSort
      actionsColumnWidth={13}
      componentTableSettings={MailQueueTableSettings}
      contentClassName="mail-queue-container"
      customDefaultFilterValues={restSearchOptions}
      dataSource={mailQueues}
      getData={getMailQueues}
      pageTableSettings={MailQueueTableSettings}
      searchOptions={searchOptions}
      selectFiltersOptions={selectFiltersOptions}
      tableColumns={tableColumns}
      tableFooterStyles={{ paddingLeft: 13 }}
      tableGridTitle={PAGE_NAME}
      tableHeaderTitle={PAGE_NAME}
      totalCount={totalCount}
    />
  )
}
