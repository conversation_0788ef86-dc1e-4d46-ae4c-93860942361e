import { useDispatch, useSelector } from "react-redux"
import { getObjectEntries } from "@develop/fe-library/dist/utils"

import mailQueueActions from "actions/mailQueueActions"

import {
  getLocalizedMailQueues,
  mailQueuesSearchOptionsSelector,
  mailQueuesTotalCountSelector,
} from "selectors/mailQueueSelectors"

import { avaiableStatuses } from "utils/intl"

// @ts-ignore
const { get: getMailQueuesData } = mailQueueActions

export const useMailQueue = () => {
  const mailQueues = useSelector(getLocalizedMailQueues)
  const totalCount = useSelector(mailQueuesTotalCountSelector)
  const searchOptions = useSelector(mailQueuesSearchOptionsSelector)

  const { page, pageSize, ...restSearchOptions } = searchOptions

  const selectFiltersOptions = {
    status: [
      ...getObjectEntries(avaiableStatuses).map(([value, label]) => ({
        value,
        label,
      })),
    ],
  }

  const dispatch = useDispatch()

  const getMailQueues = <Type,>(searchOptions: Type) => {
    return dispatch(getMailQueuesData(searchOptions))
  }

  return {
    selectFiltersOptions,
    mailQueues,
    totalCount,
    searchOptions,
    restSearchOptions,
    getMailQueues,
  }
}
