import React from "react"

import { CustomersTableSettings } from "initialState/tableSettings"

import { UpdateCustomerFormModal } from "components/Customers/components/UpdateCustomerFormModal/UpdateCustomerFormModal"
import tableColumns from "components/Customers/customersTableColumns"
import { ConfirmModal } from "components/shared/Modal"
import { TableWrapper } from "components/shared/TableWrapper"

import l from "utils/intl"

import { useCustomers } from "./hooks"

const PAGE_NAME = "Companies"

export const Customers = () => {
  const {
    canManage,
    canView,
    canViewPartnerList,
    countries,
    currencies,
    initialValues,
    dataSource,
    modalVisible,
    searchOptions,
    selectFiltersOptions,
    totalCount,
    partners,
    channels,
    deleteModalVisible,
    hasCustomerChanged,
    nextCustomer,
    getAdditionalData,
    getCustomers,
    deleteItemHandler,
    editIconHandler,
    deleteIconHand<PERSON>,
    submitHandler,
    handleToggleDeleteModal,
    updateModalSubmitHandler,
    changePartnerIDHandler,
    closeUpdateModalHandler,
  } = useCustomers()

  return (
    <>
      <TableWrapper
        // @ts-expect-error
        isNeedSort
        actionsColumnWidth={120}
        componentTableSettings={CustomersTableSettings}
        dataSource={dataSource}
        getAdditionalData={getAdditionalData}
        getData={getCustomers}
        pageTableSettings={CustomersTableSettings}
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableColumns={tableColumns}
        tableGridTitle={PAGE_NAME}
        tableHeaderTitle={PAGE_NAME}
        totalCount={totalCount}
        tableIcons={[
          {
            checkAvailability: () => canManage || canView,
            onClick: editIconHandler,
            name: "icnEdit",
            title: "Update",
          },
          {
            onClick: deleteIconHandler,
            name: "icnDeleteOutlined",
            title: "Delete",
            checkAvailability: ({ isDeletable }: { isDeletable: boolean }) => {
              return isDeletable && canManage
            },
          },
        ]}
        onSaveCell={submitHandler}
      />

      {modalVisible ? (
        <UpdateCustomerFormModal
          updateCustomerSuccessCallback={updateModalSubmitHandler}
          onClose={closeUpdateModalHandler}
        />
      ) : null}

      {/*{modalVisible ? (*/}
      {/*  // @ts-expect-error*/}
      {/*  <CardsLayoutModal*/}
      {/*    withCloseConfirm*/}
      {/*    disabled={!canManage}*/}
      {/*    initialValues={initialValues}*/}
      {/*    title="Update company"*/}
      {/*    width={700}*/}
      {/*    controls={[*/}
      {/*      {*/}
      {/*        name: "title",*/}
      {/*        placeholder: "Company name",*/}
      {/*        type: "text",*/}
      {/*        required: true,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        name: "address",*/}
      {/*        placeholder: "Address 1",*/}
      {/*        type: "text",*/}
      {/*        required: true,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        name: "address2",*/}
      {/*        placeholder: "Address 2",*/}
      {/*        type: "text",*/}
      {/*      },*/}
      {/*      {*/}
      {/*        name: "zip",*/}
      {/*        placeholder: "Postcode",*/}
      {/*        type: "text",*/}
      {/*        required: true,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        name: "city",*/}
      {/*        placeholder: "City",*/}
      {/*        type: "text",*/}
      {/*        required: true,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        allowClear: false,*/}
      {/*        name: "country_id",*/}
      {/*        options: countries*/}
      {/*          .map(({ id, title }) => ({*/}
      {/*            label: l(title),*/}
      {/*            value: id,*/}
      {/*          }))*/}
      {/*          .sort((a, b) => a.label.localeCompare(b.label)),*/}
      {/*        placeholder: "Country",*/}
      {/*        type: "selectWithFilter",*/}
      {/*        required: true,*/}
      {/*      },*/}
      {/*      {*/}
      {/*        name: "vat_id",*/}
      {/*        note: "If no valid VAT-ID is entered, your invoice will contain the currently valid German VAT.",*/}
      {/*        placeholder: "VAT ID",*/}
      {/*        textInputPlaceholder: "DE815528383",*/}
      {/*        type: "text",*/}
      {/*      },*/}
      {/*      {*/}
      {/*        name: "currency_id",*/}
      {/*        options: currencies.map(({ id, title }) => ({*/}
      {/*          label: title,*/}
      {/*          value: id,*/}
      {/*        })),*/}
      {/*        placeholder: "Currency",*/}
      {/*        type: "select",*/}
      {/*      },*/}
      {/*      ...(canViewPartnerList*/}
      {/*        ? [*/}
      {/*            {*/}
      {/*              name: "partner_id",*/}
      {/*              options: partners.map(({ id, title }) => ({*/}
      {/*                label: title,*/}
      {/*                value: id,*/}
      {/*              })),*/}
      {/*              placeholder: "Partner ID",*/}
      {/*              type: "select",*/}
      {/*              onChange: changePartnerIDHandler,*/}
      {/*            },*/}
      {/*            {*/}
      {/*              name: "partner_channel_id",*/}
      {/*              options: channels.map(({ id, name }) => ({*/}
      {/*                label: name,*/}
      {/*                value: id,*/}
      {/*              })),*/}
      {/*              placeholder: "Channel ID",*/}
      {/*              type: "select",*/}
      {/*            },*/}
      {/*          ]*/}
      {/*        : []),*/}
      {/*    ]}*/}
      {/*    onClose={closeUpdateModalHandler}*/}
      {/*    onSubmit={updateModalSubmitHandler}*/}
      {/*  />*/}
      {/*) : null}*/}
      {deleteModalVisible ? (
        // @ts-expect-error
        <ConfirmModal
          visible
          cancelText={l("Cancel")}
          okText={hasCustomerChanged ? l("Continue") : l("Delete")}
          title={l("Delete item")}
          message={
            hasCustomerChanged
              ? `${l(
                  "You are about to delete the customer, you are on. If continue, you will be switched to customer"
                )} ${nextCustomer?.title}`
              : l("Are you sure you want to delete this item?")
          }
          onCancel={handleToggleDeleteModal}
          onOk={deleteItemHandler}
        />
      ) : null}
    </>
  )
}
