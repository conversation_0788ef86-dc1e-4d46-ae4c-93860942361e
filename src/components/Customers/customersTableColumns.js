import React from "react"
import { IconPopover } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"
import { checkIsNumber } from "@develop/fe-library/dist/utils"

import Link from "components/shared/Link"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import Typography from "components/Typography"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { CUSTOMER_VAT_ID_STATUSES } from "consts/customer"

import styles from "./customers.module.scss"

/* {
  dataIndex: The value is set in the selector of this component. And it is indicated here the same as in it.
  key: Key value set from the backend. Specified in the task.
} */

export default [
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
    sorter: true,
    type: "input",
    ellipsis: true,
    width: 68,
  },
  {
    title: "Company name",
    dataIndex: "title",
    key: "title",
    sorter: true,
    type: "input",
    width: 200,
  },
  {
    title: "Address 1",
    dataIndex: "address",
    key: "address",
    sorter: true,
    type: "input",
  },
  {
    title: "Address 2",
    dataIndex: "address2",
    key: "address2",
    sorter: true,
    type: "input",
  },
  {
    title: "Postcode",
    dataIndex: "zip",
    key: "zip",
    sorter: true,
    type: "input",
  },
  {
    title: "City",
    dataIndex: "city",
    key: "city",
    sorter: true,
    type: "input",
  },
  {
    title: "Country",
    dataIndex: "countryLabel",
    key: "countryTitle",
    sorter: true,
    type: "selectWithFilter",
  },
  {
    title: "VAT-ID",
    dataIndex: "vat_id",
    key: "vat_id",
    sorter: true,
    type: "input",
    width: 200,
    render: (text, { vat_id_is_valid }) => {
      const isValid = vat_id_is_valid === 1
      const statusMessage =
        CUSTOMER_VAT_ID_STATUSES?.[vat_id_is_valid] ||
        CUSTOMER_VAT_ID_STATUSES[0]

      return (
        <div className={styles.vatidWrapper}>
          <Typography className={text && styles.text} variant="textSmall">
            <ExportValue>{text}</ExportValue>
          </Typography>
          <IconPopover
            color={isValid ? "--color-icon-active" : "--color-icon-error"}
            content={l(statusMessage)}
            name={isValid ? "icnSafetyCertificate" : "icnClose"}
            size="--icon-size-4"
          />
          <ExportValue hiden>{l(statusMessage)}</ExportValue>
        </div>
      )
    },
  },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    sorter: true,
    type: "multySelect",
    editable: {
      options: [
        { label: "Active", value: "active" },
        { label: "Soft block", value: "soft_block" },
        { label: "Hard block", value: "hard_block" },
      ],
      title: "Update active status",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerActive",
  },
  {
    title: "Confirmed",
    dataIndex: "confirmedLabel",
    key: "confirmed",
    sorter: true,
    type: "select",
    width: 120,
    editable: {
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Update confirmed status",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerManage",
  },
  {
    title: "Repricer discount value",
    dataIndex: "discount_value",
    key: "discount_value",
    sorter: true,
    type: "number",
    editable: {
      title: "Update discount value",
      type: "number",
      numberDecorator: (number) => ln(number, 2),
    },
    permission: "customerDiscount",
  },
  {
    title: "Repricer discount type",
    dataIndex: "discountTypeLabel",
    key: "discount_type",
    sorter: true,
    width: 110,
    type: "select",
    editable: {
      options: [
        { label: "Percent", value: "PERCENT" },
        { label: "Value", value: "VALUE" },
      ],
      title: "Update discount type",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerDiscount",
  },
  {
    title: "BA discount value",
    dataIndex: "bas_discount_value",
    key: "bas_discount_value",
    sorter: true,
    type: "number",
    editable: {
      title: "Update BA discount value",
      type: "number",
      numberDecorator: (number) => ln(number, 2),
    },
    permission: "customerDiscount",
  },
  {
    title: "BA discount type",
    dataIndex: "basDiscountTypeLabel",
    key: "bas_discount_type",
    sorter: true,
    width: 110,
    type: "select",
    editable: {
      options: [
        { label: "Percent", value: "PERCENT" },
        { label: "Value", value: "VALUE" },
      ],
      title: "Update BA discount type",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerDiscount",
  },
  {
    title: "Use payment",
    dataIndex: "usePlanLabel",
    key: "use_plan",
    sorter: true,
    type: "select",
    editable: {
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Update use payment status",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerPayment",
  },
  {
    title: "Use direct debit",
    dataIndex: "usePaymentDebitLabel",
    key: "use_payment_debit",
    sorter: true,
    type: "select",
    editable: {
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Update use direct debit status",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerPayment",
  },
  {
    title: "Use credit card",
    dataIndex: "usePaymentCcLabel",
    key: "use_payment_cc",
    sorter: true,
    width: 110,
    type: "select",
    editable: {
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Update credit card status",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerPayment",
  },
  {
    title: "DPA contract?",
    dataIndex: "hasDpaContractLabel",
    key: "has_dpa_contract",
    sorter: true,
    type: "select",
  },
  {
    title: "Orders anonymized?",
    dataIndex: "dpaOrderAnonymizedLabel",
    key: "dpa_order_anonymized",
    sorter: true,
    width: 126,
    type: "select",
    editable: {
      checkIfEditable: ({ dpa_order_anonymized }) => dpa_order_anonymized === 0,
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Update order anonymized status",
      type: "select",
      shouldTranslateOptions: true,
    },
    permission: "customerManage",
  },
  {
    title: "Number of active accounts",
    dataIndex: "countActiveAccounts",
    key: "countActiveAccounts",
    sorter: true,
    type: "input",
    isNumber: true,
  },
  {
    title: "Number of active marketplaces",
    dataIndex: "countActiveMarketplaces",
    key: "countActiveMarketplaces",
    sorter: true,
    type: "input",
    isNumber: true,
  },
  {
    title: "Repricer",
    dataIndex: "useRepricerModuleLabel",
    key: "use_repricer_module",
    sorter: true,
    type: "select",
  },
  {
    title: "Repricer B2C",
    dataIndex: "useRepricerB2CModuleLabel",
    key: "use_repricer_b2c_module",
    sorter: true,
    type: "select",
  },
  {
    title: "Repricer B2B",
    dataIndex: "useRepricerB2BModuleLabel",
    key: "use_repricer_b2b_module",
    sorter: true,
    type: "select",
  },
  {
    title: "Enforcement of Repricer V2 pricing change",
    dataIndex: "selectedForNewPricingLabel",
    key: "selected_for_new_pricing",
    sorter: true,
    type: "select",
    editable: {
      alert: {
        alertType: "warning",
        message: l("This change is irreversible."),
      },
      checkIfEditable: ({ selected_for_new_pricing }) =>
        selected_for_new_pricing === 0,
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Enforcement of Repricer V2 pricing change",
      type: "select",
      shouldTranslateOptions: true,
    },
  },
  {
    title: "Repricer V2 Pricing declined by customer",
    dataIndex: "declinedNewPricingLabel",
    key: "declined_new_pricing",
    sorter: true,
    type: "select",
    editable: {
      alert: {
        alertType: "warning",
        message: l("This change is irreversible."),
      },
      checkIfEditable: ({ declined_new_pricing }) => declined_new_pricing === 0,
      options: [
        { label: "Yes", value: 1 },
        { label: "No", value: 0 },
      ],
      title: "Repricer V2 Pricing declined by customer",
      type: "select",
      shouldTranslateOptions: true,
    },
  },
  {
    title: "Pricing model V2 unlocked",
    dataIndex: "useNewPricingLabel",
    key: "use_new_pricing",
    sorter: true,
    type: "select",
  },
  {
    title: "Lost & Found",
    dataIndex: "useLostModuleLabel",
    key: "use_lost_module",
    sorter: true,
    type: "select",
  },
  {
    title: "L&F Full-Service",
    dataIndex: "lostModuleSignedFullService",
    key: "lost_module_signed_full_service",
    sorter: true,
    type: "select",
    width: 120,
  },
  {
    title: "Business Analytics",
    dataIndex: "useBasModuleLabel",
    key: "use_bas_module",
    sorter: true,
    type: "select",
  },
  {
    title: "Lost & Found commission (%)",
    dataIndex: "lostRateLabel",
    key: "lost_rate",
    sorter: true,
    width: 110,
    type: "number",
    editable: {
      title: "Update Lost & Found commission",
      type: "number",
      numberDecorator: (number) => ln(number),
    },
    permission: "customerManage",
  },
  {
    title: "L&F Full-Service commission (%)",
    dataIndex: "lostRateFullService",
    key: "lost_rate_full_service",
    sorter: true,
    width: 110,
    type: "number",
    editable: {
      title: "Update L&F Full-Service commission",
      type: "number",
      numberDecorator: (number) => ln(number),
    },
    permission: "customerManage", // TODO: to clarify if this is needed
  },
  {
    title: "Created",
    dataIndex: "created",
    key: "created",
    sorter: true,
    width: 150,
  },
  {
    title: "Partner Id",
    dataIndex: "partner_title",
    key: "partner_title",
    sorter: true,
    width: 116,
    type: "input",
    render: (text, { partner, canViewPartnerList }) =>
      canViewPartnerList ? (
        <ExportValue>
          <Link
            internal={false}
            rel="noopener noreferrer"
            styleType="primary"
            target="_blank"
            text={text}
            variant="textSmall"
            url={`${ROUTES.ADMIN_ROUTES.PATH_PARTNERS}/?id=${
              partner && partner.id
            }`}
          />
        </ExportValue>
      ) : (
        text
      ),
  },
  {
    title: "Partner channel",
    dataIndex: "partnerChannelName",
    key: "partnerChannelName",
    sorter: true,
    width: 116,
    type: "input",
    render: (text, { partner_id, canViewPartnerList, partner_channel_id }) =>
      canViewPartnerList ? (
        <ExportValue>
          <Link
            internal={false}
            rel="noopener noreferrer"
            styleType="primary"
            target="_blank"
            text={text}
            url={`${ROUTES.ADMIN_ROUTES.PATH_PARTNERS_CHANNEL}/${partner_id}?id=${partner_channel_id}`}
            variant="textSmall"
          />
        </ExportValue>
      ) : (
        text
      ),
  },
  {
    title: "Partner channel ID",
    dataIndex: "partner_channel_id",
    key: "partner_channel_id",
    sorter: true,
    width: 116,
    type: "input",
    render: (value, { partner_id, canViewPartnerList, partner_channel_id }) =>
      canViewPartnerList && checkIsNumber(value) ? (
        <ExportValue>
          <Link
            internal={false}
            rel="noopener noreferrer"
            styleType="primary"
            target="_blank"
            text={value}
            url={`${ROUTES.ADMIN_ROUTES.PATH_PARTNERS_CHANNEL}/${partner_id}?id=${partner_channel_id}`}
            variant="textSmall"
          />
        </ExportValue>
      ) : (
        value
      ),
  },
]
