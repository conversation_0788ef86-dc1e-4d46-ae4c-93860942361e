import * as yup from "yup"

import { buildErrorMessagesMapper } from "utils/formHelpers/buildErrorMessagesMapper"

export const buildSchema = () => {
  const { required: requiredErrorMessage } = buildErrorMessagesMapper()

  return yup
    .object()
    .shape({
      title: yup.string().required(requiredErrorMessage),
      address: yup.string().required(requiredErrorMessage),
      address2: yup.string().nullable(),
      zip: yup.string().required(requiredErrorMessage),
      city: yup.string().required(requiredErrorMessage),
      country_id: yup.number().required(requiredErrorMessage),
      vat_id: yup.string().nullable(),
      currency_id: yup.number().nullable(),
      partner_id: yup.number().nullable(),
      partner_channel_id: yup.number().nullable(),
    })
    .required()
}
