import { useCallback, useMemo } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { yupResolver } from "@hookform/resolvers/yup"
import { FormItem } from "@develop/fe-library"
import { buildFormSubmitFailureCallback } from "@develop/fe-library/dist/utils"

import customerActions from "actions/customerActions"
import partnerChannelActions from "actions/partnerChannelActions"

import { countriesOptionsSelector } from "selectors/countriesSelectors"
import { currenciesSelector } from "selectors/currenciesSelectors"
import { customerInitialValuesSelector } from "selectors/customerSelectors"
import { partnerChannelsorSpecificPartnerSelector } from "selectors/partnerChannelSelectors"
import { partnersSelector } from "selectors/partnersSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import l from "utils/intl"

import { buildSchema } from "../../utils"

import type { Customer } from "types/Models/Customer"

export const useUpdateCustomerFormModal = ({
  updateCustomerSuccessCallback,
}) => {
  const dispatch = useDispatch()

  const countries = useSelector(countriesOptionsSelector)
  const currencies = useSelector(currenciesSelector)
  const partners = useSelector(partnersSelector)
  const channels = useSelector(partnerChannelsorSpecificPartnerSelector)

  const { partnerList } = useSelector(permissionsSelector)

  const initialValues = useSelector(customerInitialValuesSelector)

  const schema = buildSchema()

  const form = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      title: initialValues?.title || "",
      address: initialValues?.address || "",
      address2: initialValues?.address2 || "",
      zip: initialValues?.zip || "",
      city: initialValues?.city || "",
      country_id: initialValues?.country_id,
      vat_id: initialValues?.vat_id || "",
      currency_id: initialValues?.currency_id,
      partner_id: initialValues?.partner_id,
      partner_channel_id: initialValues?.partner_channel_id,
    },
  })

  const { handleSubmit: formHandleSubmit, setError, setValue } = form

  const handleChangePartnerId = useCallback(
    (id: number | string) => {
      const successCallback = () => {
        const defaultChannel = channels.find(
          // @ts-ignore
          (channel) => !!channel.is_default
        )
        let channelId = undefined

        if (defaultChannel) {
          channelId = defaultChannel.id
        } else if (channels.length) {
          const [firstChannel] = channels

          channelId = firstChannel.id
        }

        setValue("partner_channel_id", channelId)
      }

      // @ts-expect-error
      dispatch(partnerChannelActions.getForSpecificPartner(id, successCallback))
    },
    [channels, setValue]
  )

  const handleSubmit = useCallback(
    formHandleSubmit((editedFields) => {
      const failureCallback = buildFormSubmitFailureCallback({ setError })

      // Merge edited fields with original initialValues
      const payload: Customer = {
        ...initialValues,
        ...editedFields,
      }

      return dispatch(
        customerActions.update(
          payload,
          updateCustomerSuccessCallback,
          failureCallback
        )
      )
    }),
    [initialValues, updateCustomerSuccessCallback]
  )

  const items = useMemo((): Array<FormItem> => {
    const countriesOptions = countries.map(({ id, title }) => ({
      label: l(title),
      value: id,
    }))

    countriesOptions.sort((a, b) => a.label.localeCompare(b.label))

    const currenciesOptions = currencies.map(({ id, title }) => ({
      label: title,
      value: id,
    }))

    const partnersOptions = partners.map(({ id, title }) => ({
      label: title,
      value: id,
    }))

    const channelsOptions = channels.map(({ id, name }) => ({
      label: name,
      value: id,
    }))

    return [
      {
        name: "title",
        type: "text",
        inputProps: {
          label: l("Company name"),
          isRequired: true,
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "address",
        type: "text",
        inputProps: {
          label: l("Address 1"),
          isRequired: true,
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "address2",
        type: "text",
        inputProps: {
          label: l("Address 2"),
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "zip",
        type: "text",
        inputProps: {
          label: l("Postcode"),
          isRequired: true,
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "city",
        type: "text",
        inputProps: {
          label: l("City"),
          isRequired: true,
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "country_id",
        type: "select",
        inputProps: {
          label: l("Country"),
          isRequired: true,
          options: countriesOptions,
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "vat_id",
        type: "text",
        inputProps: {
          label: l("VAT ID"),
          placeholder: "DE815528383",
          suffixIcons: [
            {
              name: "icnInfoCircle",
              color: "--color-icon-active",
              content: l(
                "If no valid VAT-ID is entered, your invoice will contain the currently valid German VAT."
              ),
            },
          ],
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "currency_id",
        type: "select",
        inputProps: {
          label: l("Currency"),
          options: currenciesOptions,
        },
        gridItemProps: {
          mSM: 12,
        },
      },
      {
        name: "partner_id",
        type: "select",
        inputProps: {
          label: l("Partner ID"),
          options: partnersOptions,
          onChangeValue: handleChangePartnerId,
        },
        gridItemProps: {
          mSM: 12,
        },
        isVisible: partnerList,
      },
      {
        name: "partner_channel_id",
        type: "select",
        inputProps: {
          placeholder: l("Channel ID"),
          options: channelsOptions,
        },
        gridItemProps: {
          mSM: 12,
        },
        isVisible: partnerList,
      },
    ]
  }, [
    channels,
    countries,
    currencies,
    partners,
    partnerList,
    handleChangePartnerId,
  ])

  return { form, items, handleSubmit }
}
