import React from "react"
import { FormItems, Modal } from "@develop/fe-library"

import l from "utils/intl"

import { useUpdateCustomerFormModal } from "./hooks"

import type { Customer } from "types/Models/Customer"

import type { UpdateCustomerFormModalProps } from "./UpdateCustomerFormModalTypes"

export const UpdateCustomerFormModal = ({
  updateCustomerSuccessCallback,
  onClose,
}: UpdateCustomerFormModalProps) => {
  const { form, items, handleSubmit, handleClose } = useUpdateCustomerFormModal(
    {
      updateCustomerSuccessCallback,
      onClose,
    }
  )

  return (
    <Modal
      visible
      cancelButtonText={l("Cancel")}
      okButtonText={l("Save")}
      title={l("Update company")}
      onCancel={handleClose}
      onOk={handleSubmit}
    >
      <FormItems<Customer>
        // @ts-expect-error
        form={form}
        gridContainerProps={{ gap: "m" }}
        items={items}
      />
    </Modal>
  )
}
