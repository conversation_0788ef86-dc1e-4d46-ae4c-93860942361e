import { UseFormReturn } from "react-hook-form"

import { NewBannerData } from "types"

type UseNewBannerFOrmModalParams = {
  campaignId: number
  handleClose: () => void
  successCallback: (data: any) => void
  hasExistingBannersWithDefaultLanguage: boolean
  isExternalCampaign: boolean
}

type UseNewBannerFOrmModalReturn = {
  form: UseFormReturn<NewBannerData>
  isUseButtonEnabled: boolean
  languagesOptions: Array<{
    value: string
    label: string
  }>
  handleSubmit: () => void
  handleCancel: () => void
}

export type UseNewBannerFormModal = (
  params: UseNewBannerFOrmModalParams
) => UseNewBannerFOrmModalReturn
