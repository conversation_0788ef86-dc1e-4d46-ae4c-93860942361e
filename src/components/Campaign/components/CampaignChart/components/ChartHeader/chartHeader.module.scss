@import "assets/styles/variables.scss";

.header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  padding: 20px 20px 0 20px;
}

.title.title {
  display: flex;
  align-items: center;
  gap: 5px;
  color: $text_main;
  font-size: 16px;
  font-weight: 700;
  margin: 0;
}

.modalList {
  padding: 0;
  margin: 10px 0 0 0;
  list-style-position: inside;
}

.modalListItem {
  display: inline;
}

.datePickerContainer {
  align-items: center;
  display: flex;
  gap: var(--gap-m);

  :global(.date-picker-input) {
    min-width: 185px;
  }
}

.label.label {
  color: $text_second;
  font-size: 13px;
  margin-bottom: 0;
}

@media (max-width: $xs) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
