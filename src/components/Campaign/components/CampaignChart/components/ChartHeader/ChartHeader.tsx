import React, { useMemo, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>over, Typo<PERSON> } from "@develop/fe-library"
import moment, { Moment } from "moment"

import DatePicker from "components/shared/DatePicker"
import { withErrorField } from "components/shared/FormFields"

import setConfirm from "utils/confirm"
import { SERVER_DATE_FORMAT } from "utils/dateConverter"
import l from "utils/intl"

import { ChartHeaderProps, MomentDateRange } from "./ChartHeaderTypes"

import styles from "./chartHeader.module.scss"

const DatePickerWithError = withErrorField(DatePicker)

const DATE_PLACEHOLDER = "No date range is selected"

export const CampaignChartHeader = ({
  dateRange,
  isLoaded,
  campaignCreationDate,
  onDateChange,
  handleSaveScreenshot,
}: ChartHeaderProps) => {
  const [errorText, setErrorText] = useState("")

  const formattedDates = useMemo(() => {
    return dateRange[0] ? dateRange.map((date) => moment(date)) : [null, null]
  }, [dateRange])

  const handleDateChange = (dates: MomentDateRange) => {
    if (dates.includes(null)) {
      setErrorText(l(DATE_PLACEHOLDER))

      return
    }

    setErrorText("")
    onDateChange(
      moment(dates[0]).format(SERVER_DATE_FORMAT),
      moment(dates[1]).format(SERVER_DATE_FORMAT)
    )
  }
  const disabledDate = (current: Moment) => {
    return current.isBefore(campaignCreationDate) || current.isAfter(moment())
  }

  const handleDownloadClick = () => {
    handleSaveScreenshot(`campaign-statistics-${dateRange[0]}-${dateRange[1]}`)
  }

  const showInfoModal = () => {
    const modalProps: any = {
      title: l("Campaign statistics"),
      message: (
        <>
          <Typography variant="--font-body-text-9">
            {l(
              "Campaign statistics provide information on banners for a particular period. The graph shows data per day for a selected period in the date picker. The table provides the cumulative value of banners for a particular period. Please note that totals for the graph & table are calculated differently:"
            )}
          </Typography>

          <ul className={styles.modalList}>
            <li>
              <Typography
                className={styles.modalListItem}
                variant="--font-body-text-9"
              >
                {l(
                  "The graph total is calculated as a sum of all banners, selected for graph visualization via the checkbox, for a particular period"
                )}
              </Typography>
            </li>
            <li>
              <Typography
                className={styles.modalListItem}
                variant="--font-body-text-9"
              >
                {l(
                  "The table total is calculated as a sum of all banners, existing in the campaign, including deleted banners, for a particular period"
                )}
              </Typography>
            </li>
          </ul>
        </>
      ),
      okText: l("Ok"),
      cancelButton: false,
    }

    setConfirm(modalProps)
  }

  return (
    <header className={styles.header}>
      <Typography className={styles.title} variant="--font-headline-5">
        {l("Campaign statistics")}

        <Icon
          color="--color-text-link"
          name="icnInfoCircle"
          size="--icon-size-2"
          onClick={showInfoModal}
        />
      </Typography>

      <div className={styles.datePickerContainer}>
        <Typography
          className={styles.label}
          color="--color-text-second"
          variant="--font-body-text-7"
        >
          {l("Show statistics for")}
        </Typography>
        <div>
          <DatePickerWithError
            isFilter
            isRange
            customMinDate={campaignCreationDate}
            disabledDate={disabledDate}
            errorText={errorText || ""}
            filterType="between"
            placeholder={l(DATE_PLACEHOLDER)}
            value={formattedDates}
            onChange={handleDateChange}
          />
        </div>
        <Popover content={l("Statistics download")}>
          <Button
            data-html2canvas-ignore
            iconOnly
            disabled={!isLoaded}
            icon="icnDownload"
            variant="secondary"
            onClick={handleDownloadClick}
          />
        </Popover>
      </div>
    </header>
  )
}
