import React from "react"
import { <PERSON>, <PERSON>lipsis, IconPop<PERSON>, Typography } from "@develop/fe-library"
import { getObjectValues } from "@develop/fe-library/dist/utils"
import { store } from "App"

import actions from "actions/advertisingActions"

import { bannersStateSelector } from "selectors/campaignStatisticsSelector"

import {
  ChartUnits,
  MAX_ITEMS,
  TOTAL_KEY,
} from "components/Campaign/components/CampaignChart"

import { setConfirm } from "utils/confirm"
import l from "utils/intl"

import { TitleWithBadge, TitleWithCheckbox, TitleWithRadio } from "./components"

export const getTableColumns = () => [
  {
    title: l("Name"),
    dataIndex: "name",
    key: "name",
    width: 90,
    min: 90,
    render: (name, { id }) => {
      const isTotal = id === TOTAL_KEY
      const bannersState = bannersStateSelector(store.getState())
      const checkedItems = getObjectValues(bannersState).filter(
        (isChecked) => !!isChecked
      )
      const checkedAmount = checkedItems.length - 1

      const onChange = (event) => {
        const isChecked = event.target.checked

        if (checkedAmount >= MAX_ITEMS && isChecked) {
          setConfirm({
            title: l("Selection unavailable"),
            message: l(
              "Only 10 items can be seen on the graph at once. Please de-select an item, before selecting a new one"
            ),
            okText: l("Ok"),
          })

          return
        }

        store.dispatch(
          actions.toggleChartBanner({
            id,
            isChecked,
          })
        )
      }

      if (isTotal) {
        return (
          <Box align="center" gap="m">
            <Typography variant="--font-body-text-8">{name}</Typography>

            <IconPopover
              color="--color-icon-active"
              content={l("Totals include statistics from deleted banners.")}
              name="icnInfoCircle"
              size="--icon-size-2"
            />
          </Box>
        )
      }

      return (
        <TitleWithCheckbox isChecked={bannersState[id]} onChange={onChange}>
          <Ellipsis
            rows={3}
            popoverProps={{
              maxWidth: 300,
            }}
            typographyProps={{
              variant: "--font-body-text-9",
            }}
          >
            {name}
          </Ellipsis>
        </TitleWithCheckbox>
      )
    },
  },
  {
    title: l("Language"),
    dataIndex: "language",
    key: "language",
    width: 90,
    min: 90,
    render: (_, { language }) =>
      language === null ? l("Default (all languages)") : language.title,
  },
  {
    dataIndex: "clicks",
    key: "clicks",
    width: 70,
    min: 70,
    title: () => {
      const isChecked =
        store.getState()?.partnerBanner.activeChartUnit === ChartUnits.Clicks
      const onChange = () => {
        store.dispatch(actions.toggleChartUnit(ChartUnits.Clicks))
      }

      return (
        <TitleWithRadio
          isChecked={isChecked}
          title={l("Clicks")}
          onChange={onChange}
        />
      )
    },
    render: (_, { clicks, color }) => {
      return <TitleWithBadge color={color} title={clicks} />
    },
  },
  {
    dataIndex: "views",
    key: "views",
    width: 80,
    min: 80,
    title: () => {
      const isChecked =
        store.getState()?.partnerBanner.activeChartUnit === ChartUnits.Views
      const onChange = () => {
        store.dispatch(actions.toggleChartUnit(ChartUnits.Views))
      }

      return (
        <TitleWithRadio
          isChecked={isChecked}
          title={l("Impressions")}
          onChange={onChange}
        />
      )
    },
    render: (_, { views, color }) => {
      return <TitleWithBadge color={color} title={views} />
    },
  },
  {
    title: () => {
      const isChecked =
        store.getState()?.partnerBanner.activeChartUnit === ChartUnits.Ctr
      const onChange = () => {
        store.dispatch(actions.toggleChartUnit(ChartUnits.Ctr))
      }

      return (
        <TitleWithRadio
          isChecked={isChecked}
          title={l("Click-through rate")}
          onChange={onChange}
        />
      )
    },
    dataIndex: "ctr",
    key: "ctr",
    width: 80,
    min: 80,
    render: (_, { views, clicks }) => {
      if (!Number(clicks)) {
        return 0
      }
      const ctr = ((clicks / views) * 100).toFixed(2)

      return `${ctr} %`
    },
  },
]
