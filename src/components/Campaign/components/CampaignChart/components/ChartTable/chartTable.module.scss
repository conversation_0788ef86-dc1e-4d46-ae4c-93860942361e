@import "assets/styles/variables.scss";

.wrapper {
  :global {
    .ant-table-wrapper {
      min-width: 700px;
    }

    .ant-table-thead {
      & > tr > th {
        font-size: 12px;
        line-height: 1.5;
        padding-top: 7.5px;
        padding-bottom: 7.5px;
        font-weight: normal;
        color: $text_second;
        border-bottom: 1px solid $border_main;
        background: none;
      }

      & > tr:last-child {
        & > td {
          border-bottom: none;
        }

        & > td:first-child {
          border-bottom-left-radius: 5px;
        }
      }
    }

    .ant-table-tbody {
      & > tr > td {
        font-size: 12px;
        padding-top: 14.5px;
        padding-bottom: 14.5px;
        line-height: 1.5;
        border-bottom: 1px solid $border_main;
      }

      & > tr:first-child > td {
        font-weight: bold;
      }

      & > tr:last-child > td {
        border-bottom: none;
      }

      & .ant-table-row:nth-child(2n + 1) {
        background-color: transparent;
      }
    }
  }
}
