import React from "react"
import { Empty, Table } from "antd"

import l from "utils/intl"

import { getTableColumns } from "./chartTableColumns"

import { Banner } from "components/Campaign/components/CampaignChart/CampaignChartTypes"

import { ChartTableProps } from "./ChartTableTypes"

import styles from "./chartTable.module.scss"

export const ChartTable = ({ isLoaded, banners }: ChartTableProps) => {
  const columns: Array<{}> = getTableColumns()

  const getRowClassName = (record: Banner, index: number): string => {
    return index !== 0 && !record?.isChecked ? "html2canvasIgnore" : ""
  }

  return (
    <div className={styles.wrapper}>
      <Table
        columns={columns}
        dataSource={!isLoaded ? [] : banners}
        loading={!isLoaded}
        pagination={false}
        rowClassName={getRowClassName}
        tableLayout="fixed"
        locale={{
          emptyText: (
            <Empty
              description={l("No data")}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ),
        }}
      />
    </div>
  )
}
