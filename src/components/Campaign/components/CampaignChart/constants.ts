export const YAXIS_WIDTHS: { [key: number]: number } = {
  1: 7.3,
  2: 14.61,
  3: 21.91,
  4: 29.21,
  5: 36.5,
  6: 40.43,
  7: 47.17,
  8: 53.91,
  9: 60.66,
  10: 67.39,
}

export enum ChartUnits {
  Views = "views",
  Clicks = "clicks",
  Ctr = "rate",
}

export const MAX_ITEMS = 10

export const PERCENTAGE_GAP = 10
export const CHART_YAXIS_GAP = 30

export const TOTAL_KEY = "summary"
export const CARTESIAN_COLOR = "#F6F8F9"
