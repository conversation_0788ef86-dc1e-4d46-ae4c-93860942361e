import React from "react"
import { Box, Button, Icon, Popover, Typography } from "@develop/fe-library"
import moment from "moment"

import { Content } from "components/Layout"
import AppHeader from "components/shared/appHeader/AppHeader"
import SimpleFooterView from "components/SimpleFooter"

import { SERVER_DATE_FORMAT } from "utils/dateConverter"
import l from "utils/intl"

import { CAMPAIGN_TYPES } from "consts"

import {
  BannerDetailsForm,
  CampaignForm,
  CampaignStatistics,
  NewBannerFormModal,
} from "./components"

import { useCampaign } from "./hooks"

export const Campaign = ({ history }: any) => {
  const {
    campaignInfo,
    dateStart,
    dateEnd,
    open,
    handleClose,
    handleOpen,
    onDateChange,
    editCampaignInfoSuccessCallback,
    handleBuildToggleAllBanners,
  } = useCampaign()

  const campaignId = campaignInfo?.id
  const isExternalCampaign =
    campaignInfo?.advertising_type === CAMPAIGN_TYPES.EXTERNAL
  const hasCampaign = Boolean(campaignId)
  const hasBanners = campaignInfo.banners && campaignInfo.banners.length > 0
  const campaignCreationDate = moment(
    campaignInfo?.created_at,
    SERVER_DATE_FORMAT
  )

  const hasExistingBannersWithDefaultLanguage = hasBanners
    ? campaignInfo.banners.some((item) => item.language_id === null)
    : false

  const handleGoBack = () => {
    history.goBack()
  }

  return (
    <>
      <AppHeader title="Edit campaign" />

      <Box gap="l" padding="m l">
        <Icon name="icnChevronLeft" onClick={handleGoBack} />
        <Typography variant="--font-headline-3">
          {l("Edit campaign")}
        </Typography>
      </Box>

      <Content>
        {hasCampaign ? (
          <Box
            dMD={{
              flexDirection: "row",
            }}
            mSM={{
              flexDirection: "column-reverse",
              gap: "m",
              paddingLeft: "m",
              paddingRight: "m",
              paddingBottom: "m",
            }}
            tb={{
              gap: "l",
              paddingLeft: "l",
              paddingRight: "l",
              paddingBottom: "l",
            }}
          >
            <Box
              dMD={{ width: "50%" }}
              flexDirection="column"
              mSM={{ width: "100%", gap: "m" }}
              tb={{ gap: "l" }}
            >
              <CampaignForm successCallback={editCampaignInfoSuccessCallback} />
              <Box
                mSM={{ flexDirection: "column", gap: "m" }}
                tb={{ flexDirection: "row", justify: "space-between" }}
                width="100%"
              >
                <Typography variant="--font-headline-3">
                  {l("Banner settings")}
                </Typography>
                <Box
                  gap="m"
                  mSM={{ flexDirection: "column" }}
                  tb={{ flexDirection: "row" }}
                >
                  <Button
                    variant="secondary"
                    onClick={handleBuildToggleAllBanners(true)}
                  >
                    {l("Enable all banners")}
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={handleBuildToggleAllBanners(false)}
                  >
                    {l("Disable all banners")}
                  </Button>
                  <Box mSM={{ display: "none" }} tb={{ display: "block" }}>
                    <Popover content={l("Add new banner")} placement="topRight">
                      <Button iconOnly icon="icnPlus" onClick={handleOpen} />
                    </Popover>
                  </Box>
                  <Box mSM={{ display: "block" }} tb={{ display: "none" }}>
                    <Button fullWidth icon="icnPlus" onClick={handleOpen}>
                      Add new
                    </Button>
                  </Box>
                </Box>
              </Box>

              {hasBanners
                ? campaignInfo.banners.map((banner) => {
                    const hasOtherBannersWithDefaultLanguage =
                      campaignInfo.banners.some(
                        (item) =>
                          item.language_id === null && item.id !== banner.id
                      )

                    return (
                      <BannerDetailsForm
                        banner={banner}
                        isExternalCampaign={isExternalCampaign}
                        successCallback={editCampaignInfoSuccessCallback}
                        hasOtherBannersWithDefaultLanguage={
                          hasOtherBannersWithDefaultLanguage
                        }
                      />
                    )
                  })
                : null}
            </Box>

            <Box
              dMD={{ width: "50%" }}
              height="min-content"
              mSM={{ width: "100%" }}
            >
              <CampaignStatistics
                campaignCreationDate={campaignCreationDate}
                dates={[dateStart, dateEnd]}
                onDateChange={onDateChange}
              />
            </Box>
          </Box>
        ) : null}
      </Content>

      <SimpleFooterView />

      <NewBannerFormModal
        campaignId={campaignId}
        handleClose={handleClose}
        isExternalCampaign={isExternalCampaign}
        successCallback={editCampaignInfoSuccessCallback}
        visible={open}
        hasExistingBannersWithDefaultLanguage={
          hasExistingBannersWithDefaultLanguage
        }
      />
    </>
  )
}
