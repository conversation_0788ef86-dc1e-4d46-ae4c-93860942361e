import React from "react"
import { Alert, Box, Typography } from "@develop/fe-library"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { getBillingFrequencyMessage } from "../PlanCard/utils"

import type { ConfirmationMessageProps } from "./ConfirmationMessageTypes"

export const ConfirmationMessage = ({
  localizedName,
  size,
  price,
  optimizationPrice,
  pricePerUnit,
  isFreemiumPlan,
  isYearlyPlan,
}: ConfirmationMessageProps) => {
  const yearlyBilledPrice: number = price * 12

  return (
    <Box flexDirection="column" gap="m" tb={{ gap: "l" }}>
      <Alert
        alertType="info"
        message={
          <Typography
            color="--color-text-main"
            component="span"
            variant="--font-body-text-7"
          >
            {l(
              "Please review the selected subscription setting. They will take effect right after the confirmation."
            )}
          </Typography>
        }
      />

      <Box
        backgroundColor="--color-row-select"
        flexDirection="column"
        gap="m"
        padding="m"
        tb={{
          padding: "l",
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "l",
        }}
      >
        <Box flexDirection="column" gap="m">
          <Box display="block">
            <Typography color="--color-text-main" variant="--font-body-text-9">
              {l("Subscription plan")}:
            </Typography>
            <Typography color="--color-text-main" variant="--font-headline-1">
              {localizedName}
            </Typography>
          </Box>

          <Box display="block">
            <Typography color="--color-text-main" variant="--font-body-text-9">
              {isFreemiumPlan
                ? l("Optimized products")
                : l("Average daily Product Optimization amount")}
              :
            </Typography>
            <Typography color="--color-text-main" variant="--font-headline-3">
              {ln(size)}
            </Typography>
          </Box>
        </Box>

        <Box flexDirection="column" gap="m">
          <Box display="block">
            <Typography color="--color-text-main" variant="--font-body-text-9">
              {l("Price")}:
            </Typography>

            <Box align="center" columnGap="s" flexWrap="wrap">
              <Typography
                color="--color-text-main"
                variant="--font-headline-1"
                whiteSpace="nowrap"
              >
                {ln(price, 2, { currency: "EUR" })}
              </Typography>
              <Typography
                color="--color-text-main"
                variant="--font-body-text-9"
              >
                {getBillingFrequencyMessage({
                  isFreemiumPlan,
                  isYearlyPlan,
                  yearlyBilledPrice,
                })}
              </Typography>
            </Box>
          </Box>

          <Box flexDirection="column" gap="s">
            <Box flexWrap="wrap" gap="s">
              <Typography
                color="--color-text-main"
                variant="--font-body-text-8"
              >
                {ln(optimizationPrice, undefined, {
                  currency: "EUR",
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                })}
              </Typography>
              <Typography
                color="--color-text-second"
                variant="--font-body-text-9"
              >
                / {l("Product Optimization")}
              </Typography>
            </Box>

            <Box flexWrap="wrap" gap="s">
              <Typography
                color="--color-text-main"
                variant="--font-body-text-8"
              >
                {ln(pricePerUnit, undefined, {
                  currency: "EUR",
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 5,
                })}
              </Typography>
              <Typography
                color="--color-text-second"
                variant="--font-body-text-9"
              >
                / {l("On-Demand Product Optimization")}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}
