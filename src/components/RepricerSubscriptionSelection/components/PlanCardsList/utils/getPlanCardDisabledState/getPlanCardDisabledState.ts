import l from "utils/intl"

import {
  DEFAULT_FREEMIUM_SIZE,
  SUBSCRIPTION_IDS,
} from "consts/repricerSubscription"

import type { GetPlanCardDisabledState } from "./GetPlanCardDisabledStateTypes"

export const getPlanCardDisabledState: GetPlanCardDisabledState = ({
  currentDuration,
  currentLevel,
  currentSize,
  isDowngradePlan,
  isUpgradePlan,
  isYearlyPlan,
  level,
  maxSkuOptimizations,
  paymentModuleVersionId,
  size,
}) => {
  const maxFreemiumOptimizations: number =
    maxSkuOptimizations || DEFAULT_FREEMIUM_SIZE

  const isFreemiumPlan: boolean =
    paymentModuleVersionId === SUBSCRIPTION_IDS.FREEMIUM

  const isSamePlan: boolean = level === currentLevel

  const isFreemiumPlanCardDisabled: boolean =
    isFreemiumPlan && (isSamePlan || size > maxFreemiumOptimizations)

  const isSamePlanAndSize: boolean =
    isSamePlan && !!currentSize && size === currentSize

  const isSamePlanWithSmallerSize: boolean =
    isSamePlan && !!currentSize && size < currentSize

  const isSamePlanWithBiggerSize: boolean =
    isSamePlan && !!currentSize && size > currentSize

  const isDurationChangedToYearly: boolean =
    currentDuration === 1 && !!isYearlyPlan

  const isSamePlanAndSizeWithUnchangedMonthlyDuration: boolean =
    isSamePlanAndSize && !isDurationChangedToYearly

  const isDurationChangedToMonthly: boolean =
    currentDuration === 12 && !isYearlyPlan

  const isSamePlanAndSizeWithUnchangedYearlyDuration: boolean =
    isSamePlanAndSize && !isDurationChangedToMonthly

  const isSmallerPlan: boolean = !!currentLevel && level < currentLevel

  const isSmallerPlanWithBiggerSize: boolean =
    isSmallerPlan && !!currentSize && size > currentSize

  const isBiggerPlan: boolean = !!currentLevel && level > currentLevel

  const isBiggerPlanWithSmallerSize: boolean =
    isBiggerPlan && !!currentSize && size < currentSize

  const isUpgradePlanModalCardDisabled: boolean =
    isUpgradePlan &&
    (isSamePlanWithSmallerSize ||
      isSamePlanAndSizeWithUnchangedMonthlyDuration ||
      isSmallerPlan ||
      isBiggerPlanWithSmallerSize)

  const isDowngradePlanModalCardDisabled: boolean =
    isDowngradePlan &&
    (isBiggerPlan ||
      isSamePlanWithBiggerSize ||
      isSamePlanAndSizeWithUnchangedYearlyDuration ||
      isSmallerPlanWithBiggerSize)

  /** Plan card disabled when:
   * 1. Freemium plan card. Selected size is bigger than 20
   * 2. Upgrade plan modal and:
   *    2.1. Smaller plan card
   *    2.2. Same plan card. Selected size smaller than the current one
   *    2.3. Same plan card, same size, and duration remained monthly (wasn't changed to yearly)
   *    2.4. Bigger plan card. Selected size smaller than the current one
   * 3. Downgrade plan modal and:
   *    2.1. Bigger plan card
   *    2.2. Same plan card. Selected size bigger than the current one
   *    2.3. Same plan card, same size, and duration remained yearly (wasn't changed to monthly)
   *    2.4. Smaller plan card. Selected size bigger than the current one
   */
  const isDisabled: boolean =
    isFreemiumPlanCardDisabled ||
    isUpgradePlanModalCardDisabled ||
    isDowngradePlanModalCardDisabled

  const samePlanAndSizeMessage: string | undefined = isSamePlanAndSize
    ? l(
        "This plan settings cannot be selected, as this plan is already enabled."
      )
    : undefined

  const smallerPlanMessage: string | undefined = isSmallerPlan
    ? l(
        "Subscription cannot be downgraded during an upgrade. You may select another subscription in “Next subscription” settings."
      )
    : undefined

  const biggerPlanMessage: string | undefined = isBiggerPlan
    ? l("Subscription cannot be upgraded during the downgrade.")
    : undefined

  const freemiumPlanMessage: string | undefined = isFreemiumPlanCardDisabled
    ? l(
        "Freemium subscription supports only {number} Product Optimizations. Please lower the daily Product Optimization slider to select this subscription.",
        { number: maxFreemiumOptimizations }
      )
    : undefined

  const upgradePlanDisabledPopoverMessage: string | undefined = isUpgradePlan
    ? samePlanAndSizeMessage || smallerPlanMessage
    : undefined

  const downgradePlanDisabledPopoverMessage: string | undefined =
    isDowngradePlan ? samePlanAndSizeMessage || biggerPlanMessage : undefined

  const buttonPopoverMessage: string | undefined = isDisabled
    ? freemiumPlanMessage ||
      upgradePlanDisabledPopoverMessage ||
      downgradePlanDisabledPopoverMessage
    : undefined

  return { buttonPopoverMessage, isDisabled }
}
