import React from "react"
import { Box } from "@develop/fe-library"

import l from "utils/intl"

import {
  DEFAULT_FREEMIUM_SIZE,
  SUBSCRIPTION_IDS,
} from "consts/repricerSubscription"

import { usePlanCardsList } from "./hooks"

import { getPlanCardDisabledState } from "./utils"

import { PLAN_CARD_HEADER_CLASSNAME } from "./constants"

import { PlanCard } from "../PlanCard"

import type { PlanCardsListProps } from "./PlanCardsListTypes"

export const PlanCardsList = ({
  currentDuration,
  currentLevel,
  currentSize,
  isDowngradePlan,
  isUpgradePlan,
  isYearlyPlan,
  size,
  subscriptions,
  buildPlanButtonClickHandler,
}: PlanCardsListProps) => {
  const { hasData } = usePlanCardsList({ size, subscriptions })

  if (!hasData) {
    return null
  }

  return (
    <Box
      flexDirection="column"
      gap="m"
      marginTop="xl"
      dSM={{
        display: "grid",
        gridTemplateColumns: `repeat(${subscriptions.length}, 1fr)`,
      }}
    >
      {subscriptions.map((subscription) => {
        const {
          paymentModuleVersionId,
          maxSkuOptimizations,
          level,
          subscriptionMatrix,
        } = subscription

        const isAdvancedSubscription: boolean =
          paymentModuleVersionId === SUBSCRIPTION_IDS.ADVANCED

        const isFreemiumSubscription: boolean =
          paymentModuleVersionId === SUBSCRIPTION_IDS.FREEMIUM

        const processedCurrentSize: number | undefined = isFreemiumSubscription
          ? DEFAULT_FREEMIUM_SIZE
          : currentSize

        const minAllowedSize: number = subscriptionMatrix[0]?.size || 0

        const paidSubscriptionSize: number =
          size < minAllowedSize ? minAllowedSize : size

        const processedSize: number = isFreemiumSubscription
          ? DEFAULT_FREEMIUM_SIZE
          : paidSubscriptionSize

        const { buttonPopoverMessage, isDisabled } = getPlanCardDisabledState({
          currentDuration,
          currentLevel,
          currentSize: processedCurrentSize,
          isDowngradePlan,
          isUpgradePlan,
          isYearlyPlan,
          level,
          maxSkuOptimizations,
          paymentModuleVersionId,
          size: processedSize,
        })

        return (
          <PlanCard
            key={paymentModuleVersionId}
            badgeText={isAdvancedSubscription ? l("Best value") : undefined}
            buttonPopoverMessage={buttonPopoverMessage}
            headerClassName={PLAN_CARD_HEADER_CLASSNAME}
            isDisabled={isDisabled}
            isUpgradePlan={isUpgradePlan}
            isYearlyPlan={isYearlyPlan}
            size={processedSize}
            subscription={subscription}
            onClickPlanButton={buildPlanButtonClickHandler(subscription)}
          />
        )
      })}
    </Box>
  )
}
