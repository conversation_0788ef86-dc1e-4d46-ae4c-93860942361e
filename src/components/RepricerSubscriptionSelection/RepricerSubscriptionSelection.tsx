import React, { ReactNode } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Icon<PERSON><PERSON><PERSON>,
  <PERSON>over,
  Slider,
  Switch,
  Typography,
} from "@develop/fe-library"
import { checkIsArray } from "@develop/fe-library/dist/utils"

import Link from "components/shared/Link"

import l from "utils/intl"
import ln from "utils/localeNumber"

import { PlanCardsList, SupportLink } from "./components"

import {
  useRepricerSubscriptionSelection,
  useRepricerSubscriptionSelectionSlider,
} from "./hooks"

import type { RepricerSubscriptionSelectionProps } from "./RepricerSubscriptionSelectionTypes"

/**
 * Component for selecting a repricer subscription plan.
 *
 * @param props.isSetPlan - Selects `Set Plan` view.
 * @param props.isSetNextPlan - Selects `Set Next Plan` view. Should be used with `size` prop.
 * @param props.isUpgradePlan - Selects `Upgrade Plan` view. Should be used with `size`, `level`, `duration` props.
 * @param props.isDowngradePlan - Selects `Downgrade Plan` view. Should be used with `size`, `level`, `duration` props.
 * @param props.size - The size of the current plan.
 * @param props.level - The level of the current plan.
 * @param props.duration - The duration of the current plan.
 * @param props.onSuccess - Callback function to be called on success.
 *
 * @returns  The rendered component.
 */
export const RepricerSubscriptionSelection = ({
  isUpgradePlan,
  isDowngradePlan,
  isSetNextPlan,
  isSetPlan,
  size: currentSize,
  level: currentLevel,
  duration: currentDuration,
  onSuccess,
}: RepricerSubscriptionSelectionProps) => {
  const {
    contactUsLink,
    disabledSwitchPopoverMessage,
    fullComparisonLink,
    isSwitchDisabled,
    isYearlyPlan,
    overviewLink,
    subscriptions,
    buildPlanButtonClickHandler,
    handleChangePlanDurationSwitch,
  } = useRepricerSubscriptionSelection({
    isSetNextPlan,
    isSetPlan,
    isUpgradePlan,
    isDowngradePlan,
    currentDuration,
    onSuccess,
  })

  const {
    customSliderMarks,
    defaultSliderValue,
    size,
    sizes,
    sliderValue,
    sliderWrapperRef,
    handleSliderChange,
    renderSliderHeader,
    renderSliderLabel,
    renderSliderValuePopover,
  } = useRepricerSubscriptionSelectionSlider({
    isUpgradePlan,
    isDowngradePlan,
    currentSize,
  })

  return (
    <Box display="block" dSM={{ padding: "l" }} padding="m">
      <Box
        align="center"
        flexDirection="column-reverse"
        gap="l"
        dSM={{
          flexDirection: "row",
          justify: "space-between",
        }}
      >
        <Box align="center" gap="m">
          <Typography variant="--font-body-text-4">
            {l("Number of products to optimize")}:
          </Typography>
          <Typography variant="--font-body-text-2">{ln(size)}</Typography>
          <IconPopover
            color="--color-icon-active"
            maxWidth={350}
            name="icnInfoCircle"
            size="--icon-size-3"
            content={l(
              'Product Optimization refers to the process where the price of a product offer (SKU) is optimized no matter how often the price is changed at least once per day, as long as the product remains in stock. Products that are either out of stock or have the "Optimization active" toggle disabled are excluded from the Product Optimization count. It is important to note that "Optimization active" does not necessarily result in a price change.'
            )}
          />
        </Box>

        <Box align="center" gap="m" minHeight={32}>
          <Typography variant="--font-body-text-7">
            {isYearlyPlan ? l("Annually billing") : l("Monthly billing")}
          </Typography>
          {isYearlyPlan ? (
            <Badge
              color="--color-badge-5"
              content={l("Get {months} months free", { months: 2 })}
            />
          ) : null}
          <Popover content={disabledSwitchPopoverMessage}>
            <Switch
              isChecked={isYearlyPlan}
              isDisabled={isSwitchDisabled}
              onChange={handleChangePlanDurationSwitch}
            />
          </Popover>
        </Box>
      </Box>

      <Box ref={sliderWrapperRef} display="block" marginTop="l" width="100%">
        {checkIsArray(sizes) ? (
          <>
            {sizes.length <= 1 ? (
              <Alert
                alertType="info"
                message={l(
                  "The maximum amount of products to optimize is selected for the current subscription. If a larger volume is required, please <a>contact support</a>.",
                  {
                    a: (chunks: ReactNode) => (
                      // @ts-expect-error
                      <Link
                        internal={false}
                        styleType="primary"
                        text={chunks}
                        type="span"
                        url={contactUsLink}
                        variant="text"
                      />
                    ),
                  }
                )}
              />
            ) : (
              <Slider
                hasLargeMarkLabels
                customMarks={customSliderMarks}
                defaultValue={defaultSliderValue}
                largeMarkLabelsSide="bottom"
                max={sizes.length - 1}
                min={0}
                renderHeader={renderSliderHeader}
                renderLabel={renderSliderLabel}
                renderValuePopover={renderSliderValuePopover}
                size="xs"
                value={sliderValue}
                minEnabledPopoverProps={{
                  content: l(
                    "Product Optimization amount cannot be lower than in the current subscription."
                  ),
                }}
                onChange={handleSliderChange}
              />
            )}
          </>
        ) : null}
      </Box>

      <PlanCardsList
        buildPlanButtonClickHandler={buildPlanButtonClickHandler}
        currentDuration={currentDuration}
        currentLevel={currentLevel}
        currentSize={currentSize}
        isDowngradePlan={!!isDowngradePlan}
        isUpgradePlan={!!isUpgradePlan}
        isYearlyPlan={isYearlyPlan}
        size={size}
        subscriptions={subscriptions}
      />

      <Box
        flexDirection="column"
        gap="m"
        marginTop="m"
        dSM={{
          marginTop: "l",
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",
        }}
      >
        <SupportLink
          href={overviewLink}
          iconName="icnCalculator"
          text={l("Learn more about price calculation")}
        />

        <SupportLink
          href={fullComparisonLink}
          iconName="icnFileSearch"
          text={l("Full comparison")}
        />
      </Box>
    </Box>
  )
}
