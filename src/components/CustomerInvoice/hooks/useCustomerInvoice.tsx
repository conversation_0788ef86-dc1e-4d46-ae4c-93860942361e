import { useDispatch, useSelector } from "react-redux"
import { ROUTES } from "@develop/fe-library/dist/routes"

import customerInvoiceActions from "actions/customerInvoiceActions"
import gridActions from "actions/gridActions"

import {
  customerInvoiceChargeCreditCardsInvoicesConfirmationModalVisibleSelector,
  customerInvoiceGenerateNewInvoiceModalVisibleSelector,
  customerInvoiceItemsSelector,
  customerInvoicePublishInvoicesConfirmationModalVisibleSelector,
  customerInvoiceSearchOptionsSelector,
  customerInvoiceTotalCountSelector,
  filtersOptionsSelector,
} from "selectors/customerInvoiceSelectors"
import { permissionsSelector } from "selectors/userSelectors"

const {
  //@ts-ignore
  delete: deleteCustomerInvoice,
  //@ts-ignore
  get: getCustomerInvoicesData,
  //@ts-ignore
  displayGenerateNewInvoiceModal,
  //@ts-ignore
  displayPublishInvoicesConfirmationModal,
  //@ts-ignore
  displayChargeCreditCardsInvoicesConfirmationModal,
  //@ts-ignore
  generate: generateNewInvoicesData,
  //@ts-ignore
  publish: publishInvoicesData,
  //@ts-ignore
  charge: chargeCreditCardsData,
  //@ts-ignore
  getPreviewFile: getPreview,
  //@ts-ignore
  getSepaXmlFile: getSepaXml,
  //@ts-ignore
  update,
  //@ts-ignore
  updateInvoice,
  //@ts-ignore
} = customerInvoiceActions
// @ts-ignore
const { pushUrl } = gridActions

export const useCustomerInvoice = () => {
  const { customerInvoiceManage, customerInvoiceList } =
    useSelector(permissionsSelector)
  const dataSource = useSelector(customerInvoiceItemsSelector)

  const searchOptions = useSelector(customerInvoiceSearchOptionsSelector)
  const totalCount = useSelector(customerInvoiceTotalCountSelector)
  const generateNewInvoiceModalVisible = useSelector(
    customerInvoiceGenerateNewInvoiceModalVisibleSelector
  )
  const publishInvoicesConfirmationModalVisible = useSelector(
    customerInvoicePublishInvoicesConfirmationModalVisibleSelector
  )
  const chargeCreditCardsInvoicesConfirmationModalVisible = useSelector(
    customerInvoiceChargeCreditCardsInvoicesConfirmationModalVisibleSelector
  )
  const selectFiltersOptions = useSelector(filtersOptionsSelector)

  const dispatch = useDispatch()

  const getCustomerInvoices = <Type,>(searchOptions: Type): void => {
    dispatch(getCustomerInvoicesData(searchOptions, false))
  }

  const toggleGenerateNewInvoiceModal = <Type,>(
    visible: boolean,
    initialValues: Type
  ): void => {
    dispatch(displayGenerateNewInvoiceModal(visible, initialValues))
  }

  const togglePublishInvoicesModal = <Type,>(
    visible: boolean,
    initialValues: Type
  ): void => {
    dispatch(displayPublishInvoicesConfirmationModal(visible, initialValues))
  }

  const toggleChargeCreditCardsModal = <Type,>(
    visible: boolean,
    initialValues: Type
  ): void => {
    dispatch(
      displayChargeCreditCardsInvoicesConfirmationModal(visible, initialValues)
    )
  }

  const publishInvoices = <Type,>(
    payload: Type,
    callback?: () => void,
    failureCallback?: () => void
  ): void => {
    dispatch(publishInvoicesData(payload, callback, failureCallback))
  }

  const chargeCreditCards = <Type,>(
    payload?: Type,
    callback?: () => void,
    failureCallback?: () => void
  ): void => {
    dispatch(chargeCreditCardsData(payload, callback, failureCallback))
  }

  const onChargeCreditCard = ({ id }: { id: number }) => {
    const payload = { invoiceIds: [id] }

    chargeCreditCards(payload)
  }

  const reloadCustomerInvoices = (): void => {
    dispatch(pushUrl(searchOptions))
  }

  const generateInvoices = <Type,>(
    payload: Type,
    callback?: () => void,
    failureCallback?: (error: any) => void
  ): void => {
    dispatch(generateNewInvoicesData(payload, callback, failureCallback))
  }

  const generateNewInvoiceModalSubmitHandler = <Type,>(
    payload: Type,
    failureCallback?: () => void
  ) => {
    generateInvoices(
      payload,
      () => {
        toggleGenerateNewInvoiceModal(false, undefined)
        reloadCustomerInvoices()
      },
      // @ts-ignore
      (error: any) => failureCallback(error.length ? error : [])
    )
  }

  const saveCellHandler = <Type,>(
    payload: Type,
    failureCallback?: () => void,
    successCallback?: () => void
  ) => {
    // @ts-ignore
    const updateAction = payload?.payment_method ? updateInvoice : update

    return dispatch(
      updateAction(
        payload,
        () => {
          successCallback && successCallback()
          getCustomerInvoices(searchOptions)
        },
        failureCallback
      )
    )
  }

  const deleteItem = (id: number | string, callback?: () => void): void => {
    dispatch(deleteCustomerInvoice(id, callback))
  }

  const downloadFileIconHandler = ({ s3_url }: { s3_url?: string | URL }) => {
    return window.open(s3_url, "_blank")?.focus()
  }

  const downloadSepaHandler = (id: number | string, version: any): void => {
    dispatch(getSepaXml(id, version))
  }

  const deleteIconHandler = ({ id }: { id: number | string }) => {
    deleteItem(id, () => getCustomerInvoices(searchOptions))
  }

  const openFile = (fileUrl?: string | URL) => {
    return window.open(fileUrl, "_blank")
  }

  const previewInvoice = <Type,>(
    invoiceKey: Type,
    callback?: () => void,
    failureCallback?: () => void
  ): void => {
    dispatch(getPreview(invoiceKey, callback, failureCallback))
  }

  const previewIconHandler = <Type,>({
    invoice_key,
  }: {
    invoice_key: Type
  }): void => {
    previewInvoice(invoice_key, openFile)
  }

  const chargeCreditCardsInvoicesConfirmationModalCloseHandler = () => {
    return toggleChargeCreditCardsModal(false, undefined)
  }

  const chargeCreditCardsInvoicesConfirmationModalOkHandler = () => {
    chargeCreditCards()
    toggleChargeCreditCardsModal(false, undefined)
  }

  const publishInvoicesConfirmationModalCloseHandler = () => {
    return togglePublishInvoicesModal(false, undefined)
  }

  const publishInvoicesConfirmationModalOkHandler = () => {
    publishInvoices(undefined)
    togglePublishInvoicesModal(false, undefined)
  }

  const generateNewInvoiceModalCloseHandler = () => {
    return toggleGenerateNewInvoiceModal(false, undefined)
  }
  const iconPlusHandler = () => toggleGenerateNewInvoiceModal(true, {})
  const iconCheckHandler = () => togglePublishInvoicesModal(true, {})
  const iconCreditCardHandler = () => toggleChargeCreditCardsModal(true, {})
  const downloadFileHandler = ({ id }: { id: number | string }): void => {
    return downloadSepaHandler(id, 34)
  }
  const forwardToDetailsHandler = ({ id }: { id: number | string }): string => {
    return `${ROUTES.ADMIN_ROUTES.PATH_TRANSACTION_WITHOUT_ID}/${id}`
  }

  return {
    canManage: customerInvoiceManage,
    hasInvoiceListPermission: customerInvoiceList,
    dataSource,
    searchOptions,
    totalCount,
    generateNewInvoiceModalVisible,
    publishInvoicesConfirmationModalVisible,
    chargeCreditCardsInvoicesConfirmationModalVisible,
    selectFiltersOptions,
    iconPlusHandler,
    iconCheckHandler,
    iconCreditCardHandler,
    onChargeCreditCard,
    generateNewInvoiceModalSubmitHandler,
    saveCellHandler,
    getCustomerInvoices,
    downloadFileIconHandler,
    previewIconHandler,
    deleteIconHandler,
    chargeCreditCardsInvoicesConfirmationModalCloseHandler,
    chargeCreditCardsInvoicesConfirmationModalOkHandler,
    publishInvoicesConfirmationModalCloseHandler,
    publishInvoicesConfirmationModalOkHandler,
    generateNewInvoiceModalCloseHandler,
    forwardToDetailsHandler,
    downloadFileHandler,
  }
}
