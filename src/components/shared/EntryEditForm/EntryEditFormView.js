import React from "react"
import PropTypes from "prop-types"
import get from "lodash/get"
import { Field, Form } from "formik"
import { QuestionCircleTwoTone } from "@ant-design/icons"
import {
  Radio,
  Select,
  Tree,
  Checkbox,
  Input,
  Popover as OldPopover,
} from "antd"
import cn from "classnames"

import FormattedMessage from "components/FormattedMessage"
import Typography from "components/Typography"
import l from "utils/intl"
import {
  DateField,
  DateTimeField,
  NumericInput,
  SelectField,
  TextArea,
  TextField,
  SwitchField,
  withErrorField,
  withErrorFieldText,
  SelectTree,
  PlaceholderCheckbox,
  RadioGroup,
  withErrorFieldSelectTree,
} from "components/shared/FormFields"
import EmailsInput from "components/shared/EmailsInput"
import { Button, PrimaryButton } from "components/shared/Buttons"

// TODO: When someone refactoring this file, need to delete one file, because this files are same
import formStyles from "./entryEditForm.module.scss"
import defaultStyles from "components/shared/EntryEditForm/entryEditForm.module.scss"

const SelectFieldWithError = withErrorField(SelectField)
const TextAreaWithError = withErrorField(TextArea)
const TextFieldWithError = withErrorField(TextField)
const SwitchFieldWithError = withErrorFieldText(SwitchField)
const DateTimeFieldWithError = withErrorField(DateTimeField)
const SelectTreeWithError = withErrorFieldSelectTree(SelectTree)
const DateFieldWithError = withErrorField(DateField)
const NumericInputWithError = withErrorField(NumericInput)
const RadioGroupWithError = withErrorFieldText(RadioGroup)
const EmailsWithError = withErrorField(EmailsInput)

const { Option } = Select
const { TreeNode } = Tree

const renderTreeNodes = (data, hasError = false) =>
  data.map((item) => {
    if (item.children) {
      return (
        <TreeNode
          title={item.title}
          key={item.key}
          dataRef={item}
          className={cn({ [formStyles.error]: hasError })}
        >
          {renderTreeNodes(item.children)}
        </TreeNode>
      )
    }

    return (
      <TreeNode
        key={item.key}
        {...item}
        className={cn({ [formStyles.error]: hasError })}
      />
    )
  })

const EntryEditFormView = ({
  controls,
  disabled: formDisabled,
  submitRef,
  isSubmitting,
  mode,
  setFieldValue,
  setValues,
  styles,
  values,
  errors,
  isSaveButtonDisabled,
}) => (
  <Form className={cn(styles.form, styles[mode])} noValidate>
    <div className={styles.container}>
      <div className={styles.controlsContainer}>
        {controls.map(
          ({
            allowClear = true,
            disabled: disabledProp,
            hidePlaceholder,
            isAvailable,
            isDisabled,
            labelText,
            name,
            note,
            onChange,
            onClick,
            onBlur,
            options,
            placeholder,
            required,
            textInputPlaceholder,
            type,
            value,
          }) => {
            if (isAvailable && !isAvailable(values)) {
              return null
            }
            const disabled = disabledProp || (isDisabled && isDisabled(values))

            switch (type) {
              case "text": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Field
                        className={styles.textField}
                        component={TextFieldWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        onBlur={onBlur}
                        onChange={onChange}
                        name={name}
                        placeholder={
                          textInputPlaceholder
                            ? textInputPlaceholder
                            : l(placeholder)
                        }
                        suffix={
                          note && (
                            <OldPopover
                              content={l(note)}
                              overlayClassName={styles.popover}
                            >
                              <QuestionCircleTwoTone
                                className={styles.tooltopIcon}
                              />
                            </OldPopover>
                          )
                        }
                      />
                    </div>
                  </div>
                )
              }
              case "textPlaceholder": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Input
                        className={styles.textField}
                        disabled={true}
                        name={name}
                        placeholder={l(placeholder)}
                        value={value}
                      />
                    </div>
                  </div>
                )
              }
              case "textArea": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Field
                        className={styles.textAreaField}
                        component={TextAreaWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={name}
                        placeholder={l(placeholder)}
                      />
                    </div>
                  </div>
                )
              }
              case "select": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Field
                        allowClear={allowClear}
                        className={styles.selectField}
                        component={SelectFieldWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={name}
                        placeholder={l(placeholder)}
                      >
                        {options.map(({ value, label }) => (
                          <Option
                            className={styles.option}
                            key={value}
                            value={value}
                          >
                            {label}
                          </Option>
                        ))}
                      </Field>
                    </div>
                  </div>
                )
              }
              case "radio": {
                return (
                  <div
                    className={cn(
                      styles.controlContainer,
                      styles.radioControlContainer
                    )}
                    key={name}
                  >
                    <div className={styles.label} />
                    <div className={styles.controlWrapper}>
                      <Field
                        component={RadioGroupWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={name}
                        onChange={
                          onChange
                            ? () => onChange(values, setValues)
                            : undefined
                        }
                      >
                        {options.map(({ value, label }) => (
                          <Radio
                            className={styles.radio}
                            key={value}
                            value={value}
                          >
                            <Typography
                              className={styles.radioLabel}
                              variant="textLarge"
                            >
                              <FormattedMessage id={label} />
                            </Typography>
                          </Radio>
                        ))}
                      </Field>
                    </div>
                  </div>
                )
              }
              case "button": {
                const setValuesClickHandler = () => onClick(values, setValues)

                const hasOnClick = onClick ? setValuesClickHandler : null

                return (
                  <div
                    className={cn(
                      styles.controlContainer,
                      formStyles.buttonControlContainer
                    )}
                    key={name}
                  >
                    <div className={styles.controlWrapper}>
                      <PrimaryButton
                        type="button"
                        className={formStyles.primaryButton}
                        onClick={hasOnClick}
                      >
                        <FormattedMessage id={"Use company address"} />
                      </PrimaryButton>
                    </div>
                  </div>
                )
              }
              case "selectWithFilter": {
                const filterOptionHandler = (input, option) => {
                  const childrenLabels = option.props.children.toLowerCase()
                  const hasChildrenLabelsSearchValue = childrenLabels.includes(
                    input.toLowerCase()
                  )
                  return hasChildrenLabelsSearchValue
                }

                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Field
                        allowClear={allowClear}
                        className={styles.selectField}
                        component={SelectFieldWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        filterOption={filterOptionHandler}
                        name={name}
                        notFoundContent={null}
                        onChange={(value) =>
                          onChange && onChange(value, setFieldValue)
                        }
                        placeholder={l(placeholder)}
                        showSearch
                        showArrow
                        forceFocused={false}
                      >
                        {options.map(({ value, label }) => (
                          <Option
                            className={styles.option}
                            key={value}
                            value={value}
                          >
                            {label}
                          </Option>
                        ))}
                      </Field>
                    </div>
                  </div>
                )
              }
              case "dateTime": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Field
                        component={DateTimeFieldWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={name}
                        placeholder={l(placeholder)}
                      />
                    </div>
                  </div>
                )
              }
              case "switch": {
                return (
                  <div
                    className={`${styles.controlContainer} ${styles.switchControlContainer}`}
                    key={name}
                  >
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div
                      className={`${styles.controlWrapper} ${styles.controlSwitchWrapper}`}
                    >
                      <Field
                        className={styles.switchField}
                        component={SwitchFieldWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        isNumeric
                        name={name}
                      />
                    </div>
                  </div>
                )
              }
              case "selectTree": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <div className={styles.label} />
                    <div className={styles.controlWrapper}>
                      <Field
                        className={styles.switchField}
                        component={SelectTreeWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={name}
                        options={options}
                        renderTreeNodes={renderTreeNodes}
                      />
                    </div>
                  </div>
                )
              }
              case "checkbox": {
                return (
                  <div
                    className={cn(
                      styles.controlContainer,
                      styles.checkboxControlContainer
                    )}
                    key={name}
                  >
                    <div className={styles.label} />
                    <div className={styles.checkboxWrapper}>
                      <Field
                        className={styles.checkbox}
                        component={PlaceholderCheckbox}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={name}
                        placeholder={placeholder}
                      />
                    </div>
                    <Typography
                      className={cn(styles.checkboxLabel)}
                      type="div"
                      variant="text"
                    >
                      <FormattedMessage
                        defaultMessage={placeholder}
                        id={placeholder}
                        values={{
                          lb: (...chunks) => (
                            <>
                              <br />
                              {chunks}
                            </>
                          ),
                        }}
                      />
                      {required && " *"}
                    </Typography>
                  </div>
                )
              }
              case "dynamicCheckbox": {
                return (
                  <div
                    className={cn(
                      styles.controlContainer,
                      styles.checkboxControlContainer
                    )}
                    key={name}
                  >
                    <div className={styles.label} />
                    <div className={styles.checkboxWrapper}>
                      <Field
                        className={styles.checkbox}
                        component={PlaceholderCheckbox}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={name}
                        placeholder={placeholder}
                      />
                    </div>
                    <Typography
                      className={cn(styles.checkboxLabel)}
                      type="div"
                      variant="text"
                    >
                      {placeholder}
                      {required ? " *" : null}
                    </Typography>
                  </div>
                )
              }
              case "termsCheckbox": {
                return (
                  <label
                    className={cn(
                      styles.controlContainer,
                      styles.checkboxControlContainer
                    )}
                    key={name}
                  >
                    <div className={styles.label} />
                    <div className={styles.checkboxWrapper}>
                      <Checkbox
                        className={styles.checkbox}
                        onChange={onChange}
                        checked={value}
                      />
                    </div>
                    <Typography
                      className={cn(styles.checkboxLabel)}
                      type="div"
                      variant="text"
                    >
                      <FormattedMessage
                        defaultMessage={placeholder}
                        id={placeholder}
                        values={{
                          lb: (...chunks) => <>{chunks}</>,
                        }}
                      />
                      {required && " *"}
                    </Typography>
                  </label>
                )
              }
              case "dateRange": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.rangeControlWrapper}>
                      <Field
                        className={styles.rangeControl}
                        component={DateFieldWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        format="DD.MM.YYYY"
                        name={`${name}.from`}
                        placeholder={l("From")}
                      />
                      <Field
                        className={styles.rangeControl}
                        component={DateFieldWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        format="DD.MM.YYYY"
                        name={`${name}.to`}
                        placeholder={l("To")}
                      />
                    </div>
                  </div>
                )
              }
              case "intRange": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.rangeControlWrapper}>
                      <Field
                        allowDecimal={false}
                        className={styles.rangeControl}
                        component={NumericInputWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={`${name}.from`}
                        placeholder={l("From")}
                      />
                      <Field
                        allowDecimal={false}
                        className={styles.rangeControl}
                        component={NumericInputWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={`${name}.to`}
                        placeholder={l("To")}
                      />
                    </div>
                  </div>
                )
              }
              case "floatRange": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.rangeControlWrapper}>
                      <Field
                        className={styles.rangeControl}
                        component={NumericInputWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={`${name}.from`}
                        placeholder={l("From")}
                      />
                      <Field
                        className={styles.rangeControl}
                        component={NumericInputWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        name={`${name}.to`}
                        placeholder={l("To")}
                      />
                    </div>
                  </div>
                )
              }
              case "label": {
                return (
                  <div
                    className={cn(styles.controlContainer, styles.labelControl)}
                    key={labelText}
                  >
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Typography
                        className={styles.fieldLabel}
                        type="div"
                        variant="textLarge"
                      >
                        {labelText}
                      </Typography>
                    </div>
                  </div>
                )
              }
              case "emails": {
                return (
                  <div className={styles.controlContainer} key={name}>
                    <Typography
                      className={cn(styles.label, {
                        [styles.labelHidden]: hidePlaceholder,
                      })}
                      type="div"
                      variant="textLarge"
                    >
                      <FormattedMessage id={placeholder} />
                      {required && " *"}
                    </Typography>
                    <div className={styles.controlWrapper}>
                      <Field
                        className={styles.textField}
                        component={EmailsWithError}
                        disabled={isSubmitting || disabled || formDisabled}
                        value={(get(values, name) || "").split(",")}
                        name={name}
                        errorText={get(errors, name)}
                        placeholder={
                          textInputPlaceholder
                            ? textInputPlaceholder
                            : l(placeholder)
                        }
                        onChange={(values) =>
                          setFieldValue(name, (values || []).join(","))
                        }
                      />
                    </div>
                  </div>
                )
              }
              default:
                return null
            }
          }
        )}
      </div>
      <Button
        className={styles.hiddenButton}
        htmlType="submit"
        ref={submitRef}
        disabled={isSaveButtonDisabled}
      />
    </div>
  </Form>
)

EntryEditFormView.propTypes = {
  controls: PropTypes.array.isRequired,
  disabled: PropTypes.bool,
  isSubmitting: PropTypes.bool.isRequired,
  mode: PropTypes.string,
  styles: PropTypes.object,
  submitRef: PropTypes.object.isRequired,
  isSaveButtonDisabled: PropTypes.bool,
}

EntryEditFormView.defaultProps = {
  mode: "",
  styles: defaultStyles,
  isSaveButtonDisabled: false,
}

export default EntryEditFormView
