import React, { useState } from "react"
import PropTypes from "prop-types"
import { Dropdown } from "antd"

import Typography from "components/Typography"
import EditableCellModal from "./components/EditableCellModal"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import styles from "components/shared/EditableCell/editableCell.module.scss"

const EditableCellView = ({
  control,
  initialValues,
  fieldName,
  onSave,
  text,
  variant,
  canDisableSubmitButton,
  onBuildCustomOptions,
}) => {
  const [displayModal, setDisplayModal] = useState(false)
  const { type, numberDecorator } = control

  return (
    <Dropdown
      onVisibleChange={(visible) => {
        setDisplayModal(visible)
      }}
      getPopupContainer={(node) => node.parentNode}
      trigger={["click"]}
      visible={displayModal}
      overlay={
        displayModal ? (
          <EditableCellModal
            control={control}
            fieldName={fieldName}
            initialValues={initialValues}
            onClose={() => setDisplayModal(false)}
            onSave={onSave}
            canDisableSubmitButton={canDisableSubmitButton}
            onBuildCustomOptions={onBuildCustomOptions}
          />
        ) : (
          <></>
        )
      }
      overlayStyle={{ width: 250 }}
    >
      <Typography
        type="div"
        className={styles.rowCell}
        variant={variant || "textSmall"}
      >
        <ExportValue>
          {type === "number" && typeof numberDecorator === "function"
            ? numberDecorator(text)
            : text}
        </ExportValue>
      </Typography>
    </Dropdown>
  )
}

EditableCellView.propTypes = {
  control: PropTypes.object.isRequired,
  fieldName: PropTypes.string.isRequired,
  initialValues: PropTypes.object.isRequired,
  onSave: PropTypes.func.isRequired,
  text: PropTypes.oneOfType([PropTypes.string.isRequired, PropTypes.number]),
}

export default EditableCellView
