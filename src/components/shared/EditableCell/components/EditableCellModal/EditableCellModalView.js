import React, { useEffect, useRef, useState } from "react"
import FocusLock from "react-focus-lock"
import { Select } from "antd"
import { Alert, Box, Typography } from "@develop/fe-library"
import { Field, Form, Formik } from "formik"
import isEqual from "lodash/isEqual"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import { FormKeypressHandler } from "components/KeypressHandler"
import { Button, PrimaryButton } from "components/shared/Buttons"
import {
  NumericInput,
  SelectField,
  TextField,
  withErrorField,
} from "components/shared/FormFields"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { fieldTypesConstants } from "consts"

import styles from "./editableCellModal.module.scss"

const SelectFieldWithError = withErrorField(SelectField)
const TextFieldWithError = withError<PERSON>ield(TextField)
const NumericInputWithError = withError<PERSON>ield(NumericInput)

const { Option } = Select

const { SELECT_FIELD_TYPE, NUMBER_FIELD_TYPE } = fieldTypesConstants

const EditableCellModalView = ({
  control: {
    options,
    title,
    description = "",
    type,
    shouldTranslateOptions,
    alert,
  },
  fieldName,
  initialValues,
  onClose,
  onSave,
  canDisableSubmitButton,
  onBuildCustomOptions,
}) => {
  const [innerFocus, setInnerFocus] = useState(false)
  const [canSubmit, setCanSubmit] = useState(false)
  const [selectValue, setSelectValue] = useState(null)

  const primaryButtonRef = useRef(null)

  const isSelectFieldType = type === SELECT_FIELD_TYPE
  const isSelectFieldTypeAndNeedSubmitByEnterKeypress =
    canSubmit && isSelectFieldType

  const selectOptionsType = onBuildCustomOptions
    ? onBuildCustomOptions
    : options

  const selectChangeHandler = (value) => {
    setSelectValue(value)
  }

  const submitHandler = ({ isSubmitting, submitForm }) => {
    return () => {
      !isSubmitting && submitForm()
    }
  }

  const keypressPrimaryButtonHandler = ({ isSubmitting, submitForm }) => {
    return (event) => {
      const isEnterKeypress = event && event.keyCode === 13

      if (!isSelectFieldType) {
        return null
      }

      if (isEnterKeypress) {
        setCanSubmit(true)
        submitHandler({ isSubmitting, submitForm })
      }

      return
    }
  }

  useEffect(() => {
    setTimeout(() => setInnerFocus(true), 500)
  })

  useEffect(() => {
    const isNeedFocusToPrimaryButton =
      primaryButtonRef && selectValue !== null && selectValue !== undefined

    if (isNeedFocusToPrimaryButton) {
      primaryButtonRef?.current?.focus()
    }
  }, [selectValue])

  return (
    <FocusLock disabled={!innerFocus}>
      <Formik
        initialValues={initialValues}
        onSubmit={(payload, { setErrors, setSubmitting }) => {
          if (isEqual(initialValues, payload)) {
            onClose()
            setSubmitting(false)

            return
          }
          onSave(
            payload,
            (errors = []) => {
              setErrors(
                errors.reduce((acc, { field, message }) => {
                  acc[field] = message

                  return acc
                }, {})
              )
              setSubmitting(false)
            },
            () => {
              onClose()
              setSubmitting(false)
            }
          )
        }}
      >
        {({ isSubmitting, submitForm }) => (
          <Form noValidate className={styles.container}>
            {isSelectFieldType ? (
              <FormKeypressHandler
                onClose={onClose}
                onSubmit={submitHandler({ isSubmitting, submitForm })}
                onEnter={
                  isSelectFieldTypeAndNeedSubmitByEnterKeypress
                    ? submitHandler({ isSubmitting, submitForm })
                    : null
                }
              />
            ) : (
              <FormKeypressHandler
                onClose={onClose}
                onEnter={submitHandler({ isSubmitting, submitForm })}
                onSubmit={submitHandler({ isSubmitting, submitForm })}
              />
            )}
            <Typography textAlign="left" variant="--font-body-text-6">
              <FormattedMessage id={title} />
            </Typography>
            {description ? (
              <Box paddingTop="s">
                <Typography textAlign="left" variant="--font-body-text-9">
                  <FormattedMessage id={description} />
                </Typography>
              </Box>
            ) : null}
            {alert ? <Alert {...alert} /> : null}
            <div className={styles.field}>
              {isSelectFieldType ? (
                <Field
                  className={styles.control}
                  component={SelectFieldWithError}
                  disabled={isSubmitting}
                  name={fieldName}
                  style={{ width: "100%", minWidth: 136 }}
                  onChange={selectChangeHandler}
                >
                  {checkIsArray(selectOptionsType)
                    ? selectOptionsType.map(({ label, value }) => (
                        <Option key={value} value={value}>
                          {shouldTranslateOptions ? l(label) : label}
                        </Option>
                      ))
                    : null}
                </Field>
              ) : type === NUMBER_FIELD_TYPE ? (
                <Field
                  className={styles.control}
                  component={NumericInputWithError}
                  disabled={isSubmitting}
                  name={fieldName}
                />
              ) : (
                <Field
                  forceFocused
                  className={styles.control}
                  component={TextFieldWithError}
                  disabled={isSubmitting}
                  name={fieldName}
                />
              )}
              <div className={styles.buttonsContainer}>
                <Button
                  iconOnly
                  className={styles.button}
                  disabled={isSubmitting}
                  icon="icnClose"
                  name="Close"
                  onClick={onClose}
                />
                <PrimaryButton
                  ref={primaryButtonRef}
                  iconOnly
                  className={styles.button}
                  htmlType="submit"
                  icon="icnCheck"
                  name="Submit"
                  type="button"
                  disabled={
                    !!(isSubmitting || canDisableSubmitButton?.(selectValue))
                  }
                  onClick={submitHandler({ isSubmitting, submitForm })}
                  onKeyDown={keypressPrimaryButtonHandler({
                    isSubmitting,
                    submitForm,
                  })}
                />
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </FocusLock>
  )
}

EditableCellModalView.propTypes = {
  control: PropTypes.object.isRequired,
  fieldName: PropTypes.string.isRequired,
  initialValues: PropTypes.object.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func,
}

export default EditableCellModalView
