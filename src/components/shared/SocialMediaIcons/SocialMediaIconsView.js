import { Icon } from "@ant-design/compatible"
import React from "react"
import styles from "./socialMediaIcons.module.scss"
import cn from "classnames"
import {
  FacebookShareButton,
  LinkedinShareButton,
  TwitterShareButton,
} from "react-share"

const FB_APP_ID = 1319193144796249

export const FacebookSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.33317 8.50016H8.83317V9.00016V14.1668H7.1665V9.00016V8.50016H6.6665H5.1665V6.8335H6.6665H7.1665V6.3335V4.46683C7.1665 3.56117 7.45058 2.91718 7.88307 2.49877C8.31856 2.07746 8.95776 1.8335 9.76184 1.8335C10.2967 1.8335 10.8111 1.85877 11.1665 1.88379V3.16683H10.6665C9.93912 3.16683 9.39124 3.34211 9.08511 3.79672C8.94309 4.00762 8.88486 4.23633 8.85834 4.43645C8.83312 4.62672 8.83315 4.82411 8.83317 4.98592L8.83317 5.00016V6.3335V6.8335H9.33317H11.0261L10.6094 8.50016H9.33317Z"
      fill="#0055CC"
      stroke="#0055CC"
    />
  </svg>
)

export const TwitterSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.6747 3.87021L13.675 3.87069L13.675 3.87074C14.2868 3.80471 14.8808 3.63536 15.4267 3.40006C15.0126 4.00293 14.4934 4.53982 13.9011 4.97333L13.9009 4.97294L13.9009 4.97382C13.9098 5.10594 13.9098 5.23806 13.9098 5.37021C13.9098 9.39675 10.8457 14.0357 5.24612 14.0357C3.5221 14.0357 1.92116 13.5363 0.57167 12.671C0.815484 12.6994 1.05051 12.7083 1.30505 12.7083C2.72839 12.7083 4.03923 12.2261 5.08575 11.4064L5.08541 11.4059L5.08506 11.4055L5.08496 11.4054L5.08542 11.4054C3.74847 11.3768 2.62568 10.5006 2.23978 9.2941C2.42878 9.32082 2.61604 9.34041 2.81398 9.34041C3.08458 9.33965 3.354 9.30485 3.61591 9.23682L3.61576 9.23628L3.61562 9.23574L3.61561 9.23568L3.61588 9.23573C2.22149 8.95364 1.17525 7.72708 1.17525 6.24699V6.21044C1.59716 6.44591 2.06866 6.57854 2.55146 6.59755L2.55148 6.59699L2.5515 6.59644L2.5515 6.59634L2.55159 6.5964C1.73221 6.04826 1.19489 5.11636 1.19489 4.05949C1.19313 3.51955 1.33602 2.989 1.6087 2.52297C3.10701 4.37095 5.3605 5.57791 7.88716 5.71005L7.88719 5.70949L7.88722 5.70894L7.88731 5.70728L7.88774 5.70938C7.84132 5.48263 7.81275 5.24695 7.81275 5.01128C7.81275 3.33301 9.16965 1.96541 10.8568 1.96541C11.2738 1.96449 11.6866 2.04946 12.0693 2.21502C12.452 2.38058 12.7966 2.62319 13.0814 2.92773L13.0811 2.92807L13.0819 2.9279C13.7632 2.79617 14.4166 2.54748 15.013 2.19287C14.7857 2.89606 14.3098 3.49226 13.6744 3.86973L13.6747 3.87021Z"
      fill="#56CCF2"
      stroke="#56CCF2"
      stroke-width="0.00111607"
    />
  </svg>
)

export const LinkedinSvg = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.36791 6.15351L8.35344 6.76161L9.2918 7.01375C9.4986 6.63628 9.8937 6.31446 10.4111 6.11147C10.9259 5.90953 11.526 5.84045 12.0912 5.93645C12.6535 6.03195 13.1645 6.28697 13.535 6.71727C13.9028 7.1444 14.1666 7.7841 14.1666 8.71351V13.5002H12.5333V9.62018C12.5333 8.85316 12.3028 8.23529 11.8999 7.80872C11.4976 7.38285 10.9586 7.1865 10.4255 7.2055C9.34152 7.24413 8.35329 8.15136 8.35329 9.62018V13.5002H6.72663V6.15351H8.36791ZM4.12663 3.33326C4.12652 3.55428 4.03861 3.76619 3.88226 3.9224C3.7259 4.0786 3.51389 4.16629 3.29288 4.16618C3.07186 4.16607 2.85995 4.07817 2.70374 3.92181C2.54754 3.76545 2.45985 3.55344 2.45996 3.33243C2.46007 3.11141 2.54797 2.8995 2.70433 2.74329C2.86069 2.58709 3.0727 2.4994 3.29371 2.49951C3.51472 2.49962 3.72664 2.58753 3.88284 2.74388C4.03905 2.90024 4.12674 3.11225 4.12663 3.33326ZM2.49996 13.5002V6.15351H4.16663V13.5002H2.49996Z"
      fill="#2D9CDB"
      stroke="#2D9CDB"
    />
  </svg>
)

const icons = {
  fb: FacebookSvg,
  tw: TwitterSvg,
  in: LinkedinSvg,
}

export const SocialMediaIcon = ({ type, ...props }) => (
  <Icon component={icons[type]} {...props} />
)

const ShareIcon = ({ className, url, ...props }) => {
  switch (props.type) {
    case "fb":
      return (
        <FacebookShareButton appId={FB_APP_ID} url={url}>
          <div
            className={cn(
              styles.container,
              styles.fb,
              className,
              "share-icon-container",
              "fb"
            )}
          >
            <SocialMediaIcon className="social-icon" {...props} />
          </div>
        </FacebookShareButton>
      )
    case "tw":
      return (
        <TwitterShareButton url={url}>
          <div
            className={cn(
              styles.container,
              className,
              "share-icon-container",
              "tw"
            )}
          >
            <SocialMediaIcon className="social-icon" {...props} />
          </div>
        </TwitterShareButton>
      )
    case "in":
      return (
        <LinkedinShareButton url={url}>
          <div
            className={cn(
              styles.container,
              className,
              "share-icon-container",
              "in"
            )}
          >
            <SocialMediaIcon className="social-icon" {...props} />
          </div>
        </LinkedinShareButton>
      )
    default:
      return null
  }
}

export default ShareIcon
