import React from "react"
import PropTypes from "prop-types"
import { Upload } from "antd"
import { Alert, Box } from "@develop/fe-library"

import { Button } from "components/shared/Buttons"
import Modal from "components/shared/Modal"
import FormattedMessage from "components/FormattedMessage"
import Typography from "components/Typography"

import styles from "components/shared/UploadModal/uploadModal.module.scss"

import "components/shared/UploadModal/uploadModal.scss"

const UploadModal = ({
  accept,
  error,
  fileName,
  fileUrl,
  onClose,
  onUpload,
  title,
}) => (
  <Modal
    title={<FormattedMessage id={title} />}
    visible
    footer={null}
    className="upload-modal"
    width={410}
    onCancel={onClose}
  >
    {error ? (
      <Box display="block" marginBottom="m">
        <Alert alertType="error" message={error} />
      </Box>
    ) : null}
    <div className={styles.container}>
      <Upload
        accept={accept}
        className={styles.upload}
        customRequest={onUpload}
        listType="picture"
        name="file"
        showUploadList={{
          showPreviewIcon: false,
          showRemoveIcon: false,
          showDownloadIcon: true,
        }}
        fileList={
          fileName
            ? [
                {
                  uid: "-1",
                  name: fileName,
                  status: "done",
                  url: fileUrl,
                  thumbUrl: fileUrl,
                },
              ]
            : undefined
        }
      >
        <Button className={styles.uploadButton} icon="icnUpload">
          <FormattedMessage id={title} />
        </Button>
      </Upload>
    </div>
    <Typography className={styles.allowedLabel} type="div" varint="textLarge">
      <b>
        <FormattedMessage id="Please note." />{" "}
      </b>
      <FormattedMessage id="Allowed file extensions:" />
      {` ${accept}`}
    </Typography>
  </Modal>
)

UploadModal.propTypes = {
  accept: PropTypes.string,
  fileName: PropTypes.string,
  fileUrl: PropTypes.string,
  onClose: PropTypes.func.isRequired,
  onUpload: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
}

UploadModal.defaultProps = {
  accept: ".jpeg,.jpg,.gif,.png,.pdf",
}

export default UploadModal
