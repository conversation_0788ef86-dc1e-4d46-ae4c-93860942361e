import { Option, SelectProps } from "@develop/fe-library"

type PickerProps = Pick<
  SelectProps,
  | "displayedValue"
  | "errorDisplayType"
  | "errorMessage"
  | "errorPopoverPlacement"
  | "hasCheckIcon"
  | "hasChevronIcon"
  | "hasClearIcon"
  | "hasSearchIcon"
  | "isContentMinWidthMatchedWithTrigger"
  | "isFullWidth"
  | "isGlobal"
  | "label"
  | "options"
  | "renderOption"
  | "size"
>

type Value = Option["value"]

export type SelectWithPaginationProps = PickerProps & {
  defaultValue?: Value
  isGlobal?: SelectProps["isGlobal"]
  page: number
  pageSize: number
  selectedValue: Value
  totalCount: number
  onClose?: () => void
  onPageChange: (page: number) => void
  onSelect: (value: Value) => void
}
