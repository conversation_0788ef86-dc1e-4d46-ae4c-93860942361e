@import "assets/styles/variables.scss";

.spiner {
  border-radius: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid $int_disable_on;
  border-left: 2px solid $icon_active;
  transform: translateZ(0);
  animation: spin 1.1s infinite linear;
}

.spiner.small {
  width: 16px;
  height: 16px;
}

.spiner.large {
  width: 32px;
  height: 32px;
}

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
