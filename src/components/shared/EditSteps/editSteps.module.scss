@import "assets/styles/variables.scss";

.container {
  align-items: center;
  display: flex;
  height: 70px;
  margin-bottom: 10px;
  max-width: 930px;
}

.step {
  align-items: center;
  display: flex;
  margin-left: 30px;
  margin-right: 30px;

  .stepLabel.stepLabel {
    color: $text_second;
  }

  .stepNumber {
    align-items: center;
    border: 1px solid $border_main;
    border-radius: 50%;
    color: $text_second;
    display: flex;
    font-size: 13px;
    justify-content: center;
    height: 24px;
    line-height: 24px;
    margin-right: 10px;
    width: 24px;
  }

  &.active,
  &.completed {
    .stepLabel.stepLabel {
      color: $text_main;
    }

    .stepNumber {
      border-color: $border_active;
      color: $text_link;
    }

    :global(.anticon) {
      color: $icon_active;
    }
  }

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }
}

.divider {
  background-color: $border_main;
  flex-grow: 1;
  height: 1px;

  &.completed {
    background-color: $border_active;
  }
}

@media (max-width: $md) {
  .step {
    margin-left: 20px;
    margin-right: 20px;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

@media (max-width: $xs) {
  .step {
    .stepLabel {
      display: none;
    }

    .stepNumber {
      margin-right: 0;
    }
  }
}
