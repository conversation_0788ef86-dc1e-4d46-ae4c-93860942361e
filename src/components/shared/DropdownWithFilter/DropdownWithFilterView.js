import React, { useState } from "react"
import PropTypes from "prop-types"
import { CloseCircleFilled, DownOutlined } from "@ant-design/icons"
import { Dropdown, Button } from "antd"
import cn from "classnames"

import FilterableList from "components/shared/FilterableList"

import styles from "components/shared/DropdownWithFilter/dropdownWithFilter.module.scss"

const DropdownWithFilterView = ({
  className,
  currentPage,
  filterField,
  filterValue,
  items,
  onClear,
  onChange,
  onClose,
  onPageChange,
  onSearch,
  optionComponent,
  placeholder,
  selectedValue,
  totalCount,
  value,
  filterClassName = "",
}) => {
  const [visible, setVisible] = useState(false)

  return (
    <Dropdown
      onVisibleChange={(visibilityState) => {
        if (!visibilityState) {
          onClose()
        }

        setVisible(visibilityState)
      }}
      overlay={
        visible ? (
          <FilterableList
            className={`${styles.overlay} ${filterClassName}`}
            component={optionComponent}
            currentPage={currentPage}
            field={filterField}
            filterValue={filterValue}
            items={items}
            onClick={(...args) => {
              setVisible(false)

              onChange(...args)
            }}
            onPageChange={onPageChange}
            onSearch={onSearch}
            selectedValue={selectedValue}
            totalCount={totalCount}
          />
        ) : (
          <></>
        )
      }
      trigger={["click"]}
      visible={visible}
    >
      <Button className={cn(styles.button, className)}>
        <span className={styles.label}>{value || placeholder}</span>
        {onClear && value ? (
          <CloseCircleFilled
            className={styles.clearIcon}
            onClick={(e) => {
              e.stopPropagation()

              onClear()
            }}
          />
        ) : (
          <DownOutlined className={styles.icon} />
        )}
      </Button>
    </Dropdown>
  )
}

DropdownWithFilterView.propTypes = {
  className: PropTypes.string,
  currentPage: PropTypes.number.isRequired,
  filterClassName: PropTypes.string,
  filterField: PropTypes.string.isRequired,
  filterValue: PropTypes.string.isRequired,
  items: PropTypes.array.isRequired,
  onChange: PropTypes.func.isRequired,
  onClose: PropTypes.func.isRequired,
  onPageChange: PropTypes.func.isRequired,
  onSearch: PropTypes.func,
  optionComponent: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  selectedValue: PropTypes.number,
  totalCount: PropTypes.number.isRequired,
  value: PropTypes.string,
}

DropdownWithFilterView.defaultProps = {
  className: "",
}

export default DropdownWithFilterView
