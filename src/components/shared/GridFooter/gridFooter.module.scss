@import "assets/styles/variables.scss";

.sizeSelect {
  margin-right: 22px;
  width: 80px;
  & :global(.ant-select-selector.ant-select-selector) {
    border: 1px solid $border_main;
  }
  & :global(.ant-select-arrow .anticon) {
    color: $text_placeholders;
  }
}

.settingsButton {
  margin-left: 8px;

  &.firstButton {
    margin-left: 0;
  }
}

.gotoInput.gotoInput {
  margin-left: 6px;
  width: 64px;
}

.gotoContainer {
  margin-left: 16px;
}

.container {
  background-color: $main_bg;
  border-top: 1px solid $border_main;
  bottom: 0;
  padding: 0 10px;
  position: fixed;
  width: 100%;
  z-index: 25;
}

.gridContainer {
  height: 54px;
  width: 100%;
}

.paginationContainer {
  display: flex;
  justify-content: center;
}

.hiddenSettingsButton {
  visibility: hidden;
}

.widthSettingsButton {
  margin-right: 10px;
}

.totalResults {
  & * {
    font-size: 12px;
    color: var(--color-text-second);
  }
}

.gridFooterText.gridFooterText {
  display: inline-block;
  color: inherit;
}

@media (max-width: $xl) {
  .settingsButton {
    padding: 0;
  }
}

@media (max-width: $lg) {
  .totalResults {
    display: none;
  }
}

@media (max-width: $md) {
  .gotoContainer {
    display: none;
  }

  .sizeSelect {
    margin-right: 0;
  }
}

@media (max-width: $xs) {
  .container {
    justify-content: center;
  }
  .sizeSelect {
    width: auto;
    :global(.ant-select-selector.ant-select-selector) {
      height: 40px;
    }
    :global(.ant-select-selection-item.ant-select-selection-item) {
      line-height: 40px;
    }
  }
}
