import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { ROUTES } from "@develop/fe-library/dist/routes"

import countriesActions from "actions/countriesActions"
import partnersActions from "actions/partnersActions"

import { countriesOptionsSelector } from "selectors/countriesSelectors"
import {
  filtersOptionsSelector,
  isEuCountrySelector,
  partnerItemsSelector,
  partnersInitialValuesSelector,
  partnersModalVisibleSelector,
  partnersSearchOptionsSelector,
  partnersTotalCountSelector,
  partnersUploadModalVisibleSelector,
} from "selectors/partnersSelectors"
import { permissionsSelector } from "selectors/userSelectors"

import { checkIsArray } from "utils/arrayHelpers"

import { ErrorsObjectType, ErrorType } from "types/ErrorTypes"

const {
  // @ts-ignore
  displayModal,
  // @ts-ignore
  displayUploadModal,
  // @ts-ignore
  get: getPartnersData,
  // @ts-ignore
  update,
  // @ts-ignore
  upload,
} = partnersActions
//   @ts-ignore
const { getAll: getCountries } = countriesActions

export const usePartners = () => {
  const { partnerManage } = useSelector(permissionsSelector)
  const dataSource = useSelector(partnerItemsSelector)
  const selectFiltersOptions = useSelector(filtersOptionsSelector)
  const initialValues = useSelector(partnersInitialValuesSelector)
  const searchOptions = useSelector(partnersSearchOptionsSelector)
  const totalCount = useSelector(partnersTotalCountSelector)
  const modalVisible = useSelector(partnersModalVisibleSelector)
  const uploadModalVisible = useSelector(partnersUploadModalVisibleSelector)
  const countries = useSelector(countriesOptionsSelector)
  const isEuCountry = useSelector(
    isEuCountrySelector(initialValues?.country_id)
  )

  const [isEU, setIsEU] = useState(isEuCountry)
  const [errors, setErrors] = useState<ErrorsObjectType>({})

  const history = useHistory()
  const dispatch = useDispatch()

  const getPartners = <Type,>(searchOptions: Type): void => {
    dispatch(getPartnersData(searchOptions))
  }

  const getAdditionalData = (): void => {
    dispatch(getCountries())
  }

  const toggleModal = <Type,>(visible: boolean, initialValues: Type): void => {
    dispatch(displayModal(visible, initialValues))
  }

  const toggleUploadModal = <Type,>(
    visible: boolean,
    initialValues: Type
  ): void => {
    setErrors({})

    dispatch(displayUploadModal(visible, initialValues))
  }

  const submitPartnerModalHandler = (
    payload: any,
    failureCallback?: () => void,
    successCallback?: () => void
  ): void => {
    dispatch(
      update(
        {
          ...payload,
          payout_for_modules: payload.payout_for_modules ?? "",
          partner_id: payload.id,
        },
        () => {
          if (successCallback) {
            successCallback()
          }
          getPartners(searchOptions)
        },
        failureCallback
      )
    )
  }

  const editButtonHandler = (payload: any = {}) => {
    const { partnerPaymentDebit } = payload
    const { iban, account_holder, swift, bank_account_number } =
      partnerPaymentDebit || {}

    return toggleModal(true, {
      ...payload,
      iban,
      account_holder,
      swift,
      bank_account_number,
    })
  }

  const uploadButtonHandler = <Type,>(payload: Type) => {
    return toggleUploadModal(true, payload)
  }

  const uploadModalHandler = ({ file }: { file: string | Blob }) => {
    const formData = new FormData()

    formData.append("file", file)

    dispatch(
      upload(
        initialValues.id,
        formData,
        () => {
          getPartners(searchOptions)
          setErrors({})
        },
        (errors: ErrorType[]) => {
          if (checkIsArray(errors)) {
            const errorsObject = errors.reduce((result, error) => {
              result[error.field] = error.message

              return result
            }, {} as ErrorsObjectType)

            setErrors(errorsObject)
          }
        }
      )
    )
  }

  const navigateToChannelsHandler = ({ id }: { id: number | string }): void => {
    return history.push(`${ROUTES.ADMIN_ROUTES.PATH_PARTNERS_CHANNEL}/${id}`)
  }

  const changeIsEuCountryHandler = (countryId: number | string) => {
    const country = countries.find(
      ({ id }: { id: number | string }) => countryId === id
    )

    setIsEU(!!country?.eu)
  }

  const createButtonHandler = () => toggleModal(true, {})

  const closeUploadModalHandler = () => toggleUploadModal(false, undefined)

  const closePartnerModalHandler = () => {
    return toggleModal(false, undefined)
  }

  useEffect(() => {
    setIsEU(!!(modalVisible && isEuCountry))
  }, [modalVisible, isEuCountry])

  return {
    canManage: partnerManage,
    dataSource,
    selectFiltersOptions,
    searchOptions,
    initialValues,
    totalCount,
    modalVisible,
    uploadModalVisible,
    isEU,
    countries,
    getPartners,
    getAdditionalData,
    errors,
    createButtonHandler,
    editButtonHandler,
    uploadButtonHandler,
    navigateToChannelsHandler,
    changeIsEuCountryHandler,
    closeUploadModalHandler,
    uploadModalHandler,
    closePartnerModalHandler,
    submitPartnerModalHandler,
  }
}
