import times from "lodash/times"

import { FIRST_INCOMES } from "components/Partners/consts"

import l from "utils/intl"

import { PartnerFormikFields } from "components/Partners/partnerTypes"

import { Params } from "./buildCountAndPercentFieldsTypes"

export const buildCountAndPercentFields = ({
  countName,
  countPlaceholder,
  countLabelSingular,
  countLabelPlural,
  percentName,
}: Params) => {
  return [
    {
      allowClear: false,
      isAvailable: ({
        payment_type,
        is_internal_partner,
      }: PartnerFormikFields) =>
        payment_type === FIRST_INCOMES && !is_internal_partner,
      name: countName,
      placeholder: countPlaceholder,
      type: "select",
      required: true,
      options: times(6, (index) => ({
        label: `${index + 1} ${
          index === 0 ? l(countLabelSingular) : l(countLabelPlural)
        }`,
        value: index + 1,
      })),
      value: 3,
    },
    {
      isAvailable: ({
        payment_type,
        is_internal_partner,
      }: PartnerFormikFields) =>
        payment_type === FIRST_INCOMES && !is_internal_partner,
      name: percentName,
      placeholder: "% to pay",
      type: "number",
      required: true,
    },
  ]
}
