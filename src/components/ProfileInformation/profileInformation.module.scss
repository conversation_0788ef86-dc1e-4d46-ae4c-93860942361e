@import "assets/styles/variables.scss";

.container {
  display: flex;
  flex: 1 1;
  height: 100%;
}

.topContainer,
.mainContainer,
.bottomContainer,
.rightContainer {
  padding: 20px;
}

.topContainer {
  border-bottom: solid 1px $border_main;
}

.bottomContainer {
  border-top: solid 1px $border_main;
  padding-left: 0;
}

.withRigntContainer {
  .leftContainer {
    width: 100%;
    max-width: 768px;
    border-right: solid 1px $border_main;
    flex: 1 1;
  }
}

.formContainer {
  max-width: 768px;
  border-right: solid 1px $border_main;
  padding: 20px;
  min-height: 100%;
  height: 100%;
}

.formPopover {
  max-width: 260px;
}

.toolIcon {
  padding: 6px;
  position: relative;
}

.formTitle,
.formSubTitle {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.formSubTitle {
  font-size: 14px;
}

.formRow {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  &.isLast {
    margin-bottom: 20px;
  }
  :global(.error-wraper) {
    display: block;
  }
  &.checkList {
    align-items: flex-start;
  }
}

.formRowWithButton {
  display: flex;
}

.formRowTitle {
  width: 212px;
  font-size: 13px;
}

.formInput {
  width: 100%;
  flex: 1 1;
}

.formRowButton {
  margin-left: 10px;
  min-width: auto;
}

.buttonActions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;

  button {
    &[type="submit"] {
      margin-left: 10px;
    }
  }
}

@media (max-width: $lg) {
  .container {
    display: block;
  }

  .formContainer,
  .leftContainer {
    border-right: none !important;
  }

  .leftContainer {
    width: 100%;
    max-width: 100%;
    padding-bottom: 80px;
  }

  .rightContainer {
    border-top: solid 1px $border_main;
  }
}

@media (max-width: $md) {
  .leftContainer {
    padding-bottom: 40px;
  }
}

@media (max-width: $xs) {
  .formRowTitle {
    display: block;
    margin-bottom: 5px;
  }
  .formRow {
    display: block;
    margin-bottom: 10px;
  }
  .leftContainer {
    padding-bottom: 30px;
  }
  .topContainer,
  .mainContainer,
  .rightContainer,
  .bottomContainer,
  .formContainer {
    padding: 10px;
  }
  .button {
    width: calc(50% - 5px);
  }
}
