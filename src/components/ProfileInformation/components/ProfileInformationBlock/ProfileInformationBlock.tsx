import React from "react"
import {
  Box,
  Descriptions,
  Ellipsis,
  Icon,
  IconPopover,
  Typography,
} from "@develop/fe-library"

import { RestrictedIcon } from "components/Restrict"
import Link from "components/shared/Link"

import { checkIsFunction } from "utils/validationHelper"

import { ROW_PROPS_MAP } from "./constants"

export const ProfileInformationBlock = ({ items }) => {
  return (
    <Descriptions
      gridColumnTemplates={[
        {
          mSM: "100%",
          tb: "180px",
        },
        {
          mSM: "100%",
          tb: "auto",
        },
      ]}
      gridContainerProps={{
        gap: "l",
      }}
      items={items?.map(
        ({
          value,
          url,
          warningIconText,
          labelIcon,
          label,
          type,
          onEdit,
          managePermission,
          popoverMessage,
        }) => {
          const { variant, endOfLineCharacter, paddingTop, iconSize } =
            ROW_PROPS_MAP[type] || ROW_PROPS_MAP.default

          const warningIconColor = !!url
            ? "--color-icon-active"
            : "--color-icon-static"

          const hasValueAsLink: boolean = !!url && !!value
          const hasValueAsText: boolean = !url && !!value
          const hasWarningIcon: boolean = !!warningIconText

          return {
            label: (
              <Box key={label} align="center" gap="m" paddingTop={paddingTop}>
                {labelIcon ? (
                  <Icon
                    color="--color-icon-static"
                    name={labelIcon}
                    size="--icon-size-5"
                  />
                ) : null}

                <Typography variant={variant}>
                  {label}
                  {endOfLineCharacter}
                </Typography>
              </Box>
            ),
            value: (
              <Box align="center" gap="m" paddingTop={paddingTop}>
                {hasValueAsLink ? (
                  <Link
                    internal={false}
                    styleType="primary"
                    text={value}
                    url={url}
                  />
                ) : null}

                {hasValueAsText ? (
                  <Ellipsis
                    typographyProps={{
                      variant:
                        type === "title"
                          ? "--font-headline-5"
                          : "--font-body-text-7",
                    }}
                  >
                    {value}
                  </Ellipsis>
                ) : null}

                {hasWarningIcon ? (
                  <IconPopover
                    color={warningIconColor}
                    content={warningIconText}
                    name="icnWarning"
                    size={iconSize}
                  />
                ) : null}

                {checkIsFunction(onEdit) ? (
                  <RestrictedIcon
                    managePermission={managePermission}
                    name="icnEdit"
                    popoverMessage={popoverMessage}
                    size={iconSize}
                    onClick={onEdit}
                  />
                ) : null}
              </Box>
            ),
          }
        }
      )}
    />
  )
}
