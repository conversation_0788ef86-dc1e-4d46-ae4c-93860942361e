import React, { useState } from "react"
import { Popover as OldPopover, Select } from "antd"
import { Form } from "@ant-design/compatible"
import { QuestionCircleTwoTone } from "@ant-design/icons"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import cn from "classnames"
import { Field } from "formik"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import withOutlineLabel from "components/hocs/withOutlineLabel"
import withPasswordGeneratorField from "components/hocs/withPasswordGeneratorField/withPasswordGeneratorField"
import { Button, PrimaryButton } from "components/shared/Buttons"
import {
  CheckBox,
  DateInput,
  PhoneField,
  SelectField,
  TextField,
  withErrorField,
} from "components/shared/FormFields"
import FormikWithChangeValidation from "components/shared/FormikWithChangeValidation"
import Typography from "components/Typography"
import { TYPOGRAPHY_TYPE_SPAN } from "components/Typography/TypographyView"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"

import styles from "./profileInformation.module.scss"

import "@ant-design/compatible/assets/index.css"

const TextFieldWithError = withErrorField(TextField, true, false)
const TextPasswordGeneratorField = withErrorField(
  withPasswordGeneratorField(TextField),
  true,
  false
)
const SelectWithError = withErrorField(SelectField, true, false)
const CheckBoxWithError = withErrorField(CheckBox, true, false)
const DateWithError = withErrorField(DateInput, true, false)
const PhoneFieldWithError = withErrorField(PhoneField, true, false)

const TextFieldWithErrorOutlined = withErrorField(
  withOutlineLabel(TextField),
  true,
  false
)
const TextPasswordGeneratorFieldOutlined = withErrorField(
  withPasswordGeneratorField(withOutlineLabel(TextField)),
  true,
  false
)
const SelectWithErrorOutlined = withErrorField(
  withOutlineLabel(SelectField),
  true,
  false
)
const CheckBoxWithErrorOutlined = withErrorField(
  withOutlineLabel(CheckBox),
  true,
  false
)
const DateWithErrorOutlined = withErrorField(
  withOutlineLabel(DateInput),
  true,
  false
)
const PhoneFieldWithErrorOutlined = withErrorField(
  withOutlineLabel(PhoneField),
  true,
  false
)

export const fieldTypes = {
  TextType: "textType",
  TextTypePassword: "textTypePassword",
  CheckboxType: "checkboxType",
  DateType: "dateType",
  SelectType: "selectType",
  SelectSearchType: "selectType",
  Title: "title",
  Phone: "phone",
  CheckListType: "checkListType",
}

const fieldTypeComponents = (hasTitle) => ({
  [fieldTypes.TextType]: hasTitle
    ? TextFieldWithError
    : TextFieldWithErrorOutlined,
  [fieldTypes.TextTypePassword]: hasTitle
    ? TextPasswordGeneratorField
    : TextPasswordGeneratorFieldOutlined,
  [fieldTypes.SelectType]: hasTitle ? SelectWithError : SelectWithErrorOutlined,
  [fieldTypes.CheckboxType]: hasTitle
    ? CheckBoxWithError
    : CheckBoxWithErrorOutlined,
  [fieldTypes.DateType]: hasTitle ? DateWithError : DateWithErrorOutlined,
  [fieldTypes.Phone]: hasTitle
    ? PhoneFieldWithError
    : PhoneFieldWithErrorOutlined,
})

const ProfileInformationForm = ({
  onCancel,
  formTitle,
  fields,
  onSubmit,
  initialValues,
  onChange,
}) => {
  const [phoneErrorMessages, setPhoneErrorMessages] = useState({})

  return (
    <FormikWithChangeValidation
      fields={fields.filter(({ required }) => required)}
      initialValues={initialValues}
      namesRequired={fields
        .filter(({ required }) => required)
        .map(({ name }) => name)}
      onSubmit={onSubmit}
      onChangeState={(lastChanges, allChanges, { values, isChanged }) =>
        onChange && onChange(values, isChanged)
      }
    >
      {({
        submitForm,
        isSubmitting,
        values,
        canSubmit,
        setErrors,
        setSubmitting,
      }) => (
        <Form onSubmit={submitForm}>
          <div className={styles.formContainer}>
            <div className={styles.formTitle}>{formTitle}</div>

            {fields.map((field, idx) => {
              const {
                title,
                name,
                inputType = fieldTypes.TextType,
                placeholder,
                options,
                fieldProps = {},
                isAvailable,
                isLast,
                note,
                required,
                withGenerate,
                confirmName,
                button,
                fieldComponent,
              } = field

              const hasTitle = !!title

              if (isAvailable && !isAvailable(values)) {
                return null
              }

              let fieldJsxProps = {}

              if (inputType === fieldTypes.Title) {
                return (
                  <div key={idx} className={styles.formSubTitle}>
                    <FormattedMessage id={title} />
                  </div>
                )
              }

              if (
                (inputType === fieldTypes.SelectType ||
                  inputType === fieldTypes.SelectSearchType) &&
                Array.isArray(options)
              ) {
                fieldJsxProps.children = options.map(
                  ({ id, title }, optionIdx) => (
                    <Select.Option key={optionIdx} value={id}>
                      {title}
                    </Select.Option>
                  )
                )
              }

              if (inputType === fieldTypes.SelectSearchType) {
                fieldJsxProps = {
                  ...fieldJsxProps,
                  showSearch: true,
                  filterOption: (input, option) =>
                    option.props.children
                      .toLowerCase()
                      .includes(input.toLowerCase()),
                }
              }

              if (inputType === fieldTypes.Phone) {
                fieldJsxProps = {
                  onChange: (number, isValid, { errorMessage }) => {
                    if (errorMessage) {
                      setPhoneErrorMessages({
                        ...phoneErrorMessages,
                        [name]: errorMessage,
                      })
                    } else {
                      delete phoneErrorMessages[name]
                      setPhoneErrorMessages(phoneErrorMessages)
                    }
                  },
                  errorText: phoneErrorMessages[name],
                }
              }

              if (inputType === fieldTypes.TextTypePassword) {
                fieldJsxProps = {
                  ...fieldJsxProps,
                  withGenerate,
                  confirmName,
                  type: "password",
                  autoComplete: "off",
                }
              }

              return (
                <div
                  key={idx}
                  className={cn(styles.formRow, {
                    [styles.isLast]: isLast,
                    [styles.checkList]: inputType === fieldTypes.CheckListType,
                  })}
                >
                  {hasTitle && (
                    <Typography
                      className={styles.formRowTitle}
                      type={TYPOGRAPHY_TYPE_SPAN}
                    >
                      {`${l(title)}${required ? " *" : ""}`}
                    </Typography>
                  )}
                  <div
                    className={cn(styles.formInput, {
                      [styles.formRowWithButton]: !!button,
                    })}
                  >
                    <Field
                      name={name}
                      component={
                        fieldComponent ||
                        fieldTypeComponents(!!title)[inputType]
                      }
                      placeholder={
                        hasTitle
                          ? l(placeholder)
                          : `${l(placeholder)}${required ? " *" : ""}`
                      }
                      {...{ ...fieldJsxProps, ...fieldProps }}
                      suffix={
                        note && (
                          <OldPopover
                            content={l(note)}
                            overlayClassName={styles.formPopover}
                          >
                            <QuestionCircleTwoTone
                              className={styles.toolIcon}
                            />
                          </OldPopover>
                        )
                      }
                    />
                    {button && (
                      <PrimaryButton
                        className={styles.formRowButton}
                        disabled={button.disabled || isSubmitting}
                        onClick={() =>
                          button.onClick(name, {
                            values,
                            setErrors,
                            setSubmitting,
                          })
                        }
                      >
                        <Typography variant="controlLabel">
                          <FormattedMessage id={button.title} />
                        </Typography>
                      </PrimaryButton>
                    )}
                  </div>
                </div>
              )
            })}

            <div className={styles.buttonActions}>
              <Button
                className={styles.button}
                disabled={isSubmitting}
                onClick={onCancel}
              >
                <Typography variant="controlLabel">
                  <FormattedMessage id="Back" />
                </Typography>
              </Button>
              <PrimaryButton
                className={styles.button}
                htmlType="submit"
                managePermission={permissionKeys.staffManage}
                popoverMessage={restrictPopoverMessages.alter}
                disabled={
                  isSubmitting ||
                  getObjectKeys(phoneErrorMessages).length ||
                  !canSubmit
                }
              >
                <Typography variant="controlLabel">
                  <FormattedMessage id="Save" />
                </Typography>
              </PrimaryButton>
            </div>
          </div>
        </Form>
      )}
    </FormikWithChangeValidation>
  )
}

ProfileInformationForm.propTypes = {
  fields: PropTypes.array,
  formTitle: PropTypes.string,
  initialValues: PropTypes.object,
  onChange: PropTypes.func,
}

export default ProfileInformationForm
