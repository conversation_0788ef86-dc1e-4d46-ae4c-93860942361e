import React from "react"
import { Box } from "@develop/fe-library"
import PropTypes from "prop-types"

import { ServiceDeskProfilePicture } from "components/ServiceDeskProfilePicture"

import { checkIsArray } from "utils/arrayHelpers"

import { ProfileInformationBlock } from "./components"

const ProfileInformation = ({
  mainContainer,
  topContainer,
  rightContainer,
  bottomContainer,
  fullHeight = true,
}) => {
  const hasTopContainer = checkIsArray(topContainer)
  const hasMainContainer = checkIsArray(mainContainer)
  const hasBottomContainer = checkIsArray(bottomContainer)
  const hasRightContainer = checkIsArray(rightContainer)
  const hasMainOrBottomContainer = hasMainContainer || hasBottomContainer

  const hasLeftContainer =
    hasTopContainer || hasMainContainer || hasBottomContainer

  const hasBottomBorderOnTopContainer =
    hasBottomContainer || hasMainOrBottomContainer

  return (
    <Box
      height={fullHeight ? "100%" : "auto"}
      width="100%"
      dMD={{
        display: "grid",
        gridTemplateColumns: "auto 440px",
      }}
      mSM={{
        display: "flex",
        flexDirection: "column",
      }}
    >
      {hasLeftContainer ? (
        <Box
          flexDirection="column"
          dMD={{
            hasBorder: {
              bottom: false,
              right: hasRightContainer,
            },
          }}
          mSM={{
            hasBorder: {
              bottom: hasRightContainer,
              right: false,
            },
          }}
        >
          {hasTopContainer ? (
            <Box
              flexDirection="column"
              gap="l"
              hasBorder={{ bottom: hasBottomBorderOnTopContainer }}
              padding="l"
            >
              <ProfileInformationBlock items={topContainer} />
            </Box>
          ) : null}

          {hasBottomContainer ? (
            <Box padding="l l 0 l">
              <ServiceDeskProfilePicture />
            </Box>
          ) : null}

          {hasMainOrBottomContainer ? (
            <Box flexDirection="column">
              {hasMainContainer ? (
                <Box
                  flexDirection="column"
                  gap="l"
                  hasBorder={{ bottom: hasBottomContainer }}
                  padding="l"
                >
                  <ProfileInformationBlock items={mainContainer} />
                </Box>
              ) : null}

              {hasBottomContainer ? (
                <Box flexDirection="column" gap="l" padding="l">
                  <ProfileInformationBlock items={bottomContainer} />
                </Box>
              ) : null}
            </Box>
          ) : null}
        </Box>
      ) : null}

      {hasRightContainer ? (
        <Box padding="l">
          <ProfileInformationBlock items={rightContainer} />
        </Box>
      ) : null}
    </Box>
  )
}

ProfileInformation.propTypes = {
  mainContainer: PropTypes.array,
  topContainer: PropTypes.array,
  rightContainer: PropTypes.array,
  fullHeight: PropTypes.bool,
}

export default ProfileInformation
