import React from "react"
import { Box, Typography } from "@develop/fe-library"

import { checkIsArray } from "utils/arrayHelpers"

import type { FieldErrorItemProps } from "./FieldErrorItemTypes"

export const FieldErrorItem = ({ controls }: FieldErrorItemProps) => {
  if (!checkIsArray(controls)) {
    return null
  }

  return (
    <Box
      hasBorder
      borderRadius="--border-radius"
      flexDirection="column"
      gap="m"
      padding="m"
    >
      {controls.map(({ title, value }, index) => (
        <Box key={index}>
          <Box marginRight="l" minWidth={100}>
            <Typography variant="--font-body-text-5">{title}</Typography>
          </Box>
          <Typography variant="--font-body-text-7">{value}</Typography>
        </Box>
      ))}
    </Box>
  )
}
