import {
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_SELECT,
} from "components/TableGridLayout/TableGridLayoutView"

export const useLostCasesBulkManagementTableColumns = () => {
  return [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      sorter: true,
      type: COLUMN_INPUT_TYPE_INPUT,
      width: 100,
    },
    {
      title: "Status",
      dataIndex: "statusLabel",
      key: "status",
      sorter: true,
      type: COLUMN_INPUT_TYPE_SELECT,
      width: 120,
    },
    {
      title: "Number of cases",
      dataIndex: "total_count",
      key: "total_count",
      sorter: true,
      type: COLUMN_INPUT_TYPE_NUMBER,
      width: 220,
      isNumber: true,
    },
    {
      title: "Number of rows with errors",
      dataIndex: "error_count",
      key: "error_count",
      sorter: true,
      type: COLUMN_INPUT_TYPE_NUMBER,
      width: 220,
      isNumber: true,
    },
    {
      title: "Number of successful rows",
      dataIndex: "success_count",
      key: "success_count",
      sorter: true,
      type: COLUMN_INPUT_TYPE_NUMBER,
      width: 220,
      isNumber: true,
    },
    {
      title: "Start date",
      dataIndex: "startDateLabel",
      key: "date_started",
      sorter: true,
      width: 130,
    },
    {
      title: "End date",
      dataIndex: "endDateLabel",
      key: "date_finished",
      sorter: true,
      width: 130,
    },
    {
      title: "Created on",
      dataIndex: "dateCreatedLabel",
      key: "date_created",
      sorter: true,
      width: 130,
    },
    {
      title: "Date updated",
      dataIndex: "dateUpdateLabel",
      key: "date_updated",
      sorter: true,
      width: 130,
    },
  ]
}
