import React from "react"

const Terms = () => {
  const year = new Date().getFullYear()

  return (
    <div>
      <h1>
        API-Lizenz- und Nutzungsbedingungen für Nutzer des SellerLogic Dienstes
      </h1>
      <h2>Präambel</h2>
      <h3>Definitionen:</h3>
      <p>
        <b>“Lizenzgeber”</b> bezeichnet die SellerLogic GmbH, die als Inhaberin
        des Lizenzrechts ein einfaches Nutzungsrecht (Lizenz) an der API
        (Application Programming Interface) zwecks Verbindung mit ihrem
        SellerLogic Dienst gewährt.
      </p>
      <p>
        <b>“<PERSON><PERSON>znehm<PERSON>”</b> bezeichnet den unternehmerisch tätigen Erwerber
        eines einfachen Nutzungsrechts (Lizenz) für die API (Application
        Programming Interface) zwecks Verbindung mit dem SellerLogic Dienst.
      </p>
      <p>
        <b>“Hauptvertrag”</b> bezeichnet den Nutzungsvertrag im Sinne der
        Definition in den Allgemeinen Geschäftsbedingungen zwischen SellerLogic
        und einem Nutzer des SellerLogic Dienstes.
      </p>
      <p>
        <b>“SellerLogic”</b> bezeichnet die SellerLogic GmbH, Grafenberger Allee
        277-287, 40237 Düsseldorf, Deutschland.
      </p>
      <p>
        <b>“SellerLogic Dienst”</b> bezeichnet die von SellerLogic erstellten
        Softwareanwendungen sowie angebotenen Online-Dienste, die Nutzern zur
        Unterstützung ihrer gewerblichen Tätigkeiten zur Verfügung gestellt
        werden.
      </p>
      <p>
        <b>“SellerLogic Materialien”</b> bezeichnet Materialien (z.B. API,
        Dokumentationen, Software-Bibliotheken) gleich welchen Formats oder
        gleich welchen Mediums, die SellerLogic im Rahmen und zwecks Nutzung des
        SellerLogic Dienstes zur Verfügung stellt.
      </p>
      <h2>1. Lizenz</h2>
      <p>
        Gegenstand dieser Vereinbarung zwischen Nutzern des SellerLogic Dienstes
        und SellerLogic ist die Nutzung der SellerLogic Schnittstelle und ihrer
        dazugehörigen Dokumentation (im Folgenden API genannt) zwecks
        Anwendungsprogrammierung. Der Lizenznehmer erklärt sein Einverständnis
        zu diesen Lizenzbedingungen, in dem er die API verwendet oder
        herunterlädt.
      </p>
      <p>
        Der Lizenzgeber behält sich das Recht vor, diese API-Lizenz- und
        Nutzungsbedingungen (im Folgenden API-Bedingungen genannt) oder die
        Dokumente, die durch Verweis in diese API-Bedingungen aufgenommen
        werden, jederzeit zu aktualisieren und zu ändern. Die jeweils aktuelle
        Fassung dieser API-Bedingungen wird unter der SellerLogic Webseite{" "}
        <a href="https://www.sellerlogic.com" target="_blank" rel="noreferrer">
          https://www.sellerlogic.com
        </a>{" "}
        veröffentlicht. Der Lizenzgeber kann diese API-Bedingungen durch
        Veröffentlichung einer neuen Fassung ändern. Nach erfolgtem
        E-Mail-Hinweis auf die geänderten API-Bedingungen stellt der Zugriff auf
        und die Verwendung der API, also der Betrieb der API durch Übergabe von
        Daten an SellerLogic, nach einer solchen Änderung und
        Änderungsmitteilung, die Annahme dieser Änderungen dar.
      </p>
      <p>
        Der Lizenznehmer erhält ein beschränktes, widerrufliches, nicht
        exklusives, nicht unterlizenzierbares, nicht übertragbares Recht, die
        API zu nutzen. Der Lizenznehmer darf die API nutzen, um eigene / fremde
        Software / Online-Dienste mit dem SellerLogic Dienst zu verbinden.
      </p>
      <p>Dem Lizenznehmer ist es nicht gestattet:</p>
      <ul>
        <li>
          die API zu kopieren, zu übertragen, zu verkaufen, eine Unterlizenz zu
          erteilen oder auf andere Weise die API einem Dritten zur Verfügung zu
          stellen. Dies gilt sowohl für kommerzielle als auch für
          nicht-kommerzielle Zwecke (u.a. auch im Zusammenhang mit der Nutzung
          öffentlicher Software).
        </li>
        <li>
          die API so zu verwenden, dass hierdurch eine Nutzung des SellerLogic
          Dienstes dem Lizenznehmer oder einem Dritten ermöglicht wird, die
          nicht unter die jeweiligen Lizenz- oder Nutzungsbedingungen fällt.
        </li>
        <li>
          die API zu verändern, anzupassen, zu verbinden, zu modifizieren, zu
          übersetzen, zu dekompilieren, zu zerlegen oder abgeleitete Werke des
          gesamten oder eines Teils der API von SellerLogic, ohne die vorherige
          schriftliche Zustimmung des Lizenzgebers herzustellen. Urheberrechts-
          oder Markenhinweise oder andere schutzrechtlichen Hinweise zu
          entfernen, die in der API oder dem SellerLogic Dienst enthalten sind.
        </li>
      </ul>
      <p>
        Der Lizenznehmer erkennt an, dass das Eigentum an den Rechten, an
        geistigem Eigentum in und an der SellerLogic API, den SellerLogic
        Materialien und dem SellerLogic Dienst, einschließlich aller Kopien,
        beim Lizenzgeber liegt.
      </p>
      <p>
        Der Lizenzgeber hat das Recht die API jederzeit zu aktualisieren, zu
        ändern, auszusetzen oder die API jederzeit zu unterbrechen. Der
        Lizenzgeber kann nach seinem Ermessen diese Vereinbarung und die Nutzung
        der API jederzeit kündigen. Sollte der Lizenzgeber sich entscheiden die
        Nutzung der API zu kündigen, wird er dies dem Lizenznehmer mit einer
        Frist von 90 Tagen vorab ankündigen.
      </p>
      <p>
        Der Lizenzgeber wird die API ständig weiterentwickeln und aktualisieren,
        um vorhandene Funktionen zu verbessern oder zu entfernen und zusätzliche
        Funktionen, falls nötig, zu ergänzen. Der Lizenznehmer ist selbst dafür
        verantwortlich seine API Applikation bei Bedarf zu pflegen und zu
        aktualisieren.
      </p>
      <h2>2. Rechte und Pflichten des Lizenznehmers</h2>
      <p>
        Der Lizenznehmer darf die API nutzen und eigene / fremde Software /
        Online-Dienste unter folgenden Bedingungen mit dem SellerLogic Dienst
        verbinden. Der Lizenznehmer hat vor und im laufenden Betrieb
        sicherzustellen, dass
      </p>
      <ul>
        <li>
          seine API Applikation stets ausreichend getestet ist und jeweils dem
          aktuellen Stand der Technik entspricht.
        </li>
        <li>
          der Lizenznehmer keine Marken, Handelsnamen, Abzeichen, Logo, Symbol
          oder Slogan (ob eingetragen oder nicht), die dem Lizenzgeber jetzt
          oder zukünftig gehören ohne die vorherige Zustimmung des Lizenzgebers,
          verwendet oder Dritten die Verwendung ermöglicht.
        </li>
        <li>
          seine Materialien sicher und geschützt aufbewahrt und entsprechende
          Datensicherungen, einschließlich der Anwendung von
          Verschlüsselungsverfahren, durchgeführt werden, um die Daten zu
          archivieren und sie vor unbefugtem Zugriff zu schützen. Der
          Lizenznehmer allein ist für die Entwicklung, den Inhalt, den Betrieb
          und die Instandhaltung seiner Materialien, sowie für die
          ordnungsgemäße Konfiguration und Nutzung des SellerLogic Dienstes
          verantwortlich.
        </li>
      </ul>
      <p>
        Der Lizenznehmer hält den Lizenzgeber schadlos in Bezug auf sämtliche
        Verluste, Haftungen und Kosten (einschließlich angemessener
        Anwaltskosten), die aufgrund von Forderungen, Prozessen oder Klagen
        entstehen, die sich auf einen Verstoß (oder mutmaßlichen Verstoß) des
        Lizenznehmers gegen diesen Vertrag oder einen Teil davon stützen oder
        sich anderweitig auf seine Website(s), API-Applikation oder die Nutzung
        der API beziehen. Der Lizenznehmer ist allein verantwortlich für die
        Verteidigung gegen Klagen unter Hinzuziehung eines gemeinsam
        vereinbarten Rechtsbeistands, vorbehaltlich des Rechts des Lizenzgebers,
        sich mit einem Rechtsbeistand seiner Wahl zu beteiligen. Ohne die
        vorherige schriftliche Zustimmung des Lizenzgebers, die nach alleinigem
        Ermessen des Lizenzgebers erteilt wird, macht der Lizenznehmer keine
        Klagen öffentlich bekannt und stimmt keinen Vergleichen zu, die dem
        Lizenzgeber irgendwelche Verpflichtungen oder Haftungen auferlegen.
        Gleiches gilt für die Inanspruchnahme des Lizenzgebers durch Dritte
        wegen Urheberrechtsverletzungen der API Applikation.
      </p>
      <p>
        Der Lizenznehmer verzichtet hiermit im größtmöglichen gesetzlich
        zulässigen Umfang auf sämtliche Ansprüche gegenüber dem Lizenzgeber und
        dessen (zukünftigen) Tochtergesellschaften, verbundenen Unternehmen,
        leitenden Angestellten, Vertretern, Lizenzgebern, Co-Branding-Partnern
        oder sonstigen Partnern und Mitarbeitern des Lizenzgebers und entbindet
        diese von jeglicher Haftung für Ansprüche, Schäden (tatsächlich
        entstandene Schäden und/oder Folgeschäden), Kosten und Auslagen
        (einschließlich Prozesskosten und Anwaltskosten) jeglicher Art, die in
        irgendeiner Weise aus oder in Verbindung mit seiner API-Applikation und
        der API entstehen.
      </p>
      <h2>3. Datenschutz, Verbotene Handlungen und Plattformverwendung</h2>
      <p>
        Sollte der Lizenzgeber feststellen, dass ein API-Schlüssel
        kompromittiert wurde, behält er sich das Recht vor, den Schlüssel
        zurückzusetzen oder zu widerrufen.
      </p>
      <p>
        Der Lizenznehmer verpflichtet sich, die Veröffentlichung von privaten
        oder vertraulichen geschäftlichen Informationen nicht zu erleichtern, zu
        ermöglichen oder zu fördern.
      </p>
      <p>
        Die API Applikation des Lizenznehmers muss ihren Zweck, ihre Funktionen,
        Zugriffsberechtigungen oder andere Anforderungen eindeutig
        identifizieren. Der Lizenznehmer darf außerhalb der bereitgestellten API
        keine persönlichen oder vertraulichen Informationen von Dritten abrufen
        oder erhalten (z.B. Passwort), noch Änderungen am SellerLogic Dienst
        vornehmen, die nicht für die Funktion der API Applikation vorgesehen
        oder erforderlich sind.
      </p>
      <p>
        Der Lizenzgeber behält sich das Recht vor, die Zahl der akzeptierten
        „Aufrufe“ der API zu begrenzen (rate limiting), sofern er der Ansicht
        ist, dass die Zahl der Aufrufe von einer API Applikation sich negativ
        auf den SellerLogic Dienst auswirkt.
      </p>
      <h2>
        4. Empfehlungen des Lizenznehmers / Entwicklungen des Lizenzgebers
      </h2>
      <p>
        Wenn der Lizenznehmer dem Lizenzgeber Verbesserungen für die SellerLogic
        API oder die SellerLogic Materialien vorschlägt, werden alle Rechte,
        Titel und Ansprüche an seinen Vorschlägen an den Lizenzgeber übergehen,
        auch wenn der Lizenznehmer die Vorschläge als vertraulich gekennzeichnet
        hat. Der Lizenznehmer erteilt dem Lizenzgeber hiermit ein(e)
        unwiderrufliche(s) lizenzgebührenfreie(s), weltweite(s) Recht und Lizenz
        zur Nutzung, Offenlegung, Vervielfältigung, Änderung, Übertragung und
        anderweitigen Verteilung und Nutzbarmachung seiner Vorschläge gleich auf
        welche Art und Weise über die SellerLogic Laufzeit und so lange danach,
        wie es dem Lizenznehmer nach geltendem Recht gestattet ist, die besagte
        Lizenz zu erteilen, und er erklärt sich damit einverstanden, dem
        Lizenzgeber jegliche Hilfestellung zu leisten, die er benötigen könnte,
        um seine Rechte an den Vorschlägen des Lizenznehmers zu dokumentieren,
        vervollständigen und aufrechtzuerhalten.
      </p>
      <p>
        Der Lizenznehmer erklärt sich damit einverstanden, dass der Lizenzgeber
        eigene Anwendungen, Inhalte und andere Produkte oder Dienstleistungen
        erstellen kann, die mit der API Applikation des Lizenznehmers in
        Konkurrenz stehen oder ähnlich sind. Nichts in diesen Lizenzbedingungen
        kann als eine Einschränkung oder Verhinderung des Lizenzgebers ausgelegt
        werden, solche Anwendungen, Inhalte und andere Produkte oder
        Dienstleistungen zu erstellen oder zu nutzen. Der Lizenznehmer erklärt
        sich weiterhin damit einverstanden, dass in diesen Fällen keine
        Verpflichtung des Lizenzgebers ihm gegenüber, weder finanziell noch
        anderweitig entsteht.
      </p>
      <p>
        Der Lizenzgeber kann für Zuordnungszwecke persönliche Informationen über
        Entwickler, Anfragen von Nutzern oder potenziellen Nutzern zur
        Handhabung oder für andere Zwecke im Rahmen zulässiger Datenverarbeitung
        anzeigen, sofern er es für notwendig erachtet.
      </p>
      <h2>5. Gebühren</h2>
      <p>
        Die API wird derzeit kostenlos zur Verfügung gestellt. Der Lizenzgeber
        behält sich jedoch das Recht vor für die Verwendung der API in Zukunft
        Gebühren zu berechnen. Die Berechnung von Gebühren wird der Lizenzgeber
        gegebenenfalls mit einer Frist von 90 Tagen ankündigen.
      </p>
      <h2>6. Gewährleistungsausschluss</h2>
      <p>
        Die API wird ohne Mängelgewähr, d.h. in ihrem jeweiligen Zustand und
        nach Verfügbarkeit ohne irgendeine ausdrückliche oder konkludente
        Gewährleistung bereitgestellt. Der Lizenzgeber schließt ausdrücklich
        sämtliche Gewährleistungen und Garantien aus, einschließlich der
        stillschweigenden Garantie in Bezug auf den gewöhnlichen Gebrauch, die
        Eignung für einen bestimmten Zweck, Verfügbarkeit, Sicherheit,
        Eigentumsrechte und die Nichtverletzung Rechte Dritter.
      </p>
      <p>
        Der Lizenzgeber garantiert nicht, dass die API frei von Ungenauigkeiten,
        Fehlern, Bugs oder Unterbrechungen ist oder dass sie zuverlässig,
        einwandfrei, vollständig oder anderweitig valid ist. Der Lizenzgeber
        garantiert nicht, dass
      </p>
      <ul>
        <li>die API die Anforderungen des Lizenznehmers erfüllt,</li>
        <li>die API ohne Unterbrechung verfügbar ist,</li>
        <li>
          die Ergebnisse aus der Nutzung der SellerLogic API richtig oder
          zuverlässig sind,
        </li>
        <li>
          die Qualität der Produkte, Dienstleistungen, Informationen oder
          anderen Materialien,
        </li>
      </ul>
      <p>
        die der Lizenznehmer über die API erhält, dessen Erwartungen erfüllt und
        Fehler in der API korrigiert werden.
      </p>
      <p>
        Die Nutzung der API erfolgt nach eigenem Ermessen und auf eigenes
        Risiko. Der Lizenznehmer ist allein für alle Schäden verantwortlich, die
        aus der Verwendung der API hervorgehen, einschließlich Schäden an seinem
        System oder Datenverlust. Der Lizenznehmer hat selbst eine regelmäßige
        Datensicherung sicherzustellen.
      </p>
      <h2>7. Haftungsbeschränkung</h2>
      <p>
        Der Lizenzgeber schließt jegliche Haftung soweit zulässig für indirekte
        oder beiläufig entstandene Schäden, Folgeschäden, entgangene Gewinne,
        bezifferbare Vermögensschäden aus, die aus oder in Verbindung mit der
        Verwendung der API entstehen, unabhängig davon, ob diese Schäden auf
        eine Vertragsverletzung, eine Garantieverletzung oder eine unerlaubte
        Handlung (insbesondere Fahrlässigkeit und Produkthaftung) zurückzuführen
        sind, oder für sonstige finanzielle Verluste. Dabei ist es unerheblich,
        ob der Lizenznehmer auf die Möglichkeit solcher Schäden hingewiesen
        wurde oder nicht.
      </p>
      <p>
        Diese Haftungsbeschränkung gilt nicht für Vorsatz oder grobe
        Fahrlässigkeit oder auf Schäden, die auf einer Verletzung von Leben,
        Körper oder Gesundheit beruhen.
      </p>
      <h2>8. Laufzeit/Kündigung</h2>
      <p>
        Diese Lizenzbedingungen werden mit der ersten Verwendung der API wirksam
        und sind bis zu ihrer Kündigung durch eine der Parteien gültig.
        SellerLogic kann den Lizenzvertrag mit einer Frist von 90 Tagen
        kündigen. Dem Lizenznehmer steht eine jederzeitige Kündigungsmöglichkeit
        online durch Anklicken einer Klickbox zur Verfügung, die im Anschluss
        per Email bestätigt wird. Der Lizenzgeber kann diese Lizenzvereinbarung
        jederzeit aussetzen oder kündigen, wenn der Lizenznehmer eine Bestimmung
        dieser Lizenzbedingungen verletzt. In Abhängigkeit und mit Inkrafttreten
        einer Kündigung des Hauptvertrages zur Nutzung des SellerLogic Dienstes
        gilt auch dieser API Lizenzvertrag als wirksam beendet, weil er damit
        seinen Zweck nicht mehr erfüllt.
      </p>
      <p>
        Mit Inkrafttreten einer Kündigung muss der Lizenznehmer i.ü. die Nutzung
        der SellerLogic API und aller dazugehörigen SellerLogic Materialien
        unverzüglich einstellen. Ebenso wird der Lizenznehmer nach einer
        Kündigung des SellerLogic Dienstes unverzüglich alle SellerLogic
        Materialien einschließlich der SellerLogic API Dokumentation
        unwiederbringlich vernichten.
      </p>
      <p>
        Unbeschadet des Vorstehenden kann der Lizenzgeber den Zugang zum
        SellerLogic Dienst, insbesondere zu der SellerLogic API und zu den
        SellerLogic Materialien, nach entsprechender Mitteilung an den
        Lizenznehmer aussetzen oder kündigen, wenn er feststellt:
      </p>
      <ul>
        <li>
          dass die Nutzung des SellerLogic Dienstes, der SellerLogic API oder
          der SellerLogic Materialien durch den Lizenznehmer
          <br />
          (a) ein Sicherheitsrisiko für den SellerLogic Dienst, die SellerLogic
          API, die SellerLogic Materialien oder einen anderen der SellerLogic
          Kunden darstellt,
          <br />
          (b) den SellerLogic Systemen oder einem anderen Kunden des
          Lizenzgebers Schaden zufügen kann, oder
          <br />
          (c) den Lizenzgeber oder einen Dritten einer Haftung unterwerfen kann;
        </li>
        <li>
          dass der Lizenznehmer den SellerLogic Dienst, die SellerLogic API oder
          die SellerLogic Materialien für betrügerische oder unrechtmäßige
          Aktivitäten nutzt;
        </li>
        <li>
          dass die Zurverfügungstellung des SellerLogic Dienstes oder der
          SellerLogic Materialien in irgendeiner Hinsicht einem gesetzlichen
          Verbot unterliegt.
        </li>
      </ul>
      <h2>9. Schlussbestimmungen</h2>
      <p>
        Sollte eine der Lizenzbedingungen unwirksam sein, sind die Parteien
        verpflichtet, diese unverzüglich im Wege der ergänzenden Vereinbarung
        durch eine solche Abrede zu ersetzen, die dem wirtschaftlichen Ergebnis
        der unwirksamen Bestimmung am nächsten kommt. Im Zweifel gilt die
        unwirksame Bestimmung durch eine solche Abrede als ersetzt. Die
        Wirksamkeit der Lizenzbedingungen im Übrigen bleibt unberührt.
      </p>
      <p>
        Nebenabreden wurden nicht getroffen. Änderungen und Ergänzungen zu
        diesen Bestimmungen bedürfen zu ihrer Rechtswirksamkeit der Textform.
        Dabei ist die elektronische Form (E-Mail) ausreichend. Dies gilt auch
        für die Änderung des Textformerfordernisses.
      </p>
      <p>
        Die Anwendbarkeit etwaiger Allgemeiner Geschäftsbedingungen des
        Lizenznehmers ist ausdrücklich ausgeschlossen.
      </p>
      <p>
        Es kommt ausschließlich deutsches Recht unter Ausschluss des
        UN-Kaufrechts (CISG) zur Anwendung. Gerichtsstand für alle
        Streitigkeiten ist Düsseldorf.
      </p>
      <p>Stand: Januar {year}</p>
      <p>Urheberrecht © {year} SellerLogic. Alle Rechte vorbehalten.</p>
    </div>
  )
}

export default Terms
