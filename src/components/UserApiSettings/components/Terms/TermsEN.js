import React from "react"

const Terms = () => {
  const year = new Date().getFullYear()

  return (
    <div>
      <h1>API Licence and Terms of Use for Users of the SellerLogic Service</h1>
      <h2>Preamble</h2>
      <h3>Definitions:</h3>
      <p>
        <b>"Licensor"</b> shall mean SellerLogic GmbH, which as holder of the
        license right grants a simple right of use (license) to the API
        (Application Programming Interface) for the purpose of connection to his
        SellerLogic Service.
      </p>
      <p>
        <b>"Licensee"</b> shall mean the entrepreneurial purchaser of a simple
        right of use (license) for the API (Application Programming Interface)
        for the purpose of connection with the SellerLogic Service.
      </p>
      <p>
        <b>"Main Agreement"</b> shall mean the User Agreement as defined in the
        General Terms and Conditions between SellerLogic and a User of the
        SellerLogic Service.
      </p>
      <p>
        <b>"SellerLogic"</b> shall mean SellerLogic GmbH, Grafenberger Allee
        277-287, 40237 Düsseldorf, Germany.
      </p>
      <p>
        <b>"SellerLogic Service"</b> shall mean the software applications
        created by SellerLogic and online services provided to Users to support
        their commercial activities.
      </p>
      <p>
        <b>"SellerLogic Materials"</b> shall mean materials (e.g., API,
        documentation, software libraries) of any format or medium provided by
        SellerLogic as part of and for the purpose of using the SellerLogic
        Service.
      </p>
      <h2>1. License</h2>
      <p>
        The subject of this agreement between users of the SellerLogic Service
        and SellerLogic is the use of the SellerLogic interface and his
        accompanying documentation (hereinafter referred to as API) for the
        purpose of application programming. The Licensee declares his agreement
        to these license conditions by using or downloading the API.
      </p>
      <p>
        The Licensor reserves the right to update and amend these API Licence
        Terms and Conditions of Use (hereinafter referred to as API Terms and
        Conditions) or the documents incorporated by reference in these API
        Terms and Conditions at any time. The most current version of these API
        Terms will be posted on the SellerLogic website at{" "}
        <a href="https://www.sellerlogic.com" target="_blank" rel="noreferrer">
          https://www.sellerlogic.com
        </a>{" "}
        . The Licensor may amend these API Terms by publishing a new version.
        Upon email notice of the amended API Terms, access to and use of the
        API, i.e., operation of the API by submitting data to SellerLogic,
        following such amendment and notice of amendment shall constitute
        acceptance of such amendments.
      </p>
      <p>
        The Licensee is granted a limited, revocable, non-exclusive,
        non-sublicensable, non- transferable right to use the API. The Licensee
        may use the API to connect his own / third party software / online
        services to the SellerLogic Service.
      </p>
      <p>The Licensee is not permitted to:</p>
      <ul>
        <li>
          copy, transfer, sell, sublicense or otherwise make the API available
          to a third party. This applies to both commercial and non-commercial
          purposes (including, but not limited to, in connection with the use of
          public software).
        </li>
        <li>
          use the API in such a way that it enables the Licensee or a third
          party to use the SellerLogic Service in a way that is not covered by
          the respective licence or terms of use.
        </li>
        <li>
          alter, adapt, combine, modify, translate, decompile, disassemble or
          create derivative works of all or any part of the SellerLogic API
          without the prior written consent of the Licensor. Remove any
          copyright or trademark notices or other proprietary notices contained
          in the API or SellerLogic Service.
        </li>
      </ul>
      <p>
        The Licensee acknowledges that ownership of the rights, intellectual
        property in and to the SellerLogic API, the SellerLogic Materials and
        the SellerLogic Service, including all copies, is vested in the
        Licensor.
      </p>
      <p>
        The Licensor shall have the right to update, modify, suspend or
        discontinue the API at any time. The Licensor may, at his discretion,
        terminate this Agreement and use of the API at any time. If the Licensor
        decides to terminate the use of the API, the Licensor will provide 90
        days' prior notice to the Licensee.
      </p>
      <p>
        The Licensor will continue to develop and update the API to improve or
        remove existing features and add additional features as necessary. The
        Licensee is responsible for maintaining and updating his API Application
        as necessary.
      </p>
      <h2>2. Rights and Obligations of the Licensee</h2>
      <p>
        The Licensee may use the API and connect own / third party software /
        online services to the SellerLogic Service under the following
        conditions. The Licensee must ensure before and during operation that
      </p>
      <ul>
        <li>
          his API application is always sufficiently tested and corresponds to
          the current technological state of the art.
        </li>
        <li>
          the Licensee does not use or permit any third party to use any trade
          mark, trade name, badge, logo, symbol or slogan (whether registered or
          unregistered) now or hereafter owned by the Licensor without the prior
          consent of the Licensor.
        </li>
        <li>
          his materials are kept safe and secure and appropriate backups are
          made, including the use of encryption procedures, to archive the data
          and protect it from unauthorised access. The Licensee is solely
          responsible for the development, content, operation and maintenance of
          his Materials, and for the proper configuration and use of the
          SellerLogic Service.
        </li>
      </ul>
      <p>
        The Licensee shall indemnify and hold the Licensor harmless from and
        against any and all losses, liabilities and costs (including reasonable
        attorneys' fees) arising out of any claim, suit or action based on the
        Licensee's breach (or alleged breach) of this Agreement or any part
        thereof or otherwise relating to his website(s), API Application or use
        of the API.
      </p>
      <p>
        The Licensee shall be solely responsible for defending any action with
        the assistance of mutually agreed legal counsel, subject to the
        Licensor's right to engage with counsel of his choice. The Licensee
        shall not publicly disclose any action or agree to any settlement
        imposing any obligation or liability on the Licensor without the
        Licensor's prior written consent, which shall be given in the Licensor's
        sole discretion.
      </p>
      <p>
        The same shall apply to any third-party claim against the Licensor for
        copyright infringement of the API Application.
      </p>
      <p>
        To the fullest extent permitted by law, the Licensee hereby waives and
        releases any and all claims against the Licensor and his (future)
        subsidiaries, affiliates, officers, agents, licensors, co-branding
        partners or other partners and employees of the Licensor for any claims,
        damages (actual and/or consequential), costs and expenses (including
        litigation costs and attorneys' fees) of any kind arising in any way out
        of or in connection with his API Application and the API.
      </p>
      <h2>3. Data Protection, Prohibited Acts and Platform Use</h2>
      <p>
        If the Licensor determines that an API key has been compromised, the
        Licensor reserves the right to reset or revoke the key.
      </p>
      <p>
        The Licensee agrees not to facilitate, enable or encourage the
        disclosure of private or confidential business information.
      </p>
      <p>
        The Licensee's API Application must clearly identify its purpose,
        functions, access permissions or other requirements. The Licensee shall
        not access or obtain any personal or confidential information from any
        third party outside of the provided API (e.g., password), nor make any
        changes to the SellerLogic Service that are not intended or required for
        the function of the API Application.
      </p>
      <p>
        The Licensor reserves the right to limit the number of accepted requests
        of the API (rate limiting), if it is of the opinion that the number of
        requests from an API application has a negative impact on the
        SellerLogic Service.
      </p>
      <h2>4. Recommendations of the Licensee / Developments of the Licensor</h2>
      <p>
        If the Licensee suggests improvements to the SellerLogic API or the
        SellerLogic Materials to the Licensor, all right, title and interest in
        and to the Licensee's suggestions shall vest in the Licensor, even if
        the Licensee has marked the suggestions as confidential. The Licensee
        hereby grants the Licensor an irrevocable royalty-free, worldwide right
        and license to use, disclose, reproduce, modify, transmit and otherwise
        distribute and make available his Proposals in any manner whatsoever
        during the SellerLogic Term and for so long thereafter, as the Licensee
        is permitted by applicable law to grant said license, and the Licensee
        agrees to provide the Licensor with any assistance that the Licensor may
        require to document, complete and maintain his rights in the Licensee's
        Proposals.
      </p>
      <p>
        The Licensee agrees that the Licensor may create his own applications,
        content and other products or services that compete with or are similar
        to the Licensee's API Application.
      </p>
      <p>
        Nothing in these Licence Terms shall be construed as restricting or
        preventing the Licensor from creating or using such applications,
        content and other products or services. The Licensee further agrees that
        no obligation of The Licensor, financial or otherwise, shall arise to it
        in such cases.
      </p>
      <p>
        The Licensor may, for attribution purposes, display personal information
        about Developers, requests from Users or potential Users for handling or
        for other purposes within the scope of permitted data processing, as he
        deems necessary.
      </p>
      <h2>5. Fees</h2>
      <p>
        The API is currently provided free of charge. However, the Licensor
        reserves the right to charge for the use of the API in the future. The
        Licensor will provide 90 days' notice of the charging of fees, when
        applicable.
      </p>
      <h2>6. Disclaimer of Warranty</h2>
      <p>
        The API is provided "as is" and "as available" without warranty of any
        kind, either express or implied. The Licensor expressly disclaims all
        warranties and guarantees, including the implied warranties of
        merchantability, fitness for a particular purpose, availability,
        security, title and non-infringement.
      </p>
      <p>
        The Licensor does not warrant that the API is free from inaccuracies,
        errors, bugs or interruptions or that it is reliable, flawless, complete
        or otherwise valid.
      </p>
      <p>The Licensor does not warrant that</p>
      <ul>
        <li>the API will meet the Licensee's requirements,</li>
        <li>the API will be available without interruption,</li>
        <li>
          the results from the use of the SellerLogic API will be accurate or
          reliable,
        </li>
        <li>
          the quality of the products, services, information or other materials,
        </li>
        <li>
          the Licensee receives through the API meets his expectations and
          errors in the API are corrected.
        </li>
      </ul>
      <p>
        The Use of the API is at the Licensee's sole discretion and risk. The
        Licensee is solely responsible for any damage resulting from the use of
        the API, including damage to his system or loss of data. The Licensee
        himself shall ensure regular data backup.
      </p>
      <h2>7. Limitation of Liability</h2>
      <p>
        The Licensor excludes all liability to the extent permitted for indirect
        or incidental damages, consequential damages, lost profits, quantifiable
        pecuniary loss arising out of or in connection with the use of the API,
        whether such damages are attributable to breach of contract, breach of
        warranty or tort (including without limitation negligence and product
        liability), or for any other pecuniary loss. It is irrelevant whether
        the Licensee has been advised of the possibility of such damages or not.
      </p>
      <p>
        This limitation of liability does not apply to intent or gross
        negligence or to damages based on injury to life, body or health.
      </p>
      <h2>8. Term/Cancellation</h2>
      <p>
        These License Terms shall become effective upon first use of the API and
        shall remain in effect until terminated by either party. SellerLogic may
        terminate the License Agreement with 90 days' notice. The Licensee has
        the option to terminate online at any time by clicking on a click box,
        which will subsequently be confirmed by email. The Licensor may suspend
        or terminate this Licence Agreement at any time if the Licensee breaches
        any provision of these Licence Terms. Subject to and upon the effective
        date of any termination of the Main Agreement for the use of the
        SellerLogic Service, this API License Agreement shall also be deemed
        effectively terminated by reason of the fact that it no longer serves
        its purpose.
      </p>
      <p>
        Upon the effective date of any termination, the Licensee shall
        immediately cease using the SellerLogic API and all related SellerLogic
        Materials. Similarly, upon termination of the SellerLogic Service, the
        Licensee shall immediately destroy irretrievably all SellerLogic
        Materials, including the SellerLogic API Documentation.
      </p>
      <p>
        Notwithstanding the foregoing, the Licensor may suspend or terminate
        access to the SellerLogic Service, including without limitation the
        SellerLogic API and the SellerLogic Materials, upon notice to the
        Licensee if he determines:
      </p>
      <ul>
        <li>
          that the Licensee's use of the SellerLogic Service, the SellerLogic
          API or the SellerLogic Materials.
          <br />
          (a) poses a security risk to the SellerLogic Service, the SellerLogic
          API, the SellerLogic Materials or any other of the SellerLogic
          Customers,
          <br />
          (b) may cause harm to the SellerLogic Systems or any other of the
          Licensor's customers; or
          <br />
          (c) may subject the Licensor or any third party to liability;
        </li>
        <li>
          that the Licensee is using the SellerLogic Service, the SellerLogic
          API or the SellerLogic Materials for fraudulent or unlawful
          activities;
        </li>
        <li>
          that the provision of the SellerLogic Service or the SellerLogic
          Materials is in any respect subject to a legal prohibition.
        </li>
      </ul>
      <h2>9. Final Provisions</h2>
      <p>
        If one of the license terms is invalid, the parties are obliged to
        replace it immediately by way of supplementary agreement with such an
        agreement that comes closest to the economic result of the invalid
        provision. In case of doubt, the invalid provision shall be deemed
        replaced by such an agreement. The validity of the remaining terms and
        conditions of the licence shall remain unaffected.
      </p>
      <p>
        No side agreements have been made. Amendments and supplementary
        provisions to these terms and conditions must be made in text form in
        order to be legally effective. Electronic form (e-mail) shall be
        sufficient. This also applies to the amendment of the text form
        requirement.
      </p>
      <p>
        The applicability of any general terms and conditions of the Licensee is
        expressly excluded.
      </p>
      <p>
        German law shall apply exclusively with the exclusion of the UN
        Convention on Contracts for the International Sale of Goods (CISG). The
        place of jurisdiction for all disputes is Düsseldorf.
      </p>
      <p>Status January {year}</p>
      <p>Copyright © {year} SellerLogic. All rights reserved.</p>
    </div>
  )
}

export default Terms
