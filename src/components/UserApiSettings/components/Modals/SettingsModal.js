import React from "react"
import PropTypes from "prop-types"
import { withRouter } from "react-router-dom"
import { Alert, Box, Typography } from "@develop/fe-library"
import { ROUTES } from "@develop/fe-library/dist/routes"

import l from "utils/intl"

import FormattedMessage from "components/FormattedMessage"
import { PrimaryButton } from "components/shared/Buttons"
import Modal from "components/shared/Modal"

const SettingsModal = ({ history, closeModal }) => {
  const handleSetup2FA = () =>
    history.push(ROUTES.GENERAL_ROUTES.PATH_USER_SETTINGS_SECURITY_SETTINGS)

  return (
    <Modal
      cancelText={l("Cancel")}
      title={l("API settings")}
      visible={true}
      width={620}
      onCancel={closeModal}
    >
      <Box flexDirection="column" gap="l">
        <Typography variant="--font-headline-3">
          <FormattedMessage id="Two-factor authentication" />
        </Typography>

        <Alert
          alertType="warning"
          message={l(
            "The global settings require you to enable two-factor authentication for your account."
          )}
        />

        <div>
          <PrimaryButton onClick={handleSetup2FA}>
            <FormattedMessage id="Setup two-factor authentication" />
          </PrimaryButton>
        </div>
      </Box>
    </Modal>
  )
}

SettingsModal.propTypes = {
  history: PropTypes.object.isRequired,
  closeModal: PropTypes.func.isRequired,
}

export default withRouter(SettingsModal)
