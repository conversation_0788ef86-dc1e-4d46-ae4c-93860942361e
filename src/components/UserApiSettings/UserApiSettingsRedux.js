import { connect } from "react-redux"

import customerActions from "actions/customerActions"
import customerAuthClientActions from "actions/customerAuthClientActions"
import privacyActions from "actions/privacyActions"
import staffActions from "actions/staffActions"
import webauthActions from "actions/webauthActions"

import { customerRepricerSubscriptionInfoSelector } from "selectors/customerSelectors"

import UserApiSettingsView from "components/UserApiSettings/UserApiSettingsView"

const { getCurrentCustomer: getCustomer, signApiAgreement } = customerActions
const { get2FAPassStatus, pass2FA } = staffActions
const {
  add: addCustomerAuthClient,
  get: getCustomerAuthClient,
  regenerate: regenerateCustomerAuthClient,
  update: updateCustomerAuthClient,
} = customerAuthClientActions
const { onDownloadUserApi } = privacyActions
const { get: getDeviceList, validate } = webauthActions

const mapStateToProps = (state) => {
  const {
    customer: {
      customer: { isApiAgreementSigned, id },
    },
    staff: {
      currentUser: {
        user: { is_enabled_2fa: is2FAEnabled },
      },
      is2FAPassed,
    },
    customerAuthClient: { authClient },
    translations: { locale },
    webauth: { deviceList },
  } = state

  const { isApiAllowed, isUpgradeRepricerSubscription } =
    customerRepricerSubscriptionInfoSelector(state)

  return {
    customerId: id,
    locale,
    authClient,
    is2FAEnabled,
    is2FAPassed,
    isApiAgreementSigned,
    deviceList,
    isApiAllowed,
    isUpgradeRepricerSubscription,
  }
}

export default connect(mapStateToProps, {
  addCustomerAuthClient,
  get2FAPassStatus,
  getCustomer,
  getCustomerAuthClient,
  pass2FA,
  regenerateCustomerAuthClient,
  signApiAgreement,
  updateCustomerAuthClient,
  onDownloadUserApi,
  getDeviceList,
  validate,
})(UserApiSettingsView)
