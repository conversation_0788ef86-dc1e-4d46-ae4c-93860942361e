import { connect } from "react-redux"

import AmazonMarketplaceContainer from "components/AmazonMarketplace/AmazonMarketplaceContainer"

import amazonMarketplaceActions from "actions/amazonMarketplacesActions"
import { amazonMarketplaceSelector } from "selectors/amazonMarketplaceSelectors"
import tableSettingsActions from "actions/tableSettingsActions"

const { get: getAmazonMarketplaces } = amazonMarketplaceActions
const { get } = tableSettingsActions

const mapStateToProps = (state) => ({
  initialValues: state.amazonMarketplaces.initialValues,
  amazonMarketplace: amazonMarketplaceSelector(state),
  searchOptions: state.amazonMarketplaces.searchOptions,
  totalCount: state.amazonMarketplaces.totalCount,
})

const mapDispatchToProps = (dispatch) => ({
  getAmazonMarketplaces: (searchOptions) =>
    dispatch(getAmazonMarketplaces(searchOptions)),
  getTableSettings: (key, callback) => dispatch(get(key, callback)),
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(AmazonMarketplaceContainer)
