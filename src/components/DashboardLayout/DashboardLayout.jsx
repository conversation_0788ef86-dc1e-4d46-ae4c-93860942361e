import React from "react"
import { Box } from "@develop/fe-library"

import { DashboardCards } from "./components/DashboardCards"

export const DashboardLayout = ({ children }) => {
  return (
    <Box height="100%" width="100%" flexDirection="column">
      <Box
        flexGrow={1}
        mSM={{ flexDirection: "column" }}
        dMD={{ flexDirection: "row" }}
      >
        <Box
          justify="center"
          mSM={{
            width: "100%",
            flexGrow: 1,
            hasBorder: {
              bottom: true,
              right: false,
            },
          }}
          dMD={{
            flexGrow: true,
            hasBorder: {
              bottom: false,
              right: true,
            },
          }}
        >
          {children}
        </Box>
        <Box
          mSM={{ width: "100%", flexGrow: 0 }}
          dMD={{
            width: "320px",
            minWidth: "320px",
            maxWidth: "320px",
          }}
        >
          <DashboardCards />
        </Box>
      </Box>
    </Box>
  )
}
