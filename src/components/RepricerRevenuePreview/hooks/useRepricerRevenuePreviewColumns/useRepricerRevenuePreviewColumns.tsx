import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { ROUTES } from "@develop/fe-library/dist/routes"
import {
  checkIsNonEmptyString,
  checkIsNumber,
} from "@develop/fe-library/dist/utils"

import { permissionsSelector } from "selectors/userSelectors"

import Link from "components/shared/Link"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import {
  COLUMN_INPUT_TYPE_DATE,
  COLUMN_INPUT_TYPE_SELECT,
} from "components/TableGridLayout/TableGridLayoutView"

import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"
import ln from "utils/localeNumber"

import { NO_TEXT, NOT_AVAILABLE, YES_TEXT } from "consts/common"
import { SUBSCRIPTION_TYPES } from "consts/repricerSubscription"

import { RepricerCustomerPlan } from "types/Models"

export const useRepricerRevenuePreviewColumns = () => {
  const { amazonCustomerAccountList: canViewAmazonCustomerAccounts } =
    useSelector(permissionsSelector)

  return useMemo(
    () => [
      {
        title: "Customer ID",
        dataIndex: "customer_id",
        key: "customer_id",
        sorter: true,
        type: "input",
        width: 68,
        render: (customer_id) =>
          customer_id ? (
            <ExportValue>
              {canViewAmazonCustomerAccounts ? (
                // @ts-expect-error
                <Link
                  internal={false}
                  styleType="primary"
                  target="_blank"
                  text={customer_id}
                  url={`${ROUTES.GENERAL_ROUTES.PATH_AMAZON_ACCOUNTS}?customerID=${customer_id}`}
                  variant="textSmall"
                />
              ) : (
                customer_id
              )}
            </ExportValue>
          ) : null,
      },
      {
        title: "Customer title",
        dataIndex: "customerTitle",
        key: "customerTitle",
        sorter: true,
        type: "input",
        width: 200,
      },
      {
        title: "Active Subscription",
        dataIndex: "has_active_subscription",
        key: "has_active_subscription",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        render: (value) => {
          return <ExportValue>{value ? l(YES_TEXT) : l(NO_TEXT)}</ExportValue>
        },
      },
      {
        title: "Subscription type",
        dataIndex: "type",
        key: "type",
        sorter: true,
        width: 100,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        render: (type) => {
          return <ExportValue>{SUBSCRIPTION_TYPES[type]}</ExportValue>
        },
      },
      {
        title: "Subscription period",
        dataIndex: "duration",
        key: "duration",
        sorter: true,
        width: 100,
        type: COLUMN_INPUT_TYPE_SELECT,
        ellipsis: true,
        render: (duration, data: RepricerCustomerPlan) => {
          const durationUnit: string | undefined =
            data?.repricerSubscriptionInfo?.durationUnit

          return (
            <ExportValue>
              {durationUnit
                ? l(
                    `{duration, plural, one {# ${durationUnit}} other {# ${durationUnit}s}}`,
                    { duration }
                  )
                : duration}
            </ExportValue>
          )
        },
      },
      {
        title: "Next contract",
        dataIndex: "auto_renew",
        key: "auto_renew",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        render: (value) => {
          return <ExportValue>{value ? l(YES_TEXT) : l(NO_TEXT)}</ExportValue>
        },
      },
      {
        title: "On-Demand Product Optimizations",
        dataIndex: "onDemandProductOptimizations",
        key: "onDemandProductOptimizations",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        render: (value) => {
          return <ExportValue>{value ? l(YES_TEXT) : l(NO_TEXT)}</ExportValue>
        },
      },
      {
        title: "Revenue",
        dataIndex: "revenue",
        key: "revenue",
        width: 200,
        sorter: true,
        type: "input",
        render: (revenue) => {
          return (
            <ExportValue>
              {checkIsNonEmptyString(revenue) ? `${revenue} €` : null}
            </ExportValue>
          )
        },
      },
      {
        title: "Subscription start date",
        dataIndex: "subscription_start_date",
        key: "subscription_start_date",
        width: 90,
        sorter: true,
        type: COLUMN_INPUT_TYPE_DATE,
        ellipsis: true,
        disabledDate: () => false,
        render: (subscription_start_date) => (
          <ExportValue>
            {subscription_start_date
              ? convertToLocalDate(subscription_start_date)
              : l(NOT_AVAILABLE)}
          </ExportValue>
        ),
      },
      {
        title: "Subscription end date",
        dataIndex: "subscription_end_date",
        key: "subscription_end_date",
        width: 90,
        sorter: true,
        type: COLUMN_INPUT_TYPE_DATE,
        ellipsis: true,
        disabledDate: () => false,
        render: (subscription_end_date) => (
          <ExportValue>
            {subscription_end_date
              ? convertToLocalDate(subscription_end_date)
              : l(NOT_AVAILABLE)}
          </ExportValue>
        ),
      },
      {
        title: "Start date",
        dataIndex: "date_start",
        key: "date_start",
        width: 90,
        sorter: true,
        type: COLUMN_INPUT_TYPE_DATE,
        ellipsis: true,
        disabledDate: () => false,
        render: (date_start) => (
          <ExportValue>
            {date_start ? convertToLocalDate(date_start) : l(NOT_AVAILABLE)}
          </ExportValue>
        ),
      },
      {
        title: "End date",
        dataIndex: "date_finish",
        key: "date_finish",
        width: 90,
        sorter: true,
        type: COLUMN_INPUT_TYPE_DATE,
        ellipsis: true,
        disabledDate: () => false,
        render: (date_finish) => (
          <ExportValue>
            {date_finish ? convertToLocalDate(date_finish) : l(NOT_AVAILABLE)}
          </ExportValue>
        ),
      },
      {
        title: "Product optimizations",
        dataIndex: "size",
        key: "size",
        width: 100,
        sorter: true,
        type: "input",
        render: (value) => {
          return (
            <ExportValue>{checkIsNumber(value) ? ln(value) : null}</ExportValue>
          )
        },
      },
      {
        title: "Used product optimizations",
        dataIndex: "units_used",
        key: "units_used",
        width: 100,
        sorter: true,
        type: "input",
        render: (value) => {
          return (
            <ExportValue>{checkIsNumber(value) ? ln(value) : null}</ExportValue>
          )
        },
      },
      {
        title: "Total product optimizations",
        dataIndex: "units_limit",
        key: "units_limit",
        width: 100,
        sorter: true,
        type: "input",
        render: (value) => {
          return (
            <ExportValue>{checkIsNumber(value) ? ln(value) : null}</ExportValue>
          )
        },
      },
      {
        title: "Paid subscription",
        dataIndex: "is_paid",
        key: "is_paid",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT,
        render: (value) => {
          return <ExportValue>{value ? l(YES_TEXT) : l(NO_TEXT)}</ExportValue>
        },
      },
    ],
    [canViewAmazonCustomerAccounts]
  )
}
