import React, { <PERSON>actN<PERSON>, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { <PERSON><PERSON>, Box, Checkbox, Modal, Typography } from "@develop/fe-library"
import { checkIsArray } from "@develop/fe-library/dist/utils"

import customerActions from "actions/customerActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { amazonAccountMarketplacesSelector } from "selectors/amazonAccountsSelectors"
import { isTransferToNewPricingModalVisibleSelector } from "selectors/customerSelectors"
import { repricerSubscriptionPlansSelector } from "selectors/repricerSubscriptionSelectors"
import { localeSelector } from "selectors/userSelectors"

import { useStartSetupWizard } from "hooks"

import l from "utils/intl"
import { getTemplLink } from "utils/localesLinks"

import { COMPANY_NAME } from "consts/general"
import { OFFER_TYPES, PRODUCT_NAMES, PRODUCTS } from "consts/product"

import styles from "../../transferToNewPricing.module.scss"

const { setIsTransferToNewPricingModalVisible } = customerActions
const { toggleModal: changeStep } = moduleSetupWizardActions

export const TransferToNewPricingModal = () => {
  const dispatch = useDispatch()

  const [isTermsAccepted, setIsTermsAccepted] = useState(false)

  const amazonAccountMarketplaces = useSelector(
    amazonAccountMarketplacesSelector
  )
  const { last } = useSelector(repricerSubscriptionPlansSelector)
  const locale = useSelector(localeSelector)
  const isTransferToNewPricingModalVisible = useSelector(
    isTransferToNewPricingModalVisibleSelector
  )

  const startSetupWizard = useStartSetupWizard()

  if (!isTransferToNewPricingModalVisible) {
    return null
  }

  const handleModalClose = (): void => {
    dispatch(setIsTransferToNewPricingModalVisible(false))
  }

  const openRepricerSubscriptionsHandler = (): void => {
    const isRepricerTrial: boolean =
      !checkIsArray(amazonAccountMarketplaces) && last === null

    isRepricerTrial
      ? startSetupWizard({
          productName: PRODUCTS.repricer,
          forceUseNewPricing: true,
        })
      : dispatch(
          changeStep(true, "repricerSubscriptions", PRODUCTS.repricer, {
            connectAccountId: "",
            isAccountSettings: false,
            isOnlySubscriptionChoice: true,
          })
        )

    handleModalClose()
  }

  return (
    <Modal
      visible
      cancelButtonText={l("Cancel")}
      okButtonText={l("Choose plan")}
      width="--modal-size-m"
      okButtonProps={{
        disabled: !isTermsAccepted,
      }}
      title={l(
        "Transition to the New {companyName} {productName} subscription model",
        {
          companyName: COMPANY_NAME,
          productName: PRODUCT_NAMES[PRODUCTS.repricer],
        }
      )}
      onClose={handleModalClose}
      onOk={openRepricerSubscriptionsHandler}
    >
      <Box flexDirection="column" gap="m">
        <Typography variant="--font-body-text-7">
          {l(
            "As the next step, select a subscription plan that best suits your needs. Here are the primary change points:"
          )}
        </Typography>

        <Box
          className={styles.coloredList}
          component="ul"
          flexDirection="column"
          gap="s"
          margin="0 0 m"
          paddingLeft="l"
        >
          <li>
            <Typography variant="--font-body-text-7">
              {l("{productName} now has different subscription plans", {
                productName: PRODUCT_NAMES[PRODUCTS.repricer],
              })}
            </Typography>
          </li>
          <li>
            <Typography variant="--font-body-text-7">
              {l(
                "Any subscription plan can be set to monthly or annual billing"
              )}
            </Typography>
          </li>
          <li>
            <Typography variant="--font-body-text-7">
              {l(
                "Instead of a subscription for each marketplace, there is only one subscription, which can include unlimited accounts, marketplaces, products & {b2b} and {b2c} business",
                {
                  b2b: OFFER_TYPES.b2b,
                  b2c: OFFER_TYPES.b2c,
                }
              )}
            </Typography>
          </li>
          <li>
            <Typography variant="--font-body-text-7">
              {l(
                "Now, you are paying only for Product Optimizations and no longer for the number of uploaded SKUs per marketplace. Product Optimizations refers to the process where the price of an active product offer (SKU) on stock is optimized no matter how often the price is changed a day"
              )}
            </Typography>
          </li>
          <li>
            <Typography variant="--font-body-text-7">
              {l(
                "To determine the right tariff, we recommend we analyze the number of your active product optimizations with inventory"
              )}
            </Typography>
          </li>
          <li>
            <Typography variant="--font-body-text-7">
              {l(
                "You can choose a Starter Plan unless you have activated {companyName} API usage or custom user permissions. In such cases, you need to transfer to the Advanced Plan",
                {
                  companyName: COMPANY_NAME,
                }
              )}
            </Typography>
          </li>
        </Box>

        <Alert
          alertType="info"
          description={
            <Box flexDirection="column" gap="m">
              <Typography variant="--font-body-text-7">
                {l(
                  "The price of the subscription is affected by the subscription plan and the amount of Product Optimizations Limit you select."
                )}
              </Typography>
            </Box>
          }
        />

        <Typography variant="--font-body-text-7">
          {l(
            "After subscription plan selection, a summary of your selection will be shown, and the new subscription model will come into effect only after its confirmation."
          )}
        </Typography>

        <Box gap="m">
          <Checkbox onChange={setIsTermsAccepted} />

          <Typography variant="--font-body-text-7">
            {l("I accept the <a>General Terms and Conditions</a>.", {
              a: (...chunks: (string | ReactNode)[]) => (
                <a
                  href={getTemplLink(locale)}
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  {chunks}
                </a>
              ),
            })}
          </Typography>
        </Box>
      </Box>
    </Modal>
  )
}
