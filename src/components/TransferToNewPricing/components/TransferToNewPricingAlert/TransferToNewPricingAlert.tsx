import React, { ReactNode } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Alert } from "@develop/fe-library"

import customerActions from "actions/customerActions"

import { languageSelector } from "selectors/translationsSelectors"

import { RestrictedButtonPopover } from "components/shared/Buttons"

import { useRedirectToContactPage } from "hooks"

import l from "utils/intl"
import { AMAZON_DYNAMIC_REPRICER_TOOL_LINKS } from "utils/localesLinks"
import { replaceLanguageCode } from "utils/replaceLanguageCode"

import { permissionKeys, restrictPopoverMessages } from "consts"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"

const { setIsTransferToNewPricingModalVisible } = customerActions

export const TransferToNewPricingAlert = () => {
  const dispatch = useDispatch()
  const language = useSelector(languageSelector)

  const { contactUsUrl } = useRedirectToContactPage()

  const transferToNewPricingHandler = (): void => {
    dispatch(setIsTransferToNewPricingModalVisible(true))
  }

  const billingLink: string = replaceLanguageCode({
    url: "https://support.sellerlogic.com/{localeCode}/general/subscriptions-overview",
    language,
  })

  return (
    <Alert
      alertType="info"
      action={
        <RestrictedButtonPopover
          fullWidth
          managePermission={permissionKeys.amazonCustomerAccountManage}
          popoverMessage={restrictPopoverMessages.alter}
          popoverPlacement="top"
          onClick={transferToNewPricingHandler}
        >
          {l("Transfer now")}
        </RestrictedButtonPopover>
      }
      // a, span, div, it`s an all links
      description={l(
        "We've updated our pricing model to serve you better. Subscription management & account addition to {productName} is locked for the transition time. To complete your account transition, click the button below to explore and select the plan that suits you best. Please find additional information about the <a>{productName} Pricing</a> and <span>Billing</span>. If you have any questions, please <div>get in touch with us</div>.",
        {
          productName: PRODUCT_NAMES[PRODUCTS.repricer],
          a: (...chunks: (string | ReactNode)[]) => (
            <a
              href={AMAZON_DYNAMIC_REPRICER_TOOL_LINKS[language]}
              rel="noreferrer"
              target="_blank"
            >
              {chunks}
            </a>
          ),
          span: (...chunks: (string | ReactNode)[]) => (
            <a href={billingLink} rel="noreferrer" target="_blank">
              {chunks}
            </a>
          ),
          div: (...chunks: (string | ReactNode)[]) => (
            <a href={contactUsUrl} rel="noreferrer" target="_blank">
              {chunks}
            </a>
          ),
        }
      )}
    />
  )
}
