@import "assets/styles/variables.scss";

.wrapper {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
}

.container {
  align-items: center;
  display: flex;
  flex-direction: column;
  max-width: 740px;
  padding: 0 20px;
  width: 100%;
}

.errorLabel.errorLabel {
  color: var(--color-text-error);
  font-size: 72px;
  font-weight: 700;
  padding-top: 20px;
  line-height: normal;
}

.wrongLabel.wrongLabel {
  font-size: 36px;
  font-weight: 500;
  line-height: normal;
}

.errorCodeContainer {
  padding: 30px 0 0;
  text-align: center;
  width: 100%;
}

.errorCodeLabel.errorCodeLabel {
  font-weight: 700;
}

.errorCode {
  word-wrap: break-word;
}

.buttonLabel.buttonLabel {
  font-weight: 500;
}

.button {
  align-items: center;
  border: 1px solid var(--color-border-main);
  border-radius: 2px;
  color: var(--color-text-main);
  display: flex;
  height: 40px;
  justify-content: center;
  margin-top: 40px;
  padding: 4px 50px;

  &:hover {
    border-color: var(--color-int-on-active);
    color: var(--color-text-main);
  }
}

@media (max-width: $md) {
  .errorLabel.errorLabel {
    font-size: 48px;
  }

  .wrongLabel.wrongLabel {
    font-size: 24px;
    font-weight: 700;
  }
}

@media (max-width: $xs) {
  .button {
    width: 100%;
  }

  .errorCodeLabel.errorCodeLabel {
    display: block;
    text-align: center;
  }
}
