import { useDispatch, useSelector } from "react-redux"

import customerSubscriptionActions from "actions/customerSubscriptionActions"

import {
  customerRepricerSubscriptionStatisticSelector,
  customerSubscriptionPageOptionsSelector,
  customerSubscriptionSearchOptionsSelector,
  customerSubscriptionSelector,
  filtersOptionsSelector,
} from "selectors/customerSubscriptionSelectors"

import { PRODUCTS } from "consts/product"

const {
  // @ts-ignore
  get: getCustomerSubscription,
  // @ts-ignore
  getStatistic,
  // @ts-ignore
  getSubscriptionTypes,
} = customerSubscriptionActions

export const useAdminDashboard = () => {
  const { totalCount } = useSelector(customerSubscriptionPageOptionsSelector)
  const searchOptions = useSelector(customerSubscriptionSearchOptionsSelector)
  const selectFiltersOptions = useSelector(filtersOptionsSelector)
  const dataSource = useSelector(customerSubscriptionSelector)
  const statisticData = useSelector(
    customerRepricerSubscriptionStatisticSelector
  )

  const dispatch = useDispatch()

  const getCustomerSubscriptionHandler = <Type,>(searchOptions: Type) => {
    return dispatch(getCustomerSubscription(searchOptions))
  }

  const getAdditionalData = () => {
    dispatch(getStatistic())
    dispatch(getSubscriptionTypes(PRODUCTS.repricer))
  }

  return {
    totalCount,
    searchOptions,
    selectFiltersOptions,
    dataSource,
    statisticData,
    getCustomerSubscriptionHandler,
    getAdditionalData,
  }
}
