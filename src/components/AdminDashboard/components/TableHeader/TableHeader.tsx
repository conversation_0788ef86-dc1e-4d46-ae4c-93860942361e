import React from "react"
import type { Tab } from "@develop/fe-library"
import { Box, Tabs } from "@develop/fe-library"

import l from "utils/intl"

import { OFFER_TYPES } from "consts/product"

import { TableHeaderTabContent } from "../TableHeaderTabContent"

import type { TableHeaderProps } from "./TableHeaderTypes"

export const TableHeader = ({
  b2b,
  b2c,
  avg_paid_contract_value,
  avg_paid_contract_value_discounted,
  avg_paid_customer_value,
  avg_paid_customer_value_discounted,
  avg_paid_subscriptions,
  avg_trial_subscriptions,
  count_paid_customers,
  count_paid_subscriptions,
  count_trial_customers,
  count_trial_subscriptions,
  total_amount,
  total_amount_discounted,
}: TableHeaderProps) => {
  const tabsitems: Tab[] = [
    {
      contents: (
        <TableHeaderTabContent
          avg_paid_contract_value={avg_paid_contract_value}
          avg_paid_customer_value={avg_paid_customer_value}
          avg_paid_subscriptions={avg_paid_subscriptions}
          avg_trial_subscriptions={avg_trial_subscriptions}
          count_paid_customers={count_paid_customers}
          count_paid_subscriptions={count_paid_subscriptions}
          count_trial_customers={count_trial_customers}
          count_trial_subscriptions={count_trial_subscriptions}
          total_amount={total_amount}
          total_amount_discounted={total_amount_discounted}
          avg_paid_contract_value_discounted={
            avg_paid_contract_value_discounted
          }
          avg_paid_customer_value_discounted={
            avg_paid_customer_value_discounted
          }
        />
      ),
      icon: "icnSync",
      key: 0,
      label: l("All"),
    },
    {
      contents: <TableHeaderTabContent {...b2b} />,
      icon: "icnB2B",
      key: 1,
      label: OFFER_TYPES.b2b,
    },
    {
      contents: <TableHeaderTabContent {...b2c} />,
      icon: "icnB2C",
      key: 2,
      label: OFFER_TYPES.b2c,
    },
  ]

  return (
    <Box display="block" hasBorder={{ top: true }} paddingTop="m">
      <Tabs
        contentPadding="var(--padding-m) var(--padding-l)"
        headerPadding="l"
        items={tabsitems}
      />
    </Box>
  )
}
