import React from "react"
import moment from "moment"
import { ROUTES } from "@develop/fe-library/dist/routes"

import FormattedMessage from "components/FormattedMessage"
import Typography from "components/Typography"
import ExportValue from "components/TableGridLayout/components/ExportValue"
import Link from "components/shared/Link"
import AmountColumn from "components/AdminDashboard/components/AmountColumn"

import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"

import { OFFER_TYPES } from "consts/product"

import {
  COLUMN_INPUT_TYPE_DATE,
  COLUMN_INPUT_TYPE_SELECT,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "components/TableGridLayout/TableGridLayoutView"

export default [
  {
    title: "Customer ID",
    dataIndex: "customer_id",
    key: "customer_id",
    sorter: true,
    type: "input",
    ellipsis: true,
    width: 68,
  },
  {
    title: "Customer title",
    dataIndex: "customer",
    key: "customer",
    sorter: true,
    type: "input",
    width: 200,
  },
  {
    title: "Customer use contract?",
    dataIndex: "usePlanLabel",
    key: "use_plan",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT,
  },
  {
    title: "Amazon Marketplace",
    dataIndex: "marketplace_id",
    key: "marketplace_id",
    sorter: true,
    width: 116,
    type: COLUMN_INPUT_TYPE_SELECT,
    render: (
      marketplace_id,
      { marketplace_extension, marketplace, seller_id }
    ) => (
      <ExportValue>
        <Link
          internal={false}
          url={`https://www.amazon.${marketplace_extension}/gp/aag/main?ie=UTF8&marketplaceID=${marketplace_id}&seller=${seller_id}`}
          styleType="primary"
          variant="textSmall"
          text={marketplace}
          rel="noopener noreferrer"
          target="_blank"
        />
      </ExportValue>
    ),
  },
  {
    title: "Offer type",
    dataIndex: "offer_type",
    key: "offer_type",
    sorter: true,
    width: 90,
    type: COLUMN_INPUT_TYPE_SELECT,
    ellipsis: true,
    render: (offer_type) => {
      const offerType =
        offer_type === OFFER_TYPES.b2c ? OFFER_TYPES.b2c : OFFER_TYPES.b2b

      return <ExportValue>{offerType}</ExportValue>
    },
  },
  {
    title: `${l("Max count products")} (${moment().format("MMMM")})`,
    notLocalizeTitle: true,
    dataIndex: "count_product",
    key: "count_product",
    type: "input",
    sorter: true,
    width: 150,
    render: (
      count_product,
      {
        customer_id,
        marketplace_id,
        seller_id,
        date_start,
        locked_by_empty_payment,
      }
    ) => (
      <>
        <ExportValue>
          <Link
            internal={false}
            url={`${ROUTES.ADMIN_ROUTES.PATH_PRODUCT_ACTIVITIES}?customer_id=${customer_id}&amazon_marketplace_id=${marketplace_id}&amazonCustomerAccountTitle=${seller_id}&day=${date_start}&day_filterType=equal_to&`}
            styleType="primary"
            variant="textSmall"
            text={count_product}
            rel="noopener noreferrer"
            target="_blank"
          />
        </ExportValue>
        {locked_by_empty_payment ? (
          <ExportValue newLine={true}>
            {l("Locked (Empty payment data)")}
          </ExportValue>
        ) : null}
      </>
    ),
  },
  {
    title: "MWS information",
    dataIndex: "has_mws_errors",
    key: "has_mws_errors",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT,
    width: 200,
    render: (
      _,
      {
        count_mws_access_denied,
        first_mws_access_denied_at,
        first_mws_access_denied_error,
      }
    ) => {
      if (count_mws_access_denied > 0) {
        return (
          <>
            <ExportValue newLine={true}>{l("MWS Access Denied")}</ExportValue>
            <ExportValue newLine={true}>
              <Typography type="div" variant="textSmall">
                <FormattedMessage
                  defaultMessage="(Last count: {lastCount}, first at: {firstAt}, first error: {error})"
                  id="(Last count: {lastCount}, first at: {firstAt}, first error: {error})"
                  values={{
                    lastCount: count_mws_access_denied,
                    firstAt: convertToLocalDate(first_mws_access_denied_at),
                    error: first_mws_access_denied_error
                      ? first_mws_access_denied_error
                      : "",
                  }}
                />
              </Typography>
            </ExportValue>
          </>
        )
      }
      return null
    },
  },
  {
    title: "Contract",
    dataIndex: "periodLabel",
    key: "period",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
    width: 200,
  },
  {
    title: "Auto renew?",
    dataIndex: "autoRenewLabel",
    key: "auto_renew",
    sorter: true,
    type: COLUMN_INPUT_TYPE_SELECT,
  },
  {
    title: "€ / Month",
    dataIndex: "total_amount_discounted",
    key: "total_amount_discounted",
    width: 200,
    sorter: true,
    type: "input",
    render: (total_amount_discounted, { total_amount }) => (
      <AmountColumn
        totalAmount={total_amount}
        totalAmountDiscounted={total_amount_discounted}
      />
    ),
  },
  {
    title: "Date start",
    dataIndex: "date_start",
    key: "date_start",
    width: 90,
    min: 80,
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE,
    withDayTime: true,
    ellipsis: true,
    render: (date_start) => (
      <ExportValue>{date_start && convertToLocalDate(date_start)}</ExportValue>
    ),
  },
  {
    title: "Date finish",
    dataIndex: "date_finish",
    key: "date_finish",
    width: 90,
    min: 80,
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE,
    withDayTime: true,
    disabledDate: () => false,
    ellipsis: true,
    render: (date_finish) => (
      <ExportValue>
        {date_finish && convertToLocalDate(date_finish)}
      </ExportValue>
    ),
  },
  {
    title: "Date expired",
    dataIndex: "date_locking",
    key: "date_locking",
    width: 90,
    min: 80,
    sorter: true,
    type: COLUMN_INPUT_TYPE_DATE,
    withDayTime: true,
    disabledDate: () => false,
    ellipsis: true,
    render: (date_locking) => (
      <ExportValue>
        {date_locking && convertToLocalDate(date_locking)}
      </ExportValue>
    ),
  },
]
