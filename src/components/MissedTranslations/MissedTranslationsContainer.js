import React, { Component } from "react"
import PropTypes from "prop-types"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import { Content, Footer } from "components/Layout"
import { CardsLayout } from "components/CardsLayout"
import CardsLayoutTitle from "components/CardsLayoutTitle"
import AppHeader from "components/shared/appHeader/AppHeader"
import { GridFooter } from "components/shared/GridFooter"

import { DELETE_ITEM } from "utils/confirm"

class MissedTranslationsContainer extends Component {
  componentDidMount() {
    const { getData, searchOptions } = this.props
    const initialSearchOptions = {
      ...searchOptions,
      ...getUrlSearchParams({ locationSearch: document.location.search }),
    }
    initialSearchOptions.page = parseInt(initialSearchOptions.page)
    initialSearchOptions.pageSize = parseInt(initialSearchOptions.pageSize)

    getData(initialSearchOptions)
  }

  onSearch = ({ filter }) => {
    const { getData, searchOptions } = this.props

    getData({
      ...searchOptions,
      filter: filter,
      page: 1,
    })
  }

  onPageChange = (page) => {
    const { getData, searchOptions } = this.props

    getData({
      ...searchOptions,
      page,
    })
  }

  onPageSizeChange = (pageSize) => {
    const { getData, searchOptions } = this.props

    getData({
      ...searchOptions,
      page: 1,
      pageSize,
    })
  }

  onDelete = ({ message }) => {
    const { deleteItem, getData, searchOptions } = this.props

    deleteItem(message, () => getData(searchOptions))
  }

  onAdd = ({ message }) => {
    const { dataSource, getData, publishMessages, searchOptions } = this.props

    publishMessages(message ? [message] : dataSource.map(({ id }) => id), () =>
      getData(searchOptions)
    )
  }

  render() {
    const { dataSource, searchOptions, totalCount } = this.props

    return (
      <>
        <AppHeader title={"Missed translations"} />
        <Content>
          <CardsLayoutTitle
            buttons={[
              { icon: "icnPlus", onClick: this.onAdd, title: "Add all" },
            ]}
            initialValues={{ filter: searchOptions.filter }}
            onSearch={this.onSearch}
            searchFields={[
              {
                name: "filter",
                placeholder: "Message",
                type: "text",
              },
            ]}
            title="Missed translations"
            sortTip={false}
          />
          <CardsLayout
            hasStatusIcon={false}
            icons={[
              {
                onClick: this.onDelete,
                confirm: {
                  template: DELETE_ITEM,
                },
                title: "Delete",
                name: "icnDeleteOutlined",
              },
              {
                onClick: this.onAdd,
                title: "Add to translation",
                name: "icnFileAdd",
              },
            ]}
            items={dataSource}
          />
        </Content>
        <Footer>
          <GridFooter
            currentPage={searchOptions.page}
            onPageChange={this.onPageChange}
            onPageSizeChange={this.onPageSizeChange}
            pageSize={searchOptions.pageSize}
            tableSettingsVisible={false}
            totalResults={totalCount}
            isSettingButtonsVisible={false}
          />
        </Footer>
      </>
    )
  }
}

MissedTranslationsContainer.propTypes = {
  dataSource: PropTypes.array.isRequired,
  deleteItem: PropTypes.func.isRequired,
  getData: PropTypes.func.isRequired,
  publishMessages: PropTypes.func.isRequired,
  searchOptions: PropTypes.object.isRequired,
  totalCount: PropTypes.number,
}

export default MissedTranslationsContainer
