import { connect } from "react-redux"

import MissedTranslationsContainer from "components/MissedTranslations/MissedTranslationsContainer"

import missedTranslationsActions from "actions/missedTranslationsActions"
import { missedTranslationsSelector } from "selectors/missedTranslationsSelectors"

const {
  get: getMissedTranslations,
  delete: deleteMissedTranslation,
  publish: publishMessages,
} = missedTranslationsActions

const mapStateToProps = (state) => ({
  dataSource: missedTranslationsSelector(state),
  searchOptions: state.missedTranslations.searchOptions,
  totalCount: state.missedTranslations.totalCount,
})

const mapDispatchToProps = (dispatch) => ({
  getData: (searchOptions) => dispatch(getMissedTranslations(searchOptions)),
  deleteItem: (message, callback) =>
    dispatch(deleteMissedTranslation(message, callback)),
  publishMessages: (messages, callback) =>
    dispatch(publishMessages(messages, callback)),
})

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(MissedTranslationsContainer)
