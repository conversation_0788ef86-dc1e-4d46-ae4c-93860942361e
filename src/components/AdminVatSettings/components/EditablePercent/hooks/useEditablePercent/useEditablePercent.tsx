import React, { useState } from "react"
import { useForm } from "react-hook-form"
import type { FormItem } from "@develop/fe-library"
import { useFormReset } from "@develop/fe-library/dist/hooks"
import {
  buildFormSubmitFailureCallback,
  checkIsNumber,
} from "@develop/fe-library/dist/utils"
import { yupResolver } from "@hookform/resolvers/yup"

import { RestrictedButtonPopover } from "components/shared/Buttons"

import l from "utils/intl"

import { restrictPopoverMessages } from "consts"

import { buildSchema } from "./utils"

import { EDITABLE_PERCENT_FIELDS } from "./consts"

import type {
  EditablePercentForm,
  UseEditablePercentProps,
} from "./UseEditablePercentTypes"

export const useEditablePercent = ({
  onSave,
  value,
  managePermission,
}: UseEditablePercentProps) => {
  const [isDropdownVisible, setIsDropdownVisible] = useState(false)

  const form = useForm<EditablePercentForm>({
    defaultValues: { [EDITABLE_PERCENT_FIELDS.value]: value },
    mode: "onChange",
    resolver: yupResolver<EditablePercentForm>(buildSchema()),
  })

  const {
    reset,
    handleSubmit,
    setError,
    setFocus,
    formState: { isDirty, isSubmitting },
  } = form

  const isSaveButtonDisabled: boolean = isSubmitting || !isDirty

  useFormReset({
    reset,
    initialValues: { [EDITABLE_PERCENT_FIELDS.value]: value },
  })

  const handleSave = handleSubmit(async ({ vat_value: vatValue }) => {
    if (!checkIsNumber(vatValue)) return

    await onSave({
      value: vatValue,
      failureCallback: buildFormSubmitFailureCallback<EditablePercentForm>({
        setError,
      }),
      successCallback: handleClose,
    })
  })

  const handleClose = (): void => {
    setIsDropdownVisible(false)
    reset()
  }

  const handleOpen = (): void => {
    setIsDropdownVisible(true)
  }

  const formItems: FormItem[] = [
    {
      type: "numeric",
      name: EDITABLE_PERCENT_FIELDS.value,
      inputProps: {
        isFullWidth: true,
        isNegativeAllowed: false,
        isDecimalAllowed: false,
      },
      gridItemProps: { always: 12 },
    },
    {
      type: "action",
      label: l("Cancel"),
      actionProps: {
        variant: "secondary",
        iconOnly: true,
        icon: "icnClose",
        onClick: handleClose,
      },
      gridItemProps: { always: 6 },
    },
    {
      type: "component",
      component: (
        <RestrictedButtonPopover
          iconOnly
          disabled={isSaveButtonDisabled}
          icon="icnCheck"
          managePermission={managePermission}
          popoverMessage={restrictPopoverMessages.alter}
          popoverPlacement="top"
          variant="primary"
          onClick={handleSave}
        />
      ),

      gridItemProps: { always: 6 },
    },
  ]

  const handleVisibilityChange = (isVisible: boolean): void => {
    if (!isVisible) {
      return
    }

    setFocus(EDITABLE_PERCENT_FIELDS.value)
  }

  return {
    form,
    formItems,
    isDropdownVisible,
    handleClose,
    handleOpen,
    handleVisibilityChange,
  }
}
