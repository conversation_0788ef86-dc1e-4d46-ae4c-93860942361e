import React from "react"

import { LostAmazonLinks as LostAmazonLinksTableSettings } from "initialState/tableSettings"

import CardsLayoutModal from "components/CardsLayoutModal"
import tableColumns from "components/LostAmazonLinks/lostAmazonLinksColumns"
import { TableWrapper } from "components/shared/TableWrapper"

import { useLostAmazonLinks } from "./hooks"

const PAGE_NAME = "Amazon links"

export const LostAmazonLinks = () => {
  const {
    canManage,
    dataSource,
    initialValues,
    modalVisible,
    searchOptions,
    selectFiltersOptions,
    totalCount,
    getPayments,
    editIconHandler,
    getAdditionalData,
    createIconHandler,
    closeModalHandler,
    submitModalHandler,
  } = useLostAmazonLinks()

  return (
    <>
      <TableWrapper
        // @ts-ignore
        isNeedSort
        actionsColumnWidth={62}
        componentTableSettings={LostAmazonLinksTableSettings}
        dataSource={dataSource}
        getAdditionalData={getAdditionalData}
        getData={getPayments}
        pageTableSettings={LostAmazonLinksTableSettings}
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableColumns={tableColumns}
        tableGridTitle={PAGE_NAME}
        tableHeaderTitle={PAGE_NAME}
        totalCount={totalCount}
        tableButtons={[
          {
            checkAvailability: () => canManage,
            icon: "icnPlus",
            title: "Create",
            onClick: createIconHandler,
            type: "primary",
          },
        ]}
        tableIcons={[
          {
            onClick: editIconHandler,
            title: "Modify",
            name: "icnEdit",
          },
        ]}
      />

      {modalVisible ? (
        // @ts-ignore
        <CardsLayoutModal
          canSubmitAfterChange
          withCloseConfirm
          disabled={!canManage}
          initialValues={initialValues}
          width={450}
          controls={[
            {
              allowClear: false,
              name: "case_type",
              options: selectFiltersOptions.case_type,
              placeholder: "Case type",
              type: "select",
              required: true,
            },
            {
              allowClear: false,
              name: "marketplace_id",
              options: selectFiltersOptions.marketplace_id,
              placeholder: "Marketplace",
              type: "select",
              required: true,
            },
            {
              name: "link",
              placeholder: "Link",
              type: "text",
              required: true,
            },
          ]}
          title={
            initialValues.link ? "Modify Amazon link" : "Create Amazon link"
          }
          onClose={closeModalHandler}
          onSubmit={submitModalHandler}
        />
      ) : null}
    </>
  )
}
