import React from "react"
import Typography from "components/Typography"
import ExportValue from "components/TableGridLayout/components/ExportValue"

export default [
  {
    title: "Case type",
    dataIndex: "caseTypeLabel",
    key: "case_type",
    sorter: true,
    type: "select",
    width: 200,
  },
  {
    title: "Marketplace",
    dataIndex: "marketplaceLabel",
    key: "marketplace_id",
    sorter: true,
    type: "select",
    width: 200,
  },
  {
    title: "Link",
    dataIndex: "link",
    key: "link",
    render: (text) => (
      <Typography type="div" variant="textSmall" style={{ textAlign: "left" }}>
        <ExportValue>{text}</ExportValue>
      </Typography>
    ),
    sorter: true,
    type: "input",
    width: 1000,
  },
]
