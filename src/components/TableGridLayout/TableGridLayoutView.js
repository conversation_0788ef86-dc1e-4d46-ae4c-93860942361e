import React, { Fragment, useEffect, useMemo, useRef, useState } from "react"
import { <PERSON> } from "react-router-dom"
import withSizes from "react-sizes"
import { Empty, Input, Select, Table } from "antd"
import { Al<PERSON>, Box, ButtonPopover, Typography } from "@develop/fe-library"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"
import cn from "classnames"
import debounce from "lodash/debounce"
import moment from "moment"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import withClearAll from "components/hocs/withClearAll/withClearAll"
import withScrollRerender from "components/hocs/withScrollRerender/withScrollRerender"
import { RestrictedIconPopover } from "components/Restrict"
import { RestrictedButtonPopover } from "components/shared/Buttons"
import CircularSpinner from "components/shared/CircularSpinner"
import DatePicker, {
  MonthPicker,
  removeOperator,
} from "components/shared/DatePicker"
import EditableCell from "components/shared/EditableCell"
import withOutlineLabel from "components/shared/GridInputWithOutlineLabel"
import MultiSelectWithoutInput from "components/shared/MultiSelectWithoutInput"
import styles from "components/TableGridLayout/tableGridLayout.module.scss"

import usePrevious from "hooks/usePrevious"

import { checkIsArray } from "utils/arrayHelpers"
import { getBreakpoint, lowerThan, SM } from "utils/breakpoints"
import { getIsShowClearButton } from "utils/clearAllUtils"
import { setConfirm } from "utils/confirm"
import { convertStringDateToMoment } from "utils/dateConverter"
import l from "utils/intl"
import ln from "utils/localeNumber"
import { removePrefixFromObjectKeys } from "utils/objectKeyPrefixProcessor"
import { screenHaveTouch } from "utils/touchHelper"

import { restrictPopoverMessages } from "consts"
import { DATE_MONTH } from "consts/date"

import ExportGridModal from "./components/ExportGridModal"
import ExportValue from "./components/ExportValue"
import NumericInputFilter from "./components/NumericInputFilter"
import TableGridSortDropdown from "./components/TableGridSortDropdown/TableGridSortDropdown"
import TableResizeHoverIcons from "./components/TableResizeHoverIcons/TableResizeHoverIcons"
import withGridFiltersNav from "./components/withGridFiltersNav/withGridFiltersNav"

import { Restrict } from "../Restrict"

import "components/TableGridLayout/tableGridLayout.scss"

const { Option } = Select

export const LOAD_TD_KEY = "__load__"
export const COLUMN_INPUT_TYPE_ACTION = "action"
export const COLUMN_INPUT_TYPE_DATE = "date"
export const COLUMN_INPUT_TYPE_SELECT = "select"
export const COLUMN_INPUT_TYPE_INPUT = "input"
export const COLUMN_INPUT_TYPE_NUMBER = "number"
export const COLUMN_INPUT_TYPE_SELECT_WITH_SEARCH = "selectWithSearch"
export const COLUMN_INPUT_TYPE_MONTH = "month"
export const COLUMN_INPUT_TYPE_SELECT_WITH_FILTER = "selectWithFilter"
export const COLUMN_INPUT_TYPE_SELECT_MULTIPLE = "multySelect"
export const COLUMN_INPUT_TYPE_DATE_RANGE = "dateRange"
export const COLUMN_TYPE_STATE_ONLY = "stateOnly"

const disabledHandler = () => {
  return null
}

const InputWithNav = withGridFiltersNav(withOutlineLabel(withClearAll(Input)))
const NumericInputWithNav = withGridFiltersNav(
  withOutlineLabel(withClearAll(NumericInputFilter))
)
const SelectWithNav = withGridFiltersNav(
  withScrollRerender(withOutlineLabel(Select), "select")
)
const MultiSelectWithNav = withGridFiltersNav(
  withScrollRerender(withOutlineLabel(MultiSelectWithoutInput), "select")
)
const DatePickerWithNav = withGridFiltersNav(
  withScrollRerender(withOutlineLabel(DatePicker))
)
const MonthRangePickerWithNav = withGridFiltersNav(
  withScrollRerender(withOutlineLabel(MonthPicker))
)

const getIconsColumn = (
  buttons,
  isAdditionalButtonContainerStyle,
  icons,
  actionColumnWidth = "auto",
  actionsColumnClassName = ""
) => {
  return {
    className: `${styles.actionsColumn} ${actionsColumnClassName}`,
    key: "actions",
    width: actionColumnWidth,
    title: checkIsArray(buttons) ? (
      <div
        className={cn(styles.buttonContainer, "action-column", {
          [styles.additionalButtonContainer]: isAdditionalButtonContainerStyle,
        })}
      >
        {buttons
          .filter(
            ({ checkAvailability }) => !checkAvailability || checkAvailability()
          )
          .map(
            (
              {
                icon,
                title: titleProp,
                type = "primary",
                placement,
                popoverPlacement,
                viewPermission = true,
                customTitle = "",
                children,
                ...props
              },
              index
            ) => {
              const content = titleProp && l(titleProp)
              const title = !icon && (customTitle || content)

              const variant = type === "primary" ? "primary" : "secondary"

              return (
                <RestrictedButtonPopover
                  key={index}
                  {...props}
                  className={styles.headButton}
                  content={customTitle || content}
                  icon={icon}
                  iconOnly={!children}
                  placement={popoverPlacement || placement}
                  type={type}
                  variant={variant}
                  viewPermission={viewPermission}
                >
                  {children}
                </RestrictedButtonPopover>
              )
            }
          )}
      </div>
    ) : null,
    type: "action",
    render: (value, item) => (
      <>
        {icons
          ?.filter(
            ({ checkAvailability }) =>
              !checkAvailability || checkAvailability(item)
          )
          ?.map(
            (
              {
                className = "",
                onClick,
                name,
                title,
                placement,
                url,
                openInNewTab,
                urlOnClick,
                confirm,
                disabledIcon,
                viewPermission = true,
                managePermission,
                popoverMessage,
                customViewRestriction,
                customManageRestriction,
                customPopoverModifier,
              },
              i
            ) => {
              const link = url && url(item)
              const WrapperComponent = link ? Link : Fragment
              const wrapperProps = link
                ? { className: styles.iconLink, key: i, to: link }
                : { key: i }

              if (openInNewTab) {
                wrapperProps.target = "_blank"
              }

              const iconClickHandler = () => {
                if (link) {
                  urlOnClick && urlOnClick()

                  return
                } else if (confirm) {
                  const _confirm = {
                    ...confirm,
                    onOk: () =>
                      confirm.onOk
                        ? confirm.onOk(item)
                        : onClick && onClick(item),
                    okText: confirm.okText || l(title),
                  }

                  if (!confirm.template && !_confirm.message) {
                    _confirm.message = l("Are you sure you want to proceed?")
                  }
                  setConfirm(_confirm)
                } else {
                  onClick && onClick(item)
                }
              }

              return (
                <WrapperComponent {...wrapperProps}>
                  <RestrictedIconPopover
                    className={`${styles.icon}  ${className}`}
                    content={title && l(title)}
                    customManageRestriction={customManageRestriction}
                    customPopoverModifier={customPopoverModifier}
                    customViewRestriction={customViewRestriction}
                    isDisabled={disabledIcon}
                    managePermission={managePermission}
                    name={name}
                    placement={placement}
                    popoverMessage={popoverMessage}
                    size="--icon-size-3"
                    viewPermission={viewPermission}
                    onClick={iconClickHandler}
                  />
                </WrapperComponent>
              )
            }
          )}
      </>
    ),
  }
}

const renderRowCell = (text, record, index, column, columnKey) => {
  let className = styles.rowCell
  let cellTextStyles = {}

  if (column.dataIndex === columnKey && !column.ellipsis) {
    className = ""
  }

  if (column.dataIndex === columnKey && column.cellTextStyles) {
    cellTextStyles = column.cellTextStyles
  }

  if (text === LOAD_TD_KEY) {
    return <CircularSpinner size="small" />
  }

  if (column.isNumber) {
    if (column.numberSettings) {
      const { fixedTo, ...numberSettings } = column.numberSettings

      text = ln(text, fixedTo, { ...numberSettings })
    } else {
      text = ln(text)
    }
  }

  return (
    <Typography
      className={cn(className, column.className)}
      color="--color-text-main"
      style={cellTextStyles}
      title={text}
      variant="--font-body-text-9"
    >
      <ExportValue>{text}</ExportValue>
    </Typography>
  )
}

const renderEditableRowCell =
  (key, control, onSave, permission) => (text, record) => {
    const { checkIfEditable, canDisableSubmitButton, onBuildCustomOptions } =
      control

    return (
      <Restrict
        managePermission={permission}
        popoverMessage={restrictPopoverMessages.alter}
      >
        {(_isDisabled) => {
          const isDisabled =
            _isDisabled || (checkIfEditable && !checkIfEditable(record))

          if (isDisabled) {
            return (
              <Typography
                className={styles.rowCell}
                color="--color-text-main"
                title={text}
                variant="--font-body-text-9"
              >
                <ExportValue>{text}</ExportValue>
              </Typography>
            )
          }

          return (
            <EditableCell
              canDisableSubmitButton={canDisableSubmitButton}
              control={control}
              fieldName={key}
              initialValues={{ id: record.id, [key]: record[key] }}
              text={text}
              onBuildCustomOptions={onBuildCustomOptions?.(record)}
              onSave={onSave}
            />
          )
        }}
      </Restrict>
    )
  }

const TableGridLayoutView = ({
  isPage = true,
  actionsColumn,
  actionsColumnWidth,
  breakpoint,
  buttons,
  isAdditionalButtonContainerStyle,
  columns,
  data,
  defaultFilterValues,
  additionalFilterValues,
  settingsColumns,
  gridLayoutHeight,
  icons,
  onChange,
  onChangeFilter: onChangeFilterOutside,
  onResize,
  setGridRef,
  onSaveCell,
  onUpdateFilter,
  overflowX,
  rowKey,
  selectFiltersOptions,
  title,
  className,
  loading,
  onRow,
  isDivided,
  summaryRow,
  searchOptions,
  hideAccounts,
  debounceTime,
  withExport,
  exportParseFromColumn,
  exportNotParseLastRows,
  exportModalMessage,
  tableHeader,
  actionsColumnClassName,
  header,
  rowSelection,
  containerClassName = "",
  showWarning,
  tableLayout = "fixed",
  isExportGridModalVisible = null,
  onExportModalClose,
  exportFileName = null,
  isExportRestricted = false,
  exportRestrictMessage = l(restrictPopoverMessages.action),
  headerRightSide = null,
  filterPrefix = "",
  urlFiltersWhiteList,
  isDisabledExportIcon = false,
  customTextPopoverExportIcon = "",
}) => {
  const initialFilterValues = additionalFilterValues || defaultFilterValues

  const [showExportGrid, setExportGridVisibility] = useState(false)
  const [filterValues, setFilterValues] = useState(initialFilterValues)
  const [needScroll, setNeedScroll] = useState(false)
  const [horisontalScroll, setHorisontalScroll] = useState(0)

  const prev = usePrevious({ searchOptions, hideAccounts })
  const memoizedPrev = useMemo(() => {
    return prev
  }, [])

  const containerRef = useRef()
  const onChangeFilterWithDebounce = useRef()

  const isCustomOpenExportModal =
    !withExport && typeof isExportGridModalVisible === "boolean"

  const handleExportGridVisible = () => {
    setExportGridVisibility(true)
  }

  const handleExportModalClose = () => {
    if (isCustomOpenExportModal) {
      onExportModalClose?.(false)

      return
    }

    setExportGridVisibility(false)
  }

  useEffect(() => {
    if (isCustomOpenExportModal) {
      setExportGridVisibility(isExportGridModalVisible)
    }
  }, [isCustomOpenExportModal, isExportGridModalVisible])
  /* RETURN BACK THIS CRUTCH, I removed it because think we don'y need it
We need it for case when we change like exp Customer the filters don't clear
For cleaning filter old developers use it.
It will be removed after implemented normal Gird
*/
  useEffect(() => {
    const values = {
      ...defaultFilterValues,
    }

    setFilterValues(values)
  }, [defaultFilterValues])

  useEffect(() => {
    const scroll = document.getElementsByClassName("ant-table-scroll")[0]
    const tableBody =
      containerRef.current &&
      containerRef.current.querySelectorAll(".ant-table-body")[0]

    if (scroll && tableBody && tableBody.scrollLeft !== horisontalScroll) {
      containerRef.current.querySelectorAll(".ant-table-body")[0].scrollTo({
        left: horisontalScroll,
      })
    }

    if (needScroll && tableBody) {
      tableBody.scrollTo({
        top: 0,
        behavior: "smooth",
      })
      setNeedScroll(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  useEffect(() => {
    if (onChangeFilterOutside) {
      onChangeFilterWithDebounce.current = debounce(
        onChangeFilterOutside,
        debounceTime
      )
    }
  }, [onChangeFilterOutside, debounceTime])

  useEffect(() => {
    if (!isPage) return
    const tableBody =
      containerRef.current.querySelectorAll(".ant-table-body")[0]
    const onScroll = debounce(({ target }) => {
      if (target.scrollWidth > target.offsetWidth) {
        setHorisontalScroll(target.scrollLeft)
      }
    }, 1000)

    tableBody.addEventListener("scroll", onScroll)

    return () => tableBody.removeEventListener("scroll", onScroll)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const sortField = (defaultFilterValues.sort || "").replace("-", "")
  const sortDirection =
    defaultFilterValues.sort && defaultFilterValues.sort.startsWith("-")
      ? "descend"
      : "ascend"
  const [overflowY, setOverflowY] = useState(500)

  useEffect(() => {
    if (!isPage) return

    const isTouchScreenLower575px =
      lowerThan(breakpoint, SM) && screenHaveTouch()

    if (
      isTouchScreenLower575px &&
      !/\bCrOS\b/.test(navigator?.userAgent || "")
    ) {
      containerRef.current.parentNode.classList.remove(styles.wrapper)
      setOverflowY(undefined)
    } else {
      const [firstTableElement] =
        containerRef.current.getElementsByClassName("table-grid-layout")
      const tableLayoutHeight = firstTableElement?.offsetHeight || 0

      const isAccountsVisible =
        prev && prev.hideAccounts === false && hideAccounts

      const layoutHeight = isAccountsVisible
        ? Math.max(gridLayoutHeight, tableLayoutHeight)
        : gridLayoutHeight

      containerRef.current.parentNode.classList.add(styles.wrapper)

      // DESC: It's a height of the Ant Table's header that is defined by tableHeader prop
      const [tableTitle] =
        containerRef.current.querySelectorAll(".ant-table-title")
      const tableTitleHeight = tableTitle?.offsetHeight || 0

      // DESC: Height of the table header with column titles and filters
      const [firstThead] =
        containerRef.current.querySelectorAll(".ant-table-thead")
      const theadHeight = firstThead?.offsetHeight || 0

      const headerHeight = title || withExport || headerRightSide ? 52 : 0

      const warningHeight = showWarning ? 74 : 0

      const newOverflowY =
        layoutHeight -
        warningHeight -
        headerHeight -
        theadHeight -
        tableTitleHeight

      setOverflowY(newOverflowY)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [breakpoint, hideAccounts, gridLayoutHeight, defaultFilterValues, isPage])

  useEffect(() => {
    const isNotHaveMemoizedPrevAndSearchOptions =
      !memoizedPrev || !searchOptions

    if (isNotHaveMemoizedPrevAndSearchOptions) return

    const { page } = searchOptions
    const { page: prevPage } = memoizedPrev.searchOptions

    if (prevPage !== page) {
      setNeedScroll(true)
    }
  }, [memoizedPrev, searchOptions])

  const tableColumns = useMemo(() => {
    const onChangeFilter = (params, type) => {
      if (type === COLUMN_TYPE_STATE_ONLY) {
        return
      }

      const isTypeInput =
        type === COLUMN_INPUT_TYPE_INPUT || type === COLUMN_INPUT_TYPE_NUMBER
      const isDebounced = debounceTime && onChangeFilterWithDebounce.current
      const isInputWithDebounce = isDebounced && isTypeInput
      let newParams = {
        ...removePrefixFromObjectKeys({
          object: getUrlSearchParams({
            locationSearch: document.location.search,
          }),
          prefix: filterPrefix,
          whiteList: urlFiltersWhiteList,
        }),
        ...searchOptions,
        page: 1,
      }

      if (!Array.isArray(params)) {
        params = [params]
      }

      params.forEach(({ key, value }) => {
        const separators =
          !Array.isArray(value) && !!value?.toString()?.match(/[,.\s]/g)?.length
        const isNumberWithSeparators = type === "number" && separators
        const formattedNumberValue = isNumberWithSeparators
          ? value.replace(/[.,](?=.*[.,])/g, "")
          : value

        const isValueEmptyArray = Array.isArray(value) && value.length === 0

        if (key) {
          newParams[key] = formattedNumberValue || ""
        }

        if (isValueEmptyArray) {
          const { [key]: someValue, ...restParams } = newParams

          newParams = restParams
        }

        if (!newParams[key]) {
          const { [key]: someValue, ...restParams } = newParams

          newParams = restParams
        }
      })

      const newSearchOptions = {
        ...newParams,
        page: 1,
      }

      const isShowClearButton = getIsShowClearButton({
        searchOptions: newSearchOptions,
      })

      const changeFilterData = {
        params,
        changedParams: newParams,
        newSearchOptions,
        isShowClearButton,
      }

      setFilterValues(newParams)

      if (isInputWithDebounce) {
        onChangeFilterWithDebounce.current(changeFilterData)

        return
      }

      onChangeFilterOutside(changeFilterData)
    }

    const clickInputHandler = (event) => event.stopPropagation()

    const buildChangeFilterInputHandler = ({ key, type }) => {
      return ({ target: { value } }) => {
        return onChangeFilter({ key, value: value.trim() }, type)
      }
    }

    const buildChangeFilterSelectHandler = ({ key, type }) => {
      return (value) => {
        return onChangeFilter({ key, value }, type)
      }
    }

    const buildChangeFilterDateHandler = ({ key, type }) => {
      return (momentDate, stringDate, { filterType, stringDateForServer }) =>
        onChangeFilter(
          [
            {
              key,
              value: stringDateForServer,
            },
            {
              key: `${key}_filterType`,
              value: stringDate ? filterType : undefined,
            },
          ],
          type
        )
    }

    const buildFilterOptionHandler = (input, option) => {
      return (
        option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    }

    const buildRender = ({ column, key, render, editable, permission }) => {
      const renderer =
        render ||
        (editable
          ? renderEditableRowCell(key, editable, onSaveCell, permission)
          : renderRowCell)

      return (...params) => renderer(...[...params, column, key])
    }

    const changeStartResizingHandler = ({ dataIndex, key }) => {
      return (event) => {
        onResize({
          dataIndex,
          columKey: key,
          event: event,
        })
      }
    }

    const buildColumn = ({ column }) => {
      const {
        key,
        render,
        sorter,
        title,
        type,
        editable,
        width = 100,
        permission,
        minDate,
        withDayTime,
        dataIndex,
        disabled = false,
        notLocalizeTitle,
        disabledDatePickerDates,
        isDatePickerWithFilter = true,
        isClearDatesToReset,
        clearToDates,
        disabledDate,
        isMonthPickerRange = true,
        showTimeZoneToggle = true,
        filterType,
        convertUtcToUserTimeZone = true,
      } = column

      const sortOrder = sortField === key && sortDirection

      const resizeIcon = onResize ? (
        <TableResizeHoverIcons
          iconTitle={l("Resize table")}
          onStartResizing={changeStartResizingHandler({ dataIndex, key })}
        />
      ) : null

      switch (type) {
        case COLUMN_INPUT_TYPE_INPUT: {
          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <InputWithNav
                  className={styles.filterInput}
                  columnKey={key}
                  disabled={disabled}
                  inputType={type}
                  placeholder={notLocalizeTitle ? title : l(title)}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  title={notLocalizeTitle ? title : l(title)}
                  value={filterValues[key]}
                  onChange={buildChangeFilterInputHandler({ key, type })}
                  onChangeSort={onChange}
                  onClick={clickInputHandler}
                />
              </>
            ),
          }
        }
        case COLUMN_INPUT_TYPE_NUMBER: {
          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <NumericInputWithNav
                  className={styles.filterInput}
                  columnKey={key}
                  disabled={disabled}
                  inputType={type}
                  placeholder={l(title)}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  title={l(title)}
                  value={filterValues[key]}
                  onChange={buildChangeFilterInputHandler({ key, type })}
                  onChangeSort={onChange}
                  onClick={clickInputHandler}
                />
              </>
            ),
          }
        }
        case COLUMN_INPUT_TYPE_SELECT: {
          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <SelectWithNav
                  allowClear
                  columnKey={key}
                  disabled={disabled}
                  dropdownMatchSelectWidth={false}
                  id={key}
                  inputType={type}
                  placeholder={l(title)}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  className={cn(
                    styles.filterSelect,
                    "table-grid-layout__filter-select"
                  )}
                  value={
                    (selectFiltersOptions[key] || []).length
                      ? filterValues[key]
                      : ""
                  }
                  onChange={buildChangeFilterSelectHandler({ key, type })}
                  onChangeSort={onChange}
                >
                  {checkIsArray(selectFiltersOptions[key])
                    ? selectFiltersOptions[key].map(
                        ({ label, value, isPassive }) => (
                          <Option
                            key={value}
                            title={label}
                            value={value}
                            className={cn(styles.option, {
                              [styles.passiveOption]: isPassive,
                            })}
                          >
                            {label}
                          </Option>
                        )
                      )
                    : null}
                </SelectWithNav>
              </>
            ),
          }
        }
        case COLUMN_INPUT_TYPE_SELECT_MULTIPLE: {
          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <MultiSelectWithNav
                  allowClear
                  withChangeRerender
                  columnKey={key}
                  disabled={disabled}
                  dropdownMatchSelectWidth={false}
                  inputType={type}
                  mode="multiple"
                  placeholder={l(title)}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  value={filterValues[key]}
                  className={cn(
                    styles.filterSelect,
                    "table-grid-layout__filter-select"
                  )}
                  onChange={buildChangeFilterSelectHandler({ key, type })}
                  onChangeSort={onChange}
                >
                  {checkIsArray(selectFiltersOptions[key])
                    ? selectFiltersOptions[key].map(
                        ({ label, value, isPassive }) => (
                          <Option
                            key={value}
                            title={label}
                            value={value}
                            className={cn(styles.option, {
                              [styles.passiveOption]: isPassive,
                            })}
                          >
                            {label}
                          </Option>
                        )
                      )
                    : null}
                </MultiSelectWithNav>
              </>
            ),
          }
        }
        case COLUMN_INPUT_TYPE_DATE: {
          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <DatePickerWithNav
                  allowClear={false}
                  columnKey={key}
                  disabled={disabled}
                  disabledDate={disabledDate}
                  filterType={filterValues[`${key}_filterType`]}
                  inputType={type}
                  isFilter={isDatePickerWithFilter}
                  placeholder={l(title)}
                  rightIconWidth={28}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  withDayTime={withDayTime}
                  className={cn(
                    styles.filterSelect,
                    "table-grid-layout__filter-date"
                  )}
                  value={
                    filterValues[key]
                      ? moment(removeOperator(filterValues[key]))
                      : null
                  }
                  onChange={buildChangeFilterDateHandler({ key, type })}
                  onChangeSort={onChange}
                />
              </>
            ),
          }
        }
        case COLUMN_INPUT_TYPE_DATE_RANGE: {
          const defaultDisabledDate = (current) => {
            if (disabledDatePickerDates) {
              return disabledDatePickerDates(current)
            }

            return (
              current &&
              (current > moment().endOf("day") ||
                (minDate && current < minDate.startOf("day")))
            )
          }

          const disabledDateType = disabledDate || defaultDisabledDate

          let [rangeFrom, rangeTo] = filterValues[key]
            ? filterValues[key].split(" - ")
            : []

          rangeFrom = removeOperator(rangeFrom)

          const changeFilterHandler = (
            momentDates,
            stringDates,
            { isUTC, filterType, stringLastFilterSettings, stringDateForServer }
          ) => {
            const isFirstDateAndUTCFormat = stringDates[0] && isUTC
            const isOneOrZero = isFirstDateAndUTCFormat ? "1" : "0"
            const typeUTC = isUTC !== undefined ? isOneOrZero : isUTC

            return onChangeFilter(
              [
                {
                  key,
                  value: stringDateForServer,
                },
                {
                  key: `${key}_filterType`,
                  value: stringDates[0] ? filterType : undefined,
                },
                {
                  key: `${key}_lastFilter`,
                  value: stringDates[0] && stringLastFilterSettings,
                },
                {
                  key: `${key}_isUTC`,
                  value: typeUTC,
                },
              ],
              type
            )
          }

          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <DatePickerWithNav
                  allowClear
                  isRange
                  withDayTime
                  clearToDates={clearToDates}
                  columnKey={key}
                  convertUtcToUserTimeZone={convertUtcToUserTimeZone}
                  disabledDate={disabledDateType}
                  filterType={filterType || filterValues[`${key}_filterType`]}
                  inputType={type}
                  isClearDatesToReset={isClearDatesToReset}
                  isFilter={isDatePickerWithFilter}
                  placeholder={[l(title)]}
                  rightIconWidth={28}
                  showTimeZoneToggle={showTimeZoneToggle}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  stringLastFilterSettings={filterValues[`${key}_lastFilter`]}
                  className={cn(
                    styles.filterSelect,
                    "table-grid-layout__filter-date"
                  )}
                  isUTC={
                    filterValues[`${key}_isUTC`] &&
                    +filterValues[`${key}_isUTC`]
                  }
                  value={
                    filterValues[key]
                      ? [
                          convertStringDateToMoment(rangeFrom),
                          convertStringDateToMoment(rangeTo || rangeFrom),
                        ]
                      : null
                  }
                  onChange={changeFilterHandler}
                  onChangeSort={onChange}
                />
              </>
            ),
          }
        }
        case COLUMN_INPUT_TYPE_SELECT_WITH_SEARCH: {
          const updateFilterHandler = (value) => onUpdateFilter(key, value)

          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <SelectWithNav
                  allowClear
                  showSearch
                  columnKey={key}
                  defaultActiveFirstOption={false}
                  disabled={disabled}
                  dropdownMatchSelectWidth={false}
                  filterOption={false}
                  inputType={type}
                  notFoundContent={null}
                  placeholder={l(title)}
                  showArrow={false}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  value={filterValues[key]}
                  onChange={buildChangeFilterSelectHandler({ key, type })}
                  onChangeSort={onChange}
                  onSearch={updateFilterHandler}
                >
                  {selectFiltersOptions[key]?.map(
                    ({ label, value, isPassive }) => (
                      <Option
                        key={value}
                        value={value}
                        className={cn(styles.option, {
                          [styles.passiveOption]: isPassive,
                        })}
                      >
                        {label}
                      </Option>
                    )
                  )}
                </SelectWithNav>
              </>
            ),
          }
        }
        case COLUMN_INPUT_TYPE_MONTH: {
          let inputValue = null
          let [monthFrom, monthTo] = filterValues[key]
            ? filterValues[key].split(" - ")
            : []

          const isRangeWithDates = isMonthPickerRange && monthFrom && monthTo
          const isSingleWithFromDate = !isMonthPickerRange && monthFrom

          if (isRangeWithDates) {
            inputValue = [
              moment(monthFrom, DATE_MONTH),
              moment(monthTo, DATE_MONTH),
            ]
          }

          if (isSingleWithFromDate) {
            inputValue = [
              moment(monthFrom, DATE_MONTH),
              moment(monthFrom, DATE_MONTH),
            ]
          }

          const changeFilterHandler = (value) => {
            const typeValue = Array.isArray(value)
              ? `${value[0]?.format(DATE_MONTH)} - ${value[1]?.format(
                  DATE_MONTH
                )}`
              : value && value.format(DATE_MONTH)

            return onChangeFilter(
              {
                key,
                value: typeValue,
              },
              type
            )
          }

          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <MonthRangePickerWithNav
                  columnKey={key}
                  disabled={disabled}
                  format={DATE_MONTH}
                  inputType={type}
                  isRange={isMonthPickerRange}
                  placeholder={l(title)}
                  rightIconWidth={28}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  value={inputValue}
                  className={cn(
                    styles.filterSelect,
                    "table-grid-layout__filter-date"
                  )}
                  onChange={changeFilterHandler}
                  onChangeSort={onChange}
                />
              </>
            ),
          }
        }
        /* TODO: In this Project we don't have this
        FIXME: Should with implementation of new Grid change it
*/
        // case COLUMN_INPUT_TYPE_AUTOCOMPLETE: {
        //   const clickAutocompleteHandler = (event) => {
        //     const node = event.target.nodeName

        //     const isNodePathOrSvg = node === "path" || node === "svg"

        //     if (isNodePathOrSvg) {
        //       onChangeFilter({ key, value: "" }, type)
        //     }
        //   }

        //   const inputSetFilterValuesHandler = (event) => {
        //     // No need to request immediately, allow typing to let autocomplete work, trigger request only on Enter
        //     setFilterValues({
        //       ...filterValues,
        //       [key]: event.target.value,
        //     })
        //   }

        //   const keyDownHandler = (event) => {
        //     if (event.keyCode === 13) {
        //       onChangeFilter({ key, value: filterValues[key] }, type)
        //     }
        //   }

        //   return {
        //     ...column,
        //     sortOrder,
        //     sorter: false,
        //     render: buildRender({ column, key, render, editable, permission }),
        //     width: width,
        //     title: (
        //       <>
        //         {resizeIcon}
        //         <AutoCompleteWithNav
        //           className={cn(
        //             styles.filterSelect,
        //             "table-grid-layout__filter-select"
        //           )}
        //           allowClear
        //           sorter={sorter}
        //           sortOrder={sortOrder}
        //           sortDirection={sortDirection}
        //           onChangeSort={onChange}
        //           showSearch
        //           value={filterValues[key]}
        //           columnKey={key}
        //           placeholder={l(title)}
        //           notFoundContent={null}
        //           filterOption={buildFilterOptionHandler}
        //           onSelect={buildChangeFilterSelectHandler({ key, type })}
        //           onClick={clickAutocompleteHandler}
        //           onInput={inputSetFilterValuesHandler}
        //           onKeyDown={keyDownHandler}
        //         >
        //           {selectFiltersOptions[key]?.map(({ label, value = null }) => (
        //             <AutoComplete.Option value={label} key={value}>
        //               {label}
        //             </AutoComplete.Option>
        //           ))}
        //         </AutoCompleteWithNav>
        //       </>
        //     ),
        //   }
        // }
        case COLUMN_INPUT_TYPE_SELECT_WITH_FILTER: {
          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            width: width,
            title: (
              <>
                {resizeIcon}
                <SelectWithNav
                  allowClear
                  showSearch
                  columnKey={key}
                  defaultActiveFirstOption={false}
                  disabled={disabled}
                  dropdownMatchSelectWidth={false}
                  filterOption={buildFilterOptionHandler}
                  inputType={type}
                  notFoundContent={null}
                  placeholder={l(title)}
                  sortDirection={sortDirection}
                  sorter={sorter}
                  sortOrder={sortOrder}
                  className={cn(
                    styles.filterSelect,
                    "table-grid-layout__filter-select"
                  )}
                  value={
                    Array.isArray(filterValues[key])
                      ? filterValues[key][0]
                      : filterValues[key]
                  }
                  onChange={buildChangeFilterSelectHandler({ key, type })}
                  onChangeSort={onChange}
                >
                  {selectFiltersOptions[key]?.map(({ label, value }) => (
                    <Option key={value} className={styles.option} value={value}>
                      {label}
                    </Option>
                  ))}
                </SelectWithNav>
              </>
            ),
          }
        }
        /* TODO: In this Project we don't have this
        FIXME: Should with implementation of new Grid change it
*/
        // case COLUMN_INPUT_TYPE_SELECT_WITH_SEARCH_MULTIPLE: {
        //   return {
        //     ...column,
        //     sortOrder,
        //     sorter: false,
        //     render: buildRender({ column, key, render, editable, permission }),
        //     width: width,
        //     title: (
        //       <>
        //         {resizeIcon}
        //         <SelectWithFilterMultiple
        //           inputType={type}
        //           sortOrder={sortOrder}
        //           columnKey={key}
        //           sortDirection={sortDirection}
        //           onChangeSort={onChange}
        //           sorter={sorter}
        //           className={cn(
        //             styles.filterSelect,
        //             "table-grid-layout__filter-select"
        //           )}
        //           value={filterValues[key]}
        //           onChange={buildChangeFilterSelectHandler({ key, type })}
        //           placeholder={l(title)}
        //           dropdownMatchSelectWidth={false}
        //         >
        //           {selectFiltersOptions[key]?.map(
        //             ({ label, value, isPassive, withMarker, color }) => (
        //               <Option
        //                 className={cn(styles.option, {
        //                   [styles.passiveOption]: isPassive,
        //                   [styles.withMarkerOption]: withMarker,
        //                 })}
        //                 style={withMarker && { "--background-color": color }}
        //                 title={label}
        //                 key={value}
        //                 value={value}
        //               >
        //                 {label}
        //               </Option>
        //             )
        //           )}
        //         </SelectWithFilterMultiple>
        //       </>
        //     ),
        //   }
        // }
        case COLUMN_INPUT_TYPE_ACTION: {
          return column
        }
        default: {
          return {
            ...column,
            sortOrder,
            sorter: false,
            render: buildRender({ column, key, render, editable, permission }),
            className: "table-grid-layout__text-title",
            width: width,
            title: (
              <>
                {resizeIcon}
                <Typography
                  color="--color-text-main"
                  variant="--font-body-text-9"
                  className={cn(styles.headTitle, {
                    [styles.titleWithSorter]: sorter,
                  })}
                >
                  {sorter && (
                    <TableGridSortDropdown
                      className={styles.headTitleSort}
                      columnKey={key}
                      sortDirection={sortDirection}
                      sortOrder={sortOrder}
                      onChange={onChange}
                    />
                  )}
                  {title && <FormattedMessage id={title} />}
                </Typography>
              </>
            ),
          }
        }
      }
    }

    const columnsType = actionsColumn
      ? [
          getIconsColumn(
            buttons,
            isAdditionalButtonContainerStyle,
            icons,
            actionsColumnWidth,
            actionsColumnClassName
          ),
          ...columns,
        ]
      : columns

    return checkIsArray(columnsType)
      ? columnsType?.map((column) => {
          return buildColumn({
            column,
          })
        })
      : []
  }, [
    filterValues,
    selectFiltersOptions,
    settingsColumns,
    icons,
    buttons,
    actionsColumn,
    columns?.length,
  ])

  return (
    <div
      ref={(ref) => {
        containerRef.current = ref
        setGridRef && setGridRef(ref)
      }}
      className={`${styles.container} ${containerClassName}`}
    >
      {isPage ? (
        <Box align="center" justify="space-between" padding="m l">
          {title ? (
            <Typography variant="--font-headline-3">
              <FormattedMessage id={title} />
            </Typography>
          ) : null}

          <Box>
            {withExport ? (
              // TODO:L Should refact big different between BAS and RRP/LOT
              <ButtonPopover
                iconOnly
                isGlobal
                className={styles.uploadBtn}
                disabled={isDisabledExportIcon || !data.length}
                icon="icnUpload"
                placement="topRight"
                variant="secondary"
                content={
                  customTextPopoverExportIcon || l("Export filtered results")
                }
                onClick={handleExportGridVisible}
              />
            ) : null}
            {headerRightSide ? headerRightSide : null}
          </Box>
        </Box>
      ) : null}

      {showWarning ? (
        <Box display="block" margin="m l l l">
          <Alert
            alertType="warning"
            message={l(
              "This section is synchronized with the Production environment. Please treat any changes carefully. Review before saving your edits."
            )}
          />
        </Box>
      ) : null}
      {header}
      <Table
        fixed
        columns={tableColumns}
        dataSource={data}
        loading={loading}
        pagination={false}
        rowKey={rowKey}
        rowSelection={rowSelection}
        tableLayout={tableLayout}
        title={tableHeader}
        width={100}
        className={cn("table-grid-layout", className, {
          divided: isDivided,
          noAction: !actionsColumn,
          withSummary: summaryRow,
          [styles.noData]: data.length === 0,
        })}
        locale={{
          emptyText: (
            <Empty
              description={l("No data")}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ),
        }}
        scroll={{
          x: overflowX || undefined,
          y: overflowY || undefined,
        }}
        onChange={onChange}
        onRow={onRow}
      />
      {showExportGrid && (
        <ExportGridModal
          columns={columns}
          fileName={l(exportFileName || title)}
          gridRef={containerRef.current}
          message={exportModalMessage}
          notParseLastRows={exportNotParseLastRows}
          parseFromColumn={exportParseFromColumn}
          onCancel={handleExportModalClose}
        />
      )}
    </div>
  )
}

TableGridLayoutView.propTypes = {
  actionsColumn: PropTypes.bool.isRequired,
  actionsColumnWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  buttons: PropTypes.array,
  isAdditionalButtonContainerStyle: PropTypes.bool,
  columns: PropTypes.array.isRequired,
  data: PropTypes.array.isRequired,
  defaultFilterValues: PropTypes.object,
  gridLayoutHeight: PropTypes.number,
  icons: PropTypes.array,
  onSaveCell: PropTypes.func,
  onUpdateFilter: PropTypes.func,
  overflowX: PropTypes.number,
  overflowY: PropTypes.number,
  rowKey: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),
  selectFiltersOptions: PropTypes.object,
  loading: PropTypes.bool,
  isDivided: PropTypes.bool,
  onRow: PropTypes.func,
  summaryRow: PropTypes.bool,
  debounceTime: PropTypes.number,
  withExport: PropTypes.bool,
  exportParseFromColumn: PropTypes.number,
  isExportGridModalVisible: PropTypes.bool,
  onExportModalClose: PropTypes.func,
  exportFileName: PropTypes.oneOfType([null, PropTypes.string]),
  headerRightSide: PropTypes.oneOfType([null, PropTypes.node]),
}

TableGridLayoutView.defaultProps = {
  actionsColumn: true,
  buttons: [],
  isAdditionalButtonContainerStyle: false,
  defaultFilterValues: {},
  overflowX: 1000,
  rowKey: "id",
  icons: [],
  summaryRow: false,
  debounceTime: 1000,
  onChangeFilter: () => undefined,
}

const mapSizesToProps = ({ width }) => ({
  breakpoint: getBreakpoint(width),
  gridLayoutHeight: (
    document.getElementsByClassName("table-grid-layout")[0] || {}
  ).offsetHeight,
})

export default withSizes(mapSizesToProps)(TableGridLayoutView)
