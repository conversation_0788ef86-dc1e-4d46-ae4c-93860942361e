@import "assets/styles/variables.scss";

.container {
  height: 100%;
  overflow: hidden;
}

.headTitle {
  &.titleWithSorter {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 5px;
    overflow: hidden;
    word-break: normal !important;
    white-space: normal !important;
  }
  .headTitleSort {
    margin-right: 4px;
    position: relative;
    z-index: 10;
  }
}

.filterSelect {
  width: 100%;

  :global(.ant-select-selection__placeholder) {
    font-size: 12px;
  }

  :global(.ant-calendar-picker-input) {
    font-size: 12px;
    font-weight: 400;
  }

  :global(.ant-select-selection__clear) {
    right: 5px;
  }
  :global(.ant-calendar-picker-clear) {
    right: 20px;
  }
  :global(.ant-select-selection--multiple) {
    padding-bottom: 0;
  }
}

.option.option {
  font-size: 12px;
  height: 32px;
  &.passiveOption {
    color: $text_second;
  }
}

.filterInput,
.filterSelect,
.headTitle {
  font-family: Roboto, sans-serif;
  font-weight: 400;
}

.headButton {
  flex-shrink: 0;
  margin-right: 10px;

  &:last-child {
    margin-right: 0px;
  }
}

.actionsColumn.actionsColumn.actionsColumn {
  padding-left: 25px;
  padding-right: 15px;
  width: 120px;
}

.buttonContainer {
  display: flex;
}

.additionalButtonContainer {
  display: block;
  padding-right: var(--padding-l);
}

.iconLink {
  margin-right: 7px;

  &:last-child {
    margin-right: 0;
  }
}

.icon {
  margin-right: 7px;
  padding: 3px;
  color: $icon_clicable;
  font-size: 16px;

  &:last-child {
    margin-right: 0;
  }
}

.rowCell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wrapper.wrapper.wrapper {
  flex: 1 1 auto;
  height: 100%;
  overflow: hidden;
  min-height: 300px;
}

.multySelectContainer {
  position: relative;

  .multySelectIcon {
    font-size: 12px;
    pointer-events: none;
    position: absolute;
    right: 6px;
    top: 10px;
    transition: transform 0.3s;
  }

  :global(.ant-select-open) ~ .multySelectIcon {
    transform: rotate(180deg);
  }
}

.noData {
  :global(.ant-table-placeholder) {
    height: 170px;
    pointer-events: none;

    :global(.ant-empty) {
      width: calc(100vw - 32px);
    }
  }

  :global(.ant-table-scroll) {
    overflow-x: auto !important;

    :global(.ant-table-body) {
      overflow-x: visible !important;
    }
  }
}

.uploadBtn {
  z-index: 22;
}
/* Copy from another project
It will be removed
*/
.withMarkerOption {
  display: flex;
  align-items: center;

  &:before {
    content: "";
    width: 8px;
    height: 8px;
    background-color: var(--background-color);
    border: solid 1px #fff;
    margin-right: 7px;
    border-radius: 50%;
  }
}

@media (max-width: $xs) {
  .container {
    padding-top: 10px;
  }
}
