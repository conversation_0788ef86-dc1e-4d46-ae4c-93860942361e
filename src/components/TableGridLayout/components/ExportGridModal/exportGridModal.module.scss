@import "assets/styles/variables.scss";

.select {
  width: 100%;
}
.text {
  margin-bottom: 20px;
  color: $text_second;
}

.controlContainer {
  align-items: flex-start;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controlsWrapper {
  flex-grow: 1;
  max-width: 250px;
}

.label {
  color: $text_main;
  margin-right: 10px;
  width: 100px;
}

@media (max-width: $xs) {
  .label {
    margin-bottom: 6px;
    width: 100%;
  }
  .controlContainer {
    display: block;
  }
  .controlsWrapper,
  .controlsWrapper {
    width: 100%;
    max-width: 100%;
  }
}
