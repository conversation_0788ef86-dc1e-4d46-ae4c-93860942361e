import React, { useState, useEffect, useCallback } from "react"
import cn from "classnames"
import styles from "./withGridFiltersNav.module.scss"
import { DownOutlined } from "@ant-design/icons"
import "./withGridFiltersNav.scss"
import TableGridSortDropdown from "../TableGridSortDropdown/TableGridSortDropdown"
import {
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_INPUT,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "components/TableGridLayout/TableGridLayoutView"
import withScrollRerender from "components/hocs/withScrollRerender/withScrollRerender"

const SortDropdonWithScrollRerender = withScrollRerender(TableGridSortDropdown)

export default (WrappedComponent) => (props) => {
  const {
    sortControls,
    sortOrder,
    columnKey,
    sortDirection,
    onChangeSort,
    sorter,
    ...inputProps
  } = props
  const {
    title,
    inputType,
    onBlur: onBlurInput,
    onFocus: onFocusInput,
    placeholder,
  } = inputProps

  const [focused, setFocused] = useState(false)
  const [withRightIcon, setWithRightIcon] = useState(false)

  useEffect(() => {
    setWithRightIcon(
      inputType !== COLUMN_INPUT_TYPE_INPUT &&
        inputType !== COLUMN_INPUT_TYPE_NUMBER
    )
  }, [inputType])

  const onBlur = useCallback(
    (e) => {
      onBlurInput && onBlurInput(e)
      setFocused(false)
    },
    [onBlurInput]
  )

  const onFocus = useCallback(
    (e) => {
      onFocusInput && onFocusInput(e)
      setFocused(false)
    },
    [onFocusInput]
  )

  return (
    <div title={title || placeholder} className={styles.container}>
      <div
        className={cn(
          styles.wrapper,
          "grid-filter",
          `grid-filter-${inputType}`,
          {
            "grid-filter-sorted": sorter,
            [styles.sorted]: sorter,
            "grid-filter-focused": focused,
          }
        )}
      >
        {inputType === COLUMN_INPUT_TYPE_SELECT_MULTIPLE && (
          <DownOutlined className={"grid-filter-multiselect-icon"} />
        )}
        {sorter ? (
          <SortDropdonWithScrollRerender
            className={styles.sortDropdown}
            controls={sortControls}
            sortOrder={sortOrder}
            columnKey={columnKey}
            sortDirection={sortDirection}
            onChange={onChangeSort}
          />
        ) : null}
        <WrappedComponent
          {...inputProps}
          onBlur={onBlur}
          onFocus={onFocus}
          withLeftIcon={sorter}
          withRightIcon={withRightIcon}
        />
      </div>
    </div>
  )
}
