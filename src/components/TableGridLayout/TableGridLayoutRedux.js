import { connect } from "react-redux"

import TableGridLayoutView from "components/TableGridLayout/TableGridLayoutView"
import { permissionsSelector } from "selectors/userSelectors"

const mapStateToProps = (state) => {
  const {
    adminbar: { extended, hideAccounts },
  } = state

  return {
    hideAccounts,
    extended,
    permissions: permissionsSelector(state),
    language: state.translations.locale,
  }
}

export default connect(mapStateToProps, null)(TableGridLayoutView)
