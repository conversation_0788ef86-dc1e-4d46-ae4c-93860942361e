import { OptionWithColor } from "types/OptionWithColor"

import { Option } from "@develop/fe-library/dist/lib/components/Select/SelectTypes"

export type StatusOption = OptionWithColor & {
  count?: number
}

export interface SearchParams
  extends Record<string, string | string[] | undefined> {
  customerId?: string
  caseType?: string[]
  createdAt?: string
}

export type TableProps = {
  lostCaseTypesOptions: Array<Option>
  filterPrefix: string
  selectedCaseTypes?: Array<string>
  statusOptions: Array<Option>
}

export type UseColumns = (
  params: Pick<TableProps, "lostCaseTypesOptions" | "statusOptions">
) => Array<any>

export type SearchOptions<DataItem> = {
  page?: number
  pageSize?: number
  sort?: string
} & DataItem

export type SelectFilterOptions = {
  status: Array<Option>
  caseType: Array<Option>
}

export type OnEdit = (params: { id: number }) => void

export type UseTable<DataItem> = () => {
  data: Array<DataItem>
  totalCount: number
  searchOptions: SearchOptions<DataItem>
  getData: (searchOptionsNew: SearchOptions<DataItem>) => void
  onEdit?: OnEdit
}
