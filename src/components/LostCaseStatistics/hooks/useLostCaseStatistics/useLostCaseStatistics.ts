import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Option } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"

import lostCaseActions from "actions/lostCaseActions"

import { lostCaseDefaultFiltersSelector } from "selectors/lostCaseSelectors"

import { useSearchParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"

import { SearchParams } from "components/LostCaseStatistics/LostCaseStatisticsTypes"

const { getFilters: getLostCaseFilters } = lostCaseActions as any

export const useLostCaseStatistics = () => {
  const dispatch = useDispatch()

  const { caseType: selectedCaseTypes = [] } = useSearchParams<SearchParams>([
    "caseType",
  ])

  const { caseTypes, statuses } = useSelector(lostCaseDefaultFiltersSelector)

  const lostCaseTypesOptionsAll: Array<Option> = !!caseTypes
    ? getObjectKeys(caseTypes)
        .filter((key) => key !== "return-fee")
        .map((key) => ({
          label: caseTypes[key],
          value: key,
        }))
    : []

  const statusOptions: Array<Option> = !!statuses
    ? getObjectKeys(statuses).map((key) => ({
        label: statuses[key],
        value: key,
      }))
    : []

  const lostCaseTypesOptions: Array<Option> = checkIsArray(selectedCaseTypes)
    ? selectedCaseTypes.map((caseType) => {
        const foundOption = lostCaseTypesOptionsAll.find(
          (option) => option.value === caseType
        )

        if (!foundOption) {
          return {
            label: caseType,
            value: caseType,
          }
        }

        return foundOption
      })
    : lostCaseTypesOptionsAll

  useEffect(() => {
    dispatch(getLostCaseFilters())
  }, [])

  return {
    lostCaseTypesOptionsAll,
    lostCaseTypesOptions,
    statusOptions,
  }
}
