import React from "react"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { StatusesList } from "../StatusesList"

import { LegendsProps } from "./LegendsTypes"

export const Legends = ({ statusesList }: LegendsProps) => {
  return (
    <Box
      align="stretch"
      flexDirection="column"
      gap="m"
      justify="flex-start"
      width="280px"
    >
      <Typography variant="--font-headline-5">
        {l("Case statistics")}
      </Typography>

      <Typography
        color="--color-text-placeholders"
        variant="--font-body-text-9"
      >
        {l("Statuses")}
      </Typography>

      <StatusesList statusesList={statusesList} />
    </Box>
  )
}
