import React from "react"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { StatusesList } from "../StatusesList"

import { StatusOption } from "components/LostCaseStatistics/LostCaseStatisticsTypes"

import { ChartTooltipProps } from "./ChartTooltipTypes"

import styles from "./chartTooltip.module.scss"

export const ChartTooltip = ({
  active,
  payload = [],
  statusesList,
  buildTitle,
}: ChartTooltipProps) => {
  if (!active) {
    return null
  }

  const title = buildTitle(payload[0]?.payload?.name)

  const result: Array<StatusOption> = statusesList.map(
    ({ value, label, color }, index) => {
      const count: number = payload[index]?.payload?.[value] || 0

      return {
        value,
        label,
        color,
        count,
      }
    }
  )

  return (
    <Box
      backgroundColor="--color-main-background"
      borderRadius="--border-radius"
      className={styles.chartTooltip}
      flexDirection="column"
      gap="m"
      padding="m"
    >
      <Typography variant="--font-body-text-5">{l(title)}</Typography>

      <StatusesList statusesList={result} />
    </Box>
  )
}
