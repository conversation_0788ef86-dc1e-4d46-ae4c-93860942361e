import React from "react"
import { Box, Typography } from "@develop/fe-library"

import l from "utils/intl"

import { StatusesListProps } from "./StatusesListTypes"

export const StatusesList = ({ statusesList }: StatusesListProps) => {
  return (
    <Box flexDirection="column" gap="m">
      {statusesList.map(({ value, label, color, count }) => {
        const hasCount: boolean = count !== undefined && count !== null

        return (
          <Box key={value} align="center" gap="m">
            <Box
              backgroundColor={color}
              borderRadius="--border-radius"
              display="block"
              flexShrink={0}
              height="4px"
              width="10px"
            />

            <Box flex={1}>
              <Typography variant="--font-body-text-9">{l(label)}</Typography>
            </Box>

            {hasCount ? (
              <Box>
                <Typography variant="--font-body-text-9">{count}</Typography>
              </Box>
            ) : null}
          </Box>
        )
      })}
    </Box>
  )
}
