import React, { useMemo } from "react"
import { useSelector } from "react-redux"
import { Box, Typography } from "@develop/fe-library"
import { getObjectKeys } from "@develop/fe-library/dist/utils"
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import {
  isLostCaseStatisticsGraphDataLoadingSelector,
  lostCaseStatisticsGraphDataSelector,
} from "selectors/lostCaseStatisticsSelectors"
import { languageSelector } from "selectors/translationsSelectors"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"
import ln from "utils/localeNumber"

import { statuses } from "consts/lostCase"

import { ChartTooltip, Legends } from "./components"

import { buildTickFormatter } from "./utils"

import {
  BAR_CATEGORY_GAP,
  BAR_GAP,
  BAR_SIZE,
  GRAPH_VERTICAL_AXIS_WIDTH,
} from "./constants"

import { StatusOption } from "components/LostCaseStatistics/LostCaseStatisticsTypes"

export const Chart = ({ lostCaseTypesOptions }) => {
  const locale = useSelector(languageSelector)

  const isLoading = useSelector(isLostCaseStatisticsGraphDataLoadingSelector)

  const lostCaseStatisticsGraphData = useSelector(
    lostCaseStatisticsGraphDataSelector
  )

  const statusesList: Array<StatusOption> = useMemo(() => {
    return getObjectKeys(statuses).map((key) => {
      const item = statuses[key]

      return {
        value: item?.key.toString(),
        label: item?.label.toString(),
        color: item?.color.toString(),
      }
    })
  }, [])

  const width = useMemo(() => {
    const statusesCount = statusesList.length
    const caseTypesCount = lostCaseStatisticsGraphData.length

    const caseTypeWidth = statusesCount * (BAR_SIZE + BAR_GAP)

    return (
      caseTypesCount * (BAR_CATEGORY_GAP + caseTypeWidth) +
      GRAPH_VERTICAL_AXIS_WIDTH
    )
  }, [lostCaseStatisticsGraphData.length, statusesList.length])

  const tooltipOffset = useMemo(() => {
    return (statusesList.length * (BAR_SIZE + BAR_GAP) + BAR_CATEGORY_GAP) / 2
  }, [statusesList.length])

  const formatYAxis = (value: number): string => {
    return ln(value, null, {
      notation: "compact",
      minimumFractionDigits: 0,
      maximumFractionDigits: 1,
      locale,
    })
  }

  const hasNoData: boolean =
    !checkIsArray(lostCaseStatisticsGraphData) && !isLoading

  return (
    <Box
      hasBorder
      borderRadius="--border-radius"
      width="100%"
      mSM={{
        flexDirection: "column",
        padding: "m",
        gap: "m",
      }}
      mXL={{
        flexDirection: "row",
        padding: "l",
        gap: "l",
      }}
    >
      <Box
        className="chart-with-watermark"
        overflowX="scroll"
        overflowY="clip"
        width="100%"
        mSM={{
          height: 350,
        }}
        mXL={{
          height: "auto",
        }}
      >
        {hasNoData ? (
          <Box
            align="center"
            height="100%"
            justify="center"
            position="absolute"
            width="100%"
          >
            <Typography color="--color-text-main" variant="--font-headline-3">
              {l("No data found for the selected period")}
            </Typography>
          </Box>
        ) : null}

        <ResponsiveContainer height="100%" minWidth="100%" width={width}>
          <BarChart
            barCategoryGap={BAR_CATEGORY_GAP}
            barGap={BAR_GAP}
            barSize={BAR_SIZE}
            data={lostCaseStatisticsGraphData}
          >
            <XAxis
              dataKey="name"
              stroke="var(--color-border-main)"
              tickFormatter={buildTickFormatter(lostCaseTypesOptions)}
              tickLine={false}
              tick={{
                fontFamily: "Roboto",
                fontSize: "12px",
                fill: "var(--color-text-placeholders)",
              }}
            />
            <YAxis
              axisLine={false}
              stroke="var(--color-text-second)"
              tickFormatter={formatYAxis}
              tickLine={false}
            />

            <Tooltip
              cursor={false}
              offset={tooltipOffset}
              content={
                <ChartTooltip
                  buildTitle={buildTickFormatter(lostCaseTypesOptions)}
                  statusesList={statusesList}
                />
              }
            />

            {statusesList.map(({ value, color = "grey" }) => (
              <Bar
                key={value}
                dataKey={value}
                fill={color}
                radius={[2, 2, 0, 0]}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      </Box>
      <Legends statusesList={statusesList} />
    </Box>
  )
}
