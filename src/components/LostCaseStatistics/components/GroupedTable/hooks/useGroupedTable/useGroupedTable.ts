import { useDispatch, useSelector } from "react-redux"

import lostCaseStatisticsActions from "actions/lostCaseStatisticsActions"

import {
  lostCaseStatisticsGroupedSearchOptionsSelector,
  lostCaseStatisticsGroupedSelector,
} from "selectors/lostCaseStatisticsSelectors"

import { SEARCH_OPTIONS_INITIAL } from "components/LostCaseStatistics/constants"

import { useSearchParams } from "hooks"

import { combineArrayFilters } from "utils/combineArrayFilters"
import { combineDateRangeFilters } from "utils/combineDateRangeFilters"

import {
  SearchOptions,
  SearchParams,
  UseTable,
} from "components/LostCaseStatistics/LostCaseStatisticsTypes"

import { DataItem } from "./UseGroupedTableTypes"

// @ts-ignore
const { setGroupedSearchOptions, getLostCaseStatisticsGrouped } =
  lostCaseStatisticsActions

export const useGroupedTable: UseTable<DataItem> = () => {
  const dispatch = useDispatch()

  const searchOptions = useSelector(
    lostCaseStatisticsGroupedSearchOptionsSelector
  )

  const { data = [], totalCount = 0 } = useSelector(
    lostCaseStatisticsGroupedSelector
  )

  const {
    caseType: selectedCaseTypes = [],
    customerId: selectedCustomers = "",
    createdAt,
  } = useSearchParams<SearchParams>(["caseType", "customerId", "createdAt"])

  const getData = (searchOptionsNew: SearchOptions<DataItem>): void => {
    const searchOptionsAll: SearchOptions<DataItem> = {
      ...SEARCH_OPTIONS_INITIAL,
      ...searchOptionsNew,
    }

    const combinedCaseTypes = combineArrayFilters({
      filterValue: searchOptionsAll.caseType,
      globalFilterValue: selectedCaseTypes,
    })

    const customerIdsGlobal = selectedCustomers
      ? selectedCustomers?.split(",")
      : []

    const combinedCustomerId = combineArrayFilters({
      filterValue: [],
      globalFilterValue: customerIdsGlobal,
    })?.join(",")

    const createdAtCombined = combineDateRangeFilters({
      globalFilterValue: createdAt,
    })

    const requestParams = {
      ...searchOptionsAll,
      caseType: combinedCaseTypes,
      customerId: combinedCustomerId,
      createdAt: createdAtCombined,
    }

    dispatch(setGroupedSearchOptions(searchOptionsAll))

    dispatch(
      getLostCaseStatisticsGrouped(
        requestParams,
        () => {},
        () => {}
      )
    )
  }

  return {
    data,
    totalCount,
    searchOptions,
    getData,
  }
}
