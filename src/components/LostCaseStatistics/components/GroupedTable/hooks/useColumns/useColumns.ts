import { useMemo } from "react"

import {
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "components/TableGridLayout/TableGridLayoutView"

import l from "utils/intl"

import { UseColumns } from "components/LostCaseStatistics/LostCaseStatisticsTypes"

export const useColumns: UseColumns = ({
  lostCaseTypesOptions,
  statusOptions,
}) => {
  return useMemo(() => {
    return [
      {
        title: "Case type",
        dataIndex: "caseType",
        key: "caseType",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
        width: 120,
        render: (caseType: string) => {
          return lostCaseTypesOptions?.find(
            (option) => option.value === caseType
          )?.label
        },
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
        width: 120,
        render: (status: string) => {
          const label = statusOptions?.find(
            (option) => option.value === status
          )?.label

          return label ? l(label) : ""
        },
      },
      {
        title: "Unique customers",
        dataIndex: "countCustomers",
        key: "countCustomers",
        type: COLUMN_INPUT_TYPE_NUMBER,
        sorter: true,
        width: 120,
      },
      {
        title: "Amount of cases",
        dataIndex: "countCases",
        key: "countCases",
        type: COLUMN_INPUT_TYPE_NUMBER,
        sorter: true,
        width: 120,
      },
    ]
  }, [lostCaseTypesOptions])
}
