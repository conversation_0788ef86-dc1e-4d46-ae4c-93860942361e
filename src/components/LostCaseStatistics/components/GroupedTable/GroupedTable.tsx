import React from "react"
import { Box } from "@develop/fe-library"

import { LostCaseStatisticsGroupedTableSettings } from "initialState/tableSettings"

import {
  SEARCH_OPTIONS_INITIAL,
  TABLE_FOOTER_GRID_COLUMNS,
} from "components/LostCaseStatistics/constants"
import { TableWrapper } from "components/shared/TableWrapper"

import { useColumns, useGroupedTable } from "./hooks"

import {
  SelectFilterOptions,
  TableProps,
} from "components/LostCaseStatistics/LostCaseStatisticsTypes"

import styles from "./groupedTable.module.scss"

export const GroupedTable = ({
  lostCaseTypesOptions,
  filterPrefix,
  statusOptions,
}: TableProps) => {
  const columns = useColumns({ lostCaseTypesOptions, statusOptions })

  const { data, totalCount, searchOptions, getData } = useGroupedTable()

  const selectFiltersOptions: SelectFilterOptions = {
    status: statusOptions,
    caseType: lostCaseTypesOptions,
  }

  return (
    <Box hasBorder borderRadius="--border-radius" display="block" width="100%">
      <TableWrapper
        // @ts-ignore
        isCustomPageSize
        isNeedSort
        actionsColumn={false}
        componentTableSettings={LostCaseStatisticsGroupedTableSettings}
        contentClassName={styles.table}
        customDefaultSort="status"
        customPageSize={SEARCH_OPTIONS_INITIAL.pageSize}
        dataSource={data}
        filterPrefix={filterPrefix}
        footerGridColumns={TABLE_FOOTER_GRID_COLUMNS}
        footerGridContainerStyles={{ height: "unset" }}
        getData={getData}
        isCopyrightVisibleWithNoData={false}
        isPageSizeSelectVisible={false}
        isResizingEnabled={false}
        isSettingButtonsVisible={false}
        pageTableSettings={LostCaseStatisticsGroupedTableSettings}
        rowKey="index"
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableColumns={columns}
        totalCount={totalCount}
        withExport={false}
        tableFooterStyles={{
          position: "static",
          padding: "var(--padding-m)",
          justifyContent: "center",
        }}
        urlFiltersWhiteList={[
          "page",
          "pageSize",
          "sort",
          "status",
          "caseType",
          "countCustomers",
          "countCases",
        ]}
      />
    </Box>
  )
}
