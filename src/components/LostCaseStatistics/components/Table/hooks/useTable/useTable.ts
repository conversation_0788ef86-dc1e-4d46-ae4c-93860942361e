import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"

import lostCaseStatisticsActions from "actions/lostCaseStatisticsActions"

import {
  lostCaseStatisticsSearchOptionsSelector,
  lostCaseStatisticsSelector,
} from "selectors/lostCaseStatisticsSelectors"

import { SEARCH_OPTIONS_INITIAL } from "components/LostCaseStatistics/constants"

import { useSearchParams } from "hooks"

import { checkIsArray } from "utils/arrayHelpers"
import { combineArrayFilters } from "utils/combineArrayFilters"
import { combineDateRangeFilters } from "utils/combineDateRangeFilters"

import {
  OnEdit,
  SearchOptions,
  SearchParams,
  UseTable,
} from "components/LostCaseStatistics/LostCaseStatisticsTypes"

import { DataItem } from "./UseTableTypes"

// @ts-ignore
const { setSearchOptions, getLostCaseStatistics, clearLostCaseStatistics } =
  lostCaseStatisticsActions

export const useTable: UseTable<DataItem> = () => {
  const { push } = useHistory()

  const dispatch = useDispatch()

  const searchOptions = useSelector(lostCaseStatisticsSearchOptionsSelector)

  const { data = [], totalCount } = useSelector(lostCaseStatisticsSelector)

  const {
    caseType: selectedCaseTypes = [],
    customerId: selectedCustomers = "",
    createdAt,
  } = useSearchParams<SearchParams>(["caseType", "customerId", "createdAt"])

  const getData = (searchOptionsNew: SearchOptions<DataItem>): void => {
    const searchOptionsAll: SearchOptions<DataItem> = {
      ...SEARCH_OPTIONS_INITIAL,
      ...searchOptionsNew,
    }

    const combinedCaseTypes = combineArrayFilters({
      filterValue: searchOptionsAll.caseType,
      globalFilterValue: selectedCaseTypes,
    })

    const customerIdsLocal = searchOptionsAll.customerId
      ? searchOptionsAll.customerId.split(",")
      : []

    const customerIdsGlobal = selectedCustomers
      ? selectedCustomers?.split(",")
      : []

    const areBothCustomerFiltersSet: boolean =
      checkIsArray(customerIdsGlobal) && checkIsArray(customerIdsLocal)

    if (areBothCustomerFiltersSet) {
      const hasAtLeastOneMatch: boolean = customerIdsLocal.some((item) =>
        customerIdsGlobal.includes(item)
      )

      if (!hasAtLeastOneMatch) {
        dispatch(clearLostCaseStatistics())

        return
      }
    }

    const combinedCustomerId = combineArrayFilters({
      filterValue: customerIdsLocal,
      globalFilterValue: customerIdsGlobal,
    })?.join(",")

    const createdAtCombined = combineDateRangeFilters({
      filterValue: searchOptionsAll.createdAt,
      globalFilterValue: createdAt,
    })

    const requestParams = {
      ...searchOptionsAll,
      caseType: combinedCaseTypes,
      customerId: combinedCustomerId,
      createdAt: createdAtCombined,
    }

    dispatch(setSearchOptions(searchOptionsAll))

    dispatch(
      getLostCaseStatistics(
        requestParams,
        () => {},
        () => {}
      )
    )
  }

  const handleEdit: OnEdit = ({ id }) => {
    push(`/lost/cases/${id}`)
  }

  return {
    data,
    totalCount,
    searchOptions,
    getData,
    onEdit: handleEdit,
  }
}
