import { useMemo } from "react"
import moment, { Moment } from "moment/moment"

import {
  COLUMN_INPUT_TYPE_DATE_RANGE,
  COLUMN_INPUT_TYPE_NUMBER,
  COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
} from "components/TableGridLayout/TableGridLayoutView"

import { useSearchParams } from "hooks"

import { convertToLocalDate } from "utils/dateConverter"
import { getDateRangeFromString } from "utils/getDateRangeFromString"
import l from "utils/intl"

import {
  SearchParams,
  UseColumns,
} from "components/LostCaseStatistics/LostCaseStatisticsTypes"

export const useColumns: UseColumns = ({
  lostCaseTypesOptions,
  statusOptions,
}) => {
  const { createdAt } = useSearchParams<SearchParams>(["createdAt"])

  return useMemo(() => {
    const startOfMonth: Moment = moment().startOf("month")

    const clearToDates: [Moment, Moment] = [startOfMonth, moment()]

    const [from, to] = getDateRangeFromString({
      dateRange: createdAt,
      defaultDates: clearToDates,
    })

    return [
      {
        title: "Case type",
        dataIndex: "case_type",
        key: "caseType",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
        width: 120,
        render: (case_type: string) => {
          return lostCaseTypesOptions?.find(
            (option) => option.value === case_type
          )?.label
        },
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        sorter: true,
        type: COLUMN_INPUT_TYPE_SELECT_MULTIPLE,
        width: 120,
        render: (status: string) => {
          const label = statusOptions?.find(
            (option) => option.value === status
          )?.label

          return label ? l(label) : ""
        },
      },
      {
        title: "Creation date",
        dataIndex: "created_at",
        key: "createdAt",
        sorter: true,
        type: COLUMN_INPUT_TYPE_DATE_RANGE,
        filterType: "between",
        isDatePickerWithFilter: false,
        showTimeZoneToggle: false,
        isClearDatesToReset: true,
        convertUtcToUserTimeZone: false,
        clearToDates: [from, to],
        disabledDate: (currentDate: moment.Moment) => {
          return !currentDate.isBetween(from, to, "days", "[]")
        },
        width: 120,
        render: (created_at: string) => {
          return !created_at
            ? l("N/A")
            : convertToLocalDate(created_at, { timeZone: "UTC" }, true)
        },
      },
      {
        title: "Customer ID",
        dataIndex: "customer_id",
        key: "customerId",
        type: COLUMN_INPUT_TYPE_NUMBER,
        sorter: true,
        width: 120,
      },
    ]
  }, [lostCaseTypesOptions, createdAt])
}
