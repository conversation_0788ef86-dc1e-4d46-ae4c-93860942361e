import React from "react"
import { Box } from "@develop/fe-library"

import { LostCaseStatisticsTableSettings } from "initialState/tableSettings"

import {
  SEARCH_OPTIONS_INITIAL,
  TABLE_FOOTER_GRID_COLUMNS,
} from "components/LostCaseStatistics/constants"
import { TableWrapper } from "components/shared/TableWrapper"

import { useColumns, useTable } from "./hooks"

import {
  SelectFilterOptions,
  TableProps,
} from "components/LostCaseStatistics/LostCaseStatisticsTypes"

import styles from "./table.module.scss"

export const Table = ({
  lostCaseTypesOptions,
  filterPrefix,
  statusOptions,
}: TableProps) => {
  const columns = useColumns({ lostCaseTypesOptions, statusOptions })

  const { data, totalCount, searchOptions, getData, onEdit } = useTable()

  const selectFiltersOptions: SelectFilterOptions = {
    status: statusOptions,
    caseType: lostCaseTypesOptions,
  }

  return (
    <Box hasBorder borderRadius="--border-radius" display="block" width="100%">
      <TableWrapper
        // @ts-ignore
        isCustomPageSize
        isNeedSort
        isStartFromColumnZeroIndex
        actionsColumnWidth={60}
        componentTableSettings={LostCaseStatisticsTableSettings}
        contentClassName={styles.table}
        customPageSize={SEARCH_OPTIONS_INITIAL.pageSize}
        dataSource={data}
        filterPrefix={filterPrefix}
        footerGridColumns={TABLE_FOOTER_GRID_COLUMNS}
        footerGridContainerStyles={{ height: "unset" }}
        getData={getData}
        isCopyrightVisibleWithNoData={false}
        isPageSizeSelectVisible={false}
        isResizingEnabled={false}
        isSettingButtonsVisible={false}
        pageTableSettings={LostCaseStatisticsTableSettings}
        searchOptions={searchOptions}
        selectFiltersOptions={selectFiltersOptions}
        tableColumns={columns}
        totalCount={totalCount}
        withExport={false}
        tableFooterStyles={{
          position: "static",
          padding: "var(--padding-m)",
          justifyContent: "center",
        }}
        tableIcons={[
          {
            onClick: onEdit,
            title: "Edit",
            name: "icnEdit",
          },
        ]}
        urlFiltersWhiteList={[
          "page",
          "pageSize",
          "sort",
          "status",
          "caseType",
          "createdAt",
          "customerId",
        ]}
      />
    </Box>
  )
}
