import { useEffect } from "react"
import { useDispatch } from "react-redux"
import { useLocation } from "react-router-dom"
import { DATE_OUTPUT_FORMATS } from "@develop/fe-library/dist/consts"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"
import { format, startOfDay, startOfMonth } from "date-fns"

import gridActions from "actions/gridActions"
import lostCaseStatisticsActions from "actions/lostCaseStatisticsActions"

import { checkIsArray } from "utils/arrayHelpers"
import { removeNullAndUndefined } from "utils/objectHelpers"

import { SearchParams } from "components/LostCaseStatistics/LostCaseStatisticsTypes"

import { CreatedAtUrlParams, InputModeType } from "types/UrlParams/dates"

import { OnSelectCaseTypes, OnSelectCustomers } from "./UseHeaderTypes"

// @ts-ignore
const { pushUrl } = gridActions
const { getLostCaseStatisticsGraphData } = lostCaseStatisticsActions

export const useHeader = () => {
  const dispatch = useDispatch()

  const { search } = useLocation()

  const searchParams = getUrlSearchParams<SearchParams & InputModeType>({
    locationSearch: search,
  })

  const {
    customerId: selectedCustomers = "",
    caseType: selectedCaseTypes = [],
    createdAt,
    inputMode,
  } = searchParams

  const handleUpdateUrlParams = (
    params: SearchParams & InputModeType
  ): void => {
    const allParams = {
      ...searchParams,
      ...(params ?? {}),
    }

    const { createdAt, customerId, caseType } = allParams

    const createdAtWithTime = (): string => {
      const [from, to] = createdAt?.split(" - ") || []

      const fromWithTime = format(
        startOfDay(new Date(from)),
        DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT_WITH_TIME
      )
      const toWithTime = format(
        startOfDay(new Date(to)),
        DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT_WITH_TIME
      )

      return `${fromWithTime} - ${toWithTime}`
    }

    const requestParams = removeNullAndUndefined({
      customerId,
      caseType,
      createdAt: createdAtWithTime(),
    })

    const urlParamsNew = removeNullAndUndefined(allParams)

    dispatch(pushUrl(urlParamsNew))

    dispatch(
      getLostCaseStatisticsGraphData(
        requestParams,
        () => {},
        () => {}
      )
    )
  }

  const handleSelectDateRange = (params: CreatedAtUrlParams): void => {
    handleUpdateUrlParams(params)
  }

  const handleSelectCustomers: OnSelectCustomers = (selectedCustomersNew) => {
    if (!selectedCustomersNew) {
      handleUpdateUrlParams({
        customerId: undefined,
      })

      return
    }

    handleUpdateUrlParams({
      customerId: selectedCustomersNew.join(","),
    })
  }

  const handleSelectCaseTypes: OnSelectCaseTypes = (selectedCaseTypes) => {
    if (!checkIsArray(selectedCaseTypes)) {
      handleUpdateUrlParams({
        caseType: undefined,
      })

      return
    }

    const selectedCaseTypesAsStrings = selectedCaseTypes.map(
      (caseType) => caseType as string
    )

    handleUpdateUrlParams({
      caseType: selectedCaseTypesAsStrings,
    })
  }

  const handleClearCaseTypes = (): void => {
    handleSelectCaseTypes([])
  }

  useEffect(() => {
    const isDateRangeInitialized: boolean = !!createdAt && !!inputMode

    if (isDateRangeInitialized) {
      handleUpdateUrlParams({})

      return
    }

    const today = new Date()
    const from = format(
      startOfMonth(today),
      DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT
    )
    const to = format(today, DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT)

    handleUpdateUrlParams({
      createdAt: `${from} - ${to}`,
      inputMode: "currentMonth",
    })
  }, [])

  return {
    handleSelectDateRange,

    selectedCustomers: selectedCustomers ? selectedCustomers.split(",") : [],
    handleSelectCustomers,

    selectedCaseTypes,
    handleSelectCaseTypes,
    handleClearCaseTypes,
  }
}
