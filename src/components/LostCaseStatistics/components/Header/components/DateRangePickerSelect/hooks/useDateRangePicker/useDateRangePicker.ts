import { useCallback, useEffect, useState } from "react"
import { useSelector } from "react-redux"
import type { InputMode } from "@develop/fe-library"
import { DATE_OUTPUT_FORMATS } from "@develop/fe-library/dist/consts"
import { endOfDay, format, Interval } from "date-fns"

import { staffCurrentUserLocaleSelector } from "selectors/staffSelectors"
import { languageSelector } from "selectors/translationsSelectors"

import { useSearchParams } from "hooks"

import { getDateRangePickerLabels } from "utils/datePicker"
import l from "utils/intl"

import { LOCALES } from "consts/locales"

import { getRangeFromParam } from "./constants"

import { DateRangePickerSelectedTypes } from "types/DatePicker"
import { HandleSetUrlParams } from "types/UrlParams"
import { CreatedAtUrlParams } from "types/UrlParams/dates"

export const useDateRangePicker = (
  onSetUrlParams: HandleSetUrlParams<CreatedAtUrlParams>
) => {
  const urlParams = useSearchParams(["createdAt", "inputMode"])

  const language = useSelector(languageSelector)
  const locale = useSelector(staffCurrentUserLocaleSelector)

  const dateRangePickerLabels = getDateRangePickerLabels({
    clear: l("Close"),
  })

  const [inputMode, setInputMode] = useState<InputMode | undefined>(() => {
    if (!urlParams.inputMode) {
      return undefined
    }

    return urlParams.inputMode as InputMode
  })

  const [range, setRange] = useState<Interval>()

  useEffect(() => {
    const isUrlRangeEmpty: boolean = !urlParams.createdAt

    if (isUrlRangeEmpty) {
      return
    }

    // HINT: Typescript moment
    // DESC: If the range is not initialized and the urlParams.createdAt are exist
    //       we should populate createdAt to the range
    if (!range && urlParams.createdAt) {
      const nextRange = getRangeFromParam(urlParams.createdAt)

      setRange(nextRange)
    }
  }, [range, urlParams.createdAt])

  useEffect(() => {
    const isInputModeEmpty: boolean = !inputMode && !!urlParams.inputMode

    if (isInputModeEmpty) {
      setInputMode(urlParams.inputMode as InputMode)
    }
  }, [inputMode, urlParams.inputMode])

  const handleDateRangeSelect = useCallback(
    (value: DateRangePickerSelectedTypes) => {
      setRange(value.selected)
      setInputMode(value.inputMode)

      const from = format(
        value.selected.start,
        DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT
      )
      const to = format(
        value.selected.end,
        DATE_OUTPUT_FORMATS.SERVER_DATE_FORMAT
      )

      onSetUrlParams({
        createdAt: `${from} - ${to}`,
        inputMode: value.inputMode,
      })
    },
    []
  )

  return {
    dateRangePickerLocale: LOCALES[locale],
    dateRangePickerLanguage: LOCALES[language],
    dateRangePickerLabels,
    toDate: endOfDay(new Date()),
    range,
    inputMode,
    handleDateRangeSelect,
  }
}
