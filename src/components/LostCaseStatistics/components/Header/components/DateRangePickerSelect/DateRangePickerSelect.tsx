import React, { memo } from "react"
import { DateRangePicker } from "@develop/fe-library"

import { useDateRangePicker } from "./hooks/useDateRangePicker"

import { DateRangePickerSelectProps } from "./DateRangePickerSelectTypes"

const DateRangePickerSelectComponent = ({
  onSetUrlParams,
}: DateRangePickerSelectProps) => {
  const {
    dateRangePickerLabels,
    dateRangePickerLanguage,
    dateRangePickerLocale,
    handleDateRangeSelect,
    inputMode,
    range,
    toDate,
  } = useDateRangePicker(onSetUrlParams)

  return (
    <DateRangePicker
      isFullWidth
      inputMode={inputMode}
      isClearIconVisible={false}
      labels={dateRangePickerLabels}
      language={dateRangePickerLanguage}
      locale={dateRangePickerLocale}
      selected={range}
      toDate={toDate}
      onSelect={handleDateRangeSelect}
    />
  )
}

export const DateRangePickerSelect = memo(DateRangePickerSelectComponent)
