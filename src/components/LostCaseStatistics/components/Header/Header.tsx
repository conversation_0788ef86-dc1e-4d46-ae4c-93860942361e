import React from "react"
import { Box, Select, Typography } from "@develop/fe-library"

import { CustomerSelectionIcon } from "components/CustomerSelectionIcon"

import { checkIsArray } from "utils/arrayHelpers"
import { getSelectedCountText } from "utils/getSelectedCountText"
import l from "utils/intl"

import { useHeader } from "./hooks"

import { DateRangePickerSelect } from "./components/DateRangePickerSelect"

import { HeaderProps } from "./HeaderTypes"

export const Header = ({ lostCaseTypesOptions }: HeaderProps) => {
  const {
    handleSelectDateRange,

    selectedCustomers,
    handleSelectCustomers,

    selectedCaseTypes,
    handleClearCaseTypes,
    handleSelectCaseTypes,
  } = useHeader()

  if (!checkIsArray(lostCaseTypesOptions)) {
    return null
  }

  return (
    <>
      <Box flexDirection="column" gap="m">
        <Typography variant="--font-headline-3">
          {l("Case statistics")}
        </Typography>
        <Box
          gap="m"
          mSM={{
            display: "flex",
            flexDirection: "column",
          }}
          mXL={{
            display: "grid",
            gridTemplateColumns: "200px 200px auto",
          }}
        >
          <DateRangePickerSelect onSetUrlParams={handleSelectDateRange} />

          <Select
            hasClearIcon
            hasSearch
            isFullWidth
            isMultiSelect
            defaultValue={selectedCaseTypes}
            label={l("Case type")}
            options={lostCaseTypesOptions}
            renderSelectedCount={getSelectedCountText}
            tagsMode="count"
            onClear={handleClearCaseTypes}
            onSelectValue={handleSelectCaseTypes}
          />

          <CustomerSelectionIcon
            selectedCustomers={selectedCustomers}
            onSelectCustomers={handleSelectCustomers}
          />
        </Box>
      </Box>
    </>
  )
}
