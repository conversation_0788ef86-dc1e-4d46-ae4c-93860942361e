import React from "react"
import { Box, Grid } from "@develop/fe-library"

import { Chart, GroupedTable, Header, Table } from "./components"

import { useLostCaseStatistics } from "./hooks"

import { FILTER_PREFIXES, ITEM_PROPS } from "./constants"

export const LostCaseStatistics = () => {
  const { lostCaseTypesOptionsAll, lostCaseTypesOptions, statusOptions } =
    useLostCaseStatistics()

  return (
    <Box flexDirection="column">
      <Box {...ITEM_PROPS}>
        <Header lostCaseTypesOptions={lostCaseTypesOptionsAll} />
      </Box>

      <Box {...ITEM_PROPS}>
        <Grid container gap="l">
          <Grid item mSM={12}>
            <Chart lostCaseTypesOptions={lostCaseTypesOptions} />
          </Grid>

          <Grid item dMD={6} mSM={12}>
            <Table
              filterPrefix={FILTER_PREFIXES.lcs}
              lostCaseTypesOptions={lostCaseTypesOptions}
              statusOptions={statusOptions}
            />
          </Grid>

          <Grid item dMD={6} mSM={12}>
            <GroupedTable
              filterPrefix={FILTER_PREFIXES.lcsg}
              lostCaseTypesOptions={lostCaseTypesOptions}
              statusOptions={statusOptions}
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}
