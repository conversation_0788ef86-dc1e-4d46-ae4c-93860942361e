import React, { useState, useEffect, useCallback } from "react"
import PropTypes from "prop-types"
import { Upload } from "antd"
import cn from "classnames"

import styles from "./S3Uploader.module.scss"

const { Dragger } = Upload

const getCleanFileUrl = (fullUrl) => fullUrl.split("?")[0]
const S3Uploader = ({
  accept,
  maxCount,
  getSignedUrl,
  onUploadFinish,
  content,
  className,
  shouldClear,
  onChangeAttachment,
  updateClearAttachment,
  listType,
  imageUrl,
  withDrag,
  hideAfterUpload,
  errorCallback,
  onSuccessValidation,
  disabled,
  multiple,
}) => {
  const [fileList, setFileList] = useState([])
  const [isUploading, setIsUploading] = useState(false)

  const UploadWrapper = withDrag ? Dragger : Upload

  useEffect(() => {
    if (shouldClear) {
      setFileList([])
    }
    if (imageUrl) {
      setFileList([
        {
          uid: "-1",
          status: "done",
          name: imageUrl.split("/").pop(),
          url: imageUrl,
        },
      ])
    }
  }, [shouldClear, imageUrl])

  const onChange = useCallback(
    ({ fileList: newFileList }) => {
      setFileList(newFileList)
      onChangeAttachment(newFileList.map((newFile) => newFile.name))
    },
    [onChangeAttachment]
  )

  const onPreview = async (file) => {
    let src = file.url
    if (!src) {
      src = await new Promise((resolve) => {
        const reader = new FileReader()
        reader.readAsDataURL(file.originFileObj)
        reader.onload = () => resolve(reader.result)
      })
    }
    const image = new Image()
    image.src = src
    const imgWindow = window.open(src)
    imgWindow.document.write(image.outerHTML)
  }

  const uploadImage = async (options) => {
    const { onSuccess, file } = options
    const isLt1M = file.size / 1024 / 1024 < 1
    const isPicture =
      file.type === "image/jpeg" ||
      file.type === "image/png" ||
      file.type === "image/gif"
    if (!isLt1M || !isPicture) {
      errorCallback && errorCallback()
      setFileList([])
      return
    }
    onSuccessValidation && onSuccessValidation()
    setIsUploading(true)
    const clearFileName = file.name.replace(/\s+/g, "-")
    getSignedUrl(
      clearFileName,
      async (response) => {
        const uploadResponse = await fetch(response.data.url, {
          method: "PUT",
          body: file,
        })
        onSuccess("Ok")
        onUploadFinish({
          fileName: clearFileName,
          fileUrl: getCleanFileUrl(uploadResponse.url),
        })
        updateClearAttachment && updateClearAttachment()
        setIsUploading(false)
      },
      (errors) => {
        if (errorCallback) {
          errorCallback(errors)
        }

        setIsUploading(false)
        setFileList([])
      }
    )
  }

  return (
    <>
      <UploadWrapper
        className={cn(className, {
          [styles.hidden]:
            hideAfterUpload && (isUploading || fileList.length > 0),
        })}
        accept={accept}
        listType={listType || "picture"}
        maxCount={maxCount}
        fileList={fileList}
        onChange={onChange}
        onPreview={onPreview}
        customRequest={uploadImage}
        multiple={multiple}
        disabled={disabled}
      >
        {!isUploading ? content : null}
      </UploadWrapper>
    </>
  )
}

S3Uploader.propTypes = {
  accept: PropTypes.string,
  maxCount: PropTypes.number,
  getSignedUrl: PropTypes.func.isRequired,
  onUploadFinish: PropTypes.func.isRequired,
  content: PropTypes.element,
  signedUrl: PropTypes.string,
  shouldClear: PropTypes.bool,
  multiple: PropTypes.bool,
}

S3Uploader.defaultProps = {
  accept: "*",
  signedUrl: "",
  shouldClear: false,
  withDrag: false,
  hideAfterUpload: false,
  disabled: false,
  multiple: true,
}

export default S3Uploader
