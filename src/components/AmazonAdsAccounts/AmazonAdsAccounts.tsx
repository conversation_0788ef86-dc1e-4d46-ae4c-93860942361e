import React, { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

import amazonAdsAccountsActions from "actions/amazonAdsAccountsActions"
import basCustomerPlanActions from "actions/basCustomerPlanActions"

import { amazonAdsAccountsSelector } from "selectors/amazonAdsAccountsSelectors"
import { isBasActive as isBasActiveSelector } from "selectors/basCustomerPlanSelector"

import { useAccountScroll } from "hooks"

import { AmazonAdsAccount, HeaderSection } from "./components"

import { BLOCK_ID_PREFIX } from "./constants"

import { AmazonAdsAccount as AmazonAdsAccountType } from "types"

const { getAmazonAdsAccounts } = amazonAdsAccountsActions
const { getBasPlan } = basCustomerPlanActions

export const AmazonAdsAccounts = () => {
  const dispatch = useDispatch()

  const amazonAdsAccounts: Array<AmazonAdsAccountType> = useSelector(
    amazonAdsAccountsSelector
  )
  const isBasActive = useSelector(isBasActiveSelector)

  const {
    initiallyTargetedId,
    initiallyTargetedTabKey,
    buildHandleChangeHash,
  } = useAccountScroll({
    BLOCK_ID_PREFIX,
  })

  useEffect(() => {
    dispatch(getAmazonAdsAccounts({}, false, () => {}))
    dispatch(getBasPlan({ cache: true, successCallback: null }))
  }, [])

  return (
    <div>
      <HeaderSection count={amazonAdsAccounts?.length} />
      {amazonAdsAccounts?.map((account) => {
        const { id } = account
        const blockId = BLOCK_ID_PREFIX + id

        const tabKeyInitial =
          id.toString() === initiallyTargetedId
            ? initiallyTargetedTabKey
            : undefined

        return (
          <div key={id} id={blockId}>
            <AmazonAdsAccount
              account={account}
              isBasActive={isBasActive}
              tabKeyInitial={tabKeyInitial}
              onChangeHash={buildHandleChangeHash(id)}
            />
          </div>
        )
      })}
    </div>
  )
}
