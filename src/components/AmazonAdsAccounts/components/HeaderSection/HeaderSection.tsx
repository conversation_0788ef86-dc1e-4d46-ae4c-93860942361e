import React, { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useHistory } from "react-router-dom"
import { Box, Typography } from "@develop/fe-library"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import amazonAdsAccountsWizardActions from "actions/amazonAdsAccountsWizardActions"

import { isBasActive as isBasActiveSelector } from "selectors/basCustomerPlanSelector"

import { RestrictedButtonPopover } from "components/shared/Buttons"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages, STATUSES } from "consts"
import { PRODUCT_NAMES } from "consts/product"

const { start } = amazonAdsAccountsWizardActions

export const HeaderSection = ({ count }) => {
  const dispatch = useDispatch()
  const history = useHistory()

  const isBasActive = useSelector(isBasActiveSelector)

  const urlSearchParams = getUrlSearchParams({
    locationSearch: document.location.search,
  })

  const handleCreateAccount = (): void => {
    if (urlSearchParams?.shouldOpenAdsAccountCreateModal) {
      const urlParams = new URLSearchParams(document.location.search)

      urlParams.delete("shouldOpenAdsAccountCreateModal")

      history.replace({
        search: urlParams.toString(),
      })
    }

    dispatch(start(STATUSES.ready))
  }

  const addButtonPopoverContent = isBasActive
    ? l("Add Ads account")
    : l("Enable {productName} subscription to create Amazon Ads account", {
        productName: PRODUCT_NAMES.bas,
      })

  useEffect(() => {
    if (urlSearchParams?.shouldOpenAdsAccountCreateModal === "1") {
      handleCreateAccount()
    }
  }, [urlSearchParams?.shouldOpenAdsAccountCreateModal])

  return (
    <Box
      gap="m"
      hasBorder={{ bottom: true }}
      mSM={{
        padding: "m",
        align: "flex-start",
      }}
      mXL={{
        padding: "m l",
        align: "center",
      }}
    >
      <Box flexDirection="column" flexGrow={1}>
        <Typography variant="--font-headline-3">
          {l("Amazon Ads account management")}
        </Typography>
        <Typography
          color="--color-text-placeholders"
          variant="--font-body-text-4"
        >
          {l("{count} accounts", { count })}
        </Typography>
      </Box>

      <RestrictedButtonPopover
        iconOnly
        content={addButtonPopoverContent}
        disabled={!isBasActive}
        icon="icnPlus"
        managePermission={permissionKeys.amazonAdsAccountManage}
        placement="topRight"
        popoverMessage={l(restrictPopoverMessages.alter)}
        popoverPlacement="topRight"
        variant="primary"
        onClick={handleCreateAccount}
      />
    </Box>
  )
}
