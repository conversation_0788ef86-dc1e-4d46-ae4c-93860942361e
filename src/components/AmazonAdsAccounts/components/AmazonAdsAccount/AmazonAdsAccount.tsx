import React, { useState } from "react"
import { useDispatch } from "react-redux"
import { <PERSON>ert, Box, Typography } from "@develop/fe-library"

import amazonAdsAccountsActions from "actions/amazonAdsAccountsActions"

import { RestrictedPrimaryButton } from "components/shared/Buttons"
import TabsComponent from "components/TabsComponent/TabsComponent"

import { DELETE_ITEM, setConfirm } from "utils/confirm"
import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"

import {
  AccountInformation,
  BusinessAnalytics,
  BusinessAnalyticsNotConnected,
} from "./components"

import { TAB_KEYS } from "./constants"

import type { AmazonAdsAccountProps } from "./AmazonAdsAccountTypes"

const { deleteAmazonAdsAccount } = amazonAdsAccountsActions

export const AmazonAdsAccount = ({
  account,
  onChangeHash,
  tabKeyInitial,
  isBasActive,
}: AmazonAdsAccountProps) => {
  const dispatch = useDispatch()

  const { id, name, is_deleted } = account

  const [activeTab, setActiveTab] = useState(
    tabKeyInitial || TAB_KEYS.accountInfo
  )

  const handleChangeTab = (tabKey: string): void => {
    setActiveTab(tabKey)
    onChangeHash(tabKey)
  }

  const handleDeleteAccount = (): void => {
    // @ts-ignore
    setConfirm({
      template: DELETE_ITEM,
      onOk: () => dispatch(deleteAmazonAdsAccount(id)),
    })
  }

  return (
    <>
      <Box
        mSM={{
          flexDirection: "column",
          gap: "m",
          padding: "m",
          justify: "center",
        }}
        mXL={{
          flexDirection: "row",
          gap: "m",
          padding: "m l",
          align: "center",
        }}
      >
        <Box flexGrow={1} mSM={{ gap: "m" }}>
          <Typography variant="--font-body-text-4">
            {l("Amazon account")}:
          </Typography>

          <Typography color="--color-text-second" variant="--font-body-text-4">
            {name}
          </Typography>
        </Box>

        <Box mSM={{ flexGrow: 1 }} mXL={{ flexGrow: 0 }}>
          {!!is_deleted ? (
            <Alert
              isContentFullWidth
              alertType="warning"
              message={l("Account deleted")}
            />
          ) : (
            <RestrictedPrimaryButton
              fullWidth
              managePermission={permissionKeys.amazonAdsAccountManage}
              popoverMessage={restrictPopoverMessages.alter}
              onClick={handleDeleteAccount}
            >
              {l("Delete account")}
            </RestrictedPrimaryButton>
          )}
        </Box>
      </Box>

      <Box display="block" hasBorder={{ bottom: true }}>
        <TabsComponent
          // @ts-ignore
          hideIconsOnMobile
          activeTab={activeTab}
          tabs={[
            {
              key: TAB_KEYS.accountInfo,
              label: l("Account Information"),
              icon: "icnUser",
              contents: <AccountInformation account={account} />,
            },
            {
              key: TAB_KEYS.businessAnalytics,
              label: l("Business Analytics"),
              icon: "icnPieChart",
              contents: isBasActive ? (
                <BusinessAnalytics account={account} />
              ) : (
                <BusinessAnalyticsNotConnected />
              ),
            },
          ]}
          onChangeTab={handleChangeTab}
        />
      </Box>
    </>
  )
}
