import React, { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Link, useHistory } from "react-router-dom"
import { Box, Icon, Typography } from "@develop/fe-library"
import { PRODUCTS } from "@develop/fe-library/dist/consts"
import { ROUTES } from "@develop/fe-library/dist/routes"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"

import { amazonCustomerAccountsSelector } from "selectors/userSelectors"

import { Restrict } from "components/Restrict"
import { RestrictedPrimaryButton } from "components/shared/Buttons"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"

const { getAll: getAmazonCustomerAccounts } = amazonCustomerAccountsActions

const {
  GENERAL_ROUTES: { PATH_SUBSCRIPTION },
} = ROUTES

export const BusinessAnalyticsNotConnected = () => {
  const [isEnableClickedAndDataLoaded, setIsEnableClickedAndDataLoaded] =
    useState(false)

  const dispatch = useDispatch()

  const { push } = useHistory()

  const customerAccounts = useSelector(amazonCustomerAccountsSelector)

  const handleEnable = (): void => {
    // Get customer accounts list and set flag to true when the data is loaded
    const callback = () => {
      setIsEnableClickedAndDataLoaded(true)
    }

    dispatch(getAmazonCustomerAccounts(false, callback))
  }

  useEffect(() => {
    // When the customer accounts list is loaded, get id of the first account
    // and redirect to the accounts page to specific account's business analytics tab

    const isSkipped =
      !isEnableClickedAndDataLoaded || !checkIsArray(customerAccounts)

    if (isSkipped) {
      return
    }

    const newUrl = `${PATH_SUBSCRIPTION}#tab=${PRODUCTS.BAS}`

    push(newUrl)
  }, [isEnableClickedAndDataLoaded])

  return (
    <Box
      align="center"
      flexDirection="column"
      justify="center"
      minHeight="400px"
      mSM={{
        gap: "m",
        padding: "m",
      }}
      mXL={{
        gap: "l",
        padding: "l",
      }}
    >
      <Icon
        color="--color-icon-static"
        name="icnWarning"
        size="--icon-size-11"
      />

      <Typography textAlign="center" variant="--font-headline-2">
        {l("There is no active subscription for Business Analytics")}
      </Typography>

      <Typography textAlign="center" variant="--font-body-text-7">
        {l(
          "You can enable it on the <a>Amazon seller account management</a> page",
          {
            a: (text) => (
              <Restrict
                managePermission={permissionKeys.amazonCustomerAccountList}
                popoverMessage={restrictPopoverMessages.view}
              >
                <Link to={PATH_SUBSCRIPTION}>{text}</Link>
              </Restrict>
            ),
          }
        )}
      </Typography>

      <RestrictedPrimaryButton
        managePermission={permissionKeys.amazonCustomerAccountList}
        popoverMessage={restrictPopoverMessages.view}
        onClick={handleEnable}
      >
        {l("Enable")}
      </RestrictedPrimaryButton>
    </Box>
  )
}
