import React from "react"
import { Box, Typography } from "@develop/fe-library"
import cn from "classnames"

import { AccountRow } from "components/AccountRow"
import { Restrict } from "components/Restrict"
import { RestrictedButton } from "components/shared/Buttons"

import l from "utils/intl"

import { permissionKeys, restrictPopoverMessages } from "consts"

import { AccountNameForm } from "./components"

import { useAccountInformation } from "./hooks"

import type { AccountInformationProps } from "./AccountInformationTypes"

import styles from "./accountInformation.module.scss"

export const AccountInformation = ({ account }: AccountInformationProps) => {
  const { id, name, is_deleted } = account

  const {
    isEditMode,
    buildHandleEdit,
    handleFinishEdit,
    createdAtString,
    validityLabel,
    handleRenewToken,
  } = useAccountInformation({
    account,
  })

  return (
    <Box
      flexDirection="column"
      minHeight="400px"
      className={cn({
        [styles.disabled]: !!is_deleted,
      })}
    >
      <AccountRow title={l("Account ID")}>{id}</AccountRow>
      <AccountRow title={l("Amazon Ads account name")}>
        {!isEditMode ? (
          <Restrict
            managePermission={permissionKeys.amazonAdsAccountManage}
            popoverMessage={restrictPopoverMessages.alter}
          >
            {(isDisabled) => {
              const cursor = isDisabled ? "not-allowed" : "pointer"
              const color = isDisabled
                ? "--color-text-disable"
                : "--color-text-link"

              return (
                <Box cursor={cursor} onClick={buildHandleEdit(isDisabled)}>
                  <Typography color={color} variant="--font-body-text-7">
                    {name}
                  </Typography>
                </Box>
              )
            }}
          </Restrict>
        ) : (
          <AccountNameForm
            id={id}
            name={name}
            onFinishEdit={handleFinishEdit}
          />
        )}
      </AccountRow>
      <AccountRow title={l("Amazon API authorization status")}>
        <Box
          align="center"
          gap="m"
          mSM={{ width: "100%" }}
          mXL={{ width: "200px" }}
        >
          <Box flexGrow={1}>{validityLabel}</Box>
          <RestrictedButton
            managePermission={permissionKeys.amazonAdsAccountManage}
            popoverMessage={restrictPopoverMessages.alter}
            variant="secondary"
            onClick={handleRenewToken}
          >
            {l("Renew")}
          </RestrictedButton>
          {/*TODO: Implement this icon when an endpoint for token status check is ready, FOR LATER DEVELOPMENT*/}
          {/*<IconPopover*/}
          {/*  name="icnWarning"*/}
          {/*  size="--icon-size-5"*/}
          {/*  color="--color-icon-error"*/}
          {/*  content="Some text"*/}
          {/*/>*/}
        </Box>
      </AccountRow>
      <AccountRow title={l("Created")}>{createdAtString}</AccountRow>
    </Box>
  )
}
