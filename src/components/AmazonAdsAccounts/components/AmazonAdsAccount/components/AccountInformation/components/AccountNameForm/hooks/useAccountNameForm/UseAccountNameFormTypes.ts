import type {
  Sub<PERSON><PERSON><PERSON><PERSON>,
  UseFormHandleSubmit,
  UseFormReturn,
} from "react-hook-form"

import type { AmazonAdsAccountNameFormData } from "types"

type UseAccountNameFormParams = {
  id: number
  name: string
  onFinishEdit: () => void
}

type UseAccountNameFormResult = {
  form: UseFormReturn<AmazonAdsAccountNameFormData>
  formHandleSubmit: UseFormHandleSubmit<AmazonAdsAccountNameFormData>
  handleSubmit: SubmitHandler<AmazonAdsAccountNameFormData>
  isSaveButtonDisabled: boolean
}

export type UseAccountNameForm = (
  params: UseAccountNameFormParams
) => UseAccountNameFormResult
