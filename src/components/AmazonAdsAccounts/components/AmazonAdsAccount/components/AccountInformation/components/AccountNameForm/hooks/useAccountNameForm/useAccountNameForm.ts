import { SubmitHandler, useForm } from "react-hook-form"
import { useDispatch } from "react-redux"
// @ts-ignore
import { buildFormSubmitFailureCallback } from "@develop/fe-library/dist/utils"
import { yupResolver } from "@hookform/resolvers/yup"

import amazonAdsAccountsActions from "actions/amazonAdsAccountsActions"

import { buildSchema } from "./utils"

import type { AmazonAdsAccountNameFormData } from "types"

import type { UseAccountNameForm } from "./UseAccountNameFormTypes"

const { updateAmazonAdsAccount } = amazonAdsAccountsActions

export const useAccountNameForm: UseAccountNameForm = ({
  id,
  name,
  onFinishEdit,
}) => {
  const dispatch = useDispatch()

  const form = useForm<AmazonAdsAccountNameFormData>({
    defaultValues: {
      name,
    },
    mode: "onChange",
    resolver: yupResolver<AmazonAdsAccountNameFormData>(buildSchema()),
  })

  const {
    handleSubmit: formHandleSubmit,
    setError,
    formState: { isDirty, isValid, isSubmitting },
  } = form

  const isSaveButtonDisabled = !isDirty || !isValid || isSubmitting

  const handleSubmit: SubmitHandler<AmazonAdsAccountNameFormData> = async (
    values: AmazonAdsAccountNameFormData
  ) => {
    const failureCallback =
      buildFormSubmitFailureCallback<AmazonAdsAccountNameFormData>({
        setError,
      })

    await dispatch(
      updateAmazonAdsAccount(id, values, failureCallback, onFinishEdit)
    )
  }

  return {
    form,
    formHandleSubmit,
    handleSubmit,
    isSaveButtonDisabled,
  }
}
