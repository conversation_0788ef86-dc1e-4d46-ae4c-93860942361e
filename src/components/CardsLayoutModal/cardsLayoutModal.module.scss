@import "assets/styles/variables.scss";

.buttonsContainer {
  border-top: 1px solid $border_main;
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
}

.submitButton {
  margin-left: 10px;
}

.controlContainer {
  align-items: flex-start;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;

  &.checkboxControlContainer {
    align-items: center;
    justify-content: flex-start;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.controlsContainer {
  background-color: $main_bg;
  max-height: calc(100vh - 320px);
  overflow: auto;
  padding: 20px;
}

.selectField,
.textAreaField,
.textField {
  width: 100%;
  max-width: 310px;
}

.outlinedTextField {
  width: 100%;
}

.textField,
.outlinedTextField {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.controlsContainer.controlsContainer textarea,
.textAreaField.textAreaField {
  margin-bottom: 0;
  height: 100px;
}

.label {
  color: $text_main;
  margin-right: 10px;
  min-width: 145px;
  padding-top: 9px;
  position: relative;
  width: 145px;
}

.multiline {
  padding-top: 4px;
}

.controlWrapper {
  flex-grow: 1;
  max-width: calc(100% - 140px);

  & > span {
    width: 100%;
  }
}

.limitWidthControlWrapper {
  & > span {
    max-width: 310px;
  }
}

.selectControlWrapper {
  width: 100%;
  max-width: 310px;
}

.controlSwitchWrapper {
  padding-top: 5px;
  text-align: left;
}

.option {
  height: 32px;
}

.checkbox {
  margin-right: 10px;
}

.controlsWrapper {
  display: flex;
  width: 100%;

  > span {
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.combinedControlField {
  flex-basis: 50%;
  flex-grow: 1;
}

.tooltopIcon {
  padding: 6px;
  position: relative;
}

.popover {
  max-width: 260px;
}

.errorWrapperSelect {
  display: flex;
}

@media (max-width: $xs) {
  .controlContainer {
    align-items: flex-start;
    flex-direction: column;

    &.switchControlContainer {
      flex-direction: row;
      align-items: center;
      .label {
        margin-bottom: 0;
      }
      .controlSwitchWrapper {
        text-align: right;
      }
    }

    &.checkboxControlContainer {
      flex-direction: row;

      .label {
        display: none;
      }
    }
  }

  .controlWrapper,
  .selectField,
  .textAreaField,
  .textField {
    max-width: unset;
    width: 100%;

    &.controlSwitchWrapper {
      width: auto;
    }

    :global(.ant-select) {
      width: 100%;
    }
  }

  .controlsContainer {
    max-height: calc(100vh - 145px);
    padding: 15px 10px;
  }

  .label {
    margin-bottom: 6px;
    padding-top: 0;
    top: 0;
  }

  .controlsWrapper {
    flex-direction: column;

    > span,
    > div {
      margin-right: 0;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .buttonsContainer {
    padding-left: 10px;
    padding-right: 10px;
    &:first-child {
      margin-left: 0;
    }
  }
  .cancelButton,
  .submitButton {
    width: calc(50% - 5px);
  }
}
