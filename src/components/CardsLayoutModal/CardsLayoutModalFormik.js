import React from "react"
import PropTypes from "prop-types"
import FormikWithChangeValidation from "components/shared/FormikWithChangeValidation"
import CardsLayoutModalView from "components/CardsLayoutModal/CardsLayoutModalView"
import { checkIsArray } from "utils/arrayHelpers"

const CardsLayoutModalFormik = ({
  buttons,
  controls,
  disabled,
  disableSubmitButton,
  initialValues,
  onClose,
  onSubmit,
  closeAfterSubmit,
  submitButtonLabel,
  validationSchema,
  onFormChange,
  canSubmitAfterChange,
  managePermission,
  popoverMessage,
}) => {
  return (
    <FormikWithChangeValidation
      managePermission={managePermission}
      initialValues={initialValues}
      namesRequired={controls
        .map(({ required, name }) => required && name)
        .filter((name) => name)}
      onSubmit={(payload, { setErrors, setSubmitting }) => {
        onSubmit(
          payload,
          (errors = []) => {
            if (checkIsArray(errors)) {
              setErrors(
                errors.reduce((acc, { field, message }) => {
                  acc[field] = message

                  return acc
                }, {})
              )
            }

            setSubmitting(false)
          },
          () => {
            !closeAfterSubmit && setSubmitting(false)
          }
        )
      }}
      onFormIsChanged={onFormChange}
      validationSchema={validationSchema}
      canSubmitAfterChange={canSubmitAfterChange}
    >
      {(props) => (
        <CardsLayoutModalView
          {...props}
          buttons={buttons}
          closeAfterSubmit={closeAfterSubmit}
          controls={controls}
          disabled={disabled}
          disableSubmitButton={disableSubmitButton}
          onClose={onClose}
          submitButtonLabel={submitButtonLabel}
          managePermission={managePermission}
          popoverMessage={popoverMessage}
        />
      )}
    </FormikWithChangeValidation>
  )
}

CardsLayoutModalFormik.propTypes = {
  buttons: PropTypes.array,
  closeAfterSubmit: PropTypes.bool.isRequired,
  controls: PropTypes.array.isRequired,
  disabled: PropTypes.bool,
  disableSubmitButton: PropTypes.bool,
  initialValues: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  onFormChange: PropTypes.func.isRequired,
  validationSchema: PropTypes.object,
}

CardsLayoutModalFormik.defaultProps = {
  closeAfterSubmit: true,
  initialValues: {},
}

export default CardsLayoutModalFormik
