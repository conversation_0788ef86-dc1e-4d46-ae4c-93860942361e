import { connect } from "react-redux"

import ExtensionRequestFormFormik from "./ExtensionRequestFormFormik"
import extensionRequestActions from "actions/extensionRequestActions"

const { submit } = extensionRequestActions

const mapDispatchToProps = (dispatch) => ({
  submitRequest: (request, callback) => dispatch(submit(request, callback)),
})

export default connect(null, mapDispatchToProps)(ExtensionRequestFormFormik)
