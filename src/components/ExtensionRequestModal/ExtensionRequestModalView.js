import React from "react"
import { ConfirmModal } from "components/shared/Modal"
import PropTypes from "prop-types"
import FormattedMessage from "components/FormattedMessage"

import ExtensionRequestForm from "./components/ExtensionRequestForm"

import "components/ExtensionRequestModal/extensionRequestModal.scss"

const ExtensionRequestModalView = (props) => {
  const { onClose, visible } = props

  return (
    <ConfirmModal
      title={<FormattedMessage id="Extension request" />}
      visible={visible}
      footer={null}
      className="extension-request-modal"
      onCancel={onClose}
    >
      <ExtensionRequestForm onClose={onClose} />
    </ConfirmModal>
  )
}

ExtensionRequestModalView.propTypes = {
  onClose: PropTypes.func.isRequired,
  visible: PropTypes.bool,
}

ExtensionRequestModalView.defaultProps = {
  visible: false,
}

export default ExtensionRequestModalView
