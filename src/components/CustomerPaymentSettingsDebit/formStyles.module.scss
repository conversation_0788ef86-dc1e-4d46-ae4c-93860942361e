@import "assets/styles/variables.scss";

.controlContainer {
  align-items: center;
  display: flex;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &.radioControlContainer {
    margin: 20px 0;
  }

  &.labelControl {
    margin-bottom: 18px;
  }
}

.controlsContainer {
  border: solid 1px $border_main;
  border-radius: 5px;
  max-width: 930px;
  padding: 30px 40px 40px;
}

.selectField,
.textAreaField,
.textField {
  max-width: 650px;
  width: 100%;
}

.textField {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.switchField {
}

.textAreaField.textAreaField {
  height: 100px;
}

.label.label {
  margin-right: 10px;
  min-width: 160px;
  overflow: hidden;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 160px;
}

.controlWrapper {
  flex-grow: 1;
  max-width: 650px;

  & > span {
    width: 100%;
  }
}

.controlSwitchWrapper {
  text-align: right;
}

.option {
  height: 32px;
}

.hiddenButton {
  left: -9999px;
  position: absolute;
}

.labelHidden {
  height: 0;
  visibility: hidden;
}

.checkboxWrapper {
  margin-right: 10px;
}

.checkboxLabel.checkboxLabel {
  color: $int_active_on;
  max-width: 625px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

.rangeControlWrapper.rangeControlWrapper {
  display: flex;
  flex-grow: 1;
  max-width: 650px;

  & > span:not(:global(.ant-calendar-picker)) {
    flex-grow: 1;
    margin-right: 10px;
    min-width: unset;
    width: auto;

    &:last-child {
      margin-right: 0;
    }
  }
}

.rangeControl {
  width: 100%;
}

.fieldsValidation {
  .label.label {
    min-width: 112px;
    text-align: left;
    width: 112px;
  }
}

.filtersEdit {
  .controlContainer {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.radio {
  padding-right: 80px;

  &:last-child {
    padding-right: 0;
  }
}

.radioLabel.radioLabel {
  color: $text_second;
}

.checkboxControlContainer {
  align-items: flex-start;
}

.fieldLabel.fieldLabel {
  color: $text_main;
  font-weight: 700;
}

@media (max-width: $md) {
  .controlsContainer {
    padding: 20px;
  }

  .radio {
    padding-right: 60px;

    &:last-child {
      padding-right: 0;
    }
  }
}

@media (max-width: $xs) {
  .controlContainer {
    align-items: flex-start;
    flex-direction: column;
    margin-bottom: 15px;

    &.switchControlContainer {
      flex-direction: row;
    }

    &.checkboxControlContainer {
      flex-direction: row;

      .label {
        display: none;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    &.radioControlContainer {
      margin: 15px 0;
    }
  }

  .controlWrapper,
  .selectField,
  .textAreaField,
  .textField {
    max-width: unset;
    width: 100%;

    &.controlSwitchWrapper {
      width: auto;
    }

    :global(.ant-select) {
      width: 100%;
    }
  }

  .controlsContainer {
    padding: 10px;
  }

  .label.label {
    margin-bottom: 6px;
    padding-top: 0;
    text-align: left;
    top: 0;

    &.labelHidden {
      margin: 0;
    }
  }

  .rangeControlWrapper.rangeControlWrapper {
    display: flex;
    flex-direction: column;
    max-width: unset;
    width: 100%;

    & > input,
    & > span {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .radio {
    margin-bottom: 15px;
    padding-right: 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
