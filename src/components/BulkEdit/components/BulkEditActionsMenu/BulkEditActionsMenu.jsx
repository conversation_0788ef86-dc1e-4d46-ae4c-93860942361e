import React from "react"
import { Popover } from "antd"
import { Icon } from "@ant-design/compatible"
import PropTypes from "prop-types"

import FormattedMessage from "components/FormattedMessage"
import TypographyView from "components/Typography"
import { TYPOGRAPHY_TYPE_SPAN } from "components/Typography/TypographyView"

import l from "utils/intl"

import { SelectedTextLabel } from "./SelectedTextLabel"

import styles from "./bulkEditActionsMenu.module.scss"

const getIcon = (icon) => {
  return typeof icon === "string" ? (
    <Icon className={styles.icon} type={icon} />
  ) : (
    icon
  )
}

const generateHandleClick = (onClick) => () => {
  if (onClick) {
    onClick()
  }
}

const BulkEditActionsMenu = ({
  selectedCount,
  totalCount,
  selectedTotalCount,
  filtered,
  buttons,
}) => {
  const { innerWidth: windowWidth } = window

  const isVisible = selectedCount || selectedTotalCount

  if (!isVisible) {
    return null
  }

  return (
    <>
      <div className={styles.container}>
        <div className={styles.selected}>
          <SelectedTextLabel
            filtered={filtered}
            selectedCount={selectedCount}
            selectedTotalCount={!!selectedTotalCount}
            totalCount={totalCount}
          />
        </div>

        {buttons.map(({ title, icon, onClick }) => (
          <div
            key={title}
            className={styles.button}
            onClick={generateHandleClick(onClick)}
          >
            {windowWidth < 768 ? (
              <Popover content={l(title)}>{getIcon(icon)}</Popover>
            ) : (
              getIcon(icon)
            )}
            <TypographyView type={TYPOGRAPHY_TYPE_SPAN}>
              <FormattedMessage id={title} />
            </TypographyView>
          </div>
        ))}
      </div>
    </>
  )
}

BulkEditActionsMenu.propTypes = {
  selectedCount: PropTypes.number,
  selectedTotalCount: PropTypes.bool,
  onClose: PropTypes.func,
  buttons: PropTypes.array,
  callBackType: PropTypes.string,
}

export { BulkEditActionsMenu }
