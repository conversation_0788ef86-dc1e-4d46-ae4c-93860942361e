@import "assets/styles/media";
@import "assets/styles/variables";

.settingsTitle {
  display: block;
  margin-bottom: 12px;
}

.settingsContent {
  display: grid;
  grid-template-columns: none;

  @include mediaMin(768) {
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 20px;
  }

  @include mediaMin(1366) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.settingsItem {
  border: 1px solid $border-color;
  margin: 10px 0px;
  padding-left: 20px;
  min-height: 96px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  :global(.ant-checkbox + span) {
    margin-left: 0;
    font-size: 13px;
  }
}
