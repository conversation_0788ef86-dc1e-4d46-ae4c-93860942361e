import React, { Component } from "react"
import PropTypes from "prop-types"

import ChangePasswordFormik from "./ChangePasswordFormik"

class ChangePasswordContainer extends Component {
  onChangePassword = (payload, failureCallback) => {
    const { sync, updateInfo, userId } = this.props

    updateInfo({ ...payload, id: userId }, () => sync(), failureCallback)
  }

  render() {
    return <ChangePasswordFormik onSubmit={this.onChangePassword} />
  }
}

ChangePasswordContainer.propTypes = {
  sync: PropTypes.func.isRequired,
  updateInfo: PropTypes.func.isRequired,
  userId: PropTypes.number.isRequired,
}

export default ChangePasswordContainer
