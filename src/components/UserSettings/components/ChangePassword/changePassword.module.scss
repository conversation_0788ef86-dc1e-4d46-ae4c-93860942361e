@import "assets/styles/variables.scss";

.container {
  padding: 20px;
  max-width: 710px;
}

.heading {
  margin-bottom: 20px;
  max-width: 720px;
}

.title.title {
  color: $text_main;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.controls {
  margin-bottom: 20px;
}

.controlContainer {
  align-items: center;
  display: flex;
  margin-bottom: 10px;

  &:last-child {
    margin-block: 0;
  }
}

.fieldLabel {
  flex-shrink: 0;
  line-height: 1.2;
  margin-right: 25px;
  min-width: 160px;
}

.fieldWrapper {
  flex-grow: 1;
  max-width: 525px;
}

.buttonsContainer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}

.button {
  min-width: unset;
}

.field {
  font-size: 12px !important;
}

@media (max-width: $md) {
  .buttonsContainer {
    max-width: unset;
  }

  .fieldWrapper {
    max-width: unset;
    width: 100%;
  }
}

@media (max-width: $sm) {
  .container {
    padding: 10px;
  }

  .heading {
    margin-bottom: 10px;
  }

  .controlContainer {
    align-items: flex-start;
    flex-direction: column;
  }

  .fieldLabel {
    margin-bottom: 5px;
  }

  .button {
    flex-grow: 1;
  }
}
