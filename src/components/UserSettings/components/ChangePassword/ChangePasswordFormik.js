import React from "react"
import { Formik } from "formik"
import PropTypes from "prop-types"

import ChangePasswordView from "./ChangePasswordView"
import validationSchema from "validationSchemas/expiredPasswordValidationSchema"

const ChangePasswordFormik = ({ onSubmit }) => (
  <Formik
    initialValues={{ password: "", passwordConfirm: "" }}
    onSubmit={(payload, { setErrors, setSubmitting }) => {
      onSubmit(payload, (errors = []) => {
        setErrors(
          errors.reduce((acc, { field, message }) => {
            acc[field] = message

            return acc
          }, {})
        )

        setSubmitting(false)
      })
    }}
    validationSchema={validationSchema}
  >
    {(props) => <ChangePasswordView {...props} />}
  </Formik>
)

ChangePasswordFormik.propTypes = {
  onSubmit: PropTypes.func.isRequired,
}

export default ChangePasswordFormik
