@import "assets/styles/variables.scss";

.container {
  max-width: 400px;
}

.savePhoneButton {
  margin-top: 0;
  margin-bottom: 0;
}

.codeSelect {
  width: 87px;

  :global(.ant-select-selection-selected-value) {
    align-items: center;
    display: flex !important;
  }

  :global(.ant-select-selection.ant-select-selection) {
    background-color: $main_bg;
    border: 1px solid $border_main;
  }
}

.countryImg {
  border-radius: 50%;
  height: 16px;
  margin-right: 5px;
  width: 16px;
}

.countryOption {
  align-items: center;
  display: flex;
}

.phone {
  &.hasError {
    border-color: $error_border;
  }
}

.radioLabel.radioLabel {
  color: $text_main;
}

.radioGroup {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.button.button.button {
  min-width: 120px;
}

.sendButton {
  margin-bottom: 20px;
}

.button.button.button {
  margin-right: 10px;

  &:last-child {
    margin-right: 0;
  }
}

.codeInput {
  &.hasError {
    border-color: $error_border;
  }
}

.errorIcon {
  position: absolute;
  right: 10px;
  top: 9px;
  z-index: 10;
}

.errorTextClass.errorTextClass {
  bottom: -2px;
  color: $error_text;
  display: none;
  height: 0;
  position: absolute;
}

.inputWrapper {
  margin-bottom: 15px;
  position: relative;
}

@media (max-width: $lg) {
  .errorIcon {
    display: none;
  }

  .errorTextClass.errorTextClass {
    display: block;
  }
}

@media (max-width: $sm) {
  .sendButton {
    margin-bottom: 10px;
  }

  .button.button.button {
    width: 100%;
  }

  .radioGroup {
    flex-direction: column;
  }

  .radio {
    margin-bottom: 13px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .errorTextClass.errorTextClass {
    height: auto;
    margin-top: 5px;
    position: static;
  }
}
