import { connect } from "react-redux"

import PhoneConfirmationContainer from "./PhoneConfirmationContainer"
import staffActions from "actions/staffActions"
import twoFAActions from "actions/twoFAActions"
import cacheAPIActions from "actions/cacheAPIActions"

const { purgeCache } = cacheAPIActions
const { changeView } = twoFAActions

const {
  checkCode,
  sendCode,
  syncCurrent: syncCurrentUser,
  update: updateUser,
} = staffActions

const mapStateToProps = (state) => {
  const {
    customer: {
      customer: { country },
    },
    staff: {
      currentUser: {
        id: userId,
        phone,
        user: { is_enabled_2fa: twoFAEnabled },
      },
    },
  } = state
  const { code = "de" } = country || {}

  return {
    defaultCountryCode: code,
    phone,
    twoFAEnabled,
    userId,
  }
}

export default connect(mapStateToProps, {
  changeView,
  checkCode,
  sendCode,
  syncCurrentUser,
  updateUser,
  purgeCache,
})(PhoneConfirmationContainer)
