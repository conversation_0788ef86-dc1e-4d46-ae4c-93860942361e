import React from "react"
import { FormItems } from "@develop/fe-library"

import l from "utils/intl"

import { useDeactivate2FA } from "./hooks"

export const Deactivate2FA = () => {
  const { form, handleSubmit, handleCancel } = useDeactivate2FA()

  return (
    <FormItems
      form={form}
      boxContainerProps={{
        mXL: {
          maxWidth: 400,
        },
      }}
      gridContainerProps={{
        gap: "m",
      }}
      items={[
        {
          type: "text",
          name: "secret",
          inputProps: {
            label: l("Enter code"),
          },
          gridItemProps: {
            mSM: 12,
          },
        },
        {
          type: "action",
          label: l("Cancel"),
          actionProps: {
            variant: "secondary",
            onClick: handleCancel,
            fullWidth: true,
          },
          gridItemProps: {
            mSM: 12,
            mXL: "min-content",
          },
        },
        {
          type: "action",
          label: l("Deactivate"),
          actionProps: {
            variant: "primary",
            onClick: handleSubmit,
            fullWidth: true,
          },
          gridItemProps: {
            mSM: 12,
            mXL: "min-content",
          },
        },
      ]}
    />
  )
}
