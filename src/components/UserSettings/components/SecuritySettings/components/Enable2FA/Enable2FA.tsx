import React from "react"
import { useDispatch, useSelector } from "react-redux"
import { But<PERSON> } from "@develop/fe-library"

import twoFAActions from "actions/twoFAActions"

import { staffCurrentUserSelector } from "selectors/staffSelectors"

import FormattedMessage from "components/FormattedMessage"

// @ts-ignore
const { changeView } = twoFAActions

export const Enable2FA = () => {
  const dispatch = useDispatch()

  const { is_phone_number_confirmed: phoneConfirmed } = useSelector(
    staffCurrentUserSelector
  )

  const onEnable = () => {
    dispatch(changeView(phoneConfirmed ? "activate" : "confirmPhone"))
  }

  return (
    <div>
      <Button onClick={onEnable}>
        <FormattedMessage id="Enable two-factor authentication" />
      </Button>
    </div>
  )
}
