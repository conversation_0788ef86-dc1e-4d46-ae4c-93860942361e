import React, { useEffect, useState } from "react"
import { Empty, Table } from "antd"
import { Box, IconPopover, TextInput, Typography } from "@develop/fe-library"
import PropTypes from "prop-types"

import CardsLayoutModal from "components/CardsLayoutModal"
import FormattedMessage from "components/FormattedMessage"
import { PrimaryButton } from "components/shared/Buttons"
import Modal, { ConfirmModal } from "components/shared/Modal"

import { setConfirm } from "utils/confirm"
import { convertToLocalDate } from "utils/dateConverter"
import l from "utils/intl"

import styles from "./biometricID.module.scss"

const getErrorMessage = {
  0: l(
    "Biometric ID save request is either timed out or canceled. Please try again."
  ),
  11: l("This Biometric ID is already registered."),
}

const actions = {
  create: "create",
  update: "update",
  delete: "delete",
}

const BiometricIDView = ({
  list,
  getList,
  toggleModal,
  modalVisible,
  register,
  updateDevice,
  deleteDevice,
  updateDeviceId,
  setDeviceID,
  is2FAPassed,
  is2FAEnabled,
  get2FAPassStatus,
  pass2FA,
  isNeed2FaForcedActivation,
  biometricIDSetup,
  getBiometricIDSetup,
  setBiometricIDSetup,
  updateBiometricIDSetup,
}) => {
  const biometricId = !!biometricIDSetup?.id ? biometricIDSetup.id : null

  useEffect(() => {
    getList()
    get2FAPassStatus(undefined)
    getBiometricIDSetup()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const [twoFAModalVisible, setTwoFAModalVisible] = useState(false)
  const [currentAction, setCurrentAction] = useState("")
  const [currentBiometricIdItem, setCurrentBiometricIdItem] =
    useState(undefined)
  const [errorCode, setErrorModal] = useState(undefined)
  const [twoFACode, setTwoFACode] = useState("")
  const [twoFAError, setTwoFAError] = useState()
  const [infoModalVisible, setInfoModalVisible] = useState(false)
  const [isInfo, setIsInfo] = useState(false)
  const [values, setValues] = useState({
    name: updateDeviceId
      ? list.find(
          (device) => device.public_key_credential_id === updateDeviceId
        ).name
      : "",
  })

  const handleSave = async ({ name }, failureCallback) => {
    if (updateDeviceId) {
      const successCallback = () => {
        setDeviceID(undefined)
        toggleModal(false)
        getList()
      }

      updateDevice(updateDeviceId, name, successCallback)
    } else {
      const successCallback = () => {
        if (biometricId) {
          updateBiometricIDSetup(biometricId, true)
        } else {
          setBiometricIDSetup(true)
        }
        toggleModal(false)
        getList()
      }
      const errorCallback = (errorCode) => {
        setErrorModal(errorCode)
        failureCallback()
      }

      register(name, successCallback, errorCallback)
    }
  }

  const handleInfoButtonClick = () => {
    setIsInfo(true)
    setInfoModalVisible(true)
  }

  const handleCreate = (is2FAPassed) => {
    if (is2FAPassed || (!is2FAEnabled && !isNeed2FaForcedActivation)) {
      if (biometricId) {
        toggleModal(true)
      } else {
        setInfoModalVisible(true)
      }
    } else {
      setTwoFAModalVisible(true)
      setCurrentAction(actions.create)
    }
  }

  const handleCreateButtonClick = () => {
    handleCreate(is2FAPassed)
  }

  const handleEdit = ({ is2FAPassed, item }) => {
    const selectedItem = item || currentBiometricIdItem

    if (!is2FAEnabled && isNeed2FaForcedActivation) return

    if (is2FAPassed || (!is2FAEnabled && !isNeed2FaForcedActivation)) {
      setDeviceID(selectedItem.public_key_credential_id)
      toggleModal(true)
    } else {
      setTwoFAModalVisible(true)
      setCurrentAction(actions.update)

      if (item) {
        setCurrentBiometricIdItem(item)
      }
    }
  }

  const generateHandleEditButtonClick = (item) => () => {
    handleEdit({ is2FAPassed: false, item })
  }

  const handleDelete = ({ is2FAPassed, item }) => {
    const selectedItem = item || currentBiometricIdItem

    if (!is2FAEnabled && isNeed2FaForcedActivation) return

    if (is2FAPassed || (!is2FAEnabled && !isNeed2FaForcedActivation)) {
      setConfirm({
        title: l("Delete biometric ID"),
        message: l(
          `Are you sure you want to delete the {device} biometric ID?`,
          {
            device: selectedItem.name || "",
          }
        ),
        okText: l("Delete"),
        onOk: () =>
          deleteDevice(selectedItem.public_key_credential_id, () => getList()),
      })
    } else {
      setTwoFAModalVisible(true)
      setCurrentAction(actions.delete)

      if (item) {
        setCurrentBiometricIdItem(item)
      }
    }
  }

  const generateHandleDeleteButtonClick = (item) => () => {
    handleDelete({ is2FAPassed, item })
  }

  const get2FAPassStatusSuccessCallback = ({ isPassed }) => {
    if (!isPassed) {
      return
    }

    switch (currentAction) {
      case actions.create: {
        handleCreate(isPassed)
        break
      }
      case actions.update: {
        handleEdit({ is2FAPassed: isPassed })
        break
      }
      case actions.delete: {
        handleDelete({ is2FAPassed: isPassed })
        break
      }
      default:
        break
    }

    setCurrentAction("")
    setCurrentBiometricIdItem(undefined)
  }

  const handlePass2FA = () => {
    pass2FA(
      { secret: twoFACode },
      () => {
        get2FAPassStatus(get2FAPassStatusSuccessCallback)
        setTwoFACode("")
        setTwoFAModalVisible(false)
      },
      (errors) => {
        if (errors && errors[0]) {
          setTwoFAError(errors[0].message)
        }
      }
    )
  }

  const handleChange2FACode = (value) => {
    // Checks if the value consists of 1 to 6 digits
    const isValid = !value || /^\d{1,6}$/.test(value)

    if (!isValid) {
      return
    }

    setTwoFACode(value)
    setTwoFAError("")
  }

  return (
    <>
      <Box flexDirection="column" gap="l">
        <Box flexDirection="column" gap="m">
          <Typography variant="--font-headline-5">
            <FormattedMessage id="Biometric authentication" />
          </Typography>

          <Typography color="--color-text-second" variant="--font-body-text-7">
            <FormattedMessage id="Enabling biometric authentication allows this browser to use advanced authorization methods like Touch ID or Face ID, replacing the conventional use of passwords and the need for a separate two-factor authentication process." />
            <br />
            <FormattedMessage id="Depending on your device and browser, various “Biometric ID” options may be available." />
            <span className={styles.infoButton} onClick={handleInfoButtonClick}>
              <FormattedMessage id="More information." />
            </span>
          </Typography>
        </Box>

        <Box flexDirection="column" gap="m">
          <Box align="center" justify="space-between">
            <Typography variant="--font-body-text-2">
              <FormattedMessage id="Biometric lDs" />
            </Typography>
            <PrimaryButton
              className={styles.button}
              disabled={!is2FAEnabled && isNeed2FaForcedActivation}
              icon="icnPlus"
              title={
                !is2FAEnabled &&
                l(
                  "Biometric ID setup requires you to turn 2-factor authentication on."
                )
              }
              onClick={handleCreateButtonClick}
            >
              <FormattedMessage id="Add biometric ID" />
            </PrimaryButton>
          </Box>
          {list.length ? (
            <div className={styles.table}>
              <Table
                dataSource={list}
                pagination={false}
                tableLayout="fixed"
                columns={[
                  {
                    title: l("Name"),
                    dataIndex: "name",
                    key: "name",
                  },
                  {
                    title: l("Creation date"),
                    dataIndex: "created_at",
                    key: "date_created",
                    render: (text) => convertToLocalDate(text),
                  },
                  {
                    title: "",
                    dataIndex: "actions",
                    key: "actions",
                    render: (_, obj) => {
                      const isDisabled =
                        !is2FAEnabled && isNeed2FaForcedActivation

                      return (
                        <Box align="center" gap="m" justify="end">
                          <IconPopover
                            isDisabled={isDisabled}
                            name="icnEdit"
                            size="--icon-size-5"
                            content={l(
                              isDisabled
                                ? "Biometric ID update requires you to turn 2-factor authentication on"
                                : "Update"
                            )}
                            onClick={generateHandleEditButtonClick(obj)}
                          />
                          <IconPopover
                            name="icnDeleteOutlined"
                            size="--icon-size-5"
                            content={l(
                              isDisabled
                                ? "Biometric ID deletion requires you to turn 2-factor authentication on"
                                : "Delete"
                            )}
                            onClick={generateHandleDeleteButtonClick(obj)}
                          />
                        </Box>
                      )
                    },
                  },
                ]}
                locale={{
                  emptyText: (
                    <Empty
                      description={l("No data")}
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  ),
                }}
              />
            </div>
          ) : null}
        </Box>
      </Box>

      {modalVisible ? (
        <CardsLayoutModal
          withCloseConfirm
          title={l(`${updateDeviceId ? "Update" : "Add"} biometric ID`)}
          controls={[
            {
              name: "name",
              placeholder: "Name",
              type: "text-outline",
              required: true,
            },
          ]}
          disableSubmitButton={
            updateDeviceId ? false : values.name.length === 0
          }
          initialValues={{
            name: updateDeviceId
              ? list.find(
                  (device) => device.public_key_credential_id === updateDeviceId
                ).name
              : "",
          }}
          onSubmit={handleSave}
          onClose={() => {
            setDeviceID(undefined)
            toggleModal(false)
          }}
          onFormChange={(_, values) => {
            values && setValues(values)
          }}
        />
      ) : null}

      {twoFAModalVisible ? (
        <Modal
          visible
          cancelText={l("Cancel")}
          okText={l("Confirm")}
          title={l("Two-factor authentication")}
          width={620}
          onCancel={() => setTwoFAModalVisible(false)}
          onOk={handlePass2FA}
        >
          <TextInput
            isFullWidth
            errorMessage={twoFAError}
            label={l("Enter code")}
            value={twoFACode}
            onChange={handleChange2FACode}
          />
        </Modal>
      ) : null}

      {errorCode !== undefined ? (
        <ConfirmModal
          visible
          cancelButtonProps={{ style: { display: "none" } }}
          message={getErrorMessage[errorCode]}
          okText={l("Close")}
          title={l("Biometric ID cannot be saved")}
          width={420}
          onCancel={() => {
            setErrorModal(undefined)
            setDeviceID(undefined)
            toggleModal(false)
          }}
          onOk={() => {
            setErrorModal(undefined)
            setDeviceID(undefined)
            toggleModal(false)
          }}
        />
      ) : null}

      {infoModalVisible ? (
        <Modal
          visible
          cancelText={l("Close")}
          okText={l("Next")}
          title={l("Biometric ID guidelines")}
          okButtonProps={{
            style: isInfo ? { display: "none" } : undefined,
          }}
          onCancel={() => {
            setIsInfo(false)
            setInfoModalVisible(false)
          }}
          onOk={() => {
            setInfoModalVisible(false)
            toggleModal(true)
          }}
        >
          <Typography variant="--font-body-text-7">
            {l(
              "To enhance your security and streamline your experience, please adhere to the following guidelines for Biometric ID usage:"
            )}
          </Typography>
          <Box
            component="ul"
            display="flex"
            flexDirection="column"
            gap="s"
            margin="m 0"
            padding="0 0 0 l"
          >
            <li className={styles.listItem}>
              {l(
                "Create a unique Biometric ID for each browser and device you intend to utilize. Note that a Biometric ID created in one browser or device cannot be reused in another."
              )}
            </li>
            <li className={styles.listItem}>
              {l(
                "Once you've created at least one Biometric ID, it becomes available for login and other purposes. However, attempting to use the Biometric ID on a browser or device where it wasn't created may result in an error or declined login."
              )}
            </li>
            <li className={styles.listItem}>
              {l(
                "All biometrics-based technologies, such as Windows Hello, Apple Account, Google, or Android biometrics, offer on-device biometric authentication. This means that the facial or fingerprint data is encrypted and stored locally on the device. In this case, the created Biometric ID cannot be used on other devices with the same account."
              )}
            </li>
            <li className={styles.listItem}>
              {l(
                "Biometric ID is not supported for the Firefox browser due to related issues that cannot be resolved solely on the SellerLogic side."
              )}
            </li>
          </Box>
          <Typography variant="--font-body-text-5">
            {l("Windows Hello")}
          </Typography>
          <Box
            component="ul"
            display="flex"
            flexDirection="column"
            gap="s"
            margin="m 0"
            padding="0 0 0 l"
          >
            <li className={styles.listItem}>
              {l(
                "On devices with enabled Windows Hello, the Biometric ID needs to be set only once and can then be used across all browsers on that device. Any attempt to set the same type of Biometric ID in another browser will be canceled, citing that the Biometric ID is already registered."
              )}
            </li>
          </Box>
          <Typography variant="--font-body-text-5">
            {l("Apple Account")}
          </Typography>
          <Box
            component="ul"
            display="flex"
            flexDirection="column"
            gap="s"
            margin="m 0"
            padding="0 0 0 l"
          >
            <li className={styles.listItem}>
              {l(
                "The same behavior applies to Apple devices with enabled Face ID. Once the Biometric ID is set in one browser, it can then be used for all browsers on that device."
              )}
            </li>
            <li className={styles.listItem}>
              {l(
                "However, for Touch ID, enrollment and authentication are limited to macOS. To use Touch ID in different browsers, it should be set up for each browser separately."
              )}
            </li>
          </Box>
        </Modal>
      ) : null}
    </>
  )
}

BiometricIDView.defaultProps = {
  list: [],
}

BiometricIDView.propTypes = {
  list: PropTypes.array.isRequired,
  getList: PropTypes.func.isRequired,
  toggleModal: PropTypes.func.isRequired,
  modalVisible: PropTypes.bool.isRequired,
  register: PropTypes.func.isRequired,
  updateDevice: PropTypes.func.isRequired,
  deleteDevice: PropTypes.func.isRequired,
  updateDeviceId: PropTypes.string.isRequired,
  setDeviceID: PropTypes.string.isRequired,
  is2FAPassed: PropTypes.bool.isRequired,
  is2FAEnabled: PropTypes.bool.isRequired,
  get2FAPassStatus: PropTypes.func.isRequired,
  pass2FA: PropTypes.func.isRequired,
  isNeed2FaForcedActivation: PropTypes.bool,
  biometricIDSetup: PropTypes.object,
  getBiometricIDSetup: PropTypes.func,
  setBiometricIDSetup: PropTypes.func,
  updateBiometricIDSetup: PropTypes.func,
}

export default BiometricIDView
