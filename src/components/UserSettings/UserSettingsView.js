import React, { useEffect, useState } from "react"
import { useSelector } from "react-redux"
import { withRout<PERSON> } from "react-router-dom"
import { getObjectEntries } from "@develop/fe-library/dist/utils"
import cn from "classnames"
import PropTypes from "prop-types"

import { hasServiceDeskAccessSelector } from "selectors/serviceDeskProfileSelectors"

import FormattedMessage from "components/FormattedMessage"
import { Content } from "components/Layout"
import AppHeader from "components/shared/appHeader/AppHeader"
import SimpleFooterView from "components/SimpleFooter"
import TabsComponent from "components/TabsComponent/TabsComponent"
import { entityTypes, UserInformation } from "components/UserInformation"
import NotificationRedux from "components/UserSettings/components/notification/NotificationRedux"

import { MD } from "utils/breakpoints"
import l from "utils/intl"

import { PROFILE_PAGE_TITLE } from "./consts"

import ChangeCurrency from "./components/ChangeCurrency"
import ChangePassword from "./components/ChangePassword"
import { SecuritySettings } from "./components/SecuritySettings"

import styles from "./userSettingView.module.scss"

import "./userSettingView.scss"

const tabKeys = {
  information: "1",
  currency: "2",
  notification: "3",
  securitysettings: "4",
}

const UserSettingsView = ({
  allowNotifications,
  isPasswordOutdated,
  need2FA,
  roleSide,
  location: { hash },
  isInvitedUser,
}) => {
  const [activeTabKey, setActiveTabKey] = useState(
    need2FA && !isPasswordOutdated ? "4" : tabKeys[hash.replace("#", "")] || "1"
  )

  const hasServiceDeskAccess = useSelector(hasServiceDeskAccessSelector)

  const tabs = [
    {
      key: "1",
      disabled: need2FA && !isPasswordOutdated,
      label: l("Information"),
      icon: "icnUser",
      contents: isPasswordOutdated ? (
        <ChangePassword />
      ) : activeTabKey === "1" ? (
        <UserInformation entityType={entityTypes.userSetting} />
      ) : null,
    },
    {
      key: "2",
      disabled: need2FA || isPasswordOutdated,
      label: l("Currency"),
      icon: "icnCurrencyExchange",
      contents: activeTabKey === "2" ? <ChangeCurrency /> : null,
    },
    {
      key: "4",
      disabled: isPasswordOutdated,
      label: l("Security settings"),
      icon: "icnSafety",
      contents: activeTabKey === "4" ? <SecuritySettings /> : null,
    },
  ]

  useEffect(
    () => {
      window.location.hash = getObjectEntries(tabKeys).find(
        ([, value]) => value === activeTabKey
      )[0]
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )

  const isNotificationsDisabled =
    isInvitedUser ||
    ((!allowNotifications || need2FA || isPasswordOutdated) &&
      !hasServiceDeskAccess)

  if (roleSide !== "TRANSLATOR") {
    tabs.splice(1, 0, {
      key: "3",
      disabled: isNotificationsDisabled,
      label: l("Notification"),
      icon: "icnBell",
      contents: activeTabKey === "3" ? <NotificationRedux /> : null,
    })
  }

  return (
    <>
      <AppHeader title={PROFILE_PAGE_TITLE} />
      <h2 className={styles.pageTitle}>
        <FormattedMessage id={PROFILE_PAGE_TITLE} />
      </h2>
      <Content className={`${styles.content} userSettingView`}>
        <TabsComponent
          hideIconsOnMobile
          className={styles.tabsComponent}
          defaultActiveTab={activeTabKey}
          mobileBreakPoint={MD}
          tabClassName={styles.wide}
          tabs={tabs}
          tabsContainerClassName={cn(
            styles.wide,
            styles.userSettingTabsContainer
          )}
          onChangeTab={(tabKey) => {
            window.location.hash = getObjectEntries(tabKeys).find(
              ([, value]) => value === tabKey
            )[0]
            setActiveTabKey(tabKey)
          }}
        />
      </Content>
      <SimpleFooterView className={styles.simpleFooter} />
    </>
  )
}

UserSettingsView.propTypes = {
  allowNotifications: PropTypes.bool,
  isPasswordOutdated: PropTypes.bool.isRequired,
}

UserSettingsView.defaultProps = {
  allowNotifications: false,
}

export default withRouter(UserSettingsView)
