@import "assets/styles/variables.scss";

.base {
  color: $text_main;
  font-family: <PERSON>o, sans-serif;
  letter-spacing: normal;
  line-height: normal;
  font-stretch: normal;
  font-style: normal;
  font-weight: normal;
}

.title {
  font-size: 15px;
}

.navLink {
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.controlLabel {
  color: inherit;
  font-size: 14px;
  font-weight: bold;
}

.modalTitle {
  font-size: 16px;
  font-weight: bold;
}

.menuItem {
  font-size: 14px;
  font-weight: 500;
}

.textLarge {
  font-size: 14px;
}

.text {
  font-size: 13px;
}

.textSmall {
  font-size: 12px;
}

.controlLabelSmall {
  color: inherit;
  font-size: 11px;
}

.secondary {
  color: $text_second;
}

.errorLabel {
  color: $text_main;
  font-size: 12px;
  padding-left: 11px;
  position: relative;
}

.required {
  @extend .errorLabel;
  position: initial;
  top: auto;
  padding: unset;
}

.header {
  font-size: 24px;
  font-weight: bold;
}

.hiden {
  display: none !important;
}

@media (max-width: $xs) {
  .header {
    font-size: 20px;
  }
}
