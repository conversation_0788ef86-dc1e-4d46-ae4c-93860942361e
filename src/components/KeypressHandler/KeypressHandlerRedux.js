import { connect } from "react-redux"

import KeypressHandlerView from "./KeypressHandlerView"
import keypressPriorityActions from "actions/keypressPriorityActions"

const mapStateToProps = (state) => {
  const {
    keypressPrioritySettings: { priorityArray },
  } = state
  return {
    priorityArray,
  }
}

const mapDispatchToProps = (dispatch) => ({
  setPriority: (priority, isAdd) =>
    dispatch(keypressPriorityActions.setPriority(priority, isAdd)),
})

export default connect(mapStateToProps, mapDispatchToProps)(KeypressHandlerView)
