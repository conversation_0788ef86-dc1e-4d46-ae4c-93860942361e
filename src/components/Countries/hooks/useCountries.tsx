import { useDispatch, useSelector } from "react-redux"

import countriesActions from "actions/countriesActions"
import languagesActions from "actions/languagesActions"

import {
  countriesInitialValuesSelector,
  countriesModalVisibleSelector,
  countriesSearchOptionsSelector,
  countriesTotalCountSelector,
  countryItemsSelector,
  filtersOptionsSelector,
} from "selectors/countriesSelectors"
import { permissionsSelector } from "selectors/userSelectors"

const {
  // @ts-ignore
  displayModal,
  // @ts-ignore
  get: getCountries,
  // @ts-ignore
  update,
} = countriesActions
// @ts-ignore
const { getAll: getLanguages } = languagesActions

export const useCountries = () => {
  const searchOptions = useSelector(countriesSearchOptionsSelector)
  const initialValues = useSelector(countriesInitialValuesSelector)
  const modalVisible = useSelector(countriesModalVisibleSelector)
  const totalCount = useSelector(countriesTotalCountSelector)
  const selectFiltersOptions = useSelector(filtersOptionsSelector)
  const dataSource = useSelector(countryItemsSelector)

  const { countryManage } = useSelector(permissionsSelector)

  const dispatch = useDispatch()

  const getLanguagesData = () => dispatch(getLanguages())

  const getCountriesData = <Type,>(searchOptions: Type) =>
    dispatch(getCountries(searchOptions))

  const toggleModal = <Type,>(visible: boolean, initialValues: Type) => {
    return dispatch(displayModal(visible, initialValues))
  }

  const updateData = <Type,>({
    payload,
    callback,
    failureCallback,
  }: {
    payload: Type
    callback?: () => void
    failureCallback?: () => void
  }) => dispatch(update(payload, callback, failureCallback))

  const onSave = (
    {
      id,
      active,
      code,
      eu,
      payment_method,
      title,
    }: {
      id: number
      active: any
      code: any
      eu: any
      payment_method: any
      title: string
    },
    failureCallback?: () => void,
    successCallback?: () => void
  ) => {
    return updateData({
      payload: {
        id,
        active,
        code,
        eu,
        payment_method,
        title,
      },
      callback: () => {
        successCallback && successCallback()
        getCountriesData(searchOptions)
      },
      failureCallback,
    })
  }

  const onEdit = <Type,>(payload: Type) => {
    toggleModal(true, payload)
  }

  const closeModalHandler = () => {
    toggleModal(false, undefined)
  }

  return {
    searchOptions,
    selectFiltersOptions,
    dataSource,
    countryManage,
    totalCount,
    initialValues,
    modalVisible,
    getLanguagesData,
    onEdit,
    closeModalHandler,
    getCountriesData,
    onSave,
  }
}
