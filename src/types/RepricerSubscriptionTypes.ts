import { RepricerCustomerPlan } from "./Models"

type RepricerEventStatisticItem = {
  b2b: number
  b2c: number
  total: number
}

export type RepricerEventTotalStatisticItemBySeller = {
  seller_id: string
} & RepricerEventStatisticItem

export type GetRepricerEventTotalResponse = {
  total: RepricerEventStatisticItem
  sellers: RepricerEventTotalStatisticItemBySeller[]
}

export type RepricerEventGroupedByDateItem = {
  date: string
} & RepricerEventStatisticItem

export type RepricerEventsGroupedByDate = RepricerEventGroupedByDateItem[]

export type OptimizationsState = GetRepricerEventTotalResponse & {
  grouped: RepricerEventsGroupedByDate
}

export type GetRepricerCustomerPlanStatisticResponse = {
  offersWithActiveOptimization: number
}

export type GetRepricerCustomerPlansResponse = {
  data: RepricerCustomerPlan[]
  totalCount: number
  pageCount: number
  currentPage: number
  pageSize: number
}
