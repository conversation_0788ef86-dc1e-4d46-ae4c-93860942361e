type MarketplaceOption = {
  label: string
  value: string
}

type CountryOption = MarketplaceOption & {
  disabled: boolean
  checked: boolean
}

type RepricerAccountMarketplace = {
  active: 0 | 1
  roleSide: string
  amazonDomain: string
  amazonMarketplaceId: string
  auto_renew: 0 | 1
  canBeDeleted: boolean
  canManageDuration: boolean
  canManageRoot: boolean
  currentPaymentModulePeriodId: number
  customerAccountId: number
  dateFinish: string
  dateStart: string
  deleted: 0 | 1
  offer_type: string
  extension: string
  id: number
  sellerId: string
  subscriptionId: number
  subscriptionTitle: string
  title: string
  isSelectedForNewPricing: boolean
}

type LostMarketplaceType = {
  active: boolean
  id: string
  title: string
}

type LostMarketplace = {
  amazonDomain: string
  active: number
  id: string
  lostActive: number
  sellerId: string
  title: string
  types: LostMarketplaceType[]
}

export type AmazonAccountItemForEdit = {
  id: number
  allowInitiatePayout: boolean
  amazonId: number
  amazonRegion: string
  accountZoneId: string
  amazonMarketplacesOptions: MarketplaceOption[]
  payoutMarketplaces: MarketplaceOption[]
  authToken: string | null
  countryOptions: CountryOption[]
  countryValue: string[]
  dateCreated: string
  fbaStockLocationDate: string
  fbaStockStorage: string
  isFbaDeMarketplace: boolean
  mwsAuthorization: boolean
  globalShipping: string
  ignoreAmazonShipping: 0 | 1
  loadOrders: 0 | 1
  marketplaceId: string
  marketplaceTitle: string
  name: string
  sellerId: string
  repricerAccountMarketplaces: RepricerAccountMarketplace[]
  repricerAccountMarketplacesB2C: RepricerAccountMarketplace[]
  repricerAccountMarketplacesB2B: RepricerAccountMarketplace[]
  lostMarketplaces: LostMarketplace[]
  lostMarketplacesLocksTypesForGlobalFilter: string[]
  lostActive: 0 | 1
  isNeedHideFullService: 0 | 1
  useLostModule: 0 | 1
  useBasModule: boolean
  bas_module_connected_status: string
  minLostEstimatedPrice: string
  isDeletable: boolean
  deleted: 0 | 1
  lost_service_type: string
  fs_credentials_status: string
  fs_email: string
  fsLoginEmail: string
  merchantId: string | null
  fs_is_wizard_finished: 0 | 1
  lostModuleConnectedStatus: string
  exclude_from_repricer: 0 | 1
  use_repricer_b2c_module: 0 | 1
  use_repricer_b2b_module: 0 | 1
  amazon_notification_enabled: 0 | 1
}
