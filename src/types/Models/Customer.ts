import { Country } from "types/Models/Country"

export type Customer = {
  id: number
  title: string
  address: string
  address2: string
  zip: string
  city: string
  country_id: number
  phone_1: string
  phone_2: string
  email: string
  active: 0 | 1
  currency_id: null
  created: string
  updated_at: string
  confirmed: 0 | 1
  vat_id: string
  product_loaded: 0 | 1
  discount_type: string
  discount_value: string
  use_plan: 0 | 1
  billing_email: string
  billing_title: string
  billing_address_1: string
  billing_address_2: string
  billing_zip: string
  billing_city: string
  billing_country_id: string
  use_payment_debit: 0 | 1
  use_payment_cc: 0 | 1
  mongo_db_created: 0 | 1
  locked_by_empty_payment: 0 | 1
  count_mws_access_denied: 0 | 1
  first_mws_access_denied_at: null
  elastic_search_created: 0 | 1
  vat_id_invalid_amount: 0 | 1
  use_lost_module: 0 | 1
  lost_module_signed: 0 | 1
  lost_module_signed_full_service: 0 | 1
  lost_service_type: string
  use_repricer_module: 0 | 1
  has_dpa_contract: 0 | 1
  dpa_deadline: string
  dpa_contract_user: 0 | 1
  dpa_contract_time: null
  dpa_contract_user_title: null
  dpa_contract_user_email: null
  dpa_order_anonymized: 0 | 1
  partner_id: number
  lost_rate: number
  lost_rate_full_service: number
  lost_refund_amount: string | null
  lost_refund_layer_notified: 0 | 1
  forced_2fa_activation_date: string | null
  use_bas_module: 0 | 1
  bas_module_started: 0 | 1
  db_index: number
  is_demo: 0 | 1
  bas_locked_by_empty_payment: 0 | 1
  lost_locked_by_empty_payment: 0 | 1
  repricer_locked_by_empty_payment: 0 | 1
  bas_discount_type: string
  bas_discount_value: string
  user_com_company_id: number | null
  allow_lost_full_service: 0 | 1
  use_repricer_b2b_module: 0 | 1
  use_repricer_b2c_module: 0 | 1
  api_version: number
  promo_code_id: number | null
  amazon_fees_vat: number | null
  amazon_fees_vat_country_id: number | null
  amazon_fees_vat_enabled: 0 | 1
  amazon_fees_vat_enabled_global: 0 | 1
  freemium_bas_enabled: 0 | 1
  use_new_pricing: 0 | 1
  selected_for_new_pricing: 0 | 1
  declined_new_pricing: 0 | 1
  new_pricing_migration_date: string | null
  use_repricer_plan_auto_upgrade: 0 | 1
  repricer_overdraft_disabled_at: string | null
  country: Country
  partner: {
    id: number
    title: string
  }
  partnerChannel: {
    id: number
    name: string
  }
  promoCode: {
    id: number
    code: string
  }
  partner_channel_id: number
  countActiveAccounts: number
  countActiveMarketplaces: number
  isDeletable: boolean
  hasSubscription: boolean
  hasPaymentData: boolean
  ccDataCloseToExpired: boolean
  needDPASettings: boolean
  vat_id_is_valid?: 0 | 1 | null
  status: string
}
