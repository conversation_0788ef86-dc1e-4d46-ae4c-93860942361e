export const permissionKeys = {
  amazonCustomerAccountEditSubscriptionDuration:
    "amazonCustomerAccountEditSubscriptionDuration",
  rootManager: "rootManager",
  sellerLogicManager: "sellerLogicManager",
  amazonAdsAccountList: "amazonAdsAccountList",
  amazonAdsAccountManage: "amazonAdsAccountManage",
  amazonCustomerAccountList: "amazonCustomerAccountList",
  amazonCustomerAccountManage: "amazonCustomerAccountManage",
  amazonMarketplaceList: "amazonMarketplaceList",
  amazonProductCompetitorList: "amazonProductCompetitorList",
  amazonProductGroupActionList: "amazonProductGroupActionList",
  amazonProductGroupActionSave: "amazonProductGroupActionSave",
  amazonProductHistoryList: "amazonProductHistoryList",
  amazonProductList: "amazonProductList",
  amazonProductOptimizationList: "amazonProductOptimizationList",
  amazonProductOptimizationSave: "amazonProductOptimizationSave",
  amazonProductService: "amazonProductService",
  amazonProductDimensionsAndWeightExportManage:
    "amazonProductDimensionsAndWeightExportManage",
  amazonProductDimensionsAndWeightExportView:
    "amazonProductDimensionsAndWeightExportView",
  amazonProductDimensionsAndWeightImportManage:
    "amazonProductDimensionsAndWeightImportManage",
  amazonProductDimensionsAndWeightImportView:
    "amazonProductDimensionsAndWeightImportView",
  amazonProductDimensionList: "amazonProductDimensionList",
  amazonProductDimensionManage: "amazonProductDimensionManage",
  amazonZoneList: "amazonZoneList",
  authClientList: "authClientList",
  authClientManage: "authClientManage",
  bannerDelete: "bannerDelete",
  bannerManage: "bannerManage",
  bannerView: "bannerView",
  basDashboardView: "basDashboardView",
  basIndirectCostManage: "basIndirectCostManage",
  basIndirectCostView: "basIndirectCostView",
  basMyProductsManage: "basMyProductsManage",
  basMyProductsView: "basMyProductsView",
  basOrderView: "basOrderView",
  basProductCostExportCreate: "basProductCostExportCreate",
  basProductCostExportList: "basProductCostExportList",
  basProductCostImportCreate: "basProductCostImportCreate",
  basProductCostImportList: "basProductCostImportList",
  basProductExportSettingList: "basProductExportSettingList",
  basProductExportSettingManage: "basProductExportSettingManage",
  basProductExportTemplateDelete: "basProductExportTemplateDelete",
  basProductExportTemplateList: "basProductExportTemplateList",
  basProductExportTemplateManage: "basProductExportTemplateManage",
  basProductExportTemplateRequest: "basProductExportTemplateRequest",
  basProductExportTemplateView: "basProductExportTemplateView",
  basProductImportSettingList: "basProductImportSettingList",
  basProductImportSettingManage: "basProductImportSettingManage",
  basRevenuePreviewList: "basRevenuePreviewList",
  basTransactionView: "basTransactionView",
  caseOverviewManage: "caseOverviewManage",
  caseOverviewView: "caseOverviewView",
  countryList: "countryList",
  countryManage: "countryManage",
  customerActive: "customerActive",
  customerDiscount: "customerDiscount",
  customerList: "customerList",
  customerManage: "customerManage",
  customerPayment: "customerPayment",
  customerView: "customerView",
  customerAuthClientList: "customerAuthClientList",
  customerAuthClientManage: "customerAuthClientManage",
  customerInvoiceList: "customerInvoiceList",
  customerInvoiceManage: "customerInvoiceManage",
  customerInvoiceSummaryList: "customerInvoiceSummaryList",
  customerInvoiceSummaryStatList: "customerInvoiceSummaryStatList",
  customerInvoiceTransactionList: "customerInvoiceTransactionList",
  customerInvoiceTransactionManage: "customerInvoiceTransactionManage",
  customerPaymentSettingsList: "customerPaymentSettingsList",
  customerPaymentSettingsManage: "customerPaymentSettingsManage",
  customerSubscriptionList: "customerSubscriptionList",
  customerVatSettingManage: "customerVatSettingManage",
  customerVatSettingView: "customerVatSettingView",
  estimatedInvoiceVolumeList: "estimatedInvoiceVolumeList",
  globalVatSettingManage: "globalVatSettingManage",
  globalAmazonAccountsList: "globalAmazonAccountsList",
  globalAmazonAccountsManage: "globalAmazonAccountsManage",
  internalNoteList: "internalNoteList",
  internalNoteManage: "internalNoteManage",
  invoiceList: "invoiceList",
  languageManage: "languageManage",
  lfDashboardView: "lfDashboardView",
  lostCaseAmazonLinkList: "lostCaseAmazonLinkList",
  lostCaseAmazonLinkManage: "lostCaseAmazonLinkManage",
  lostCaseBulkEditList: "lostCaseBulkEditList",
  lostCaseBulkEditManage: "lostCaseBulkEditManage",
  lostCaseStatisticView: "lostCaseStatisticView",
  lostCaseTemplateManage: "lostCaseTemplateManage",
  lostCaseTemplateView: "lostCaseTemplateView",
  lostPaymentHistoryManage: "lostPaymentHistoryManage",
  lostPaymentHistoryView: "lostPaymentHistoryView",
  lostTransactionStatisticView: "lostTransactionStatisticView",
  mailerQueueList: "mailerQueueList",
  mailerTemplateList: "mailerTemplateList",
  mailerTemplateManage: "mailerTemplateManage",
  navigationManage: "navigationManage",
  orderStatisticList: "orderStatisticList",
  organizationManage: "organizationManage",
  organizationView: "organizationView",
  partnerList: "partnerList",
  partnerManage: "partnerManage",
  partnerCreditNoteList: "partnerCreditNoteList",
  partnerCreditNoteManage: "partnerCreditNoteManage",
  paymentModuleList: "paymentModuleList",
  paymentModuleManage: "paymentModuleManage",
  paymentModuleVersionList: "paymentModuleVersionList",
  paymentModuleVersionManage: "paymentModuleVersionManage",
  privacyList: "privacyList",
  privacyManage: "privacyManage",
  productActivityList: "productActivityList",
  productActivityManage: "productActivityManage",
  productExportList: "productExportList",
  productExportManage: "productExportManage",
  productExportSettingList: "productExportSettingList",
  productExportSettingManage: "productExportSettingManage",
  productExportTemplateDelete: "productExportTemplateDelete",
  productExportTemplateList: "productExportTemplateList",
  productExportTemplateManage: "productExportTemplateManage",
  productExportTemplateRequest: "productExportTemplateRequest",
  productExportTemplateView: "productExportTemplateView",
  productGroupDelete: "productGroupDelete",
  productGroupList: "productGroupList",
  productGroupManage: "productGroupManage",
  productImportList: "productImportList",
  productImportManage: "productImportManage",
  productImportSettingList: "productImportSettingList",
  productImportSettingManage: "productImportSettingManage",
  rbacRoleList: "rbacRoleList",
  rbacRoleManage: "rbacRoleManage",
  repricerDashboardView: "repricerDashboardView",
  repricerRevenuePreviewList: "repricerRevenuePreviewList",
  sdBusinessHoursList: "sdBusinessHoursList",
  sdBusinessHoursManage: "sdBusinessHoursManage",
  sdCannedResponseGroupsManage: "sdCannedResponseGroupsManage",
  sdCannedResponsesList: "sdCannedResponsesList",
  sdCannedResponsesManage: "sdCannedResponsesManage",
  sdChangelogView: "sdChangelogView",
  sdDepartmentsList: "sdDepartmentsList",
  sdDepartmentsManage: "sdDepartmentsManage",
  sdEmailTemplateGroupsManage: "sdEmailTemplateGroupsManage",
  sdEmailTemplatesList: "sdEmailTemplatesList",
  sdEmailTemplatesManage: "sdEmailTemplatesManage",
  sdSkillGroupsManage: "sdSkillGroupsManage",
  sdSkillsList: "sdSkillsList",
  sdSkillsManage: "sdSkillsManage",
  sdSupportChannelsList: "sdSupportChannelsList",
  sdSupportChannelsManage: "sdSupportChannelsManage",
  sdTaskManagementManage: "sdTaskManagementManage",
  sdTaskManagementView: "sdTaskManagementView",
  sdSubtasksManage: "sdSubtasksManage",
  sdTasksBulkOperations: "sdTasksBulkOperations",
  sdTasksDelete: "sdTasksDelete",
  sdTasksList: "sdTasksList",
  sdTasksManage: "sdTasksManage",
  sdCustomTasksViewsCreate: "sdCustomTasksViewsCreate",
  sdSharedInboxView: "sdSharedInboxView",
  sdUsersList: "sdUsersList",
  sdUsersManage: "sdUsersManage",
  staffList: "staffList",
  staffManage: "staffManage",
  staffView: "staffView",
  strategyStopSchedulerDelete: "strategyStopSchedulerDelete",
  strategyStopSchedulerList: "strategyStopSchedulerList",
  strategyStopSchedulerManage: "strategyStopSchedulerManage",
  strategyStopSchedulerView: "strategyStopSchedulerView",
  supportCasesList: "supportCasesList",
  supportCasesManage: "supportCasesManage",
  surveyManage: "surveyManage",
  templateDelete: "templateDelete",
  templateList: "templateList",
  templateManage: "templateManage",
  templateView: "templateView",
  translationManage: "translationManage",
  translationView: "translationView",
  twoFactorAuthManage: "twoFactorAuthManage",
  userAllowLogin: "userAllowLogin",
  userAuthorize: "userAuthorize",
  userList: "userList",
  userManage: "userManage",
  userView: "userView",
  userHistoryList: "userHistoryList",
  amazonSellerCentralAccountsList: "amazonSellerCentralAccountsList",
  amazonSellerCentralAccountsManage: "amazonSellerCentralAccountsManage",
  amazonSellerCentralInvitationsList: "amazonSellerCentralInvitationsList",
  amazonSellerCentralInvitationsManage: "amazonSellerCentralInvitationsManage",
  promoCodeList: "promoCodeList",
  promoCodeManage: "promoCodeManage",
} as const

export const serviceDeskPermissionKeysArray = [
  "sdBusinessHoursList",
  "sdBusinessHoursManage",
  "sdCannedResponseGroupsManage",
  "sdCannedResponsesList",
  "sdCannedResponsesManage",
  "sdChangelogView",
  "sdDepartmentsList",
  "sdDepartmentsManage",
  "sdEmailTemplateGroupsManage",
  "sdEmailTemplatesList",
  "sdEmailTemplatesManage",
  "sdSkillGroupsManage",
  "sdSkillsList",
  "sdSkillsManage",
  "sdSupportChannelsList",
  "sdSupportChannelsManage",
  "sdTaskManagementManage",
  "sdTaskManagementView",
  "sdSubtasksManage",
  "sdTasksBulkOperations",
  "sdTasksDelete",
  "sdTasksList",
  "sdTasksManage",
  "sdCustomTasksViewsCreate",
  "sdSharedInboxView",
  "sdUsersList",
  "sdUsersManage",
]
