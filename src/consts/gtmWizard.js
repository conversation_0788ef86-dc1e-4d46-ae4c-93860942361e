import { PRODUCTS } from "consts/product"

export const productGTMNames = {
  [PRODUCTS.repricer]: "Repricer",
  [PRODUCTS.repricerB2C]: "RepricerB2C",
  [PRODUCTS.repricerB2B]: "RepricerB2B",
  [PRODUCTS.lost]: "LostAndFound",
  [PRODUCTS.bas]: "BusinessAnalytics",
}

/**
 * * null in gtmModalNames it`s component don`t tracking in gtm
 * ! key in gtmModalNames it`s event category see inside component
 */

export const gtmMainModalNames = {
  enableRepricerProducts: "modalEnableRepricerProducts",
  repricerTrialModal: "modalRepricerTrial",
  repricerSubscriptions: "modalRepricerSubscriptions",
  billingInformation: "modalWizardBillingInformation",
  connectChoseAmazonAccount: "modalWizardChooseAmazonAccount",
  connectChoseHomeMarketplace: "modalWizardChoseHomeMarketplace",
  connectChoseModule: "modalWizardProductSelection",
  connectChoseRegion: "modalWizardChooseRegion",
  createAccount: "modalWizardAccountConnected",
  dpaSettings: "modalWizardDPASettings",
  paymentCC: "modalWizardPaymentCCSettings",
  paymentDebit: "modalWizardPaymentDebitSettings",
  subscriptionUpdate: "modalSubscriptionUpdate",
  success: "modalWizardSuccess",
  summary: "modalWizardSummary",
  // Full-Service
  createFullServiceAccount: "modalWizardCreateFullServiceAccount",
  successConnectionFullService: "modalWizardSuccessConnectionFullService",
  connectAmazonAccountsForBAS: "modalWizardConnectAmazonAccountsForBAS",
  connectAmazonAccountsForBASOnlyInfo:
    "modalWizardConnectAmazonAccountsForBASOnlyInfo",
}

export const blacklistTitleProductGTM = [
  gtmMainModalNames.connectChoseModule,
  gtmMainModalNames.enableRepricerProducts,
]
