export const PRODUCTS = {
  repricer: "repricer",
  oldRepricer: "oldRepricer",
  repricerB2C: "repricerB2C",
  repricerB2B: "repricerB2B",
  lost: "lost-and-found",
  LOST_FULL_SERVICE_SETUP: "lf-fs-setup",
  bas: "bas",
  serviceDesk: "service-desk",
  crm: "crm",
} as const

export const PRODUCT_NAMES = {
  [PRODUCTS.repricer]: "Repricer",
  [PRODUCTS.oldRepricer]: "Repricer",
  [PRODUCTS.repricerB2C]: "Repricer B2C",
  [PRODUCTS.repricerB2B]: "Repricer B2B",
  [PRODUCTS.lost]: "Lost & Found",
  [PRODUCTS.bas]: "Business Analytics",
  [PRODUCTS.serviceDesk]: "Service desk",
} as const

export const PRODUCT_NAME_TITLES = {
  [PRODUCT_NAMES[PRODUCTS.repricer]]: "Repricer",
  [PRODUCT_NAMES[PRODUCTS.oldRepricer]]: "Repricer B2C",
  [PRODUCT_NAMES[PRODUCTS.lost]]: "Lost & Found",
  [PRODUCT_NAMES[PRODUCTS.bas]]: "Business Analytics",
  [PRODUCT_NAMES[PRODUCTS.serviceDesk]]: "Service desk",
} as const

export const OFFER_TYPES = {
  b2c: "B2C",
  b2b: "B2B",
} as const

export const SELECTED_REPRICER_PRODUCT = {
  [OFFER_TYPES.b2c]: PRODUCTS.repricerB2C,
  [OFFER_TYPES.b2b]: PRODUCTS.repricerB2B,
} as const

export const REPRICER_OFFER_TYPE = {
  [PRODUCTS.repricerB2C]: OFFER_TYPES.b2c,
  [PRODUCTS.repricerB2B]: OFFER_TYPES.b2b,
} as const

export const REVERSE_REPRICER_FIND_OFFER_TYPE = {
  [PRODUCTS.repricerB2B]: OFFER_TYPES.b2c,
  [PRODUCTS.repricerB2C]: OFFER_TYPES.b2b,
} as const

export const OFFER_TYPE_COLUMN_FILTER_OPTIONS = [
  { value: OFFER_TYPES.b2c, label: OFFER_TYPES.b2c },
  { value: OFFER_TYPES.b2b, label: OFFER_TYPES.b2b },
] as const
