export const globalStatuses = {
  new: {
    key: "new",
    label: "New",
  },
  "in-progress": {
    key: "in-progress",
    label: "In progress",
  },
  "on-review": {
    key: "on-review",
    label: "On review",
  },
  completed: {
    key: "completed",
    label: "Closed",
  },
} as const

export const LOST_CASES_STATUSES_KEYS = {
  NEW: "new",
  IN_PROGRESS: "in-progress",
  REIM_WAITING: "reim-waiting",
  REIM_CHECKING: "reim-checking",
  REIM_EMPTY: "reim-empty",
  ON_REVIEW: "on-review",
  NEED_REVIEW: "need-review",
  NEED_CUSTOMER_REVIEW: "need-customer-review",
  REViEWED: "reviewed",
  SUCCESSFULLY: "successfully",
  REJECTED: "rejected",
  PENDING: "pending",
  DISABLED: "disabled",
  NEED_AGENT_REVIEW: "need-agent-review",
} as const

export const statuses = {
  new: {
    key: "new",
    label: "New",
    color: "#2F80ED",
  },
  "in-progress": {
    key: "in-progress",
    label: "In progress",
    color: "#8BC34A",
  },
  "reim-waiting": {
    key: "reim-waiting",
    label: "Waiting for reimbursement",
    color: "#FA8C16",
  },
  "reim-checking": {
    key: "reim-checking",
    label: "Waiting for reimbursement confirmation",
    color: "#FF4D4F",
  },
  "reim-empty": {
    key: "reim-empty",
    label: "Zero reimbursement",
    color: "#780650",
  },
  "on-review": {
    key: "on-review",
    label: "On review",
    color: "#D70606",
  },
  "need-review": {
    key: "need-review",
    label: "Manual review",
    color: "#D4380D",
  },
  "need-customer-review": {
    key: "need-customer-review",
    label: "Require customer action",
    color: "#F89191",
  },
  "need-agent-review": {
    key: "need-agent-review",
    label: "Agent action required",
    color: "#EC7100",
  },
  reviewed: {
    key: "reviewed",
    label: "Reviewed manually",
    color: "#D48806",
  },
  successfully: {
    key: "successfully",
    label: "Successful",
    color: "#FFAA00",
  },
  rejected: {
    key: "rejected",
    label: "Rejected",
    color: "#D4B106",
  },
  pending: {
    key: "pending",
    label: "Pending",
    color: "#5B8C00",
  },
  disabled: {
    key: "disabled",
    label: "Disabled",
    color: "#00A646",
  },
} as const

export const caseTypes = {
  order: {
    key: "order",
    label: "Order",
  },
  "order-fee": {
    key: "order-fee",
    label: "FBA Fee",
  },
  "return-fee": {
    key: "return-fee",
    label: "FBA Return Fee",
  },
  "return-warehouse-lost": {
    key: "return-warehouse-lost",
    label: "Lost return in warehouse",
  },
  stock: {
    key: "stock",
    label: "Stock",
  },
  "stock-damaged": {
    key: "stock-damaged",
    label: "Damaged / Destroyed",
  },
  "inbound-shipment": {
    key: "inbound-shipment",
    label: "Inbound Shipment",
  },
  other: {
    key: "other",
    label: "Other",
  },
} as const

export const BULK_STATUSES = {
  NEW: "New",
  IN_PROGRESS: "In progress",
  DONE: "Done",
  TERMINATED: "Terminated",
} as const

export default {
  globalStatuses,
  statuses,
}
