import { ColumnSetting } from "@develop/fe-library"
import { getObjectEntries, getObjectKeys } from "@develop/fe-library/dist/utils"

import {
  AMAZON_SELLER_CENTRAL_ACCOUNTS_TABLE_SETTINGS_KEY,
  COLUMN_DEFAULT_VISIBILITY,
  COLUMN_TITLES,
} from "consts/amazonSellerCentralAccounts"

import { TableSettingsMap } from "types/TableSettings"

export const PAGE_DEFAULT = 1
export const PAGE_SIZE_DEFAULT = 25
export const SORT_DEFAULT = "-id"

export const tableSettings: TableSettingsMap = {
  [AMAZON_SELLER_CENTRAL_ACCOUNTS_TABLE_SETTINGS_KEY]: {
    titles: COLUMN_TITLES,
    defaultVisibility: COLUMN_DEFAULT_VISIBILITY,
  },
} as const

export const defaultSettings: Record<
  string,
  Array<ColumnSetting>
> = getObjectKeys(tableSettings).reduce((acc, key) => {
  const tableSetting = tableSettings[key]

  if (!tableSetting) {
    return acc
  }

  const { titles, defaultVisibility } = tableSetting

  return {
    ...acc,
    [key]: getObjectEntries(titles).map(([name, label]) => ({
      label,
      name,
      value: defaultVisibility[name],
    })),
  }
}, {})
