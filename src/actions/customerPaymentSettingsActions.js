import api from "api"
import { store } from "App"
import { customerSelector } from "selectors/userSelectors"
import generateActions from "utils/generateActions"
import { removeNullAndUndefined } from "utils/objectHelpers"

const actionPrefix = "customerPaymentSettings"

const {
  customerPaymentSettingsService: {
    getCustomerPaymentSettings,
    updateCreditCard,
    updateDebit,
    updateEmail,
    validateIban,
  },
} = api

export const types = {
  get: [
    `${actionPrefix}/get_customer_payment_settings_request`,
    `${actionPrefix}/get_customer_payment_settings_success`,
    `${actionPrefix}/get_customer_payment_settings_failure`,
  ],
  setInitialValues: [
    `${actionPrefix}/set_initial_values_request`,
    `${actionPrefix}/set_initial_values_success`,
    `${actionPrefix}/set_initial_values_failure`,
  ],
  updateCreditCard: [
    `${actionPrefix}/update_credit_card_request`,
    `${actionPrefix}/update_credit_card_success`,
    `${actionPrefix}/update_credit_card_failure`,
  ],
  updateDebit: [
    `${actionPrefix}/update_debit_request`,
    `${actionPrefix}/update_debit_success`,
    `${actionPrefix}/update_debit_failure`,
  ],
  updateEmail: [
    `${actionPrefix}/update_email_request`,
    `${actionPrefix}/update_email_success`,
    `${actionPrefix}/update_email_failure`,
  ],
  validateIban: [
    `${actionPrefix}/validate_iban_request`,
    `${actionPrefix}/validate_iban_success`,
    `${actionPrefix}/validate_iban_failure`,
  ],
  setValuesForValidateVatID: `${actionPrefix}/set_values_for_validate_vat_id`,
}

const getInitialValues = ({
  billing: {
    addressLine1,
    addressLine2,
    city,
    companyName,
    countryId,
    email,
    useBillingAddress = 0,
    zip,
  },
  payment,
  paymentTitle,
}) => {
  const {
    account_holder,
    bank,
    bic,
    card_holder,
    country,
    creditor_identifier,
    iban,
  } = payment || {}

  return {
    accountHolder: account_holder,
    bank,
    bic,
    billingEmail: email,
    billingCompanyName: companyName,
    billingAddressLine1: addressLine1,
    billingAddressLine2: addressLine2,
    billingCity: city,
    billingZip: zip,
    billingCountryId: countryId,
    cardHolder: card_holder,
    country,
    creditorIdentifier: creditor_identifier,
    iban,
    paymentTitle,
    useBillingAddress: useBillingAddress,
  }
}

const definitions = {
  get: () => ({
    callApi: getCustomerPaymentSettings,
  }),
  updateCreditCard: (payload, failureCallback, successCallback) => ({
    callApi: () => updateCreditCard(payload),
    failureCallback,
    successCallback,
  }),
  setInitialValues: (payload, successCallback) => ({
    callApi: async () => {
      const state = store.getState()
      const customer = customerSelector(state)
      const isSameCustomer = payload?.customerId === customer?.id

      if (isSameCustomer) {
        return {
          data: {
            initialValues: payload,
          },
        }
      }

      const settings = getInitialValues(
        (await getCustomerPaymentSettings()).data
      )

      return {
        data: {
          initialValues: { customerId: customer?.id, ...settings },
        },
      }
    },
    successCallback,
  }),
  updateDebit: (payload, failureCallback, successCallback) => ({
    callApi: () => updateDebit(removeNullAndUndefined(payload)),
    failureCallback,
    successCallback,
  }),
  updateEmail: (payload, failureCallback, successCallback) => ({
    callApi: () => updateEmail(payload),
    failureCallback,
    successCallback,
  }),
  validateIban: (payload, failureCallback, successCallback) => ({
    callApi: () => validateIban(payload),
    failureCallback,
    successCallback,
  }),
  setValuesForValidateVatID: ({ countryID, prevVatID }) => {
    return { countryID, prevVatID }
  },
}

export default generateActions(definitions, types)
