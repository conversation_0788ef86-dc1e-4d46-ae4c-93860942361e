import api from "api"

import generateActions from "utils/generateActions"

const actionPrefix = "caseStatistics"

const {
  lostCaseStatisticsService: {
    getLostCaseStatistics,
    getLostCaseStatisticsGrouped,
    getLostCaseStatisticsGraphData,
  },
} = api

export const types = {
  setSearchOptions: `${actionPrefix}/set_search_options`,
  setGroupedSearchOptions: `${actionPrefix}/set_grouped_search_options`,
  clearLostCaseStatistics: `${actionPrefix}/clear_lost_case_statistics`,
  getLostCaseStatistics: [
    `${actionPrefix}/get_lost_case_statistics_request`,
    `${actionPrefix}/get_lost_case_statistics_success`,
    `${actionPrefix}/get_lost_case_statistics_failure`,
  ],
  getLostCaseStatisticsGrouped: [
    `${actionPrefix}/get_lost_case_statistics_grouped_request`,
    `${actionPrefix}/get_lost_case_statistics_grouped_success`,
    `${actionPrefix}/get_lost_case_statistics_grouped_failure`,
  ],
  getLostCaseStatisticsGraphData: [
    `${actionPrefix}/get_lost_case_statistics_graph_data_request`,
    `${actionPrefix}/get_lost_case_statistics_graph_data_success`,
    `${actionPrefix}/get_lost_case_statistics_graph_data_failure`,
  ],
}

let actions

const definitions = {
  setSearchOptions: (searchOptions) => searchOptions,
  setGroupedSearchOptions: (searchOptions) => searchOptions,
  clearLostCaseStatistics: () => {},
  getLostCaseStatistics: (
    searchOptions,
    successCallback,
    failureCallback,
    dispatch
  ) => ({
    callApi: () => getLostCaseStatistics(searchOptions),
    successCallback,
    failureCallback,
  }),
  getLostCaseStatisticsGrouped: (
    searchOptions,
    successCallback,
    failureCallback,
    dispatch
  ) => ({
    callApi: () => getLostCaseStatisticsGrouped(searchOptions),
    successCallback,
    failureCallback,
  }),
  getLostCaseStatisticsGraphData: (
    params,
    successCallback,
    failureCallback
  ) => ({
    callApi: () => getLostCaseStatisticsGraphData(params),
    successCallback,
    failureCallback,
  }),
}

actions = generateActions(definitions, types)

export default actions
