import { ActionTypeAsync } from "actions/types"

export function withActionPrefix(prefix: string) {
  function createActionPath(actionType: string): string
  function createActionPath(actionType: string, isAsync: true): ActionTypeAsync
  function createActionPath(
    actionType: string,
    isAsync = false
  ): string | ActionTypeAsync {
    const actionPath = `${prefix}/${actionType}`

    if (!isAsync) {
      return actionPath
    }

    return [
      `${actionPath}_request`,
      `${actionPath}_success`,
      `${actionPath}_failure`,
    ]
  }

  return createActionPath
}
