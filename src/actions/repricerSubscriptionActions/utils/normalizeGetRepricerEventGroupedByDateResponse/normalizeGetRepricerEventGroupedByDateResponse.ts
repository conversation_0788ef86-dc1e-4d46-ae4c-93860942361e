import { eachMonthOfInterval, format, parseISO } from "date-fns"

import { DATE_FNS_FORMATS } from "consts/dateTime"

import type { NormalizeGetRepricerEventGroupedByDateResponse } from "./NormalizeGetRepricerEventGroupedByDateResponseTypes"

/**
 * This function processes the response data to ensure that it includes entries for each month
 * within the specified date range, even if there are no events for some months. It only processes
 * the data if the period is "month".
 *
 * @param params - The parameters passed to the API call.
 * @param response - The response data to be normalized, which is an array of objects containing date and event counts.
 *
 * @returns The normalized response data, which is an array of objects containing date and event counts for each month
 *          within the specified date range.
 */
export const normalizeGetRepricerEventGroupedByDateResponse: NormalizeGetRepricerEventGroupedByDateResponse =
  ({ params, response }) => {
    const { dateEnd, dateStart, period = "month" } = params

    if (period !== "month") {
      return response
    }

    const existingData = new Map(response.map((item) => [item.date, item]))

    const intervalMonths = eachMonthOfInterval({
      start: parseISO(dateStart),
      end: parseISO(dateEnd),
    })

    const normalizedResponse = intervalMonths.map((date) => {
      const key = format(date, DATE_FNS_FORMATS.YEAR_AND_MONTH)

      return existingData.get(key) || { date: key, total: 0, b2b: 0, b2c: 0 }
    })

    return normalizedResponse
  }
