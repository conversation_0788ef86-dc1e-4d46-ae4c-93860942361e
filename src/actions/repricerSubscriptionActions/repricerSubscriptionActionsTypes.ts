import { ActionTypeAsync } from "actions/types"

type AsyncTypeName =
  | "getRepricerSubscriptions"
  | "getRepricerSubscription"
  | "cancelNextRepricerCustomerPlan"
  | "cancelRepricerCustomerPlan"
  | "renewCurrentRepricerCustomerPlan"
  | "getRepricerCustomerPlanHistory"
  | "getRepricerCustomerPlanSubscriptions"
  | "getRepricerCustomerPlans"
  | "setNextRepricerCustomerPlan"
  | "deleteRepricerCustomerPlan"
  | "setRepricerCustomerPlan"
  | "upgradeRepricerCustomerPlan"
  | "downgradeRepricerCustomerPlan"
  | "setTrialRepricerCustomerPlan"
  | "getRepricerEventTotal"
  | "getRepricerEventGroupedByDate"
  | "getRepricerCustomerPlanStatistic"
  | "getRepricerCustomerPlanStatisticWithStock"
  | "ignoreNextSubscriptionAlert"
  | "remindLaterNextSubscriptionAlert"

export type RepricerSubscriptionActionTypesKeys = AsyncTypeName

export type RepricerSubscriptionActionTypes = {
  [K in AsyncTypeName]: ActionTypeAsync
}

export type RepricerSubscriptionActionDefinitions = {
  [K in RepricerSubscriptionActionTypesKeys]: (...args: any[]) => any
}
