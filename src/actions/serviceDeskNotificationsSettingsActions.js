import api from "api"
import generateActions from "utils/generateActions"
import {
  transformSettings,
  preparePayload,
} from "utils/serviceDeskNotificationsSettings"
const {
  serviceDeskNotificationsSettingsService: {
    fetchAllSettings,
    patchAllSettings,
  },
} = api

const prefix = `service_desk_notifications_settings`

export const types = {
  fetchAllSettings: [
    `${prefix}/fetch_all_request`,
    `${prefix}/fetch_all_success`,
    `${prefix}/fetch_all_failure`,
  ],
  patchAllSettings: [
    `${prefix}/patch_all_request`,
    `${prefix}/patch_all_success`,
    `${prefix}/patch_all_failure`,
  ],
  setSettings: `${prefix}/set_settings`,
}

const definitions = {
  fetchAllSettings: () => {
    return {
      callApi: async () => {
        const { data } = await fetchAllSettings()
        const settings = transformSettings(data)
        return {
          data: settings,
        }
      },
    }
  },
  patchAllSettings: (dispatch, getState) => {
    const {
      serviceDeskNotificationsSettings: { settings },
    } = getState()
    const payload = preparePayload(settings)
    return {
      callApi: async () => {
        return patchAllSettings(payload)
      },
    }
  },
  setSettings: (payload) => payload,
}

export default generateActions(definitions, types)
