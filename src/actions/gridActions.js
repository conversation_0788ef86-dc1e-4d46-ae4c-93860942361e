/// <reference path="./gridActions.d.ts"/>
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"
import { push } from "connected-react-router"

import api from "api"
import { getProduct, updateProduct } from "api/productsService"
import { getOptimizationTemplates } from "api/optimizationTemplatesService"
import { getAmazonCustomerAccountsMarketPlaces } from "api/amazonCustomerAccountMarketPlaceService"

import generateActions from "utils/generateActions"
import { ValidationErrorToFormik } from "utils/objectHelpers.ts"
import { checkIsArray } from "utils/arrayHelpers"

import CurrentAmazonCustomerAccountMarketplacePlan from "models/CurrentAmazonCustomerAccountMarketplacePlan"
import AmazonCustomerAccountMarketplace from "models/AmazonCustomerAccountMarketplace"
import AmazonMarketplace from "models/AmazonMarketplace"

import { permissionsSelector } from "selectors/userSelectors"

const PRODUCT_PREFIX = "products"

const {
  productsService: { getProducts },
} = api

export const types = {
  changeSearchOptions: `${PRODUCT_PREFIX}/change_search_options`,
  clearProductsForFilter: `${PRODUCT_PREFIX}/clear_products_for_filter`,
  pushUrl: [
    `${PRODUCT_PREFIX}/push_product_url`,
    `${PRODUCT_PREFIX}/push_product_url`,
    `${PRODUCT_PREFIX}/push_product_url`,
  ],
  get_products: [
    `${PRODUCT_PREFIX}/get_products`,
    `${PRODUCT_PREFIX}/get_success`,
    `${PRODUCT_PREFIX}/get_failure`,
  ],
  get_markets: [
    `${PRODUCT_PREFIX}/get_market_places`,
    `${PRODUCT_PREFIX}/get_market_places_success`,
    `${PRODUCT_PREFIX}/get_market_places_failure`,
  ],
  init: [
    `${PRODUCT_PREFIX}/initialization`,
    `${PRODUCT_PREFIX}/initialization_success`,
    `${PRODUCT_PREFIX}/initialization_failure`,
  ],
  selectUnselectProduct: `${PRODUCT_PREFIX}/select_product`,
  selectUnselectProductRange: `${PRODUCT_PREFIX}/select_product_range`,
  selectAll: `${PRODUCT_PREFIX}/selectAll`,
  selectTotalCount: `${PRODUCT_PREFIX}/selectTotalCount`,
  useSelectedList: `${PRODUCT_PREFIX}/use_selected_list`,
  onBulkActions: `${PRODUCT_PREFIX}/onBulkActions`,
  onGetProductData: [
    `${PRODUCT_PREFIX}/start_getting_product`,
    `${PRODUCT_PREFIX}/success_getting_product`,
    `${PRODUCT_PREFIX}/failure_getting_product`,
  ],
  showMinMaxFrom: `${PRODUCT_PREFIX}/show_min_max_form`,
  showEditNumberForm: `${PRODUCT_PREFIX}/show_edit_number_form`,
  onUpdateProduct: [
    `${PRODUCT_PREFIX}/start_updating_product`,
    `${PRODUCT_PREFIX}/success_updating_product`,
    `${PRODUCT_PREFIX}/failure_updating_product`,
  ],
  updateGridRow: `${PRODUCT_PREFIX}/update_grid_row`,
  getProductsForFilter: [
    `${PRODUCT_PREFIX}/get_products_for_filter_request`,
    `${PRODUCT_PREFIX}/get_products_for_filter_success`,
    `${PRODUCT_PREFIX}/get_products_for_filter_failure`,
  ],
}

let actions

const definitions = {
  changeSearchOptions: (searchOptions) => searchOptions,
  clearProductsForFilter: () => ({}),
  getProductsForFilter: (params = {}) => ({
    callApi: async () => {
      const { data: amazonCustomerAccountMarketplace } =
        await getAmazonCustomerAccountsMarketPlaces({ all: 1 })

      const AmazonCustomerAccountMarketplaces =
        amazonCustomerAccountMarketplace.map((object) => {
          const {
            currentAmazonCustomerAccountMarketplacePlan,
            amazonMarketplace,
            ...other
          } = object

          return new AmazonCustomerAccountMarketplace({
            ...other,
            amazonMarketplace: new AmazonMarketplace(amazonMarketplace),
            currentAmazonCustomerAccountMarketplacePlan:
              new CurrentAmazonCustomerAccountMarketplacePlan(
                currentAmazonCustomerAccountMarketplacePlan || {}
              ),
          })
        })

      return {
        data: {
          marketPlaces: AmazonCustomerAccountMarketplaces,
          productsList: (await getProducts(params)).data,
        },
      }
    },
  }),
  get_products: (params = {}, dispatch) => ({
    callApi: () => {
      /*dispatch({
        type: types.changeSearchOptions,
        payload: params,
      })*/
      if (params.useSelectedList && params.params !== undefined) {
        dispatch({
          type: types.useSelectedList,
          payload: { useSelectedList: params.useSelectedList },
        })
        params = params.params || {}
      }
      return getProducts(getUrlSearchParamsString({ params }))
    },
    doNotShowSpin: true,
  }),
  pushUrl: (params, dispatch) => ({
    async callApi() {
      dispatch({
        type: types.changeSearchOptions,
        payload: params,
      })
      dispatch(push(getUrlSearchParamsString({ params })))

      return {
        data: params,
      }
    },
  }),
  selectUnselectProduct: (product) => product,
  selectUnselectProductRange: (products) => products,
  selectAll: (selected) => ({ selected }),
  selectTotalCount: (selected) => ({ selected }),
  onBulkActions: (visible) => ({ visible }),
  onGetProductData: (id, visible, callback, dispatch) => ({
    callApi: async () => {
      const data = await getProduct(id)

      return {
        ...data,
      }
    },
    successCallback: callback,
  }),
  showMinMaxFrom: (formType, isVisible, productId) => ({
    formType,
    isVisible,
    productId,
  }),
  showEditNumberForm: (formType, isVisible, productId) => ({
    formType,
    isVisible,
    productId,
  }),
  onUpdateProduct: (id, values, callback, errorCallback, dispatch) => ({
    callApi: async () => {
      const data = await updateProduct(values, id)
      return { ...data }
    },
    successCallback: (gridObject) => {
      callback()
      dispatch(actions.updateGridRow(gridObject))
    },
    failureCallback: (payload) => {
      if (errorCallback) {
        if (checkIsArray(payload)) {
          errorCallback(ValidationErrorToFormik(payload))
        } else {
          errorCallback(payload)
        }
      }
    },
  }),
  updateGridRow: (gridObject) => gridObject,
  init: (params, dispatch, getState) => ({
    callApi: async () => {
      const { data: amazonCustomerAccountMarketplace } =
        await getAmazonCustomerAccountsMarketPlaces({ all: 1 })

      const { templateList } = permissionsSelector(getState())

      const AmazonCustomerAccountMarketplaces =
        amazonCustomerAccountMarketplace.map((object) => {
          const {
            currentAmazonCustomerAccountMarketplacePlan,
            amazonMarketplace,
            ...other
          } = object

          return new AmazonCustomerAccountMarketplace({
            ...other,
            amazonMarketplace: new AmazonMarketplace(amazonMarketplace),
            currentAmazonCustomerAccountMarketplacePlan:
              new CurrentAmazonCustomerAccountMarketplacePlan(
                currentAmazonCustomerAccountMarketplacePlan || {}
              ),
          })
        })
      const { data: strategies } = templateList
        ? await getOptimizationTemplates({
            all: 1,
            sort: "title",
          })
        : {
            data: [],
          }
      return {
        data: {
          marketPlaces: AmazonCustomerAccountMarketplaces,
          strategies,
        },
      }
    },
    doNotShowSpin: true,
  }),
}

actions = generateActions(definitions, types)

export default actions
