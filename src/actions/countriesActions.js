import api from "api"
import generateActions from "utils/generateActions"

const {
  countriesService: { getCountries, updateCountry },
} = api

const actionPrefix = "countries"

export const types = {
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  displayModal: `${actionPrefix}/display_modal`,
  showConfirmationModal: `${actionPrefix}/show_confirmation_modal`,
  get: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  getAll: [
    `${actionPrefix}/get_all_request`,
    `${actionPrefix}/get_all_success`,
    `${actionPrefix}/get_all_failure`,
  ],
  update: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],
}

const definitions = {
  changeSearchOptions: (searchOptions) => searchOptions,
  displayModal: (modalVisible = false, initialValues) => ({
    modalVisible,
    initialValues,
  }),
  showConfirmationModal: (confirmationModalVisible = false, initialValues) => ({
    confirmationModalVisible,
    initialValues,
  }),
  get: (searchOptions = {}, dispatch) => ({
    callApi: () => {
      dispatch({
        type: types.changeSearchOptions,
        payload: searchOptions,
      })

      return getCountries(searchOptions)
    },
  }),
  getAll: (payload) => ({
    callApi: () => getCountries({ all: 1, ...payload }),
  }),
  update: (payload, successCallback, failureCallback) => ({
    callApi: () => updateCountry(payload.id, payload),
    successCallback,
    failureCallback,
  }),
}

export default generateActions(definitions, types)
