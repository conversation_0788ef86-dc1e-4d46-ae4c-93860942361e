import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import api from "api"

import generateActions from "utils/generateActions"

import { TWENTY_FOUR_HOURS } from "consts/cachingTimeValues"

const {
  paymentModuleFlexibleItemService: {
    getPaymentModuleFlexibleItems,
    getPaymentModuleFlexibleItemPeriods,
    getPaymentModuleFlexibleItemSizes,
  },
  basPaymentsService: { getBasPayments },
} = api

const actionPrefix = "basPayments"

export const types = {
  getBasPaymentPlans: [
    `${actionPrefix}/get_bas_payment_request`,
    `${actionPrefix}/get_bas_payment_success`,
    `${actionPrefix}/get_bas_payment_failure`,
  ],
}

let actions

const definitions = {
  getBasPaymentPlans: ({ cache = false }, dispatch, getState) => {
    const {
      customer: { selectedCustomerId },
    } = getState()

    const searchCustomerId = getUrlSearchParams({
      locationSearch: document.location.search,
    })?.customerID

    const canUseCache = cache && searchCustomerId

    if (canUseCache) {
      cache = searchCustomerId === selectedCustomerId
    }

    return {
      cache,
      cacheKey: "basPayments",
      cacheTimeout: TWENTY_FOUR_HOURS,
      forceCache: !cache,
      callApi: async () => {
        const { id } = (await getBasPayments())?.data?.data?.[0]

        const dateRange = (await getPaymentModuleFlexibleItemSizes(id))?.data

        const paymentMatrix = (await getPaymentModuleFlexibleItems(id))?.data

        const paymentPeriods = (await getPaymentModuleFlexibleItemPeriods(id))
          ?.data

        return {
          data: {
            dateRange,
            paymentMatrix,
            paymentPeriods,
          },
        }
      },
    }
  },
}

actions = generateActions(definitions, types)
export default actions
