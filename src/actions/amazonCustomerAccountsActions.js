import { ROUTES } from "@develop/fe-library/dist/routes"
import { getUrlSearchParams } from "@develop/fe-library/dist/utils"
import { replace } from "connected-react-router"
import { sha256 } from "js-sha256"
import localForage from "localforage"

import config from "config"

import api from "api"

import basCustomerPlanActions from "actions/basCustomerPlanActions"

import generateActions from "utils/generateActions"
import l from "utils/intl"
import sleep from "utils/sleep"
import { getItem } from "utils/storage"

// import { apiVersionSelector } from "selectors/customerSelectors"
import { PRODUCTS, REPRICER_OFFER_TYPE } from "consts/product"

const {
  GENERAL_ROUTES: { PATH_AMAZON_ACCOUNTS, PATH_HOME },
} = ROUTES

const { fakeAccountCreation } = config

const actionPrefix = "amazonCustomerAccounts"
const localForageInstance = localForage.createInstance({
  driver: localForage.INDEXEDDB,
  name: "SLR Account Storage",
  storeName: "slraccountstorage",
})

const { getBasPlan } = basCustomerPlanActions

const {
  amazonCustomerAccountModuleRequestService: { getModuleRequests },
  amazonCustomerAccountService: {
    createAmazonAccount,
    deleteAmazonCustomerAccount,
    getAmazonCustomerAccounts,
    generateRegistrationUrl,
    generateSpRegistrationUrl,
    ignoreTokenRenew,
    setUseLost,
    setUseBas,
    redirect,
    tokenRedirect,
    updateAmazonCustomerAccount,
    setIncludeNextBasSubscription,
    changeAmazonSellerCentralAccount,
    getAmazonCustomerAccountPayoutSettings,
    updateAmazonCustomerAccountPayoutSettings,
    ignoreAmazonMarketplace,
    setExcludeFromRepricer,
  },
  amazonCustomerAccountStockService: {
    addAmazonCustomerAccountStock,
    deleteAmazonCustomerAccountStock,
  },
  amazonCustomerAccountMarketPlaceService: {
    addAmazonCustomerAccountMarketplace,
  },
} = api

export const types = {
  createAccount: [
    `${actionPrefix}/create_account_request`,
    `${actionPrefix}/create_account_success`,
    `${actionPrefix}/create_account_failure`,
  ],
  connectAccount: [
    `${actionPrefix}/connect_account_request`,
    `${actionPrefix}/connect_account_success`,
    `${actionPrefix}/connect_account_failure`,
  ],
  delete: [
    `${actionPrefix}/delete_request`,
    `${actionPrefix}/delete_success`,
    `${actionPrefix}/delete_failure`,
  ],
  displayAmazonFbaSettingsModal: `${actionPrefix}/display_amazon_fba_settings_modal`,
  displayRenewTokenModal: `${actionPrefix}/display_renew_token_modal`,
  displayAmazonTokenRenewModal: `${actionPrefix}/display_amazon_token_renew_modal`,
  displayModal: `${actionPrefix}/display_modal`,
  getAll: [
    `${actionPrefix}/get_all_customer_request`,
    `${actionPrefix}/get_all_customer_success`,
    `${actionPrefix}/get_all_customer_failure`,
  ],
  clearNewAccounts: `${actionPrefix}/clear_new_accounts`,
  generateRegistrationUrl: [
    `${actionPrefix}/generate_registration_url_request`,
    `${actionPrefix}/generate_registration_url_success`,
    `${actionPrefix}/generate_registration_url_failure`,
  ],
  ignoreTokenRenew: [
    `${actionPrefix}/ignore_token_renew_request`,
    `${actionPrefix}/ignore_token_renew_success`,
    `${actionPrefix}/ignore_token_renew_failure`,
  ],
  redirectToAmazon: [
    `${actionPrefix}/redirect_to_amazon_request`,
    `${actionPrefix}/redirect_to_amazon_success`,
    `${actionPrefix}/redirect_to_amazon_failure`,
  ],
  setUseLost: [
    `${actionPrefix}/set_use_lost_request`,
    `${actionPrefix}/set_use_lost_success`,
    `${actionPrefix}/set_use_lost_failure`,
  ],
  setUseBas: [
    `${actionPrefix}/set_use_bas_request`,
    `${actionPrefix}/set_use_bas_success`,
    `${actionPrefix}/set_use_bas_failure`,
  ],
  setAccountScrollTab: `${actionPrefix}/setAccountScrollTab`,
  setUseLostInProgress: `${actionPrefix}/set_use_lost_in_progress`,
  update: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],
  updateFbaSettings: [
    `${actionPrefix}/update_fba_settings_request`,
    `${actionPrefix}/update_fba_settings_success`,
    `${actionPrefix}/update_fba_settings_failure`,
  ],
  setIncludeNextBasSubscription: [
    `${actionPrefix}/set_include_next_bas_subscription_request`,
    `${actionPrefix}/set_include_next_bas_subscription_success`,
    `${actionPrefix}/set_include_next_bas_subscription_failure`,
  ],
  changeAmazonSellerCentralAccount: [
    `${actionPrefix}/change_amazon_sellercentral_account_request`,
    `${actionPrefix}/change_amazon_sellercentral_account_success`,
    `${actionPrefix}/change_amazon_sellercentral_account_failure`,
  ],
  activateBasAccount: [
    `${actionPrefix}/activate_bas_account_request`,
    `${actionPrefix}/activate_bas_account_success`,
    `${actionPrefix}/activate_bas_account_failure`,
  ],
  getAmazonCustomerAccountPayoutSettings: [
    `${actionPrefix}/get_amazon_customer_account_payout_settings_request`,
    `${actionPrefix}/get_amazon_customer_account_payout_settings_success`,
    `${actionPrefix}/get_amazon_customer_account_payout_settings_failure`,
  ],
  updateAmazonCustomerAccountPayoutSettings: [
    `${actionPrefix}/update_amazon_customer_account_payout_settings_request`,
    `${actionPrefix}/update_amazon_customer_account_payout_settings_success`,
    `${actionPrefix}/update_amazon_customer_account_payout_settings_failure`,
  ],
  ignoreAmazonMarketplace: [
    `${actionPrefix}/ignore_amazon_marketplace_request`,
    `${actionPrefix}/ignore_amazon_marketplace_success`,
    `${actionPrefix}/ignore_amazon_marketplace_failure`,
  ],
  setExcludeFromRepricer: [
    `${actionPrefix}/set_exclude_from_repricer_request`,
    `${actionPrefix}/set_exclude_from_repricer_success`,
    `${actionPrefix}/set_exclude_from_repricer_failure`,
  ],
}

let actions

const definitions = {
  createAccount: (failureCallback, successCallback, dispatch, getState) => ({
    callApi: async () => {
      const {
        router: {
          location: {
            query: { state },
          },
        },
        staff: {
          currentUser: { id: userId },
        },
        translations: { locale },
        customer: {
          customer: { repricerSubscriptionInfo },
        },
      } = getState()

      const {
        hash,
        homeMarketplace,
        marketplaces,
        productNames,
        accountModule,
        renewToken,
        redirectUrl,
      } = (await localForageInstance.getItem("newaccountdata")) || {}
      const redirectPage =
        accountModule || renewToken ? PATH_AMAZON_ACCOUNTS : redirectUrl

      dispatch(replace(redirectPage))

      if (state !== hash) {
        // eslint-disable-next-line no-throw-literal
        throw {
          payload: {
            message: l("Something went wrong. Please begin the process anew."),
          },
        }
      }

      const {
        data: { id },
      } = await createAmazonAccount({
        apiHash: hash,
        Marketplace: homeMarketplace,
      })

      if (!(accountModule || renewToken)) {
        const isOldRepricer =
          productNames === PRODUCTS.repricerB2C ||
          productNames === PRODUCTS.repricerB2B

        if (productNames === PRODUCTS.repricer && repricerSubscriptionInfo) {
          await setExcludeFromRepricer({ id, payload: { value: 0 } })
        }

        if (isOldRepricer) {
          try {
            for (const marketplace of marketplaces) {
              await addAmazonCustomerAccountMarketplace(id, {
                amazonMarketplaceId: marketplace,
                language: locale,
                offerType: REPRICER_OFFER_TYPE[productNames],
              })
            }
          } catch {}
        }
        if (productNames === PRODUCTS.lost) {
          try {
            await setUseLost(id, { value: 1 })

            let moduleRequests = await getModuleRequests({
              action: "enable",
              amazon_customer_account_id: id,
              module: "lost",
              pageSize: 1,
              sort: "-id",
              user_id: userId,
            })

            while (moduleRequests?.data?.data?.[0]?.status !== "finished") {
              await sleep(3000)

              moduleRequests = await getModuleRequests({
                action: "enable",
                amazon_customer_account_id: id,
                module: "lost",
                pageSize: 1,
                sort: "-id",
                user_id: userId,
              })
            }
          } catch {}
        } else if (productNames === PRODUCTS.bas) {
          try {
            await setUseBas(id, { value: 1 })

            let moduleRequests = await getModuleRequests({
              action: "enable",
              amazon_customer_account_id: id,
              module: "bas",
              pageSize: 1,
              sort: "-id",
              user_id: userId,
            })

            while (moduleRequests?.data?.data?.[0]?.status !== "finished") {
              await sleep(3000)

              moduleRequests = await getModuleRequests({
                action: "enable",
                amazon_customer_account_id: id,
                module: "bas",
                pageSize: 1,
                sort: "-id",
                user_id: userId,
              })
            }
          } catch {}
        }
      }

      return {
        data: {
          accountId: id,
        },
      }
    },
    successCallback,
    failureCallback,
  }),
  connectAccount: (
    accountId,
    marketplaces,
    productNames,
    successCallback,
    dispatch,
    getState
  ) => ({
    callApi: async () => {
      const {
        staff: {
          currentUser: { id: userId },
        },
        translations: { locale },
        customer: {
          customer: { repricerSubscriptionInfo },
        },
      } = getState()

      const isOldRepricer =
        productNames === PRODUCTS.repricerB2C ||
        productNames === PRODUCTS.repricerB2B

      if (productNames === PRODUCTS.repricer && repricerSubscriptionInfo) {
        await setExcludeFromRepricer({ id: accountId, payload: { value: 0 } })
      }

      if (isOldRepricer) {
        try {
          for (const marketplace of marketplaces) {
            await addAmazonCustomerAccountMarketplace(accountId, {
              amazonMarketplaceId: marketplace,
              language: locale,
              offerType: REPRICER_OFFER_TYPE[productNames],
            })
          }
        } catch {}
      }
      if (productNames === PRODUCTS.lost) {
        try {
          await setUseLost(accountId, { value: 1 })

          let moduleRequests = await getModuleRequests({
            action: "enable",
            amazon_customer_account_id: accountId,
            module: "lost",
            pageSize: 1,
            sort: "-id",
            user_id: userId,
          })

          while (moduleRequests?.data?.data?.[0]?.status !== "finished") {
            await sleep(3000)

            moduleRequests = await getModuleRequests({
              action: "enable",
              amazon_customer_account_id: accountId,
              module: "lost",
              pageSize: 1,
              sort: "-id",
              user_id: userId,
            })
          }
        } catch {}
      }

      return {
        data: {
          accountId,
          userId,
        },
      }
    },
    successCallback,
  }),
  delete: (id, callback) => ({
    callApi: () => deleteAmazonCustomerAccount(id),
    callback,
  }),
  displayAmazonFbaSettingsModal: (
    visible = false,
    accounts,
    moduleSetup,
    FbaSettingsModalDataFromProps = {}
  ) => {
    return {
      visible,
      accounts,
      moduleSetup,
      FbaSettingsModalDataFromProps,
    }
  },
  displayRenewTokenModal: ({ sellingPartnerId, loginSellerUrl }) => ({
    sellingPartnerId,
    loginSellerUrl,
  }),
  displayAmazonTokenRenewModal: ({
    visible = false,
    accounts,
    showIgnoreButton,
    closable = true,
    maskClosable = true,
    isFullServiceOnlySetup = null,
    fullServiceSellerId = null,
    connectOnlyFullServiceType = null,
    nextConnectingAccounts = null,
  }) => ({
    visible,
    accounts,
    showIgnoreButton,
    closable,
    maskClosable,
    isFullServiceOnlySetup,
    fullServiceSellerId,
    connectOnlyFullServiceType,
    nextConnectingAccounts,
  }),
  displayModal: (visible = false, modalName, requiredValues) => ({
    modalName,
    requiredValues,
    visible,
  }),
  generateRegistrationUrl: (payload, failureCallback, successCallback) => ({
    callApi: () => generateRegistrationUrl(payload),
    failureCallback,
    successCallback,
  }),
  getAll: (...args) => ({
    cache: args[0],
    cacheKey: "amazonCustomerAccounts",
    cacheTimeout: 1800000,
    callApi: async () => {
      const getState = args[args.length - 1]
      const {
        staff: {
          currentUser: { customer_id, sellerUser },
        },
      } = getState()

      let data = { data: [] }

      if (customer_id || sellerUser) {
        data = await getAmazonCustomerAccounts({ all: 1 })
      }

      return data
    },
    forceCache: !args[0],
    successCallback: args.length === 4 ? args[1] : undefined,
  }),
  ignoreTokenRenew: (id, successCallback) => ({
    callApi: () => ignoreTokenRenew(id),
    successCallback,
  }),
  redirectToAmazon: (
    {
      region,
      productNames,
      homeMarketplace,
      marketplaces,
      accountModule,
      renewToken,
      isBasActive,
      isBasModuleStarted,
      loginSellerUri,
    },
    _,
    getState
  ) => ({
    callApi: async () => {
      const hash = sha256(Date.now().toString()).substring(0, 44)

      if (loginSellerUri) {
        return {
          data: {
            registrationUrl: loginSellerUri,
            hash,
            loginSellerRedirect: true,
          },
        }
      }

      return {
        data: {
          ...(
            await generateSpRegistrationUrl({
              hash,
              marketplace_id: homeMarketplace,
              zone_id: region,
            })
          ).data,
          hash,
          loginSellerRedirect: false,
        },
      }
    },
    successCallback: async ({
      hash,
      registrationUrl,
      loginSellerRedirect = false,
    }) => {
      const devMode =
        getUrlSearchParams({ locationSearch: document.location.search })
          ?.devMode === "1"

      await localForageInstance.setItem("newaccountdata", {
        hash,
        region,
        homeMarketplace,
        marketplaces,
        productNames,
        accountModule,
        renewToken,
        isBasActive,
        isBasModuleStarted,
        loginSellerUri,
        redirectUrl: window.location.pathname,
      })

      const state = getState()
      // const api_version = apiVersionSelector(state)

      const sellerId =
        state?.amazonCustomerAccounts?.amazonTokenRenewModalProps?.accounts?.[0]
          ?.sellerId || getItem("sellerIdRefresh")

      const loginSellerUrl = `${registrationUrl}&state=0000-${hash}`

      if (devMode && fakeAccountCreation) {
        const devModeUrl = loginSellerRedirect
          ? loginSellerUrl
          : registrationUrl
        const state = getUrlSearchParams({ locationSearch: devModeUrl })?.state

        // renewToken
        //   ? tokenRedirect({ state, sellerId, api_version })
        //   : redirect({ state, api_version })
        renewToken ? tokenRedirect({ state, sellerId }) : redirect({ state })
      } else {
        loginSellerRedirect
          ? (window.location.href = loginSellerUrl)
          : (window.location.href = registrationUrl)
      }
    },
  }),
  clearNewAccounts: () => {
    return []
  },
  setUseLost: (id, payload, callback, dispatch, getState) => ({
    callApi: async () => {
      const {
        staff: {
          currentUser: { id: userId },
        },
      } = getState()

      await setUseLost(id, payload)

      dispatch({
        payload: {
          accountId: id,
          inProgress: true,
        },
        type: types.setUseLostInProgress,
      })

      let moduleRequests = await getModuleRequests({
        amazon_customer_account_id: id,
        module: "lost",
        status: "new",
        user_id: userId,
      })

      while (moduleRequests.data.data.length > 0) {
        await sleep(3000)

        moduleRequests = await getModuleRequests({
          amazon_customer_account_id: id,
          module: "lost",
          status: "new",
          user_id: userId,
        })
      }

      dispatch({
        payload: {
          accountId: id,
          inProgress: false,
        },
        type: types.setUseLostInProgress,
      })

      return {
        data: {},
      }
    },
    callback,
    doNotShowSpin: true,
  }),
  setUseBas: (id, payload) => ({
    callApi: async () => {
      await setUseBas(id, payload)

      return {
        data: {},
      }
    },
    doNotShowSpin: true,
  }),
  setIncludeNextBasSubscription: (id, payload, successCallback) => ({
    callApi: () => setIncludeNextBasSubscription(id, payload),
    successCallback,
  }),
  setAccountScrollTab: (isScrollTab) => ({
    isScrollTab,
  }),
  update: (id, payload, successCallback, failureCallback) => ({
    callApi: () => updateAmazonCustomerAccount(id, payload),
    successCallback,
    failureCallback,
  }),
  updateFbaSettings: (accounts, successCallback) => ({
    callApi: async () => {
      for (const {
        fba_stock_storage,
        fba_stock_location_date,
        id,
        stocksToAdd,
        stocksToDelete,
      } of accounts) {
        await updateAmazonCustomerAccount(id, {
          fba_settings_applied: 1,
          fba_stock_storage,
          fba_stock_location_date,
        })

        for (const marketPlaceId of stocksToAdd) {
          await addAmazonCustomerAccountStock({
            amazon_customer_account_id: id,
            amazon_marketplace_id: marketPlaceId,
          })
        }

        for (const { id: stockId } of stocksToDelete) {
          await deleteAmazonCustomerAccountStock(stockId)
        }
      }

      return {
        data: {},
      }
    },
    successCallback,
  }),
  changeAmazonSellerCentralAccount: ({
    id,
    customerId,
    amazonSellerCentralAccountId,
    forced,
    successCallback,
    failureCallback,
  }) => ({
    callApi: () =>
      changeAmazonSellerCentralAccount({
        id,
        customerId,
        amazonSellerCentralAccountId,
        forced,
      }),
    successCallback,
    failureCallback,
  }),
  activateBasAccount: (
    { accountId, userId, successCallback, failureCallback },
    dispatch
  ) => ({
    callApi: async () => {
      try {
        await setUseBas(accountId, { value: 1 })

        let moduleRequests = await getModuleRequests({
          action: "enable",
          amazon_customer_account_id: accountId,
          module: "bas",
          pageSize: 1,
          sort: "-id",
          user_id: userId,
        })

        while (moduleRequests?.data?.data?.[0]?.status !== "finished") {
          await sleep(3000)

          moduleRequests = await getModuleRequests({
            action: "enable",
            amazon_customer_account_id: accountId,
            module: "bas",
            pageSize: 1,
            sort: "-id",
            user_id: userId,
          })
        }
        dispatch(getBasPlan({ cache: false, successCallback: null }))
      } catch {}

      return {
        data: {},
      }
    },
    successCallback,
    failureCallback,
  }),
  getAmazonCustomerAccountPayoutSettings: ({
    id,
    successCallback,
    failureCallback,
  }) => ({
    callApi: () => getAmazonCustomerAccountPayoutSettings({ id }),
    successCallback,
    failureCallback,
  }),
  updateAmazonCustomerAccountPayoutSettings: ({
    id,
    payload,
    successCallback,
    failureCallback,
  }) => ({
    callApi: () => updateAmazonCustomerAccountPayoutSettings({ id, payload }),
    successCallback,
    failureCallback,
  }),
  ignoreAmazonMarketplace: ({
    id,
    payload,
    successCallback,
    failureCallback,
  }) => ({
    callApi: () => ignoreAmazonMarketplace({ id, payload }),
    successCallback,
    failureCallback,
  }),
  setExcludeFromRepricer: ({
    id,
    payload,
    successCallback,
    failureCallback,
  }) => ({
    callApi: () => setExcludeFromRepricer({ id, payload }),
    successCallback,
    failureCallback,
  }),
}

actions = generateActions(definitions, types)

export default actions
