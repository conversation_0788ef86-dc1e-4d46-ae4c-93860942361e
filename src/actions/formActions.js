import {
  getObjectKeys,
  getUrlSearchParams,
} from "@develop/fe-library/dist/utils"
import get from "lodash/get"
import merge from "lodash/merge"

import { addOptimization, updateOptimization } from "api/optimizationService"
import {
  getAmazonProducts,
  getProduct,
  setValidateProduct,
  updateProduct,
} from "api/productsService"

import gridActions from "actions/gridActions"

import { optimizationOrderTypes } from "utils/formConstants"
import generateActions from "utils/generateActions.js"

const actionPrefix = "productForm"

export const types = {
  display: `${actionPrefix}/display`,
  openProduct: [
    `${actionPrefix}/open-product-request`,
    `${actionPrefix}/open-product-request-success`,
    `${actionPrefix}/open-product-request-failure`,
  ],
  onSaveOptimization: [
    `${actionPrefix}/save-optimization-request`,
    `${actionPrefix}/save-optimization-request-success`,
    `${actionPrefix}/save-optimization-request-failure`,
  ],
  validateProductForm: [
    `${actionPrefix}/validate-product-request`,
    `${actionPrefix}/validate-product-request-success`,
    `${actionPrefix}/validate-product-request-failure`,
  ],
  onSaveProduct: [
    `${actionPrefix}/save-product-request`,
    `${actionPrefix}/save-product-request-success`,
    `${actionPrefix}/save-product-request-failure`,
  ],
  switchOptimization: `${actionPrefix}_switch_optimization`,
  switchOptimizationTemplateID: `${actionPrefix}_switch_optimization_template_id`,
  clearFormErrors: `${actionPrefix}/product-form_clear-errors`,
  showSearchAmazonProductModal: `${actionPrefix}/show-search-product-modal`,
  searchAmazonProducts: [
    `${actionPrefix}/search-amazon-products-request`,
    `${actionPrefix}/search-amazon-products-success`,
    `${actionPrefix}/search-amazon-products-failure`,
  ],
  setSearchAmazonAsins: `${actionPrefix}/set-search-amazon-asins`,
}

let actions

/**
 * Prepare limits for a form
 * @param objectData
 * @return {{limits2: *, limits: *}}
 */
export const getSeparatedLimits = (objectData) => {
  const optimizationLimits = get(
    objectData,
    "optimization.optimizationLimits",
    []
  )
  const limits = optimizationLimits.filter(
    ({ type }) => type === optimizationOrderTypes.ORDER
  )
  const limits2 = optimizationLimits.filter(
    ({ type }) => type === optimizationOrderTypes.DAY
  )

  return { limits, limits2 }
}

const definitions = {
  display: (visible = false) => ({ modalProps: { visible } }),
  switchOptimization: (optimization = false) => ({ optimization }),
  switchOptimizationTemplateID: (optimizationTemplateID) => ({
    optimizationTemplateID,
  }),
  openProduct: (product, autoFillObj, dispatch) => ({
    callApi: async () => {
      const { data: productData } = await getProduct(product.id)
      const { _forseValidate, ...otherFilds } = autoFillObj
      const { limits, limits2 } = getSeparatedLimits(productData)

      return {
        data: {
          forseValidate: _forseValidate,
          productData: {
            ...merge({}, productData, otherFilds),
            optimization: {
              ...productData.optimization,
              optimizationLimits: limits,
              optimizationLimits2: limits2,
            },
          },
          //          templates,
        },
      }
    },
    successCallback: () => {
      dispatch(actions.display(true))
      dispatch(actions.clearFormErrors())
    },
  }),
  clearFormErrors: () => ({ clear: true }),
  onSaveOptimization: (formValues, dispatch, getState) => ({
    callApi: async () => {
      const {
        productForm: {
          formProps: {
            initialValues,
            optimization: activeOptimization,
            optimizationTemplateID,
          },
        },
      } = getState()

      let lastFormValues = initialValues

      if (getObjectKeys(formValues).length > 0) {
        lastFormValues = formValues
      }

      const { optimization, id, ...all } = lastFormValues
      let updatedOptimization = { data: {} }

      /*optimization.optimizationSellers = optimization.optimizationSellers.map(
        seller => ({ ...seller, type: optimization.seller_list_type })
      )*/

      const optimizationLimits = [
        ...optimization.optimizationLimits,
        ...optimization.optimizationLimits2,
      ]

      if (activeOptimization) {
        if (optimization.id && optimizationTemplateID === null) {
          updatedOptimization = await updateOptimization(optimization.id, {
            ...optimization,
            optimizationLimits: {
              ...optimizationLimits,
            },
          })
        } else if (optimizationTemplateID === null) {
          updatedOptimization = await addOptimization({
            ...optimization,
            optimizationLimits: {
              ...optimizationLimits,
            },
          })
        }
      }

      return {
        data: {
          updatedOptimization,
          all,
          id,
        },
      }
    },
    successCallback: ({ updatedOptimization, all, id }) => {
      dispatch(actions.onSaveProduct({ id, all, updatedOptimization }))
    },
    doNotShowSpin: true,
  }),
  validateProductForm: (dispatch, getState) => ({
    callApi: () => {
      const { productForm } = getState()
      const product = productForm?.formProps?.initialValues

      product.formUpdate = 1

      return setValidateProduct(product?.id, product)
    },
  }),
  onSaveProduct: (payload, dispatch) => ({
    callApi: async () => {
      const {
        id,
        all,
        updatedOptimization: { data: upOptimization },
      } = payload

      all.optimization_id = upOptimization.id || all.optimization_id
      all.formUpdate = 1

      // API fix - have to be removed
      all.productSetting.other_fee = all.productSetting.other_fee || 0
      all.tax_amount = all.tax_amount || 0
      // -----------

      const updatedProduct = await updateProduct(all, id)

      return {
        data: updatedProduct,
      }
    },
    successCallback: (payload) => {
      const { data: updatedProduct } = payload

      dispatch(gridActions.updateGridRow(updatedProduct))
      dispatch(actions.display(false))
      dispatch(
        gridActions.get_products(
          getUrlSearchParams({
            locationSearch: document.location.search.replace("n/a", "na"),
          })
        )
      )
    },
  }),
  showSearchAmazonProductModal: (visible, marketPlaceId) => ({
    visible,
    marketPlaceId,
  }),
  searchAmazonProducts: (marketPlaceId, queryString) => ({
    callApi: () => getAmazonProducts(marketPlaceId, queryString),
  }),
  setSearchAmazonAsins: (asins) => ({ asins }),
}

actions = generateActions(definitions, types)
export default actions
