import api from "api"
import generateActions from "utils/generateActions"

const actionPrefix = "userActivity"

const {
  userActivityService: { getEntityList, getUserActivity },
} = api

export const types = {
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  get: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  getEntityList: [
    `${actionPrefix}/get_entity_list_request`,
    `${actionPrefix}/get_entity_list_success`,
    `${actionPrefix}/get_entity_list_failure`,
  ],
}

const definitions = {
  changeSearchOptions: (searchOptions) => searchOptions,
  get: (searchOptions = {}, dispatch) => ({
    callApi: () => {
      dispatch({
        type: types.changeSearchOptions,
        payload: searchOptions,
      })

      return getUserActivity(searchOptions)
    },
  }),
  getEntityList: () => ({
    callApi: () => getEntityList(),
  }),
}

export default generateActions(definitions, types)
