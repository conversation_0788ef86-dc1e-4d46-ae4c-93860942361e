import { ActionTypeAsync } from "actions/types"

type AsyncTypeName =
  | "getServiceDeskCurrentUserProfile"
  | "getServiceDeskUser"
  | "getServiceDeskProfile"
  | "patchServiceDeskUserProfile"
  | "getSignatureTemplates"
  | "deleteAvatar"
  | "changePresence"

type SyncTypeName =
  | "setRawAvatar"
  | "setAvatar"
  | "downloadAvatar"
  | "uploadAvatar"
  | "setIsExpanded"

export type ServiceDeskProfileActionTypesKeys = AsyncTypeName | SyncTypeName

export type ServiceDeskProfileActionTypes = {
  [Key in AsyncTypeName]: ActionTypeAsync
} & {
  [Key in SyncTypeName]: string
}

export type ServiceDeskProfileActionDefinitions = {
  [Key in ServiceDeskProfileActionTypesKeys]: (...args: any[]) => any
}
