import api from "api"
import generateActions from "utils/generateActions"

const actionPrefix = "lostCaseType"

const {
  lostCaseTypeService: { getLostCaseTypes },
} = api

export const types = {
  get: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
}

let actions

const definitions = {
  get: () => ({
    callApi: getLostCaseTypes,
  }),
}

actions = generateActions(definitions, types)

export default actions
