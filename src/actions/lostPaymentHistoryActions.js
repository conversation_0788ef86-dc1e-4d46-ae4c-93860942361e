import { push } from "connected-react-router"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

import api from "api"
import generateActions from "utils/generateActions"

const actionPrefix = "lostPaymentHistory"

const {
  lostPaymentHistoryService: { getPayments, modifyPayment, getPaymentsFilters },
} = api

export const types = {
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  displayModal: `${actionPrefix}/display_modal`,
  showConfirmationModal: `${actionPrefix}/show_confirmation_modal`,
  get: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  getFilters: [
    `${actionPrefix}/get_filters_request`,
    `${actionPrefix}/get_filters_success`,
    `${actionPrefix}/get_filters_failure`,
  ],
  update: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],
}

const definitions = {
  changeSearchOptions: (searchOptions) => searchOptions,
  displayModal: (modalVisible = false, initialValues) => ({
    modalVisible,
    initialValues,
  }),
  showConfirmationModal: (confirmationModalVisible = false, initialValues) => ({
    confirmationModalVisible,
    initialValues,
  }),
  get: (searchOptions = {}, successCallback, dispatch) => ({
    callApi: () => {
      dispatch({
        type: types.changeSearchOptions,
        payload: searchOptions,
      })

      dispatch(push(getUrlSearchParamsString({ params: searchOptions })))

      return getPayments(searchOptions)
    },
    successCallback,
  }),
  getFilters: (searchOptions = {}) => ({
    callApi: () => getPaymentsFilters(searchOptions),
  }),
  update: (payload, successCallback, failureCallback) => ({
    callApi: () => modifyPayment(payload),
    successCallback,
    failureCallback,
  }),
}

export default generateActions(definitions, types)
