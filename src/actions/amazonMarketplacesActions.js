import api from "api"
import generateActions from "utils/generateActions"
import { push } from "connected-react-router"
import { getUrlSearchParamsString } from "@develop/fe-library/dist/utils"

const AMAZON_MARKETPLACES = "amazon_marketplaces"

const {
  amazonMarketplacesService: { getAmazonMarketplaces },
} = api

let actions

export const types = {
  changeSearchOptions: `${AMAZON_MARKETPLACES}/change_search_options`,
  getAll: [
    `${AMAZON_MARKETPLACES}/get_all_request`,
    `${AMAZON_MARKETPLACES}/get_all_success`,
    `${AMAZON_MARKETPLACES}/get_all_failure`,
  ],
  getAllWithInactive: [
    `${AMAZON_MARKETPLACES}/get_all_with_inactive_request`,
    `${AMAZON_MARKETPLACES}/get_all_with_inactive_success`,
    `${AMAZON_MARKETPLACES}/get_all_with_inactive_failure`,
  ],
  get: [
    `${AMAZON_MARKETPLACES}/get_request`,
    `${AMAZON_MARKETPLACES}/get_success`,
    `${AMAZON_MARKETPLACES}/get_failure`,
  ],
}

const definitions = {
  getAll: (successCallback) => ({
    callApi: () => getAmazonMarketplaces({ all: 1, active: 1 }),
    successCallback,
  }),
  getAllWithInactive: () => ({
    callApi: () => getAmazonMarketplaces({ all: 1 }),
  }),
  get: (searchOptions = {}, dispatch) => ({
    callApi: () => {
      dispatch({
        type: types.changeSearchOptions,
        payload: searchOptions,
      })

      dispatch(push(getUrlSearchParamsString({ params: searchOptions })))

      return getAmazonMarketplaces(searchOptions)
    },
  }),
}

actions = generateActions(definitions, types)

export default actions
