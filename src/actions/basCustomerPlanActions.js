import { getUrlSearchParams } from "@develop/fe-library/dist/utils"

import api from "api"
import generateActions from "utils/generateActions"
import { removeNullAndUndefined } from "utils/objectHelpers"

import { TEN_MINUTES } from "consts/cachingTimeValues"

const {
  basCustomerPlanService: {
    getBasCustomerPlan,
    getBasSubscriptions,
    setBasPlan,
    cancelBasNextPlan,
    ignoreBasAlert,
    remindBasLater,
    revertBasPlan,
    setAutoRenew,
    changeBasDuration,
    cancelBasTrialPlan,
    getBasCustomerStatistic,
  },
} = api

const actionPrefix = "bas-customer-plan"

export const types = {
  getBasPlan: [
    `${actionPrefix}/get_bas_plan_request`,
    `${actionPrefix}/get_bas_plan_success`,
    `${actionPrefix}/get_bas_plan_failure`,
  ],
  getBasSubscriptions: [
    `${actionPrefix}/get_bas_subscriptions_request`,
    `${actionPrefix}/get_bas_subscriptions_success`,
    `${actionPrefix}/get_bas_subscriptions_failure`,
  ],
  setBasPlan: [
    `${actionPrefix}/set_plan_request`,
    `${actionPrefix}/set_plan_success`,
    `${actionPrefix}/set_plan_failure`,
  ],
  cancelBasNextPlan: [
    `${actionPrefix}/cancel_plan_request`,
    `${actionPrefix}/cancel_plan_success`,
    `${actionPrefix}/cancel_plan_failure`,
  ],
  remindBasLater: [
    `${actionPrefix}/remind_later_request`,
    `${actionPrefix}/remind_later_success`,
    `${actionPrefix}/remind_later_failure`,
  ],
  ignoreBasAlert: [
    `${actionPrefix}/ignore_alert_request`,
    `${actionPrefix}/ignore_alert_success`,
    `${actionPrefix}/ignore_alert_failure`,
  ],
  revertBasPlan: [
    `${actionPrefix}/revert_bas_plan_request`,
    `${actionPrefix}/revert_bas_plan_success`,
    `${actionPrefix}/revert_bas_plan_failure`,
  ],
  deleteBasPlan: [
    `${actionPrefix}/delete_bas_plan_request`,
    `${actionPrefix}/delete_bas_plan_success`,
    `${actionPrefix}/delete_bas_plan_failure`,
  ],
  cancelBasTrialPlan: [
    `${actionPrefix}/cancel_bas_plan_request`,
    `${actionPrefix}/cancel_bas_plan_success`,
    `${actionPrefix}/cancel_bas_plan_failure`,
  ],
  setAutoRenew: [
    `${actionPrefix}/set_auto_renew_request`,
    `${actionPrefix}/set_auto_renew_success`,
    `${actionPrefix}/set_auto_renew_failure`,
  ],
  changeBasDuration: [
    `${actionPrefix}/change_bas_duration_request`,
    `${actionPrefix}/change_bas_duration_success`,
    `${actionPrefix}/change_bas_duration_failure`,
  ],
  getBasCustomerStatistic: [
    `${actionPrefix}/get_bas_customer_statistic_request`,
    `${actionPrefix}/get_bas_customer_statistic_success`,
    `${actionPrefix}/get_bas_customer_statistic_failure`,
  ],
}

let actions

const definitions = {
  getBasPlan: (
    { successCallback, cache = false, pageSize = 100 },
    dispatch,
    getState
  ) => {
    const { customer: { selectedCustomerId } = {} } = getState() || {}

    const searchCustomerId = getUrlSearchParams({
      locationSearch: document.location.search,
    })?.customerID

    const canUseCache = cache && searchCustomerId

    if (canUseCache) {
      cache = searchCustomerId === selectedCustomerId
    }

    return {
      cache,
      cacheKey: "basPlan",
      cacheTimeout: TEN_MINUTES,
      forceCache: !cache,
      callApi: () => getBasCustomerPlan({ pageSize }),
      successCallback,
    }
  },

  getBasSubscriptions: ({ successCallback, failureCallback }) => ({
    callApi: () => getBasSubscriptions(),
    successCallback,
    failureCallback,
  }),

  setBasPlan: ({
    paymentModuleFlexiblePeriodId,
    language,
    activateAllPreviousAccounts,
    amazonCustomerAccountIds,
    successCallback,
    failureCallback,
  }) => ({
    callApi: () =>
      setBasPlan(
        removeNullAndUndefined({
          paymentModuleFlexiblePeriodId,
          language,
          activateAllPreviousAccounts,
          amazonCustomerAccountIds,
        })
      ),
    successCallback,
    failureCallback,
  }),

  cancelBasNextPlan: (payload, successCallback) => ({
    callApi: () => cancelBasNextPlan(payload),
    successCallback,
  }),

  remindBasLater: (payload, successCallback) => ({
    callApi: () => remindBasLater(payload),
    successCallback,
  }),

  ignoreBasAlert: (payload, successCallback) => ({
    callApi: () => ignoreBasAlert(payload),
    successCallback,
  }),

  revertBasPlan: (payload, successCallback) => ({
    callApi: () => revertBasPlan({ basCustomerPlanId: payload }),
    successCallback,
  }),

  deleteBasPlan: (params = {}, successCallback) => ({
    callApi: () => revertBasPlan(),
    successCallback,
  }),

  cancelBasTrialPlan: (payload, successCallback) => ({
    callApi: () => cancelBasTrialPlan(payload),
    successCallback,
  }),

  setAutoRenew: (payload, successCallback) => ({
    callApi: () => setAutoRenew(payload),
    successCallback,
  }),

  changeBasDuration: (payload, successCallback, failureCallback) => ({
    callApi: () => changeBasDuration(payload),
    successCallback,
    failureCallback,
  }),
  getBasCustomerStatistic: () => ({
    callApi: () => getBasCustomerStatistic(),
  }),
}

actions = generateActions(definitions, types)
export default actions
