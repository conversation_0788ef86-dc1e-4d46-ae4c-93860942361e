import api from "api"
import generateActions from "utils/generateActions"

const actionPrefix = "lostAmazonLink"

const {
  lostAmazonLinkService: {
    addLostAmazonLink,
    getLostAmazonLinks,
    updateLostAmazonLink,
  },
} = api

export const types = {
  add: [
    `${actionPrefix}/add_request`,
    `${actionPrefix}/add_success`,
    `${actionPrefix}/add_failure`,
  ],
  changeSearchOptions: `${actionPrefix}/change_search_options`,
  displayModal: `${actionPrefix}/display_modal`,
  get: [
    `${actionPrefix}/get_request`,
    `${actionPrefix}/get_success`,
    `${actionPrefix}/get_failure`,
  ],
  update: [
    `${actionPrefix}/update_request`,
    `${actionPrefix}/update_success`,
    `${actionPrefix}/update_failure`,
  ],
}

const definitions = {
  add: (payload, successCallback, failureCallback) => ({
    callApi: () => {
      return addLostAmazonLink(payload)
    },
    successCallback,
    failureCallback,
  }),
  changeSearchOptions: (searchOptions) => searchOptions,
  displayModal: (modalVisible = false, initialValues) => ({
    modalVisible,
    initialValues,
  }),
  get: (searchOptions = {}, dispatch) => ({
    callApi: () => {
      dispatch({
        type: types.changeSearchOptions,
        payload: searchOptions,
      })

      return getLostAmazonLinks(searchOptions)
    },
  }),
  update: (payload, successCallback, failureCallback) => ({
    callApi: () => {
      const { id, ...params } = payload

      return updateLostAmazonLink(id, params)
    },
    successCallback,
    failureCallback,
  }),
}

export default generateActions(definitions, types)
