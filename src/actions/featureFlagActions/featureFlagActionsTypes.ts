import { ActionAsyncDefinitionType, ActionTypeAsync } from "actions/types"

import { GetFeatureFlagRequestParams } from "api/featureFlagService/featureFlagServiceTypes"

type AsyncTypeName = "getFeatureFlag"

export type FeatureFlagActionTypesKeys = AsyncTypeName

export type FeatureFlagActionTypes = {
  [K in AsyncTypeName]: ActionTypeAsync
}

export type FeatureFlagActionDefinitions = {
  getFeatureFlag: ActionAsyncDefinitionType<GetFeatureFlagRequestParams>
}
