import api from "api"

import { withActionPrefix } from "actions/utils"

import { customerSelector } from "selectors/userSelectors"

import generateActions from "utils/generateActions"

import { FeatureFlagActionDefinitions } from "./featureFlagActionsTypes"

const createActionType = withActionPrefix("config")

const {
  featureFlagService: { getFeatureFlag },
} = api

const types = {
  getFeatureFlag: createActionType("get_feature_flag", true),
}

const definitions: FeatureFlagActionDefinitions = {
  getFeatureFlag: (
    { params = {}, successCallback = () => {}, failureCallback = () => {} },
    dispatch,
    getState
  ) => ({
    callApi: async () => {
      const customer = customerSelector(getState?.())

      return await getFeatureFlag({
        params: {
          customerId: customer?.id,
          ...params,
        },
      })
    },
    successCallback,
    failureCallback,
  }),
}

const featureFlagActions = generateActions(
  definitions,
  types
) as FeatureFlagActionDefinitions

export { definitions, featureFlagActions, types }
