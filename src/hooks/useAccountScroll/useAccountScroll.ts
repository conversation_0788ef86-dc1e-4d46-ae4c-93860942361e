import { useCallback, useEffect } from "react"
import { useHistory } from "react-router-dom"

import { useFirstRender } from "hooks"

import { getIdAndTabKeyFromHash } from "utils/getIdAndTabKeyFromHash"

import { UseAccountScroll } from "./UseAccountScrollTypes"

export const useAccountScroll: UseAccountScroll = ({ BLOCK_ID_PREFIX }) => {
  const isFirstRender = useFirstRender()

  const {
    location: { pathname, hash, search },
    replace,
  } = useHistory()

  const { id: initiallyTargetedId, tabKey: initiallyTargetedTabKey } =
    getIdAndTabKeyFromHash(hash)

  const buildHandleChangeHash = useCallback(
    (id: number) =>
      (tabKey: string): void => {
        const newHash = `#/${id}/${tabKey}`

        replace({
          search,
          pathname,
          hash: newHash,
        })
      },
    [pathname, search]
  )

  useEffect(() => {
    const isSkipped = !initiallyTargetedId || isFirstRender

    if (isSkipped) {
      return
    }

    const blockId = BLOCK_ID_PREFIX + initiallyTargetedId

    const block = document.getElementById(blockId)

    if (!block) {
      return
    }

    block.scrollIntoView()

    const currentScroll = window.scrollY

    if (currentScroll) {
      const header = document.getElementsByTagName("header")?.[0]

      window.scroll(0, currentScroll - header?.offsetHeight || 0)
    }
  }, [isFirstRender])

  return {
    initiallyTargetedId,
    initiallyTargetedTabKey,
    buildHandleChangeHash,
  }
}
