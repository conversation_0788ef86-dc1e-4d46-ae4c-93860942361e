import { useDispatch, useSelector } from "react-redux"

import userSettingsActions from "actions/userSettingsActions"

import { alertsFromUserSettingsSelector } from "selectors/userSettingSelectors"

import { alertStorageBuilder } from "utils/alertStorageBuilder"

import { ALERT_KEYS_FROM_USER_SETTINGS } from "consts"

import { UseSetUserSettingsTypes } from "./useSetUserSettingsTypes"

const { getUserSettings } = userSettingsActions

export const useSetAlertToUserSettings: UseSetUserSettingsTypes = () => {
  const alertsFromUserSettings = useSelector(alertsFromUserSettingsSelector)

  const dispatch = useDispatch()

  return ({ alertKey, data, shouldReplaceData = false, successCallback }) => {
    dispatch(
      getUserSettings(
        {
          key: ALERT_KEYS_FROM_USER_SETTINGS.ALERTS_MAIN_KEY,
          defaultSettings: {},
        },
        (responseData: any): void => {
          const {
            structuredData,
            userSettingsDefaultData,
            userSettingSaveAction,
          } = alertStorageBuilder({
            alertKey,
            data,
            alertsFromUserSettings,
            responseData,
            shouldReplaceData,
          })

          dispatch(
            userSettingSaveAction(
              {
                ...userSettingsDefaultData,
                settings: structuredData,
              },
              successCallback,
              null
            )
          )
        }
      )
    )
  }
}
