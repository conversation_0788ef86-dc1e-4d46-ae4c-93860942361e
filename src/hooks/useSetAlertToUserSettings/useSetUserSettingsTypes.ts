import { ALERT_KEYS_FROM_USER_SETTINGS } from "consts"

type AlertKeys = keyof typeof ALERT_KEYS_FROM_USER_SETTINGS
type AlertValues = typeof ALERT_KEYS_FROM_USER_SETTINGS[AlertKeys]

type UseSetUserSettingsParams = {
  alertKey: AlertValues
  data: any
  shouldReplaceData?: boolean
  successCallback?: (data: unknown) => void
}

export type UseSetUserSettingsTypes = () => (
  params: UseSetUserSettingsParams
) => void
