import { LostAmazonAccountTypes } from "components/Subscriptions/components/LostAndFound/LostAndFoundTypes"

export type UseLostAccountDataParams = {
  accountId: LostAmazonAccountTypes["id"]
}

export type LostFoundProductInformationDataWithManageSlPermission = {
  accountId: LostAmazonAccountTypes["id"]
}
export type LostFoundProductInformationDataWithoutManageSlPermission = {
  connectAccountId: LostAmazonAccountTypes["id"]
  enable: boolean
}

export type LostFoundProductInformationData =
  | LostFoundProductInformationDataWithManageSlPermission
  | LostFoundProductInformationDataWithoutManageSlPermission
