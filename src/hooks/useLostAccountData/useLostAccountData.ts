import { useDispatch, useSelector } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import customerActions from "actions/customerActions"
import fullServiceAction from "actions/fullServiceActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { lostAmazonAccountsSelector } from "selectors/amazonAccountsSelectors"
import {
  currentCustomersSelector,
  customerIdSelector,
  lostModuleSignedFullServiceSelector,
  lostModuleSignedSelector,
} from "selectors/customerSelectors"
import { staffUserIdSelector } from "selectors/staffSelectors"
import {
  amazonCustomerAccountsSelector,
  permissionsSelector,
} from "selectors/userSelectors"
import { modeSelector } from "selectors/viewModesSelector"

import { useStartFullServiceWizardCreate } from "hooks"

import { pushEvent } from "utils/gtmLoader"

import {
  CONNECT_ONLY_FULL_SERVICE_TYPES,
  FULL_SERVICE_CREDENTIAL_STATUS,
  LOST_SERVICE_TYPE,
} from "consts"
import { EVENTS_NAMES, PRODUCT_ORDER } from "consts/gtm"
import { PRODUCT_NAMES, PRODUCTS } from "consts/product"
import { VIEW_MODE } from "consts/viewMode"

import {
  LostFoundProductInformationData,
  LostFoundProductInformationDataWithManageSlPermission,
  LostFoundProductInformationDataWithoutManageSlPermission,
  UseLostAccountDataParams,
} from "./useLostAccountDataTypes"

const { getCurrentCustomer } = customerActions
const { fullServiceUpdateCredentialsStatus } = fullServiceAction
const { getAll: getAccounts } = amazonCustomerAccountsActions
const { toggleModal: changeStep } = moduleSetupWizardActions
const { displayModal, setUseLost } = amazonCustomerAccountsActions

export const useLostAccountData = ({ accountId }: UseLostAccountDataParams) => {
  const dispatch = useDispatch()
  const lostAmazonAccounts = useSelector(lostAmazonAccountsSelector)
  const lostModuleSignedFullService = useSelector(
    lostModuleSignedFullServiceSelector
  )

  const amazonCustomerAccounts = useSelector(amazonCustomerAccountsSelector)
  const lostModuleSigned = useSelector(lostModuleSignedSelector)
  const {
    allow_lost_full_service: allowLostFullService,
    lost_service_type: lostServiceType,
    hasPaymentData,
  } = useSelector(currentCustomersSelector)

  const { sellerLogicManager } = useSelector(permissionsSelector)
  const viewMode = useSelector(modeSelector)
  const userId = useSelector(staffUserIdSelector)
  const customerId = useSelector(customerIdSelector)

  const canManageSL: boolean =
    sellerLogicManager && viewMode === VIEW_MODE.admin
  const countOfAmazonAccounts: 0 | 1 = amazonCustomerAccounts.filter(
    ({ deleted }) => !deleted
  ).length
    ? 1
    : 0

  const {
    id,
    mwsAuthorization,
    sellerId,
    amazonAccountName,
    marketplaceId,
    accountZoneId,
    fsIsWizardFinished,
    lostActive,
    updatingLost,
  } = lostAmazonAccounts?.find((account) => account.id === accountId) || {}

  const activateAccountHandler = (): void => {
    dispatch(
      fullServiceUpdateCredentialsStatus({
        id,
        status: FULL_SERVICE_CREDENTIAL_STATUS.CORRECT,
        successCallback: () => {
          dispatch(
            getAccounts(false, () => {
              dispatch(getCurrentCustomer(false, undefined))
            })
          )
        },
      })
    )
  }

  const startFullServiceWizardCreateHandler = useStartFullServiceWizardCreate({
    lostModuleSignedFullService,
    marketplaceId,
    accountZoneId,
    name: amazonAccountName,
    mwsAuthorization,
    sellerId,
  })

  const isFullServiceActive: boolean =
    !!lostModuleSignedFullService &&
    !!allowLostFullService &&
    lostServiceType === LOST_SERVICE_TYPE.FULL

  const isWizardFinished: boolean =
    lostServiceType === LOST_SERVICE_TYPE.FULL && !!fsIsWizardFinished

  const isFullServiceActiveAccountWithoutFSEmail: boolean =
    isFullServiceActive && !fsIsWizardFinished && !lostActive

  const nextStep: string =
    countOfAmazonAccounts > 0
      ? "connectChoseAmazonAccount"
      : "connectChoseRegion"

  const nextWizardStepForLost: string = !hasPaymentData
    ? "billingInformation"
    : nextStep

  const handleSwitchProduct = (): void => {
    const lostFoundProductInformationDataWithManageSlPermission: LostFoundProductInformationDataWithManageSlPermission =
      {
        accountId: id,
      }

    const lostFoundProductInformationDataWithoutManageSlPermission: LostFoundProductInformationDataWithoutManageSlPermission =
      {
        connectAccountId: id,
        enable: true,
      }

    const lostFoundProductInformationData: LostFoundProductInformationData =
      canManageSL
        ? lostFoundProductInformationDataWithManageSlPermission
        : lostFoundProductInformationDataWithoutManageSlPermission

    const hasNotPaymentAndManageSlPermission: boolean =
      !hasPaymentData && !canManageSL

    const isLostFullServiceSetupWithoutContract: boolean =
      allowLostFullService && !lostModuleSignedFullService && !lostActive
    // Delete this  after FULL SERVICE MVP
    const isOldLostWithoutContract: boolean =
      !allowLostFullService && !lostModuleSigned && !lostActive

    if (!lostActive) {
      pushEvent({ ecommerce: null })

      pushEvent({
        event: EVENTS_NAMES.addToCart,
        customerId,
        userId,
        ecommerce: {
          items: [
            {
              item_id: PRODUCT_ORDER[PRODUCTS.lost],
              item_name: PRODUCT_NAMES[PRODUCTS.lost],
              index: 0,
              quantity: 1,
            },
          ],
        },
      })
    }
    if (isLostFullServiceSetupWithoutContract) {
      return dispatch(
        displayModal(
          true,
          "lostFoundProductInformation",
          lostFoundProductInformationData
        )
      )
    } else if (isOldLostWithoutContract) {
      return dispatch(
        displayModal(
          true,
          "lostFoundProductInformationMVP",
          lostFoundProductInformationData
        )
      )
    } else if (hasNotPaymentAndManageSlPermission) {
      return dispatch(
        changeStep(true, nextWizardStepForLost, PRODUCTS.lost, {
          selectedRegion: null,
          selectedMarketplaces: null,
          connectAccountId: id,
        })
      )
    }
    // Need add to full service logic after success active accounts
    dispatch(
      setUseLost(
        id,
        {
          value: lostActive ? 0 : 1,
        },
        () =>
          dispatch(
            getCurrentCustomer(false, () =>
              dispatch(
                getAccounts(false, () => {
                  if (isFullServiceActiveAccountWithoutFSEmail) {
                    dispatch(
                      changeStep(
                        true,
                        "createFullServiceAccount",
                        PRODUCTS.lost,
                        {
                          fullServiceSellerId: sellerId,
                          connectOnlyFullServiceType:
                            CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT,
                        }
                      )
                    )
                  }
                })
              )
            )
          )
      )
    )
  }

  return {
    isWizardFinished,
    fsIsWizardFinished,
    startFullServiceWizardCreateHandler,
    updatingLost,
    lostActive,
    activateAccountHandler,
    handleSwitchProduct,
  }
}
