import { useSelector } from "react-redux"

import { languageSelector } from "selectors/translationsSelectors"

import { replaceLanguageCode } from "utils/replaceLanguageCode"

export const useRedirectToContactPage = () => {
  const language = useSelector(languageSelector)

  const contactUsUrl = replaceLanguageCode({
    url: "https://www.sellerlogic.com/{languageCode}/contact-us/",
    language,
  })

  const redirectToContactPage = () => {
    window.open(contactUsUrl, "__blank")
  }

  return {
    redirectToContactPage,
    contactUsUrl,
  }
}
