import { useDispatch } from "react-redux"

import amazonCustomerAccountsActions from "actions/amazonCustomerAccountsActions"
import moduleSetupWizardActions from "actions/moduleSetupWizardActions"

import { CONNECT_ONLY_FULL_SERVICE_TYPES } from "consts"
import { PRODUCTS } from "consts/product"

import { UseStartFullServiceWizardCreateTypes } from "./useStartFullServiceWizardCreateTypes"

const { toggleModal: changeStep } = moduleSetupWizardActions

const { displayAmazonTokenRenewModal, displayModal } =
  amazonCustomerAccountsActions

export const useStartFullServiceWizardCreate: UseStartFullServiceWizardCreateTypes =
  ({
    sellerId,
    lostModuleSignedFullService,
    marketplaceId,
    accountZoneId,
    name,
    mwsAuthorization,
  }) => {
    const dispatch = useDispatch()

    return () => {
      // Do not change the order of placement

      // first check if full service contract signed
      if (!lostModuleSignedFullService) {
        dispatch(
          displayModal(true, "lostFoundProductInformation", {
            accounts: [
              {
                sellerId,
                amazonMarketplaceId: marketplaceId,
                amazonZoneId: accountZoneId,
                title: name,
              },
            ],
            fullServiceSellerId: sellerId,
            isFullServiceOnlySetup: true,
            connectOnlyFullServiceType:
              CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT,
            mwsAuthorization,
          })
        )

        return
      }

      // second check is Amazon API authorization status
      if (!mwsAuthorization) {
        dispatch(
          displayAmazonTokenRenewModal({
            visible: true,
            accounts: [
              {
                sellerId,
                amazonMarketplaceId: marketplaceId,
                amazonZoneId: accountZoneId,
                title: name,
                showIgnoreButton: false,
              },
            ],
            closable: true,
            maskClosable: false,
            isFullServiceOnlySetup: true,
            connectOnlyFullServiceType:
              CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT,
          })
        )

        return
      }

      // default step is short lost full service wizard
      dispatch(
        changeStep(true, "createFullServiceAccount", PRODUCTS.lost, {
          fullServiceSellerId: sellerId,
          connectOnlyFullServiceType:
            CONNECT_ONLY_FULL_SERVICE_TYPES.ONLY_ONE_ACCOUNT,
        })
      )
    }
  }
