import moment from "moment"

import { GetDateRangeFromString } from "./GetDateRangeFromStringTypes"

export const getDateRangeFromString: GetDateRangeFromString = ({
  dateRange = "",
  defaultDates,
}) => {
  if (!dateRange) {
    return defaultDates
  }

  const [from = "", to = ""] = dateRange?.split(" - ") || []

  const fromDate = moment(from)
  const toDate = moment(to)

  return [fromDate, toDate]
}
