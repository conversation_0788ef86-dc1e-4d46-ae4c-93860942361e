const LOCALE_CODE_PLACEHOLDER = "{localeCode}"

const LANGUAGE_CODE_PLACEHOLDER = "{languageCode}"

const LANGUAGES_TO_LOCALE_CODES_MAP = {
  en: "en_US",
  de: "de_DE",
  fr: "fr_FR",
  es: "es_ES",
} as const

const LANGUAGES_TO_LANGUAGE_CODES_MAP = {
  en: "en",
  de: "de",
  fr: "fr",
  es: "es",
} as const

export const replaceLanguageCode = ({
  url,
  language,
}: {
  url: string
  language: "en" | "de" | "fr" | "es"
}) => {
  if (!url) {
    return url
  }

  const hasLanguageOrLocaleCode =
    url.includes(LOCALE_CODE_PLACEHOLDER) ||
    url.includes(LANGUAGE_CODE_PLACEHOLDER)

  if (!hasLanguageOrLocaleCode) {
    return url
  }

  const languageCode =
    LANGUAGES_TO_LANGUAGE_CODES_MAP[language] ||
    LANGUAGES_TO_LANGUAGE_CODES_MAP.en

  const localeCode =
    LANGUAGES_TO_LOCALE_CODES_MAP[language] || LANGUAGES_TO_LOCALE_CODES_MAP.en

  return url
    .replace(LANGUAGE_CODE_PLACEHOLDER, languageCode)
    .replace(LOCALE_CODE_PLACEHOLDER, localeCode)
}
