export const setSameTextBorders = (str) => {
  const strArr = str.split("<br />")
  let borders = []
  let newLine = ""
  strArr.forEach((bit) => {
    if (
      bit.indexOf("============") > -1 ||
      bit.indexOf("------------") > -1 ||
      bit.indexOf("____________") > -1
    ) {
      const border = borders.find((b) => b && bit[10] && b[10] === bit[10])
      if (!border) borders.push(bit)

      newLine += `<span class="text-border">${border || bit}</span><br />`
    } else newLine += `${bit}<br />`
  })
  return newLine
}
