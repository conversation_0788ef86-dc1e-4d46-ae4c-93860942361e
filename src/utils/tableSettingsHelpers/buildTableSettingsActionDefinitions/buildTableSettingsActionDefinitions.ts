import api from "api"

import { languageSelector } from "selectors/translationsSelectors"

import { defaultSettings } from "consts/tableSettingsNew"

import { buildUpdateRequestPayload } from "../buildUpdateRequestPayload"
import { getTableSettingsDefaultKey } from "../getTableSettingsDefaultKey"

import {
  TableSettings,
  TableSettingsActionDefinitions,
} from "types/TableSettings"

const {
  tableSettingsService: { get, save, update, saveDefault, updateDefault },
} = api

export const buildTableSettingsActionDefinitions = ({
  storeKey,
  tableSettingsKey,
}): TableSettingsActionDefinitions => {
  return {
    getTableSettings: (mainArguments = {}, dispatch, getState) => {
      const { successCallback = () => {}, failureCallback = () => {} } =
        mainArguments

      return {
        callApi: async () => {
          let result

          // Step 1: Get user settings.
          const userDefinedTableSettings = (
            await get(tableSettingsKey)
          ).data.find((settingItem) => settingItem.key === tableSettingsKey)

          let userDefinedTableSettingsValue: TableSettings | undefined

          try {
            userDefinedTableSettingsValue = userDefinedTableSettings?.value
              ? JSON.parse(userDefinedTableSettings.value)
              : null
          } catch (e) {}

          // Typescript throws error if a constant is created for this condition
          if (
            !!userDefinedTableSettingsValue?.settings &&
            userDefinedTableSettings.value.includes("width")
          ) {
            result = {
              id: userDefinedTableSettings?.id,
              key: tableSettingsKey,
              settings: userDefinedTableSettingsValue.settings,
              pageSize: userDefinedTableSettingsValue.pageSize,
            }
          }

          // Step 2: If there are no user defined settings, get default settings.
          if (!result) {
            const state = getState()
            const language = languageSelector(state)
            const defaultKey = getTableSettingsDefaultKey({
              language,
              tableSettingsKey,
            })

            const defaultTableSettings = (await get(defaultKey, 1)).data.find(
              (settingItem) => settingItem.key === defaultKey
            )

            let defaultTableSettingsValue: TableSettings | undefined

            try {
              defaultTableSettingsValue = defaultTableSettings?.value
                ? JSON.parse(defaultTableSettings.value)
                : null
            } catch (e) {}

            // Typescript throws error if a constant is created for this condition
            if (!!defaultTableSettingsValue?.settings) {
              result = {
                id: userDefinedTableSettings?.id || null,
                key: tableSettingsKey,
                settings: defaultTableSettingsValue.settings,
                pageSize: defaultTableSettingsValue.pageSize,
              }
            }
          }

          // Step 3: If there are no default settings, get old common settings from initialValues.
          if (!result) {
            result = {
              id: userDefinedTableSettings?.id || null,
              key: tableSettingsKey,
              settings: defaultSettings[tableSettingsKey] || [],
              // pageSize: oldTableSettingsValue.pageSize,
            }
          }

          return result
        },
        successCallback,
        failureCallback,
      }
    },
    updateTableSettings: (mainArguments, dispatch, getState) => {
      const {
        // TODO: fix when ActionAsyncDefinitionType is updated
        //@ts-expect-error
        payload,
        successCallback = () => {},
        failureCallback = () => {},
      } = mainArguments

      return {
        callApi: async () => {
          const requestPayload = buildUpdateRequestPayload({
            payload,
            getState,
            storeKey,
          })

          const userDefinedTableSettings = (
            await get(tableSettingsKey)
          ).data.find((settingItem) => settingItem.key === tableSettingsKey)

          let updatedUserDefinedTableSettings

          const canUpdateTableSetting: boolean =
            !!userDefinedTableSettings && !!userDefinedTableSettings.id

          if (canUpdateTableSetting) {
            updatedUserDefinedTableSettings = (
              await update(userDefinedTableSettings.id, requestPayload)
            ).data
          } else {
            updatedUserDefinedTableSettings = (
              await save(tableSettingsKey, requestPayload)
            ).data
          }

          const updatedUserDefinedTableSettingsValue =
            updatedUserDefinedTableSettings?.value
              ? JSON.parse(updatedUserDefinedTableSettings.value)
              : null

          return {
            id: userDefinedTableSettings?.id,
            key: tableSettingsKey,
            settings: updatedUserDefinedTableSettingsValue.settings,
            pageSize: updatedUserDefinedTableSettingsValue.pageSize,
          }
        },
        successCallback,
        failureCallback,
      }
    },
    updateDefaultTableSettings: (mainArguments, dispatch, getState) => {
      const {
        // TODO: fix when ActionAsyncDefinitionType is updated
        //@ts-expect-error
        payload,
        successCallback = () => {},
        failureCallback = () => {},
      } = mainArguments

      return {
        callApi: async () => {
          const requestPayload = buildUpdateRequestPayload({
            payload,
            getState,
            storeKey,
          })

          const state = getState()
          const language = languageSelector(state)
          const defaultKey = getTableSettingsDefaultKey({
            language,
            tableSettingsKey,
          })

          let [defaultSetting] = (await get(defaultKey, 1)).data

          let updatedDefaultTableSettings
          const canUpdateDefaultTableSetting =
            !!defaultSetting && !!defaultSetting.id

          if (canUpdateDefaultTableSetting) {
            updatedDefaultTableSettings = (
              await updateDefault(defaultSetting.id, defaultKey, requestPayload)
            ).data
          } else {
            updatedDefaultTableSettings = (
              await saveDefault(defaultKey, requestPayload)
            ).data
          }

          const updatedDefaultTableSettingsValue =
            updatedDefaultTableSettings?.value
              ? JSON.parse(updatedDefaultTableSettings.value)
              : null

          return {
            id: updatedDefaultTableSettings?.id,
            key: tableSettingsKey,
            settings: updatedDefaultTableSettingsValue.settings,
            pageSize: updatedDefaultTableSettingsValue.pageSize,
          }
        },
        successCallback,
        failureCallback,
      }
    },
  }
}
