import englishLocale from "antd/es/date-picker/locale/en_GB"
import germanLocale from "antd/es/date-picker/locale/de_DE"
import frenchLocale from "antd/es/date-picker/locale/fr_FR"
import spanishLocale from "antd/es/date-picker/locale/es_ES"

import { userLocaleKey } from "consts/storage"

import { getItem } from "./storage"

export const currentUserLocale =
  getItem(userLocaleKey) || window.navigator.language

export const locales = {
  en: englishLocale,
  de: germanLocale,
  fr: frenchLocale,
  es: spanishLocale,
}

export const getSimpleLocale = (locale) => {
  return (locale || "").replace(/\s/g, "").split("-")[0].split("_")[0]
}

export const getDatePickerLocale = (locale) => {
  return locale && locales[locale] ? locales[locale] : locales.de
}

export const loadMomentLocale = async (locale) => {
  locale = getSimpleLocale(locale)
  const loadLocale = async () => {
    switch (locale) {
      case "de": {
        await import("moment/locale/de")
        break
      }
      case "fr": {
        await import("moment/locale/fr")
        break
      }
      case "es": {
        await import("moment/locale/es")
        break
      }
      default:
        await import("moment/locale/en-gb")
    }
  }
  loadLocale().catch((e) => console.log(e))
}
