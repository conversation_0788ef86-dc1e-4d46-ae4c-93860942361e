// This file will be copied from https://github.com/Azure/fetch-event-source with some changes for correct refresh token behavior and reconnection events

import { getItem } from "utils/storage"

import { getBytes, getLines, getMessages } from "./parse"

import { FetchEventSourceInit } from "./fetchEventSourceTypes"

export const EventStreamContentType = "text/event-stream"

const DefaultRetryInterval = 5000
const LastEventId = "last-event-id"

export function fetchEventSource(
  input: RequestInfo,
  {
    signal: inputSignal,
    headers: inputHeaders,
    onopen: inputOnOpen,
    onmessage,
    onclose,
    onerror,
    openWhenHidden,
    fetch: inputFetch,
    retryTimes = 2,
    ...rest
  }: FetchEventSourceInit
) {
  let retryCounter = 0

  return new Promise<void>((resolve, reject) => {
    // make a copy of the input headers since we may modify it below:
    const headers = { ...inputHeaders }

    if (!headers.accept) {
      headers.accept = EventStreamContentType
    }

    let curRequestController: AbortController

    function onVisibilityChange() {
      curRequestController.abort() // close existing request on every visibility change

      if (!document.hidden) {
        create() // page is now visible again, recreate request.
      }
    }

    if (!openWhenHidden) {
      document.addEventListener("visibilitychange", onVisibilityChange)
    }

    let retryInterval = DefaultRetryInterval
    let retryTimer = 0

    function dispose() {
      document.removeEventListener("visibilitychange", onVisibilityChange)
      window.clearTimeout(retryTimer)
      curRequestController.abort()
    }

    // if the incoming signal aborts, dispose resources and resolve:
    inputSignal?.addEventListener("abort", () => {
      dispose()
      resolve() // don't waste time constructing/logging errors
    })

    const fetch = inputFetch ?? window.fetch
    const onopen = inputOnOpen ?? defaultOnOpen

    let currentAccessToken: string | null = null

    async function create(accessToken?: string | null) {
      curRequestController = new AbortController()
      try {
        const response = await fetch(input, {
          ...rest,
          headers: {
            "Content-Type": "text/event-stream",
            ...headers,
            Authorization: `Bearer ${accessToken ?? getItem("authToken")}`,
          },
          signal: curRequestController.signal,
        })

        const shouldRefreshToken = response.status === 401

        const onOpenResponse = await onopen(response)

        if (!response.ok && shouldRefreshToken) {
          if (onOpenResponse) {
            currentAccessToken = onOpenResponse?.token ?? null
          }
        }

        await getBytes(
          response.body!,
          getLines(
            getMessages(
              (id) => {
                if (id) {
                  // store the id and send it back on the next retry:
                  headers[LastEventId] = id
                } else {
                  // don't send the last-event-id header anymore:
                  delete headers[LastEventId]
                }
              },
              (retry) => {
                retryInterval = retry
              },
              onmessage
            )
          )
        )

        onclose?.()
        dispose()
        resolve()
      } catch (err) {
        if (retryCounter++ >= retryTimes) {
          dispose()

          retryCounter = 0

          throw new Error("Failed to open event source. Too many retries.")
        }

        if (!curRequestController.signal.aborted) {
          // if we haven't aborted the request ourselves:
          try {
            // check if we need to retry:
            const interval: any = onerror?.(err) ?? retryInterval

            window.clearTimeout(retryTimer)
            retryTimer = window.setTimeout(
              () => create(currentAccessToken),
              interval
            )
          } catch (innerErr) {
            // we should not retry anymore:

            dispose()
            reject(innerErr)
          }
        }
      }
    }

    create()
  })
}

function defaultOnOpen(response: Response) {
  const contentType = response.headers.get("content-type")

  if (!contentType?.startsWith(EventStreamContentType)) {
    throw new Error(
      `Expected content-type to be ${EventStreamContentType}, Actual: ${contentType}`
    )
  }
}
