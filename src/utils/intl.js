import { createIntl, createIntlCache } from "react-intl"

import { store } from "App"
import missedTranslationsActions from "actions/missedTranslationsActions"
import config from "config"

const { add: addMissedTranslation } = missedTranslationsActions
const { disableMissedTranslations } = config

const cache = createIntlCache()
const foundMessages = []

let intl = createIntl(
  {
    locale: "en",
    messages: {},
  },
  cache
)

export const updateIntl = (locale, messages) => {
  intl = createIntl(
    {
      locale: locale === "cn" ? "zh" : locale,
      messages,
    },
    cache
  )
}

export const getIntl = () => intl

export default (id, values) => {
  if (!disableMissedTranslations && !!id) {
    new Promise((resolve) => {
      if (!intl.messages[id] && foundMessages.indexOf(id) === -1) {
        foundMessages.push(id)
        store.dispatch(addMissedTranslation(id))
      }
      resolve()
    })
  }
  return !!id ? intl.formatMessage({ id, defaultMessage: id }, values) : id
}

export const availableLanguages = {
  1: "English",
  3: "Deutsch",
  4: "Français",
  5: "中文 (中国)",
  6: "Español",
}

export const avaiableStatuses = {
  0: "New",
  1: "Sent",
  2: "In Progress",
  "-1": "Error",
}
