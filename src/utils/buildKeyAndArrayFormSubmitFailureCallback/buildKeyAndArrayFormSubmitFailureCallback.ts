import { getObjectEntries, getObjectKeys } from "@develop/fe-library/dist/utils"

import { checkIsArray } from "utils/arrayHelpers"

import { BuildKeyAndArrayFormSubmitFailureCallback } from "./buildKeyAndArrayFormSubmitFailureCallbackTypes"

export const buildKeyAndArrayFormSubmitFailureCallback: BuildKeyAndArrayFormSubmitFailureCallback =
  ({
    setError,
    options = {
      shouldFocus: true,
    },
  }) => {
    return (errors) => {
      const errorObject = errors?.errors

      if (!checkIsArray(getObjectKeys(errorObject))) {
        return
      }

      getObjectEntries(errorObject).forEach(([key, messages]): void => {
        const message = messages.join(" \n ")

        setError(
          key,
          {
            type: "manual",
            message,
          },
          options
        )
      })
    }
  }
