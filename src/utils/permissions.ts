import { permissionKeys } from "consts/permissionKeys"
import { PAGE_PERMISSIONS } from "consts/permissions"

type IsPageAccessByPermissionsProps = {
  pathname: string
  permissions: typeof permissionKeys
}

type PermissionsKeysType = keyof typeof permissionKeys
type PagePermissionsType = keyof typeof PAGE_PERMISSIONS

/**
 * Checks if a page is blocked by permissions.
 * @param {string} pathname - The pathname of the page.
 * @param {Object} permissions - The permissions object.
 * @returns {boolean} - Returns true if the page is blocked, false otherwise.
 */
export const checkIsPageBlockedByPermissions = ({
  pathname,
  permissions,
}: IsPageAccessByPermissionsProps): boolean => {
  const currentPermission =
    PAGE_PERMISSIONS?.[pathname as PagePermissionsType] || null

  // If the current permission doesn't exist, the page is not blocked
  if (!currentPermission) {
    return false
  }

  // Check if the user has the required permission
  const hasPermission =
    !!permissions?.[currentPermission as PermissionsKeysType]

  return !hasPermission
}
