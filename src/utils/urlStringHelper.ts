/**
 * Returns first part of URL
 * /one/two/ => /one
 * @param url {String}
 * @returns {String}
 */
export const getURLFirstPart = (url: string): string => {
  return url?.replace(/(\/[^/]*).*/, "$1")
}

/**
 * Checks if pathname starts with prefix and has ? or / symbols
 * or the end of the string at the end of the prefix
 * @param prefix {String}
 * @param path {String} window.location.pathname if not provided
 * @returns {Boolean}
 */
export const checkIsPathStartsWith = (
  prefix: string,
  path = window.location.pathname
): boolean => {
  const regex = new RegExp(`^${prefix}([/?]|$)`)

  return regex.test(path)
}
