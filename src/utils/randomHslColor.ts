/**
 * Generates an array of randomly generated HSL colors
 *
 * @param {number} amount - The number of colors to generate.
 * @param {number} [saturation=50] - The saturation percentage for the colors, ranging from 0 to 100
 * @param {number} [lightness=60] - The lightness percentage for the colors, ranging from 0 to 100
 * @param {number} [alpha=1] - The alpha value for the colors, ranging from 0 to 1
 * @return {string[]} An array of HSL colors in string format.
 *
 * @example
 * randomHslColors(5)
 * returns ['hsla(0,50%,60%,1)', 'hsla(72,50%,60%,1)', 'hsla(144,50%,60%,1)', 'hsla(216,50%,60%,1)', 'hsla(288,50%,60%,1)']
 */
export const randomHslColors = (
  amount: number,
  saturation: number = 50,
  lightness: number = 60,
  alpha: number = 1
): string[] => {
  const colors = []
  const delta = Math.trunc(360 / amount)

  for (let i = 0; i < amount; i++) {
    const hue = i * delta

    colors.push(`hsla(${hue},${saturation}%,${lightness}%,${alpha})`)
  }

  return colors
}
