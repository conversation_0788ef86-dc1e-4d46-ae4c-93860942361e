import { getObjectKeys, getObjectValues } from "@develop/fe-library/dist/utils"

import l from "utils/intl"

import {
  CRON_KEYS,
  CRON_TABS_KEYS,
  DAYS_1_TO_31,
  DAYS_ARRAY,
  DAYS_OBJ,
  HOURS_0_TO_23,
  MINUTES_0_TO_55_STEP_5,
  MONTH_ARRAY,
  MONTH_OBJ,
} from "consts/cron"

export const parseNumberToOption = (number) => ({
  value: parseInt(number),
  label: number,
})

export const minutesAllItems = MINUTES_0_TO_55_STEP_5.map(parseNumberToOption)

export const hoursAllValues = HOURS_0_TO_23.map(parseNumberToOption)

export const daysAllValues = DAYS_1_TO_31.map(parseNumberToOption)

export const daysOfTheWeekAllValues = DAYS_ARRAY.map(([value]) => ({
  value,
  label: l(value),
}))

export const monthAllValues = MONTH_ARRAY.map(([value]) => ({
  value,
  label: l(value),
}))

export const cronMapper = {
  minutes: {
    index: 0,
    getStartingTimeWithPeriodCron: ({ startAt, period }) => {
      return `${startAt}-59/${period}`
    },
  },
  hours: {
    index: 1,
    everyInstanceOfTimeCron: [1, "*"],
    getStartingTimeWithPeriodCron: ({ startAt, period }) => {
      return `${startAt}-23/${period}`
    },
    getBetweenTwoInstancesOfTimeCron: ({ start, finish }) => {
      if (start > finish) {
        return `${start}-23,0-${finish}`
      }

      return `${start}-${finish}`
    },
  },
  days: {
    index: 2,
    secondIndex: 4,
    secondValue: "?",
    everyInstanceOfTimeCron: [2, "?", 4, "*"],
    getStartingTimeWithPeriodCron: ({ startAt, period }) => {
      return `${startAt}-31/${period}`
    },
  },
  daysOfTheWeek: {
    index: 4,
    secondIndex: 2,
    secondValue: "?",
    getStartingTimeWithPeriodCron: ({ startAt, period }) => {
      return `${startAt}-7/${period}`
    },
  },
  months: {
    index: 3,
    everyInstanceOfTimeCron: [3, "*"],
    getStartingTimeWithPeriodCron: ({ startAt, period }) => {
      return `${startAt}-12/${period}`
    },
    getBetweenTwoInstancesOfTimeCron: ({ start, finish }) => {
      if (start > finish) {
        return `${start}-12,1-${finish}`
      }

      return `${start}-${finish}`
    },
  },
}

export const getNewCronValue = (
  currentCron,
  index,
  newValue,
  secondIndex,
  secondValue
) => {
  const cronArray = currentCron.split(" ")

  cronArray[index] = newValue
  if (secondIndex) {
    cronArray[secondIndex] = secondValue
  }

  return cronArray.join(" ")
}

export const getSerialNumber = (number) => {
  const intNumber = parseInt(number)
  let ending = l("th")

  if (intNumber === 1) ending = l("st")
  if (intNumber === 2) ending = l("nd")
  if (intNumber === 3) ending = l("rd")

  return `${number}${ending}`
}

const getSorted = (cron) => {
  const arr = cron.split(",").map((str) => parseInt(str, 10))

  return arr.sort((a, b) => a - b).join(", ")
}

const atTimeRegex = new RegExp(/^\d+(,\d+)*$/)
const atTimeLettersRegex = new RegExp(/^[A-Z]+(,[A-Z]+)*$/)
const everySpecificRegex = new RegExp(/^\d+-\d+\/\d+/)
const everyBetweenRegex = new RegExp(/^\d+-\d+$/)
const everyBetweenWithOverlapRegex = new RegExp(/^\d+-\d+,\d+-\d+/)
const lastOfTheMonthRegex = new RegExp(/^\d+L/)
const everyDayOfTheWeekRegex = new RegExp(/^\d+#\d+/)
const beforeEndOfTheMonthRegex = new RegExp(/^L-\d+/)
const nearestWeekdayOfTheMonthRegex = new RegExp(/^\d+W/)

export const getSummaryMessage = (cronPart, type, additionalPart) => {
  if (type === "day") {
    if (cronPart === "?") {
      if (additionalPart === "*") return l("every day")
      if (everySpecificRegex.test(additionalPart)) {
        return l("every {period} days starting on {startAt}", {
          period: additionalPart.split("/")[1],
          startAt: l(DAYS_ARRAY[additionalPart.split("/")[0].split("-")[0]][1]),
        })
      }
      if (atTimeLettersRegex.test(additionalPart)) {
        return l("on {days}", {
          days: additionalPart
            .split(",")
            .map((key) => l(DAYS_OBJ[key]))
            .join(", "),
        })
      }
      if (everyDayOfTheWeekRegex.test(additionalPart)) {
        return (
          "on the {day} {weekday} of the month",
          {
            day: additionalPart.split("#")[1],
            weekday: l(DAYS_ARRAY[additionalPart.split("#")[0] - 1][1]),
          }
        )
      }
    }

    if (additionalPart === "?") {
      if (atTimeRegex.test(cronPart)) {
        return l("on {numbers} day of the month", {
          numbers: getSorted(cronPart),
        })
      }
      if (everySpecificRegex.test(cronPart)) {
        return l("every {period} days starting on the {startAt} of the month", {
          period: cronPart.split("/")[1],
          startAt: cronPart.split("/")[0].split("-")[0],
        })
      }
    }
  }

  if (cronPart === "*") {
    switch (type) {
      case "minute":
        return l("every minute")
      case "hour":
        return l("every hour")
      case "month":
        return l("every month")
      default:
        return ""
    }
  }

  if (atTimeRegex.test(cronPart) || atTimeLettersRegex.test(cronPart)) {
    switch (type) {
      case "minute":
        return l("at minute {numbers}", { numbers: getSorted(cronPart) })
      case "hour":
        return l("at hour {numbers}", { numbers: getSorted(cronPart) })
      case "month":
        return l("on {months}", {
          months: cronPart
            .split(",")
            .map((key) => l(MONTH_OBJ[key]))
            .join(", "),
        })
      default:
        return ""
    }
  }
  if (everySpecificRegex.test(cronPart)) {
    switch (type) {
      case "minute":
        return l("every {period} minutes starting at minute {startAt}", {
          period: cronPart.split("/")[1],
          startAt: cronPart.split("/")[0].split("-")[0],
        })
      case "hour":
        return l("every {period} hours starting at hour {startAt}", {
          period: cronPart.split("/")[1],
          startAt: cronPart.split("/")[0].split("-")[0],
        })
      case "month":
        return l("every {period} months starting in {startAt}", {
          period: cronPart.split("/")[1],
          startAt: l(
            getObjectValues(MONTH_OBJ)[cronPart.split("/")[0].split("-")[0] - 1]
          ),
        })
      default:
        return ""
    }
  }

  if (everyBetweenRegex.test(cronPart)) {
    switch (type) {
      case "minute":
        return l("every minute between minute {start} and minute {finish}", {
          start: cronPart.split("-")[0],
          finish: cronPart.split("-")[1],
        })
      case "hour":
        return l("every hour between hour {start} and hour {finish}", {
          start: cronPart.split("-")[0],
          finish: cronPart.split("-")[1],
        })
      case "month":
        return l("every month between {start} and {finish}", {
          start: l(getObjectValues(MONTH_OBJ)[cronPart.split("-")[0] - 1]),
          finish: l(getObjectValues(MONTH_OBJ)[cronPart.split("-")[1] - 1]),
        })
      default:
        return ""
    }
  }

  if (everyBetweenWithOverlapRegex.test(cronPart)) {
    switch (type) {
      case "minute":
        return l("every minute between minute {start} and minute {finish}", {
          start: cronPart.split(",")[0].split("-")[0],
          finish: cronPart.split(",")[1].split("-")[1],
        })
      case "hour":
        return l("every hour between hour {start} and hour {finish}", {
          start: cronPart.split(",")[0].split("-")[0],
          finish: cronPart.split(",")[1].split("-")[1],
        })
      case "month":
        return l("every month between {start} and {finish}", {
          start: l(
            getObjectValues(MONTH_OBJ)[cronPart.split(",")[0].split("-")[0] - 1]
          ),
          finish: l(
            getObjectValues(MONTH_OBJ)[cronPart.split(",")[1].split("-")[1] - 1]
          ),
        })
      default:
        return ""
    }
  }
}

export const getMessage = ({
  cronExpression,
  enabledTabs = getObjectKeys(CRON_TABS_KEYS),
}) => {
  const [minuteCron, hoursCron, dayOfMonthCron, monthCron, dayOfWeekCron] =
    cronExpression.split(" ")

  let minutesSummary = ""
  let hoursSummary = ""

  if (minuteCron !== "0") {
    minutesSummary = getSummaryMessage(minuteCron, "minute") + ","
  }

  if (hoursCron !== "0") {
    hoursSummary = getSummaryMessage(hoursCron, "hour") + ","
  }

  const daySummary =
    getSummaryMessage(dayOfMonthCron, "day", dayOfWeekCron) + ","

  const monthSummary = getSummaryMessage(monthCron, "month")

  const summaryMap = {
    minutes: minutesSummary,
    hours: hoursSummary,
    days: daySummary,
    months: monthSummary,
  }

  const summary = getObjectKeys(summaryMap).map((key) => {
    const summaryValue =
      summaryMap[key] === undefined ? "undefined" : summaryMap[key]

    return enabledTabs.includes(key) ? summaryValue : ""
  })

  return `${summary.join(" ")}`
}

const getUpdatedMinutesSettings = (cronPart) => {
  if (atTimeRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.minutes.specificMinute,
      [CRON_KEYS.minutes.specificMinute]: cronPart
        .split(",")
        .map((item) => parseInt(item)),
    }
  }
  if (everySpecificRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.minutes.everySpecificMinute,
      [CRON_KEYS.minutes.everySpecificMinute]: {
        period: parseInt(cronPart.split("/")[1]),
        startAt: parseInt(cronPart.split("/")[0].split("-")[0]),
      },
    }
  }

  return {
    selectedOption: CRON_KEYS.minutes.specificMinute,
  }
}

const getUpdatedHoursSettings = (cronPart) => {
  if (atTimeRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.hours.specificHour,
      [CRON_KEYS.hours.specificHour]: cronPart
        .split(",")
        .map((item) => parseInt(item)),
    }
  }
  if (everySpecificRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.hours.everySpecificHour,
      [CRON_KEYS.hours.everySpecificHour]: {
        period: parseInt(cronPart.split("/")[1]),
        startAt: parseInt(cronPart.split("/")[0].split("-")[0]),
      },
    }
  }

  if (everyBetweenRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.hours.everyHourBetween,
      [CRON_KEYS.hours.everyHourBetween]: {
        start: parseInt(cronPart.split("-")[0]),
        finish: parseInt(cronPart.split("-")[1]),
      },
    }
  }

  if (everyBetweenWithOverlapRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.hours.everyHourBetween,
      [CRON_KEYS.hours.everyHourBetween]: {
        start: parseInt(cronPart.split(",")[0].split("-")[0]),
        finish: parseInt(cronPart.split(",")[1].split("-")[1]),
      },
    }
  }

  return {
    selectedOption: CRON_KEYS.hours.everyHour,
  }
}

const getUpdatedMonthSettings = (cronPart) => {
  if (atTimeLettersRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.months.specificMonth,
      [CRON_KEYS.months.specificMonth]: cronPart.split(","),
    }
  }
  if (everySpecificRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.months.everySpecificMonth,
      [CRON_KEYS.months.everySpecificMonth]: {
        period: parseInt(cronPart.split("/")[1]),
        startAt: parseInt(cronPart.split("/")[0].split("-")[0]),
      },
    }
  }

  if (everyBetweenRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.months.everyMonthBetween,
      [CRON_KEYS.months.everyMonthBetween]: {
        start: parseInt(cronPart.split("-")[0]),
        finish: parseInt(cronPart.split("-")[1]),
      },
    }
  }

  if (everyBetweenWithOverlapRegex.test(cronPart)) {
    return {
      selectedOption: CRON_KEYS.months.everyMonthBetween,
      [CRON_KEYS.months.everyMonthBetween]: {
        start: parseInt(cronPart.split(",")[0].split("-")[0]),
        finish: parseInt(cronPart.split(",")[1].split("-")[1]),
      },
    }
  }

  return {
    selectedOption: CRON_KEYS.months.everyMonth,
  }
}

const getUpdatedDaySettings = (dayOfMonthCron, dayOfWeekCron) => {
  if (dayOfMonthCron === "?") {
    if (atTimeLettersRegex.test(dayOfWeekCron)) {
      return {
        selectedOption: CRON_KEYS.days.specificDayOfTheMonth,
        [CRON_KEYS.days.specificDayOfTheMonth]: dayOfWeekCron.split(","),
      }
    }
    if (everySpecificRegex.test(dayOfWeekCron)) {
      return {
        selectedOption: CRON_KEYS.days.everySpecificDayOfTheMonth,
        [CRON_KEYS.days.everySpecificDayOfTheMonth]: {
          period: parseInt(dayOfWeekCron.split("/")[1]),
          startAt: parseInt(dayOfWeekCron.split("/")[0].split("-")[0]),
        },
      }
    }
    if (lastOfTheMonthRegex.test(dayOfWeekCron)) {
      return {
        selectedOption: CRON_KEYS.days.lastSpecificDayOfTheMonth,
        [CRON_KEYS.days.lastSpecificDayOfTheMonth]: parseInt(
          dayOfWeekCron.split("L")[0]
        ),
      }
    }
    if (everyDayOfTheWeekRegex.test(dayOfWeekCron)) {
      return {
        selectedOption: CRON_KEYS.days.everySpecificWeekdayOfTheMonth,
        [CRON_KEYS.days.everySpecificWeekdayOfTheMonth]: {
          period: parseInt(dayOfWeekCron.split("#")[1]),
          startAt: parseInt(dayOfWeekCron.split("#")[0]),
        },
      }
    }
  }
  if (dayOfWeekCron === "?") {
    if (atTimeRegex.test(dayOfMonthCron)) {
      return {
        selectedOption: CRON_KEYS.days.specificDay,
        [CRON_KEYS.days.specificDay]: dayOfMonthCron
          .split(",")
          .map((day) => parseInt(day)),
      }
    }
    if (everySpecificRegex.test(dayOfMonthCron)) {
      return {
        selectedOption: CRON_KEYS.days.everySpecificAnyDayOfTheMonth,
        [CRON_KEYS.days.everySpecificAnyDayOfTheMonth]: {
          period: dayOfMonthCron.split("/")[1],
          startAt: getSerialNumber(dayOfMonthCron.split("/")[0].split("-")[0]),
        },
      }
    }
    if (dayOfMonthCron === "L") {
      return {
        selectedOption: CRON_KEYS.days.lastDayOfTheMonth,
      }
    }
    if (dayOfMonthCron === "LW") {
      return {
        selectedOption: CRON_KEYS.days.lastWeekdayOfTheMonth,
      }
    }
    if (beforeEndOfTheMonthRegex.test(dayOfMonthCron)) {
      return {
        selectedOption: CRON_KEYS.days.dayBeforeTheEndOfTheMonth,
        [CRON_KEYS.days.dayBeforeTheEndOfTheMonth]: parseInt(
          dayOfMonthCron.split("-")[1]
        ),
      }
    }
    if (nearestWeekdayOfTheMonthRegex.test(dayOfMonthCron)) {
      return {
        selectedOption: CRON_KEYS.days.nearestWeekday,
        [CRON_KEYS.days.nearestWeekday]: parseInt(dayOfMonthCron.split("W")[0]),
      }
    }
  }

  return {
    selectedOption: CRON_KEYS.days.everyDay,
  }
}

export const getUpdatedState = (cron) => {
  const [minuteCron, hoursCron, dayOfMonthCron, monthCron, dayOfWeekCron] =
    cron.split(" ")

  const updatedMinuteCron =
    minuteCron === "0" ? {} : getUpdatedMinutesSettings(minuteCron)
  const updatedHoursCron =
    hoursCron === "*" ? {} : getUpdatedHoursSettings(hoursCron)
  const updatedDayCron =
    dayOfMonthCron === "?" && dayOfWeekCron === "*"
      ? {}
      : getUpdatedDaySettings(dayOfMonthCron, dayOfWeekCron)
  const updatedMonthCron =
    monthCron === "*" ? {} : getUpdatedMonthSettings(monthCron)

  return {
    [CRON_KEYS.minutes.key]: updatedMinuteCron,
    [CRON_KEYS.hours.key]: updatedHoursCron,
    [CRON_KEYS.months.key]: updatedMonthCron,
    [CRON_KEYS.days.key]: updatedDayCron,
  }
}
