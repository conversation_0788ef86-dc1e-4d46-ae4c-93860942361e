import { getObjectEntries } from "@develop/fe-library/dist/utils"
import { createSelector } from "reselect"

import { profitTypes, stockTypes, strategiesTypes } from "utils/formConstants"

const gridStateSelector = (state) => state.grid

export const tableSettingsSelector = (state) => state.tableSettings
const productsForFilterSelector = (state) => state.grid?.productsList?.data
const amazonCustomerAccountsSelector = (state) =>
  state.amazonCustomerAccounts.amazonCustomerAccounts

export const getGridPrevSearchOptionsSelector = (state) =>
  state.grid.prevSearchOptions
export const getGridSearchOptionsSelector = (state) => state.grid.searchOptions

export const gridSelector = createSelector(
  gridStateSelector,
  tableSettingsSelector,
  (grid, tableSettings) => {
    const {
      data,
      searchOptions,
      marketPlaces,
      productConditions,
      totalCount,
      loading,
      selectedProducts,
      queryParams,
      loadingParams,
      selectAll,
      selectedTotalCount,
    } = grid
    const {
      settings: { productsTableSettings: { settings } = {} },
      footerBounds,
      visible,
    } = tableSettings

    return {
      data: data,
      searchOptions: getObjectEntries(searchOptions).reduce(
        (acc, [key, value]) => {
          acc[key] = value && value.replace ? decodeURIComponent(value) : value

          return acc
        },
        {}
      ),
      columns: settings,
      tableSettingsVisible: visible,
      filtersData: {
        marketPlaces: (marketPlaces || []).reduce((acc, marketPlace) => {
          const { id } = marketPlace.amazonMarketplace

          if (
            !acc.find((m) => m.amazonMarketplace.id === id) &&
            marketPlace.active === 1
          ) {
            acc = [...acc, marketPlace]
          }

          return acc
        }, []),
        productConditions: getObjectEntries(productConditions)
          .map(([key, value]) => ({ id: key, title: value }))
          .sort(({ id: aId, title: valueA }, { id: bId, title: valueB }) => {
            if (valueA === "New") {
              return -1
            }

            if (valueB === "New") {
              return 1
            }

            return aId - bId
          }),
      },
      totalCount: totalCount,
      loading: loading,
      selectedKeys: selectedProducts.map(({ id }) => id),
      queryParams,
      loadingParams,
      selectedAll: selectAll,
      selectedTotalCount,
      footerBounds,
    }
  }
)

export const selectFiltersOptions = () => {
  const yesNo = [
    {
      value: "1",
      label: "Yes",
    },
    {
      value: "0",
      label: "No",
    },
  ]

  const na = {
    value: "na",
    label: "N/A",
  }

  return {
    stock_type: getObjectEntries(stockTypes).map(([_, value]) => ({
      value,
      label: value,
    })),
    isBuyBox: [...yesNo, na],
    strategy: [
      ...getObjectEntries(strategiesTypes).map(
        ([_, [strategyKey, strategyTitle]]) => ({
          value: strategyKey,
          label: strategyTitle,
        })
      ),
      na,
    ],
    use_strategy_logger: [...yesNo],
    optimization_active: [...yesNo],
    isPrime: [...yesNo],
    isNationalPrime: [...yesNo],
    competitivePriceThreshold: [...yesNo],
    isFeaturedMerchant: [...yesNo],
    profitType: getObjectEntries(profitTypes).map(
      ([profitKey, profitTitle]) => ({
        value: profitKey,
        label: profitTitle,
      })
    ),
  }
}

export const productsForFilterItemsSelector = createSelector(
  productsForFilterSelector,
  gridStateSelector,
  amazonCustomerAccountsSelector,
  (products, { marketPlaces }, amazonCustomerAccounts) => {
    if (!products) {
      return []
    }

    return products.map((product) => ({
      ...product,
      amazonCustomerAccounts,
      marketplace: marketPlaces
        ? marketPlaces.find(
            ({ amazonMarketplace: { id } }) => id === product.marketplace_id
          )
        : undefined,
    }))
  }
)
