import { getObjectKeys } from "@develop/fe-library/dist/utils"
import get from "lodash/get"
import isEmpty from "lodash/isEmpty"
import { createSelector } from "reselect"

import config from "config"

import { checkIsArray } from "utils/arrayHelpers"
import l from "utils/intl"

const { isCrmDisabled } = config

export const selectedRoleController = (state) => state.selectedRole.controller
export const selectedRoleData = (state) => state.selectedRole.role
export const selectedRoleOperations = (state) => state.selectedRole.operations
export const rolesList = (state) => state.selectedRole.list
export const rolesListData = (state) =>
  state.selectedRole.list.data || state.selectedRole.list

export const errors = (state) => state.selectedRole.errors
const tablesSettings = (state) => state.tableSettings.settings

export const selectedRoleSearchOptionsSelector = (state) => {
  return state.selectedRole.searchOptions
}

/**
 * Rename key name `accessOperations` to `children`
 */
export const selectFiltersOptions = () => ({
  type: [
    { label: l("SellerLogic Superadmin Type"), value: "SL_ADMIN" },
    { label: l("SellerLogic Manager Type"), value: "SL_USER" },
    { label: l("Customer Admin Type"), value: "CUSTOMER_ADMIN" },
    { label: l("Customer User Type"), value: "CUSTOMER_USER" },
    { label: l("Translator Admin Type"), value: "TRANSLATOR_ADMIN" },
    { label: l("Translator User Type"), value: "TRANSLATOR_USER" },
  ],
  side: [
    { label: l("SellerLogic"), value: "SELLERLOGIC" },
    { label: l("Customer"), value: "CUSTOMER" },
    { label: l("Translator"), value: "TRANSLATOR" },
  ],
  active: [
    { label: l("Yes"), value: "1" },
    { label: l("No"), value: "0" },
  ],
})

export const controllerSelector = createSelector(
  selectedRoleController,
  (controller) => {
    const list = controller
      .map(({ key, accessOperations, ...rest }) => {
        return {
          ...rest,
          id: key,
          children: accessOperations,
        }
      })
      .sort((a, b) => a.order - b.order)

    const groupsObject = list.reduce((result, item) => {
      const groupName = item.group || "Other"
      const subgroupName = item.sub_group || "children"

      const items = result[groupName]?.[subgroupName] || []

      return {
        ...result,
        [groupName]: {
          ...result[groupName],
          [subgroupName]: [...items, item],
        },
      }
    }, {})

    const orderWithoutCRM = [
      "SellerLogic administration",
      "Administration",
      "Repricer",
      "Lost & Found",
      "Business Analytics",
      "Service Desk",
      "Other",
    ]

    const orderWithCRM = [
      "SellerLogic administration",
      "Administration",
      "Repricer",
      "Lost & Found",
      "Business Analytics",
      "Service Desk",
      "CRM",
      "Other",
    ]

    const correctOrder = isCrmDisabled ? orderWithoutCRM : orderWithCRM

    const order = correctOrder.filter((groupName) => groupName in groupsObject)

    return order.map((groupName) => {
      const { children, ...subGroups } = groupsObject[groupName]
      const isBA = groupName !== "Business Analytics"
      const childrenMapped = isBA
        ? children
        : children.map(({ title, ...rest }) => {
            const isTitleHasBASuffix = /^BA /.test(title)
            const titleModified = isTitleHasBASuffix
              ? title?.substring(3)
              : title

            return {
              title: titleModified,
              ...rest,
            }
          })

      return {
        title: groupName,
        children: [
          ...childrenMapped,
          ...getObjectKeys(subGroups).reduce(
            (acc, subgroupName) => [
              ...acc,
              subgroupName,
              ...subGroups[subgroupName],
            ],
            []
          ),
        ],
      }
    })
  }
)

/**
 * Selector with empty operations
 */
export const defaultControllerOperations = createSelector(
  controllerSelector,
  (controller) => {
    if (!checkIsArray(controller)) {
      return {}
    }

    const build = (list) => {
      return list.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.id]: false,
          ...build(curr.hasOwnProperty("children") ? curr.children : []),
        }),
        {}
      )
    }

    return controller.reduce((acc, controllerItem) => {
      return {
        ...acc,
        ...build(controllerItem.children),
      }
    }, {})
  }
)

/**
 * Build a user permission based on role controller and role operations
 */
export const operationsValuesSelector = createSelector(
  defaultControllerOperations,
  selectedRoleOperations,
  (controllerOperations, operations) => {
    const userPermission = !operations
      ? {}
      : operations.reduce(
          (acc, curr) => ({
            ...acc,
            [curr.id]: true,
          }),
          {}
        )

    return Object.assign({}, controllerOperations, userPermission)
  }
)

/**
 * Converting value to human readable format
 */
export const rolesDataSelector = createSelector(
  rolesListData,
  selectFiltersOptions,
  (roles, selects) => {
    return roles.map((item) => {
      return {
        ...item,
        active: item.active + "" === "1" ? l("Yes") : l("No"),
        type: (selects.type.find(({ value }) => value === item.type) || {})
          .label,
        side: (selects.side.find(({ value }) => value === item.side) || {})
          .label,
      }
    })
  }
)

/**
 * Build options with custom user roles. These options use to
 * create a custom operations
 * @param {String} side role side SELLERLOGIC or CUSTOMER
 * @param {Boolean} isCustomRole should include custom roles
 * @param {Object} user user object
 */
export function selectOptionsWithCustomUserIdSelector(
  side = "",
  isCustomRole = true,
  user
) {
  const onlyActive = (x) => Number(x.active) === 1
  const activeAndSide = (x) => onlyActive(x) && x.side === side
  const filterFn = Boolean(side) ? activeAndSide : onlyActive
  const filterCustomRoles = (x) =>
    x.id.includes("customUser") && !isCustomRole ? false : true

  return createSelector(rolesOptionsForEditSelector(user), (roles) => {
    return roles
      .filter(filterFn)
      .filter(filterCustomRoles)
      .sort((a, b) => a.order - b.order)
      .map((x) => ({ label: x.title, value: x.id }))
  })
}

/**
 * Build options with custom user roles. These options use to filtering data
 * @param {String} side role side SELLERLOGIC or CUSTOMER
 * @param {Boolean} isCustomRole should include custom roles
 */
export function selectOptionsWithCustomUserSelector(
  side = "",
  isCustomRole = true
) {
  const onlyActive = (x) => Number(x.active) === 1
  const activeAndSide = (x) => onlyActive(x) && x.side === side
  const filterFn = Boolean(side) ? activeAndSide : onlyActive
  const filterCustomRoles = (x) =>
    x.id.includes("customUser") && !isCustomRole ? false : true

  return createSelector(rolesOptionsForTableSelector, (roles) => {
    return !isEmpty(roles)
      ? roles
          .filter(filterFn)
          .filter(filterCustomRoles)
          .sort((a, b) => a.order - b.order)
          .map((x) => ({ label: x.title, value: x.id }))
      : []
  })
}

/**
 * Add custom options entry for the roles options element
 */
export const rolesOptionsForTableSelector = createSelector(
  rolesListData,
  (roles) =>
    roles &&
    roles.concat([
      {
        title: "Custom Manager",
        id: "customUserSELLERLOGIC",
        side: "SELLERLOGIC",
        order: 99,
        active: 1,
      },
      {
        title: "Custom User",
        id: "customUserCUSTOMER",
        side: "CUSTOMER",
        order: 90,
        active: 1,
      },
    ])
)

/**
 * Add custom options entry with user ID
 * @param {Object} user user object
 */
export const rolesOptionsForEditSelector = (user) =>
  createSelector(rolesListData, (roles) =>
    roles.concat([
      {
        title: "Custom Manager",
        id: `customUser${user.id}`,
        side: "SELLERLOGIC",
        order: 99,
        active: 1,
      },
      {
        title: "Custom User",
        id: `customUser${user.id}`,
        side: "CUSTOMER",
        order: 90,
        active: 1,
      },
    ])
  )

/**
 * Configurations for table
 * @param {Object} columns columns configuration object
 * @param {String} tableName table name
 */
export function activeColumnSelector(columns, tableName) {
  return createSelector(tablesSettings, (tablesSettings) => {
    const setting = get(tablesSettings[tableName], "settings")

    return !isEmpty(setting)
      ? setting
          .filter(({ value }) => value)
          .map(({ name }) => columns.find(({ key }) => key === name))
      : []
  })
}
