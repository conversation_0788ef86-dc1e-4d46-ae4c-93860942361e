import { getObjectEntries } from "@develop/fe-library/dist/utils"
import { createSelector } from "reselect"

import l from "utils/intl"

import { DEFAULT_TOTAL_COUNT } from "consts/grid"
import { caseTypes } from "consts/lostCase"

const lostAmazonLinksSelector = (state) => state.lostAmazonLink.data
const amazonMarketplacesSelector = (state) =>
  state.amazonMarketplaces.amazonMarketplacesWithInactive

export const lostAmazonLinkInitialValuesSelector = (state) => {
  return state.lostAmazonLink.initialValues
}
export const lostAmazonLinkModalVisibleSelector = (state) => {
  return state.lostAmazonLink.modalVisible
}
export const lostAmazonLinkSearchOptionsSelector = (state) => {
  return state.lostAmazonLink.searchOptions
}
export const lostAmazonLinkTotalCountSelector = (state) => {
  return state.lostAmazonLink?.totalCount || DEFAULT_TOTAL_COUNT
}

export const lostAmazonLinksItemsSelector = createSelector(
  lostAmazonLinksSelector,
  amazonMarketplacesSelector,
  (lostAmazonLinks, amazonMarketplaces) =>
    lostAmazonLinks.map((lostAmazonLink) => ({
      ...lostAmazonLink,
      caseTypeLabel: l(caseTypes[lostAmazonLink.case_type]?.label),
      marketplaceLabel:
        lostAmazonLink.marketplace_id &&
        amazonMarketplaces.find(
          ({ id }) => id === lostAmazonLink.marketplace_id
        )?.title,
    }))
)

export const filterOptionsSelector = createSelector(
  amazonMarketplacesSelector,
  (amazonMarketplaces) => ({
    case_type: getObjectEntries(caseTypes).map(([, { key, label }]) => ({
      label: l(label),
      value: key,
    })),
    marketplace_id: amazonMarketplaces
      ? amazonMarketplaces.map(({ id, title }) => ({
          label: title,
          value: id,
        }))
      : [],
  })
)
