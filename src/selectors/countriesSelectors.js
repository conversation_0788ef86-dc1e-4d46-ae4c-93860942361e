import { createSelector } from "reselect"

import l from "utils/intl"
import { DEFAULT_TOTAL_COUNT } from "consts/grid"

const countriesSelector = (state) => state.countries.countries
const languagesSelector = (state) => state.languages.languagesOptions
export const countriesOptionsSelector = (state) => {
  return state.countries.countriesOptions || []
}
export const countriesSearchOptionsSelector = (state) => {
  return state.countries.searchOptions
}
export const countriesInitialValuesSelector = (state) => {
  return state.countries.initialValues
}
export const countriesModalVisibleSelector = (state) => {
  return state.countries.modalVisible
}
export const countriesTotalCountSelector = (state) => {
  return state.countries?.totalCount || DEFAULT_TOTAL_COUNT
}

export const countryItemsSelector = createSelector(
  countriesSelector,
  languagesSelector,
  (countries, languages) =>
    countries.map((country) => ({
      ...country,
      activeLabel: country.active === 1 ? l("Yes") : l("No"),
      euLabel: country.eu === 1 ? l("Yes") : l("No"),
      paymentMethodLabel:
        country.payment_method === "debit" ? l("Sepa Debit") : l("Credit card"),
      languageLabel: (
        languages.find(({ id }) => id === country.language_id) || { title: "" }
      ).title,
    }))
)

export const filtersOptionsSelector = createSelector(
  languagesSelector,
  (languages) => ({
    active: [
      { label: l("Yes"), value: "1" },
      { label: l("No"), value: "0" },
    ],
    eu: [
      { label: l("Yes"), value: "1" },
      { label: l("No"), value: "0" },
    ],
    payment_method: [
      { label: l("Credit card"), value: "cc" },
      { label: l("Sepa Debit"), value: "debit" },
    ],
    language_id: languages.map(({ id, title }) => ({
      label: title,
      value: `${id}`,
    })),
  })
)
