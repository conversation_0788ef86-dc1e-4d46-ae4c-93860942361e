import { createSelector } from "reselect"

const twoFAViewSelector = (state) => state.twoFA.view
const twoFAStateSelector = (state) =>
  state.staff.currentUser.user.is_enabled_2fa

const messages = {
  activate: `Download and install a 2FA app of your choice, such as <PERSON>thy or Google Authenticator. Use our <a>step-by-step guide</a> if you need detailed installation instructions.`,
  confirmed:
    "Please save your recovery codes in a safe location. We won't be able to restore your access without these codes. They can be used to deactivate 2-factor authentication.",
  confirmPhone: "Confirm your phone to enable two-factor authentication",
  deactivate: "Confirm your code to deactivate two-factor authentication",
  enable:
    "Increase account security by enabling two-factor authentication. Use one-time password authenticator on your mobile device or computer to enable two-factor authentication. In case you lost your recovery codes you can generate new ones, invalidating all previous codes.",
  enabled:
    "You have already enabled two-factor authentication using one-time password authenticators",
  recoveryCodes:
    "Please save your recovery codes in a safe location. We won't be able to restore your access without these codes. They can be used to deactivate 2-factor authentication.",
}

export const twoFAStepSelector = createSelector(
  twoFAStateSelector,
  twoFAViewSelector,
  (twoFAEnabled, view) => {
    if (!view) {
      return {
        message: twoFAEnabled ? messages.enabled : messages.enable,
        view: twoFAEnabled ? "enabled" : "enable",
      }
    }

    return {
      message: messages[view],
      view,
    }
  }
)

export const twoFAQrCodeImageSelector = (state) => state.twoFA?.qrCodeImage

export const twoFASecretKeySelector = (state) => state.twoFA?.secretKey

export const recoveryCodesSelector = (state) => state.twoFA?.recoveryCodes
