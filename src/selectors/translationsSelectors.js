import { createSelector } from "reselect"

import l from "utils/intl"
import { DEFAULT_TOTAL_COUNT } from "consts/grid"

const messagesItemsSelector = (state) => state.translations.messageItems
const languagesSelector = (state) => state.languages.languagesOptions
export const languageSelector = (state) => state.translations.locale
const commentsSelector = (state) => state.translations.comments
const categoriesSelector = (state) => state.translations.categories

export const isTranslationsReadySelector = (state) => state.translations.ready

export const translationsSearchOptionsSelector = (state) => {
  return state.translations.searchOptions
}
export const translationsTotalCountSelector = (state) => {
  return state.translations?.totalCount || DEFAULT_TOTAL_COUNT
}

const currentTranslationSelector = (state) =>
  state.translations.currentTranslation

const statuses = {
  TRANSLATED: "Translated",
  APPROVED: "Approved",
  PENDING: "Pending",
}

const languagesNamesSelector = createSelector(languagesSelector, (languages) =>
  languages.reduce((acc, { code, title }) => {
    acc[code] = title

    return acc
  }, {})
)

export const commentsItemSelector = createSelector(
  commentsSelector,
  (comments) => {
    const { data, ...rest } = comments
    return {
      ...rest,
      data: data.map((comment) => ({
        ...comment,
        value_label: comment.value && comment.value.toLowerCase(),
        prev_value_label:
          comment.prev_value && comment.prev_value.toLowerCase(),
      })),
    }
  }
)

export const currentTranslationItemSelector = createSelector(
  currentTranslationSelector,
  languagesNamesSelector,
  (currentTranslation, languages) => {
    return (
      currentTranslation && {
        ...currentTranslation,
        languageName: languages[currentTranslation?.language],
      }
    )
  }
)

export const messagesSelector = createSelector(
  messagesItemsSelector,
  languagesNamesSelector,
  (messages, languages) =>
    messages.map(
      ({
        comments,
        id,
        language,
        messageTranslationDuplicates,
        translation,
        translated,
        message: { category, message },
        status,
      }) => ({
        rowKey: `${id}${language}`,
        category,
        comments,
        id,
        language,
        languageName: languages[language],
        messageTranslationDuplicates,
        source: message,
        status: statuses[status],
        translation,
        translated: translated ? l("Yes") : l("No"),
      })
    )
)

export const filtersOptionsSelector = createSelector(
  languagesSelector,
  categoriesSelector,
  languageSelector,
  (languages, categories) => ({
    language: languages
      .filter(({ active }) => active)
      .map(({ code, title }) => ({
        label: title,
        value: code,
      })),
    status: [
      { label: l("Translated"), value: "TRANSLATED" },
      { label: l("Approved"), value: "APPROVED" },
      { label: l("Pending"), value: "PENDING" },
    ],
    category: categories.map((category) => ({
      label: category,
      value: category,
    })),
  })
)
