import { createSelector } from "reselect"

import { RootState } from "types/store"

const promoCodeStateSelector = (state: RootState) => state.promoCode

export const promoCodesSelector = createSelector(
  promoCodeStateSelector,
  ({ promoCodes }) => promoCodes
)

export const promoCodeTotalCountSelector = createSelector(
  promoCodeStateSelector,
  ({ totalCount }) => totalCount
)

export const promoCodeSearchOptionsSelector = createSelector(
  promoCodeStateSelector,
  ({ searchOptions }) => searchOptions
)

export const promoCodeSelector = createSelector(
  promoCodeStateSelector,
  ({ promoCode }) => promoCode
)
