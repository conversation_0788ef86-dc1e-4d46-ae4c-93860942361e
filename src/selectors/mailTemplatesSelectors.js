import { DEFAULT_TOTAL_COUNT } from "consts/grid"

export const mailTemplatesSelector = (state) => {
  return state.mailTemplates.MailTemplates
}

export const mailTemplatesSearchOptionsSelector = (state) => {
  return state.mailTemplates.searchOptions
}

export const mailTemplatesOptionsSelector = (state) => {
  return state.mailTemplates.MailTemplatesOptions
}

export const mailTemplatesTotalCountSelector = (state) => {
  return state.mailTemplates?.totalCount || DEFAULT_TOTAL_COUNT
}

export const mailTemplatesModalVisibleSelector = (state) => {
  return state.mailTemplates.modalVisible
}

export const mailTemplatesConfirmationModalVisibleSelector = (state) => {
  return state.mailTemplates.confirmationModalVisible
}

export const mailTemplatesInitialValuesSelector = (state) => {
  return state.mailTemplates.initialValues
}

export const mailTemplatesPreviewSelector = (state) => {
  return state.mailTemplates.preview
}
