import { createSelector } from "reselect"
import { convertToLocalDateTime } from "utils/dateConverter"

const paymentModulesSelector = (state) => state.paymentModules.paymentModules

export const paymentModulesItemsSelector = createSelector(
  paymentModulesSelector,
  (paymentModules) =>
    paymentModules.map((paymentModule) => ({
      ...paymentModule,
      item: paymentModule,
      labels: [
        { label: "ID", value: paymentModule.id },
        {
          label: "Created",
          value: convertToLocalDateTime(paymentModule.created, true),
        },
      ],
    }))
)
