import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/serviceDeskNotificationsSettingsActions"

const initialState = {
  settings: {},
}

const reductions = {
  [ActionTypes.fetchAllSettings[1]]: (state, settings) => {
    return {
      ...state,
      settings,
    }
  },
  [ActionTypes.setSettings]: (state, item) => ({
    ...state,
    settings: {
      ...state.settings,
      ...item,
    },
  }),
}

export default generateReducer(initialState, reductions)
