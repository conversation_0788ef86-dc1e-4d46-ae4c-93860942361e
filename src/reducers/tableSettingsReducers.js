import { getObjectEntries } from "@develop/fe-library/dist/utils"
import get from "lodash/get"

import initialState from "initialState"

import {
  DEFAULT_PAGE_SIZE,
  types as ActionTypes,
} from "actions/tableSettingsActions"

import generateReducer from "utils/generateReducer"

const reductions = {
  [ActionTypes.display]: (state, payload) => ({
    ...state,
    ...payload,
    selectedTableSettings:
      payload.selectedTableSettings || state.selectedTableSettings,
  }),
  [ActionTypes.setIsResizingMode]: (state, payload) => ({
    ...state,
    ...payload,
  }),
  [ActionTypes.footerSize](state, { bounds: footerBounds }) {
    return { ...state, footerBounds }
  },
  [ActionTypes.get[1]]: (
    state,
    { id, key: tableSettingKey, settings = [], pageSize = DEFAULT_PAGE_SIZE }
  ) => {
    const columns = state.columns[tableSettingKey]
    const initialColumns = initialState.tableSettings.columns[tableSettingKey]
    const columnsArray = columns
      ? getObjectEntries(columns).map(([key, value]) => ({ key, value }))
      : []

    return {
      ...state,
      settings: {
        ...state.settings,
        [tableSettingKey]: {
          id,
          tableSettingKey,
          pageSize,
          settings: columns
            ? columnsArray
                .reduce(
                  (acc, { key, value }, i) => {
                    if (!acc.find(({ name }) => name === key)) {
                      const item = {
                        label: value,
                        name: key,
                        value: get(
                          state,
                          `defaultSettings[${tableSettingKey}][${key}]`,
                          false
                        ),
                      }

                      if (i === 0) {
                        acc.unshift(item)

                        return acc
                      }
                      const indexPrev = acc.findIndex(
                        ({ name }) => (columnsArray[i - 1] || {}).key === name
                      )

                      if (indexPrev > -1) {
                        acc.splice(indexPrev + 1, 0, item)
                      } else {
                        acc.push(item)
                      }
                    }

                    return acc
                  },
                  [...settings]
                )
                .filter(({ name }) =>
                  columnsArray.some(({ key }) => name === key)
                )
                .map(({ name, label, ...props }) => ({
                  ...props,
                  name,
                  label: initialColumns[name] || label,
                }))
            : [],
        },
      },
    }
  },
  [ActionTypes.save[1]]: (state, { id, key, settings, pageSize }) => ({
    ...state,
    settings: {
      [key]: {
        id,
        key,
        pageSize,
        settings: state.columns[key]
          ? getObjectEntries(state.columns[key])
              .map(([key, value]) => ({ key, value }))
              .reduce(
                (acc, { key, value }) => {
                  if (!acc.find(({ name }) => name === key)) {
                    acc.push({ label: value, name: key, value: true })
                  }

                  return acc
                },
                [...settings]
              )
          : [],
      },
    },
  }),
}

export default generateReducer({}, reductions)
