import generateReducer from "utils/generateReducer"

import { types as ActionTypes } from "actions/amazonMarketplacesActions"

const initialState = {
  initialValues: {},
  modalVisible: false,
  amazonMarketplace: [],
  amazonMarketplaces: [],
  isAmazonMarketplacesLoading: false,
  searchOptions: {},
  amazonMarketplacesWithInactive: [],
}

const reductions = {
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
  }),
  [ActionTypes.get[1]]: (
    state,
    { currentPage, data, pageSize, totalCount }
  ) => ({
    ...state,
    amazonMarketplace: data,
    totalCount,
    searchOptions: {
      ...state.searchOptions,
      page: currentPage,
      pageSize: pageSize,
    },
  }),
  [ActionTypes.get[2]]: (state) => ({
    ...state,
    amazonMarketplace: [],
    totalCount: 0,
  }),
  [ActionTypes.getAll[0]]: (state) => ({
    ...state,
    isAmazonMarketplacesLoading: true,
  }),
  [ActionTypes.getAll[1]]: (state, payload) => ({
    ...state,
    amazonMarketplaces: payload,
    isAmazonMarketplacesLoading: false,
  }),
  [ActionTypes.getAll[2]]: (state) => ({
    ...state,
    isAmazonMarketplacesLoading: false,
  }),
  [ActionTypes.getAllWithInactive[1]]: (state, payload) => ({
    ...state,
    amazonMarketplacesWithInactive: payload,
  }),
}

export default generateReducer(initialState, reductions)
