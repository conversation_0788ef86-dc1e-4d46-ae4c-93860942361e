import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/productImportActions"

const initialState = {
  currentProductImport: undefined,
  errorsModalVisible: false,
  initialValues: {},
  productImports: [],
  allProductImports: [],
  searchOptions: {},
  uploadError: "",
  uploadModalVisible: false,
}

const reductions = {
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
  }),
  [ActionTypes.closeErrorsModal]: (state) => ({
    ...state,
    errorsModalVisible: false,
  }),
  [ActionTypes.displayUploadModal]: (state, { uploadModalVisible }) => ({
    ...state,
    uploadError: "",
    uploadModalVisible,
  }),
  [ActionTypes.get[1]]: (
    state,
    { currentPage, data, pageSize, totalCount }
  ) => ({
    ...state,
    productImports: data,
    totalCount,
    searchOptions: {
      ...state.searchOptions,
      page: currentPage,
      pageSize: pageSize,
    },
    activeImports: data.filter(({ status }) => status === "IN_PROGRESS"),
  }),
  [ActionTypes.get[2]]: (state) => ({
    ...state,
    productImports: [],
    totalCount: 0,
  }),
  [ActionTypes.getAllImports[1]]: (state, { data }) => ({
    ...state,
    allProductImports: data,
  }),
  [ActionTypes.getById[0]]: (state) => ({
    ...state,
    currentProductImport: undefined,
  }),
  [ActionTypes.getById[1]]: (state, payload) => ({
    ...state,
    currentProductImport: payload,
    errorsModalVisible: true,
  }),
  [ActionTypes.upload[2]]: (state, { payload: { message } }) => ({
    ...state,
    uploadError: message,
  }),
}

export default generateReducer(initialState, reductions)
