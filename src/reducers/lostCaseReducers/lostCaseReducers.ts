import { API_STATUS } from "api/constants"

import { types as ActionTypes } from "actions/lostCaseActions/lostCaseActions"

import generateReducer from "utils/generateReducer"

import { LostCaseState } from "types/store"

import { LostCaseReductions } from "./lostCaseReducersTypes"
import {
  SupportCaseResponse,
  SupportCaseStatistic,
} from "api/lostCaseService/lostCaseServiceTypes"

const { SUCCESS, FAILURE } = API_STATUS

const initialState: LostCaseState = {
  nextCaseId: null,
  currentCase: undefined,
  currentCaseActiveUser: undefined,
  lastCaseModalVisible: false,
  defaultFilters: {},
  searchOptions: {},
  totalCount: 0,
  selectedTotalCount: 0,
  filters: {},
  supportCases: [],
  supportCaseStatistic: {
    "full-service-customer-action-required-cases": 0,
    "need-review-cases": 0,
  },
}

const reductions: LostCaseReductions = {
  [ActionTypes.getNextCase[SUCCESS]]: (state, { id }) => ({
    ...state,
    nextCaseId: id,
  }),
  [ActionTypes.nextCaseId]: (state, { id }) => ({
    ...state,
    nextCaseId: id,
  }),
  [ActionTypes.setCurrentCase]: (state, payload) => ({
    ...state,
    currentCase: payload,
  }),
  [ActionTypes.displayLastCaseModal]: (state, { modalVisible }) => ({
    ...state,
    lastCaseModalVisible: modalVisible,
  }),
  [ActionTypes.setCurrentCaseActiveUser]: (state, payload) => ({
    ...state,
    currentCaseActiveUser: payload,
  }),
  [ActionTypes.getSupportCases[SUCCESS]]: (
    state,
    { currentPage, data, pageSize, totalCount }: SupportCaseResponse
  ) => ({
    ...state,
    supportCases: data,
    totalCount,
    searchOptions: {
      ...state.searchOptions,
      page: currentPage,
      pageSize: pageSize,
    },
  }),
  [ActionTypes.getSupportCases[FAILURE]]: (state) => ({
    ...state,
    supportCases: [],
    totalCount: 0,
  }),
  [ActionTypes.getFilters[SUCCESS]]: (state, payload) => ({
    ...state,
    defaultFilters: payload,
  }),
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
  }),
  [ActionTypes.getSupportCaseFilters[SUCCESS]]: (state, payload) => ({
    ...state,
    filters: payload,
  }),
  [ActionTypes.getSupportCaseStatistic[SUCCESS]]: (
    state,
    payload: SupportCaseStatistic
  ) => ({
    ...state,
    supportCaseStatistic: payload,
  }),
  [ActionTypes.getSupportCaseStatistic[FAILURE]]: (state) => ({
    ...state,
    supportCaseStatistic: initialState.supportCaseStatistic,
  }),
  [ActionTypes.selectUnselectSupportCase]: (state, updatedSupportCases) => ({
    ...state,
    supportCases: updatedSupportCases,
  }),
  [ActionTypes.selectAll]: (state, updatedSupportCases) => ({
    ...state,
    supportCases: updatedSupportCases,
  }),
  [ActionTypes.selectTotalCount]: (state, selected) => {
    return {
      ...state,
      selectedTotalCount: selected,
    }
  },
}

const reducer = generateReducer(initialState, reductions)

export default reducer
