import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/partnerCreditNotesActions"

const initialState = {
  partnerCreditNotes: [],
  searchOptions: {},
}

const reductions = {
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
  }),
  [ActionTypes.get[1]]: (
    state,
    { currentPage, data, pageSize, totalCount }
  ) => ({
    ...state,
    partnerCreditNotes: data,
    totalCount,
    searchOptions: {
      ...state.searchOptions,
      page: currentPage,
      pageSize: pageSize,
    },
  }),
  [ActionTypes.get[2]]: (state) => ({
    ...state,
    partnerCreditNotes: [],
    totalCount: 0,
  }),
  [ActionTypes.update[1]]: (state) => ({
    ...state,
    modalVisible: false,
    initialValues: {},
  }),
}

export default generateReducer(initialState, reductions)
