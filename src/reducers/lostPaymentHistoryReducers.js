import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/lostPaymentHistoryActions"

const initialState = {
  dashboardPayments: undefined,
  dashboardPaymentsTotalCount: 0,
  isDashboardPaymentsLoaded: true,
  initialValues: {},
  modalVisible: false,
  confirmationModalVisible: false,
  payments: [],
  searchOptions: {},
  defaultFilters: {
    type: [
      { label: "Reimbursement", value: "REIMBURSEMENT" },
      { label: "Adjustment", value: "ADJUSTMENT" },
      { label: "Fee-based rejection", value: "REJECTION" },
    ],
  },
  filters: {
    types: [],
  },
}

const reductions = {
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
  }),
  [ActionTypes.displayModal]: (
    state,
    { modalVisible, initialValues = {} }
  ) => ({
    ...state,
    modalVisible,
    initialValues,
  }),
  [ActionTypes.showConfirmationModal]: (
    state,
    { confirmationModalVisible, initialValues = {} }
  ) => ({
    ...state,
    confirmationModalVisible,
    initialValues,
  }),
  [ActionTypes.get[1]]: (
    state,
    { currentPage, data, pageSize, totalCount }
  ) => ({
    ...state,
    payments: data,
    totalCount,
    searchOptions: {
      ...state.searchOptions,
      page: currentPage,
      pageSize: pageSize,
    },
  }),
  [ActionTypes.getFilters[1]]: (state, filters) => ({
    ...state,
    filters,
  }),
  [ActionTypes.get[2]]: (state) => ({
    ...state,
    payments: [],
    totalCount: 0,
  }),
  [ActionTypes.update[1]]: (state) => ({
    ...state,
    modalVisible: false,
    confirmationModalVisible: false,
    initialValues: {},
  }),
}

export default generateReducer(initialState, reductions)
