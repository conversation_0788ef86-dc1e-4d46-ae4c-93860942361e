import { API_STATUS } from "api/constants"

import { adminVatSettingsInitialState } from "initialState/adminVatSettings"

import { types as ActionTypes } from "actions/adminVatSettings/adminVatSettingsActions"

import generateReducer from "utils/generateReducer"

import type { CustomerVatSetting, GlobalVatSetting } from "types/Models"

import type { AdminVatSettingsReductions } from "./adminVatSettingsReducersTypes"

const { SUCCESS } = API_STATUS

const reductions: AdminVatSettingsReductions = {
  [ActionTypes.getGlobalVatSetting[SUCCESS]]: (
    state,
    payload: GlobalVatSetting[]
  ) => ({
    ...state,
    globalSettings: payload,
  }),
  [ActionTypes.updateGlobalVatSetting[SUCCESS]]: (state, globalSettings) => ({
    ...state,
    globalSettings,
  }),
  [ActionTypes.getCustomerVatSettings[SUCCESS]]: (
    state,
    payload: CustomerVatSetting[]
  ) => ({
    ...state,
    customerSettings: payload,
  }),
  [ActionTypes.updateCustomerVatSetting[SUCCESS]]: (
    state,
    customerSettings
  ) => ({
    ...state,
    customerSettings,
  }),
  [ActionTypes.resetCustomerVatSettings[SUCCESS]]: (
    state,
    customerSettings
  ) => ({
    ...state,
    customerSettings,
  }),
}

const reducer = generateReducer(adminVatSettingsInitialState, reductions)

export default reducer
