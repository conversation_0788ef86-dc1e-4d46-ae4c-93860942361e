import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/paymentModulesActions"

const initialState = {
  initialValues: {},
  modalVisible: false,
  paymentModules: [],
  searchOptions: {},
}

const reductions = {
  [ActionTypes.changeSearchOptions]: (state, searchOptions) => ({
    ...state,
    searchOptions,
  }),
  [ActionTypes.get[1]]: (
    state,
    { currentPage, data, pageSize, totalCount }
  ) => ({
    ...state,
    paymentModules: data,
    totalCount,
    searchOptions: {
      ...state.searchOptions,
      page: currentPage,
      pageSize: pageSize,
    },
  }),
  [ActionTypes.get[2]]: (state) => ({
    ...state,
    paymentModules: [],
    totalCount: 0,
  }),
  [ActionTypes.displayModal]: (
    state,
    { modalVisible, initialValues = {} }
  ) => ({
    ...state,
    modalVisible,
    initialValues,
  }),
  [ActionTypes.update[1]]: (state) => ({
    ...state,
    modalVisible: false,
    initialValues: {},
  }),
}

export default generateReducer(initialState, reductions)
