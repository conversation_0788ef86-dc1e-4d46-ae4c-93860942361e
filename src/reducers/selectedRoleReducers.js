import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/selectedRoleActions"

const initialState = {
  controller: [],
  operations: [],
  role: {},
  list: {
    data: [],
  },
  errors: [],
  searchOptions: {},
}

const reductions = {
  [ActionTypes.createRole[1]]: (state, payload) => ({
    ...state,
  }),

  [ActionTypes.getAll[1]]: (state, payload) => ({
    ...state,
    list: payload,
  }),

  [ActionTypes.getRoles[1]]: (state, payload) => ({
    ...state,
    list: payload,
  }),

  [ActionTypes.getRoleAndOperations[0]]: (state, payload) => ({
    ...state,
    controller: [],
    operations: [],
    role: {},
    errors: [],
  }),

  [ActionTypes.getRoleAndOperations[1]]: (state, payload) => ({
    ...state,
    controller: payload[0],
    operations: payload[1],
    role: payload[2],
  }),

  [ActionTypes.getController[1]]: (state, payload) => ({
    ...state,
    controller: payload,
  }),

  [ActionTypes.getOperations[1]]: (state, payload) => ({
    ...state,
    operations: payload,
  }),

  [ActionTypes.updateOperations[0]]: (state, payload) => ({
    ...state,
    errors: [],
  }),

  [ActionTypes.updateOperations[1]]: (state, payload) => ({
    ...state,
    operations: payload.permissions,
  }),

  [ActionTypes.updateOperations[2]]: (state, payload) => ({
    ...state,
    errors: Array.isArray(payload) ? payload : [payload],
  }),

  [ActionTypes.updateTitle[0]]: (state, payload) => ({
    ...state,
    errors: [],
  }),

  [ActionTypes.updateTitle[1]]: (state, payload) => ({
    ...state,
    role: payload,
  }),

  [ActionTypes.updateRoleTitleAndPermissioins[0]]: (state, payload) => ({
    ...state,
    errors: [],
  }),

  [ActionTypes.updateRoleTitleAndPermissioins[2]]: (state, { payload }) => {
    return {
      ...state,
      errors: Array.isArray(payload) ? payload : [payload],
    }
  },

  [ActionTypes.deleteRole[1]]: (state, payload) => ({
    ...state,
  }),

  [ActionTypes.changeSearchOptions]: (state, payload) => ({
    ...state,
    searchOptions: payload,
  }),
}

export default generateReducer(initialState, reductions)
