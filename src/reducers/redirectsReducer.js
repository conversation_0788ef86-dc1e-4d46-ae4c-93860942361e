import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/redirectsActions"

const initialState = {
  moduleLockedByPaymentTriggerModal: false,
}

const reductions = {
  [ActionTypes.moduleLockedByPaymentTriggerModal]: (
    state,
    moduleLockedByPaymentTriggerModal
  ) => ({
    ...state,
    moduleLockedByPaymentTriggerModal,
  }),
}

export default generateReducer(initialState, reductions)
