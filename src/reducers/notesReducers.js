import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/notesActions"

const initialState = {
  data: [],
  visible: false,
}

const reductions = {
  [ActionTypes.display]: (state, payload) => ({
    ...state,
    ...payload,
  }),
  [ActionTypes.get[1]]: (state, payload) => ({
    ...state,
    ...payload,
  }),
}

export default generateReducer(initialState, reductions)
