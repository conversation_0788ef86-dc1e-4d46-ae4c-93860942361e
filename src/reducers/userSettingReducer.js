import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/userSettingActions"

/**
 * Initial state for the userSettingReducer.
 * @type {{
 *   availableNotifications: any[],
 *   allUserTemplates: any[],
 *   socialNetworks: any[],
 *   socialNetworkLinks: string,
 * }}
 */
const initialState = {
  availableNotifications: [],
  allUserTemplates: [],
  socialNetworks: [],
}

export default generateReducer(initialState, {
  [ActionTypes.getNotifications[1]](state, payload) {
    return { ...state, ...payload }
  },
  [ActionTypes.updateNotification](state, { item }) {
    return {
      ...state,
      availableNotifications: state.availableNotifications.map((i) => {
        if (i.id === item.id) {
          return { ...item }
        }
        return i
      }),
    }
  },
  [ActionTypes.updateUserTemplate](state, { item }) {
    return {
      ...state,
      allUserTemplates: state.allUserTemplates.map((templ) => {
        if (templ.lost_mailer_template_id === item.lost_mailer_template_id) {
          return { ...templ, ...item }
        }
        return templ
      }),
    }
  },
  [ActionTypes.getSocialNetworks[1]]: (state, networks) => ({
    ...state,
    socialNetworks: networks,
  }),
  [ActionTypes.deleteSocialNetwork[1]]: (state, networks) => ({
    ...state,
    socialNetworks: networks,
  }),
})
