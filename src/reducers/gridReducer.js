import findIndex from "lodash/findIndex"
import generateReducer from "utils/generateReducer"
import { types as gridActionTypes } from "actions/gridActions"
import { types as searchParamsTypes } from "actions/searchGridActions"

const initialState = {
  data: [],
  loadingParams: false,
  loading: false,
  pagination: {},
  searchOptions: {},
  prevSearchOptions: {},
  selectedProducts: [],
}

const reductions = {
  [gridActionTypes.clearProductsForFilter](state) {
    return {
      ...state,
      productsList: {
        data: [],
      },
    }
  },
  [gridActionTypes.getProductsForFilter[1]](
    state,
    { marketPlaces, productsList }
  ) {
    return { ...state, marketPlaces, productsList }
  },
  [gridActionTypes.get_products[0]](state, payload) {
    return { ...state, loading: true }
  },
  [gridActionTypes.get_products[1]](state, payload) {
    const {
      useSelectedList,
      selectedTotalCount,
      searchOptions,
      data: stateData,
    } = state
    const { currentPage, pageSize, totalCount } = payload
    let { data } = payload
    if (useSelectedList && !selectedTotalCount) {
      data = data.map(({ id, ...other }) => ({
        ...other,
        id,
        selected: Boolean(
          (stateData.find(({ id: prevId }) => id === prevId) || {}).selected
        ),
      }))
    }
    return {
      ...state,
      data,
      totalCount,
      loading: false,
      useSelectedList: false,
      selectedProducts: [],
      searchOptions: {
        ...searchOptions,
        page: currentPage,
        pageSize: pageSize,
      },
    }
  },
  [gridActionTypes.get_products[2]]: (state) => ({
    ...state,
    data: [],
    totalCount: 0,
    loading: false,
    selectedProducts: [],
  }),
  [gridActionTypes.changeSearchOptions]: (state, searchOptions) => {
    return {
      ...state,
      searchOptions,
      prevSearchOptions: state.searchOptions,
    }
  },
  [gridActionTypes.init[0]](state) {
    return { ...state, loadingParams: true }
  },
  [gridActionTypes.init[1]](state, payload) {
    const { marketPlaces, strategies } = payload
    return { ...state, marketPlaces, strategies, loadingParams: false }
  },
  [searchParamsTypes.init](state, { queryParams }) {
    return { ...state, queryParams }
  },
  [gridActionTypes.updateGridRow](state, gridObject) {
    const { data } = state
    const { id } = gridObject

    const index = findIndex(data, ({ id: originalId }) => originalId === id)

    const newData = [...data]
    if (index !== -1) {
      newData.splice(index, 1, gridObject)
    }

    return { ...state, data: newData }
  },
  [gridActionTypes.selectUnselectProduct](state, product) {
    const { data } = state

    const newData = data.map((oldProduct) => {
      if (oldProduct.id === product.id) {
        return { ...oldProduct, selected: !oldProduct.selected }
      }
      return oldProduct
    })

    return {
      ...state,
      data: newData,
    }
  },
  [gridActionTypes.selectUnselectProductRange](state, products) {
    const { data } = state

    const newData = data.map((oldProduct) => {
      if (products.some((product) => product.id === oldProduct.id)) {
        return { ...oldProduct, selected: !oldProduct.selected }
      }
      return oldProduct
    })

    return {
      ...state,
      data: newData,
    }
  },
  [gridActionTypes.selectAll](state, { selected: oldSelectedAll }) {
    const { data } = state

    return {
      ...state,
      data: data.map((o) => ({ ...o, selected: oldSelectedAll })),
    }
  },
  [gridActionTypes.selectTotalCount](state, { selected }) {
    return {
      ...state,
      selectedTotalCount: selected,
    }
  },
  [gridActionTypes.useSelectedList](state, { useSelectedList }) {
    return {
      ...state,
      useSelectedList,
    }
  },
  [gridActionTypes.onBulkActions](state, { visible }) {
    return { ...state, bulkActions: visible }
  },
  [gridActionTypes.onGetProductData[1]](state, payload) {
    const { data } = state
    const { id: productId } = payload
    const newData = data.map((oldProduct) =>
      oldProduct.id !== productId ? oldProduct : payload
    )
    return {
      ...state,
      data: newData,
    }
  },
  [gridActionTypes.showMinMaxFrom](state, { formType, isVisible, productId }) {
    return {
      ...state,
      minMaxFrom: isVisible
        ? {
            id: productId,
            type: formType,
          }
        : {},
    }
  },
  [gridActionTypes.showEditNumberForm](
    state,
    { formType, isVisible, productId }
  ) {
    return {
      ...state,
      editNumberForm: isVisible
        ? {
            id: productId,
            type: formType,
          }
        : {},
    }
  },
  [gridActionTypes.onUpdateProduct[1]](state, payload) {
    const { data } = state
    const { id: productId } = payload
    const newData = data.map((oldProduct) =>
      oldProduct.id !== productId ? oldProduct : payload
    )
    return {
      ...state,
      data: newData,
    }
  },
}

export default generateReducer(initialState, reductions)
