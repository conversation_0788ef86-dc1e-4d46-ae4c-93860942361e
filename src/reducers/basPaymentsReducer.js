import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/basPaymentsActions"

const initialState = {
  dateRange: [],
  paymentMatrix: [],
  paymentPeriods: [],
}

const reductions = {
  [ActionTypes.getBasPaymentPlans[1]]: (
    state,
    { dateRange, paymentMatrix, paymentPeriods }
  ) => ({
    ...state,
    dateRange,
    paymentMatrix,
    paymentPeriods,
  }),
  [ActionTypes.getBasPaymentPlans[2]]: (state) => state,
}

export default generateReducer(initialState, reductions)
