import generateReducer from "utils/generateReducer"
import { types as ActionTypes } from "actions/productCompetitorsActions"

const initialState = {
  modalVisible: false,
  data: [],
}

const reductions = {
  [ActionTypes.getProductCompetitors[1]]: (state, payload) => ({
    ...state,
    data: [],
  }),
  [ActionTypes.getProductCompetitors[1]]: (state, { data }) => ({
    ...state,
    data,
  }),
  [ActionTypes.showProductCompetitorsModal]: (state, { visible }) => ({
    ...state,
    modalVisible: visible,
  }),
}

export default generateReducer(initialState, reductions)
