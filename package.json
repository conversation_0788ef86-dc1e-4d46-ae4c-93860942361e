{"name": "@develop/layout-app", "version": "1.219.0-dev.1", "license": "UNLICENSED", "dependencies": {"@ant-design/compatible": "1.1", "@ant-design/icons": "4.7", "@hookform/resolvers": "3.1.1", "@stripe/react-stripe-js": "1.7", "@stripe/stripe-js": "1.29", "@types/jest": "26.0", "@types/lodash.debounce": "4.0.7", "@types/node": "15.14", "@types/react": "17.0", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "17.0", "antd": "4.18.9", "babel-plugin-import": "1.13", "capitalize": "2.0", "classnames": "2.3", "connected-react-router": "6.9", "date-fns": "2.30.0", "date-fns-tz": "2.0.0", "detect-browser": "5.3", "draggable": "4.2", "formik": "2.2.9", "history": "4.10.1", "html2canvas": "1.4.1", "husky": "6.0", "ibantools": "3.3", "idb": "6.1", "js-sha256": "0.9", "lint-staged": "11.2", "localforage": "1.10", "lodash": "4.17", "lodash.debounce": "4.0", "mobile-detect": "1.4", "moment": "2.29", "moment-timezone": "0.5", "node-sass": "6.0", "prettier": "2.6", "prop-types": "15.8", "quill-emoji": "^0.2.0", "react": "17.0.2", "react-app-polyfill": "2.0", "react-avatar-editor": "^13.0.0", "react-copy-to-clipboard": "5.1", "react-custom-scrollbars": "4.2", "react-device-detect": "1.17", "react-dom": "17.0.2", "react-draggable": "4.4", "react-dropzone": "11.7", "react-focus-lock": "2.8", "react-helmet": "6.1", "react-hook-form": "7.45.0", "react-html-parser": "2.0", "react-intl": "5.21", "react-intl-tel-input": "8.2", "react-is": "17.0", "react-portal": "4.2", "react-quill": "^2.0.0", "react-redux": "7.2", "react-resizable": "3.0", "react-router": "5.2.1", "react-router-dom": "5.2.1", "react-scripts": "4.0.3", "react-share": "4.4", "react-sizes": "2.0", "react-sortable-hoc": "2.0", "react-test-renderer": "17.0", "react-text-ellipsis": "1.6", "react-virtuoso": "4.7.7", "recharts": "2.1", "redux": "4.2", "redux-devtools-extension": "2.13", "redux-persist": "6.0", "redux-thunk": "2.4", "reselect": "4.1", "sass": "1.51", "smoothscroll-polyfill": "0.4", "styled-components": "5.3", "typescript": "4.4.4", "uuid": "8.3", "weekstart": "1.1", "xlsx": "0.17", "yup": "0.32"}, "scripts": {"start": "react-scripts start", "start:rc": "env-cmd -f .env.local.rc react-scripts start", "build": "react-scripts build", "test": "react-scripts test --passWithNoTests", "lint": "npx eslint . -c .eslintrc.json", "lint:tsc": "tsc", "lint:eslint": "eslint ./src", "lint:prettier:check": "prettier --check ./src", "lint:prettier:fix": "prettier --write ./src", "lint:fix": "npx eslint . --fix --ext=tsx --ext=ts -c .eslintrc.json", "eject": "react-scripts eject", "build_static": "rm -rf build/static_pages && parcel build static/*.html --out-dir build/static_pages --no-minify --no-cache --no-content-hash static/index.js --no-content-hash static/index_new.scss --no-source-maps", "dev_static": "parcel static/*.html", "build-lib": "babel src -d dist --extensions \".js\",\".jsx\",\".ts\",\".tsx\" --copy-files"}, "publishConfig": {"access": "public"}, "files": ["dist"], "eslintConfig": {"extends": "./.eslintrc.json"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "last 3 edge version"]}, "lint-staged": {"src/**/*.{js,jsx,json}": ["prettier --write", "git add"], "src/**/*.{js,jsx}": ["eslint --max-warnings=0"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "devDependencies": {"@babel/cli": "^7.15.4", "@babel/core": "^7.15.5", "@babel/plugin-syntax-jsx": "^7.14.5", "@babel/preset-env": "^7.14.5", "@babel/preset-react": "^7.14.5", "@babel/preset-typescript": "^7.15.0", "@develop/fe-library": "develop", "@develop/main-app": "develop", "@testing-library/react": "^10.0.2", "@types/capitalize": "^1.0.1", "@types/classnames": "^2.2.9", "@types/lodash": "^4.14.149", "@types/mocha": "^7.0.1", "@types/moment": "^2.13.0", "@types/react-custom-scrollbars": "^4.0.6", "@types/react-portal": "^4.0.2", "@types/react-redux": "^7.1.7", "@types/react-router-dom": "^5.1.5", "@types/react-sizes": "^2.0.0", "babel-plugin-module-resolver": "^4.1.0", "env-cmd": "^10.1.0", "eslint-config-prettier": "8.5.0", "eslint-config-react-app": "6.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-simple-import-sort": "10.0.0", "eslint-plugin-testing-library": "5.10.2", "eslint-plugin-unused-imports": "2.0.0", "jest-image-snapshot": "^4.0.0", "jsdom-screenshot": "^3.2.0"}, "resolutions": {"@types/react": "17.0.2", "@types/react-dom": "17.0.2"}, "packageManager": "yarn@3.1.0"}