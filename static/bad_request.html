<!DOCTYPE html>
<!-- https://app.zeplin.io/project/5d89e6596990b116f47a2838/screen/5efb3c989471429f868a99b9 -->
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
  </head>
  <link rel="stylesheet" href="index_new.scss" />
  <body>
    <form class="text form_wrapper bad_request">
      <img
        src="img/logo.png"
        alt="Logo"
        srcset="img/<EMAIL> 1024w, img/<EMAIL> 1920w"
        class="logo"
      />
      <div class="bad-request-title">Bad Request (#400)</div>
      <div class="bad-request-text">
        The request is missing a required parameter, includes an invalid
        parameter value, includes a parameter more than once, or is otherwise
        malformed. The code_challenge must be between 43 and 128 characters.
      </div>
      <div class="bad-request-explanation">
        The above error occurred while the Web server was processing your
        request. Please contact us if you think this is a server error. Thank
        you.
      </div>

      <!-- FOOTER #######################################################  -->
      <div class="form_wrapper__footer">
        <div class="text notice-text">
          <span>
            <a class="text link" target="_blank" href="#">Legal Notice.</a>
            Copyright © 2019 SellerLogic. All rights reserved.
          </span>
        </div>

        <ul class="terms">
          <li><a class="text link" target="_blank" href="#">Terms and Conditions</a></li>
          <li><a class="text link" target="_blank" href="#">Privacy Policy</a></li>
          <li><a class="text link" target="_blank" href="#">Imprint</a></li>
        </ul>

        <div class="footer-right">
          <a href="tel:+492115425410" class="text link phone"
            >+49 (0) 211 542 541 0</a
          >
          <ul class="languages">
            <li>
              <a href="#" class="text link">
                <img src="img/en-lang-icon.png" class="language-flag" alt="" />
                English</a
              >
            </li>
            <li>
              <a href="#" class="text link">
                <img src="img/de-lang-icon.png" class="language-flag" alt="" />
                Deutsch</a
              >
            </li>
            <li>
              <a href="#" class="text link">
                <img src="img/fr-lang-icon.png" class="language-flag" alt="" />
                Français</a
              >
            </li>
            <li>
              <a href="#" class="text link"
                ><img src="img/es-lang-icon.png" class="language-flag" alt="" />
                Español</a
              >
            </li>
          </ul>
          <div class="text selected-language">
            <img
              src="img/United-Kingdom-Flag.gif"
              class="language-flag"
              alt=""
            />
            English
          </div>
        </div>
      </div>
      <!-- FOOTER #######################################################  -->
    </form>
    <script src="index.js"></script>
  </body>

</html>
