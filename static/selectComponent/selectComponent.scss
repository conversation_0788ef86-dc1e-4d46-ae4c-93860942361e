@import 'assets/styles/variables.scss';
.visually-hidden.visually-hidden.visually-hidden.visually-hidden.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  border: 0;
  clip: rect(0 0 0 0);
  min-width: unset;
}

.custom-select.custom-select.custom-select {
  align-items: center;
  display: flex;
  position: relative;

  .arrow {
    border: solid $icon_clicable;
    border-width: 0 1px 1px 0;
    display: inline-block;
    padding: 4px;
    position: absolute;
    right: 10px;
    top: 14px;
    transform: rotate(45deg);
  }

  &.open {
    .arrow {
      top: 18px;
      transform: rotate(-135deg);
    }
  }
}

.select-dropdown {
  background-color: $main_bg;
  border-radius: 3px;
  box-shadow: 0 1px 8px 0 rgba(0, 0, 0, 0.3);
  display: none;
  left: 0;
  list-style: none;
  margin: 0;
  padding: 4px 0;
  position: absolute;
  top: calc(100% + 5px);
  width: 100%;

  &.open {
    display: block;
  }
}

.select-option {
  align-items: center;
  display: flex;
  height: 32px;
  padding: 0 10px;

  &:hover {
    background-color: #EBEDF0;
  }

  &.selected {
    background-color: #F6F8F9;
    color: $text_link;
  }
}
