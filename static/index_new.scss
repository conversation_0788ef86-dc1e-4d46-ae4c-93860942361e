@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;1,100;1,300;1,400&display=swap');

@font-face {
  font-family: 'League Gothic';
  src: url('fonts/LeagueGothic-Italic.eot');
  src: url('fonts/LeagueGothic-Italic.eot?#iefix') format('embedded-opentype'),
    url('fonts/LeagueGothic-Italic.woff2') format('woff2'),
    url('fonts/LeagueGothic-Italic.woff') format('woff'),
    url('fonts/LeagueGothic-Italic.ttf') format('truetype'),
    url('fonts/LeagueGothic-Italic.svg#LeagueGothic-Italic') format('svg');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'League Gothic';
  src: url('fonts/LeagueGothic-CondensedItalic.eot');
  src: url('fonts/LeagueGothic-CondensedItalic.eot?#iefix')
      format('embedded-opentype'),
    url('fonts/LeagueGothic-CondensedItalic.woff2') format('woff2'),
    url('fonts/LeagueGothic-CondensedItalic.woff') format('woff'),
    url('fonts/LeagueGothic-CondensedItalic.ttf') format('truetype'),
    url('fonts/LeagueGothic-CondensedItalic.svg#LeagueGothic-CondensedItalic')
      format('svg');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'League Gothic';
  src: url('fonts/LeagueGothic-Regular.eot');
  src: url('fonts/LeagueGothic-Regular.eot?#iefix') format('embedded-opentype'),
    url('fonts/LeagueGothic-Regular.woff2') format('woff2'),
    url('fonts/LeagueGothic-Regular.woff') format('woff'),
    url('fonts/LeagueGothic-Regular.ttf') format('truetype'),
    url('fonts/LeagueGothic-Regular.svg#LeagueGothic-Regular') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'League Gothic';
  src: url('fonts/LeagueGothic-CondensedRegular.eot');
  src: url('fonts/LeagueGothic-CondensedRegular.eot?#iefix')
      format('embedded-opentype'),
    url('fonts/LeagueGothic-CondensedRegular.woff2') format('woff2'),
    url('fonts/LeagueGothic-CondensedRegular.woff') format('woff'),
    url('fonts/LeagueGothic-CondensedRegular.ttf') format('truetype'),
    url('fonts/LeagueGothic-CondensedRegular.svg#LeagueGothic-CondensedRegular')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
* {
  padding: 0;
  margin: 0;
}
html {
  height: 100%;
  box-sizing: border-box;
  background-color: #f6f8f9;
}
body {
  min-height: 100%;
  margin: 0;
  padding: 0;
  position: relative;
}
.text {
  font-family: Roboto;
  font-size: 13px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: #676e7a;
}
.text.error {
  color: #000000;
  display: flex;
  padding: 17px 10px 17px 20px;
}
.form_wrapper {
  .bad-request-title {
    font-family: Roboto, sans-serif;
    font-size: 24px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #f89191;
    text-align: center;
    padding: 20.1px 0 10px 0;
  }
  .bad-request-text {
    font-family: Roboto, sans-serif;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.29;
    letter-spacing: normal;
    color: #000000;
    border-radius: 5px;
    background-color: rgba(248, 145, 145, 0.2);
    padding: 20px;
    margin: 0 10px;
  }
  .bad-request-explanation {
    font-family: Roboto, sans-serif;
    font-size: 13px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    color: #666666;
    padding: 20px 10px 10px;
    text-align: center;
  }
  .logo {
    width: 212.5px;
    height: 56.3px;
    display: block;
    margin: 0 auto;
  }
  .error-text {
    color: #f89191;
    padding: 5px 0 15px;
    display: none;
  }
  .inputs_container {
    position: relative;
    height: 51vh;
    padding: 0 30px;
    display: flex;
    flex-direction: column;
    flex: 1;
    .link {
      color: #0055cc;
      cursor: pointer;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
      &:active {
        color: #0055cc;
      }
    }
  }
  &.two_factory_auth,
  &.form_wrapper.two_factory_auth_request {
    .inputs_container {
      height: auto;
    }
  }
  .form-label {
    display: block;
    min-height: 40px;
    margin-bottom: 15px;
    .form-input {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      font-size: 12px;
      display: block;
      min-width: 100%;
      box-sizing: border-box;
      min-height: 40px;
      outline: none;
      padding: 0 10px;
      color: #666;
      border: solid 1px #d5dce0;
      border-radius: 4px;
      background-color: #fff;
      &:placeholder {
        color: #bdbdbd;
      }
      &:-webkit-autofill {
        -webkit-text-fill-color: #000 !important;
        &:hover {
          -webkit-text-fill-color: #000 !important;
        }
        &:focus {
          -webkit-text-fill-color: #000 !important;
        }
      }
      &::placeholder {
        color: #bdbdbd;
      }
    }
  }
  .form-label.error {
    margin-bottom: 0;
    .form-input {
      border: solid 1px #f89191;
    }
    + {
      .error-text {
        display: block;
      }
    }
  }
  .form-password {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    .checkbox {
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      font-size: 14px;
      line-height: 1.2;
      color: #000;
      input[type="checkbox"] {
        display: none;
        ~ {
          .checkmark {
            border: 1px solid #d9d9d9;
            display: inline-block;
            height: 16px;
            width: 16px;
            margin-right: 10px;
            border-radius: 4px;
          }
        }
        &:checked {
          ~ {
            .checkmark {
              background-image: url('img/<EMAIL>');
              background-size: 16px 16px;
              height: 16px;
              width: 16px;
              display: inline-block;
            }
          }
        }
      }
    }
    .forgot-password {
      font-size: 14px;
      color: #0055cc;
      color: #0055cc;
      cursor: pointer;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
      &:active {
        color: #0055cc;
      }
    }
  }
  .radio-btn-container {
    display: flex;
    flex-flow: column;
    width: 100%;
    margin: 10px 0;
    label {
      display: flex;
      align-items: center;
      margin: 10px 20px;
      color: #666666;
      input {
        margin: 0 10px 0 0;
      }
    }
    .radio-item {
      display: inline-block;
      position: relative;
      padding: 0 3px;
      input[type="radio"] {
        display: none;
        &:checked {
          + {
            label {
              &:before {
                border: 1px solid #0055cc;
              }
              &:after {
                border-radius: 11px;
                width: 6px;
                height: 6px;
                position: absolute;
                top: 14px;
                left: 7px;
                content: " ";
                display: block;
                background: #0055cc;
              }
            }
          }
        }
      }
      label {
        color: #666;
        font-weight: normal;
        &:before {
          content: " ";
          display: inline-block;
          position: absolute;
          top: 20px;
          left: 3px;
          margin: -10px 5px 0 0;
          width: 12px;
          height: 12px;
          border-radius: 11px;
          border: 1px solid #bdbdbd;
          background-color: transparent;
        }
      }
    }
  }
  .radio-btn-container-error {
    margin: 10px 0;
  }
  .alt_way_container {
    padding: 0 140px;
    display: flex;
    flex-flow: column;
    align-items: center;
    .alt_way_error {
      margin-top: 9px;
      width: 100%;
      border-radius: 4px;
      border: solid 1px rgba(248, 145, 145, 0.3);
      background-color: rgba(248, 145, 145, 0.2);
    }
    a {
      color: #0055cc;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    .alt-way-auth-text {
      color: #000;
    }
    .code-request {
      margin-top: 10px;
      border: none;
      background-color: rgba(0, 85, 204, 0.2);
      .code-request-text {
        font-weight: 500;
        color: #0055cc;
        padding: 12px;
      }
    }
    .request-error {
      .request-error-text {
        color: #eb5757;
        font-weight: 500;
        padding: 12px;
      }
    }
    .auth-btn {
      width: 145px;
    }
  }
  .alt_way_buttons {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
  }
  .auth-btn {
    display: block;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    min-width: 145px;
    border: none;
    height: 40px;
    cursor: pointer;
    margin: 0 auto;
    box-sizing: border-box;
    outline: none;
    padding: 0 15px;
    &.set-password-btn {
      margin-top: 10px;
    }
    &.recover-password-btn {
      margin-top: 5px;
      min-width: 165px;
    }
  }
  .auth-btn.submit {
    background-color: #0055cc;
    color: #ffffff;
    &:hover {
      text-decoration: none;
      opacity: 0.6;
    }
  }
  .auth-btn.back {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    color: #000000;
    border: solid 1px #d5dce0;
    transition: 0.2s;
    &:hover {
      text-decoration: none;
      border: solid 1px #0055cc;
      color: #0055cc;
    }
  }
  .alternative-way-text {
    font-size: 14px;
    line-height: 1.61;
    color: #0055cc;
    padding-bottom: 21px;
    text-align: center;
    display: flex;
    flex-flow: column;
    padding: 30px 0;
    height: 52px;
    align-items: center;
    justify-content: space-between;
    .link {
      font-size: 14px;
      font-weight: bold;
    }
  }
  .sign-up-text {
    font-size: 16px;
    font-weight: bold;
    line-height: 1.61;
    color: #000000;
    padding-top: 30px;
    padding-bottom: 21px;
    text-align: center;
    a {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .proceed-text {
    font-size: 13px;
    color: #bdbdbd;
    text-align: center;
    position: absolute;
    bottom: 40px;
    left: 0;
    width: 100%;

    &.proceed-text-alt-way {
      box-sizing: border-box;
      padding: 0 30px;
    }

    .link {
      color: #bdbdbd;
    }
  }
}
.form_wrapper__two_factory_auth {
  font-size: 24px;
  color: #000000;
  padding-bottom: 17px;
  padding-top: 53px;
  text-align: center;
}
.form_wrapper__header,
.form_wrapper__sign-in {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  padding-top: 17.1px;
  padding-bottom: 10px;
  text-align: center;
}
.form_wrapper__footer {
  background-color: #ffffff;
  height: 56px;
  padding: 0px 20px 0 20px;
  .notice-text {
    font-size: 10px;
    line-height: 1.1;
    color: #666666;
    width: 240px;
    .link {
      color: #0055cc;
      cursor: pointer;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
      &:active {
        color: #0055cc;
      }
    }
  }
  .terms {
    list-style: none;
    text-align: center;
    padding-bottom: 15px;
    li {
      display: inline-block;
      font-size: 10px;
      line-height: 1.8;
      color: #000000;
      margin: 0 9px;
      .link {
        color: #0055cc;
        cursor: pointer;
        text-decoration: none;
        &:hover {
          text-decoration: underline;
        }
        &:active {
          color: #0055cc;
        }
      }
    }
  }
  .footer-right {
    display: flex;
    .phone {
      flex: 1;
      font-size: 12px;
      line-height: 1.8;
      text-decoration: none;
      color: #0055cc;
      cursor: pointer;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
      &:active {
        color: #0055cc;
      }
    }
    .phone.phone {
      background-image: url('img/smartphone.svg');
      background-repeat: no-repeat;
      background-position: 0 50%;
      padding-left: 18px;
      margin-right: 10px;
      display: flex;
      align-items: center;
    }
    .languages {
      display: none;
      list-style: none;
      .link {
        font-size: 10px;
        line-height: 1.8;
        text-decoration: none;
        color: #0055cc;
        cursor: pointer;
        text-decoration: none;
        &:hover {
          text-decoration: underline;
        }
        &:active {
          color: #0055cc;
        }
      }
      .active {
        color: #666666;
      }
      li {
        padding: 5px 5px;
        text-align: left;
        .link {
          display: flex;
          align-items: center;
        }
        .language-flag {
          display: block;
          width: 16px;
          height: 16px;
          border-radius: 100%;
          margin-right: 5px;
        }
      }
    }
    .languages.opened {
      display: block;
      background-color: #fff;
      position: absolute;
      bottom: 35px;
      right: 0;
      padding: 10px;
      z-index: 200;
      border: 1px solid #ccc;
      text-align: center;
    }
    .selected-language {
      flex: 1;
      font-size: 12px;
      color: #343840;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .language-flag {
        display: block;
        width: 16px;
        height: 16px;
        border-radius: 100%;
        margin-right: 5px;
      }
      &:after {
        content: '';
        background-image: url('img/dropdown_icon.png');
        background-size: cover;
        background-repeat: no-repeat;
        display: block;
        width: 10px;
        height: 10px;
        margin-left: 7px;
        cursor: pointer;
      }
    }
  }
}
.form_wrapper__footer.big-footer {
  display: none;
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  align-items: center;
  grid-template-columns: repeat(3, 1fr);
  .notice-text {
    text-align: left;
    .link {
      font-size: 10px;
    }
  }
  > .terms {
    li {
      .link {
        font-size: 10px;
        &:hover {
          color: #0055cc;
        }
      }
    }
  }
}
.forgot-password__link-wrapper {
  color: #000;
  font-size: 14px;
  font-weight: 500;
  margin-top: 60px;
  text-align: center;

  .sign-in-link {
    font-size: 14px;
    font-weight: 500;
  }
}
.forgot-password-page.forgot-password-page {
  min-height: unset;
}
@media all and (min-width: 320px) and (max-width: 374px) {
  .text.error {
    padding: 10px 10px 12px 10px;
  }
  .form_wrapper {
    .proceed-text {
      bottom: 0;
      left: 10vw;
      width: 80%;
    }
    .proceed-text-alt-way {
      bottom: 5px;
      left: 10vw;
      width: 80%;
    }
    .proceed-text-error {
      bottom: 6.5px;
    }
    .proceed-text-two-factory {
      bottom: 50px;
    }
    .proceed-text-alternate {
      bottom: 90px;
    }
  }

  .forgot-password__link-wrapper {
    margin-top: 30px;
  }
}
@media all and (min-width: 375px) and (max-width: 767px) {
  .text.error {
    padding: 12px 10px 0 10px;
  }
  .form_wrapper {
    .proceed-text {
      bottom: 10px;
      left: 10vw;
      width: 80%;
    }
  }

  .forgot-password__link-wrapper {
    margin-top: 30px;
  }
}
@media all and (min-width: 768px) and (max-width: 1023px) {
  .text.error {
    padding: 20px;
  }
  .form_wrapper {
    .alt_way_container {
      .alt_way_error {
        margin-top: 18px;
      }
      padding: 0 106px;
    }
  }

  html {
    min-height: 820px;
  }
}
@media all and (min-width: 320px) and (max-width: 767px) {
  .form_wrapper {
    height: 82vh;
    padding-top: 53px;
    width: 100%;
    display: block;
    box-shadow: none;
    border: none;
    border-bottom: 1px solid #ebedf0;
    font-weight: bold;
    background-color: #ffffff;
    border-bottom: none;

    .radio-btn-container {
      margin: 20px 0;
      width: 280px;
    }
    .alt_way_container.alt_way_container {
      position: relative;
      height: auto;
      display: flex;
      flex-flow: column;
      align-items: center;
      padding: 0 20px;
      min-height: 285px;
      border-bottom: 105px solid transparent;
      .alt_way_error {
        text-align: justify;
        width: 280px;
      }
      .auth-btn {
        width: 135px;
      }
      .proceed-text-alt-way {
        margin-top: 20px;
        padding: 0;
        position: static;
        width: 100%;
      }
    }
    .alt_way_buttons {
      width: 280px;

      .auth-btn {
        min-width: 135px;
      }
    }
    .auth-btn {
      width: 100%;
    }
    .alternative-way-text {
      padding-top: 42.5px;
    }
    .sign-up-text {
      padding: 15px 0 0 0;
      font-size: 16px;
    }
    .proceed-text {
      .link {
        font-size: 12px;
        text-decoration: none;
      }
    }
    &.two_factory_auth,
    &.two_factory_auth_request {
      height: auto;

      .inputs_container.inputs_container {
        height: 51vh;

        .proceed-text-alternate {
          padding: 0;
          position: absolute;
          bottom: 20px;
        }
      }
    }
  }
  .form_wrapper__footer {
    position: absolute;
    left: 0;
    bottom: 0;
    flex-flow: column;
    align-items: center;
    height: 16vh;
    width: 100%;
    padding: 0;
    border-top: 1px solid #ebedf0;
    .notice-text {
      margin: 15px auto;
      display: flex;
      flex-flow: row;
      align-items: baseline;
      justify-content: center;
      font-size: 12px;
      line-height: 1.5;
      .link {
        font-size: 12px;
      }
      span {
        width: 240px;
        text-align: center;
      }
    }
    .terms {
      .link {
        font-size: 12px;
      }
    }
    .footer-right {
      margin: 0 20px;
    }
  }
  html {
    background-color: white;
  }
}
@media all and (min-width: 375px) and (max-width: 413px) {
  .form_wrapper {
    padding: 50px 0;
    height: 74vh;
  }
}
@media all and (min-width: 375px) and (max-width: 413px) and (min-height: 812px) {
  .form_wrapper {
    padding-top: 100px;
  }
}
@media all and (min-width: 414px) and (max-width: 767px) {
  .form_wrapper {
    padding: 50px 0;
    height: 74vh;
  }
}
@media all and (min-width: 767px) {
  .form_wrapper {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #ffffff;
    position: absolute;
    left: 50%;
    top: 47%;
    transform: translateY(-50%) translateX(-50%);
    box-shadow: 0 10px 30px 0 rgba(21, 27, 38, 0.15);
    border-radius: 10px;
    min-height: auto;
    font-weight: bold;
  }
}
@media all and (min-width: 320px) and (max-height: 568px) {
  .form_wrapper.with_error {
    padding-top: 20px;
  }
  .form_wrapper__two_factory_auth {
    padding-top: 20px;
  }
  .form_wrapper {
    .inputs_container {
      height: 57vh;
    }
  }
}

@media all and (min-width: 320px) and (max-width: 568px) {
  .form_wrapper.registration-comp {
    width: auto;
    padding: 0 20px;
    padding-top: 40px;

    .form_wrapper__header {
      font-size: 18px;
      padding-top: 40px;
      padding-bottom: 30px;
    }

    .logo {
      width: 210px;
      height: 54px;
    }

    .form_wrapper__confirmation {
      text-align: center;
      padding-bottom: 30px;
    }

    .form_wrapper__sign-in-button-wrapper {
      height: 42px;
      text-align: center;

      .sign-in-link {
        background-color: #0055cc;
        border-radius: 5px;
        color: #fff;
        display: inline-block;
        font-size: 14px;
        font-weight: 700;
        padding: 13px 25px;
        text-decoration: none;
        width: calc(100% - 50px);
      }
    }
  }
}

@media all and (min-width: 320px) and (max-width: 568px) {
  .form_wrapper.login,
  .form_wrapper.registration {
    min-height: 900px;
    padding-top: 40px;
    width: auto;

    .form_wrapper__header {
      font-size: 18px;
      padding-bottom: 20px;
      padding-top: 40px;
    }

    .logo {
      width: 210px;
      height: 54px;
    }

    .inputs_container {
      height: auto;
      padding: 0 20px;

      .form-label {
        margin-bottom: 10px;

        .form-input {
          height: 42px;
          min-height: unset;
        }

        &.error {
          margin-bottom: 0;
        }
      }

      .inputs_container__name-wrapper {
        display: flex;
        flex-direction: column;

        .name-wrapper__name-field-wrapper {
          display: flex;
          flex-direction: column;
          flex-grow: 1;
        }

        .form-label {
          flex-grow: 1;
          margin-right: 0;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .submit.auth-btn {
        height: auto;
        margin-bottom: 20px;
        margin-top: 0px;
        padding: 13px 25px;
        width: 100%;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 20px;

        .sso-label {
          color: #000;
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 10px;
          text-align: center;
        }

        .sso-providers {
          display: flex;
          justify-content: center;
          list-style: none;

          .sso-provider {
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 5px;
            box-sizing: border-box;
            height: 42px;
            margin-right: 10px;
            width: 42px;

            &:last-child {
              margin-right: 0;
            }

            &.google {
              background-image: url('img/sso-google.png');
              border: solid 1px #d5dce0;
            }

            &.facebook {
              background-image: url('img/sso-facebook.png');
              background-color: #0055cc;              ;
            }

            &.amazon {
              background-image: url('img/sso-amazon.png');
              background-color: #ffaa00;
            }

            &.apple {
              background-image: url('img/sso-apple.png');
              background-color: #000;
            }
          }
        }
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 30px;
        text-align: center;

        .sign-in-link {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .proceed-text {
        font-size: 12px;
        margin-bottom: 20px;
        position: static;
        width: 100%;
      }
    }
  }

  html {
    min-height: 950px;
  }
}

@media all and (min-width: 320px) and (max-width: 568px) {
  .form_wrapper.login.login {
    min-height: 700px;
    padding-top: 40px;
    width: auto;

    .form_wrapper__header {
      padding-top: 40px;
      padding-bottom: 20px;
    }

    .inputs_container {
      padding: 0 20px;

      .submit.auth-btn {
        margin-bottom: 20px;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 20px;
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 20px;
      }
    }
  }

  html {
    min-height: 700px;
  }

  .show-banner {
    min-height: 1050px;

    body {
      display: flex;
      flex-direction: column;
    }

    .form_wrapper.login.login {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      padding-bottom: 112px;

      .banner-wrapper {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        overflow: hidden;
        width: 100%;
        border-top: 1px solid #d5dce0;

        .banner {
          flex-grow: 1;
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}

@media all and (min-width: 768px) {
  .form_wrapper.two_factory_auth {
    padding-top: 161px;
    min-height: 722px;
    width: 603px;

    .alt_way_containe {
      margin-bottom: 100px;
    }

    .inputs_container {
      padding-bottom: 100px;
    }
  }
  .form_wrapper.two_factory_auth_request {
    padding-top: 161px;
    min-height: 722px;
    width: 603px;

    .alt_way_containe {
      margin-bottom: 100px;
    }

    .inputs_container {
      padding-bottom: 100px;
    }
  }
  .form_wrapper.with_error {
    padding-top: 133px;
    height: 722px;
    width: 603px;

    .alt_way_container,
    .inputs_container {
      margin-bottom: 100px;
    }
  }
  .form_wrapper.bad_request {
    width: 604px;
    height: 544px;
    padding-top: 80px;
  }
  .form_wrapper.error {
    height: auto;
    min-height: 484px;
  }
  .form_wrapper {
    .bad-request-title {
      font-size: 40px;
      padding-top: 40px;
      padding-bottom: 26px;
    }
    .bad-request-text {
      margin: 0 35px;
    }
    .bad-request-explanation {
      padding: 20px 70px;
    }
    .logo {
      width: 332px;
      height: 88px;
    }
    .inputs_container {
      padding: 0 104px;
    }
    .form-label {
      .form-input {
        min-height: 40px;
      }
    }
    .radio-btn-container {
      margin-top: 30px;
    }
    .alt_way_buttons {
      margin-top: 20px;
    }
    .sign-up-text {
      padding-bottom: 40px;
    }
  }
  .form_wrapper__two_factory_auth {
    padding-top: 50px;
    padding-bottom: 16px;
  }
  .form_wrapper__footer {
    display: none;
    .notice-text {
      padding-bottom: 0;
    }
    .terms {
      padding-bottom: 0;
      width: 260px;
      li {
        margin: 0 2px;
      }
    }
  }
  .form_wrapper__footer.big-footer {
    display: grid;
  }
}

@media all and (min-width: 569px) and (max-width: 768px) {
  .form_wrapper.registration-comp {
    width: 542px;
    padding: 80px 0;

    .form_wrapper__header {
      padding-top: 60px;
      padding-bottom: 40px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .form_wrapper__confirmation {
      text-align: center;
      padding-bottom: 40px;
    }

    .form_wrapper__sign-in-button-wrapper {
      height: 42px;
      text-align: center;

      .sign-in-link {
        background-color: #0055cc;
        border-radius: 5px;
        color: #fff;
        display: inline-block;
        font-size: 14px;
        font-weight: 700;
        padding: 13px 25px;
        text-decoration: none;
      }
    }
  }
}

@media all and (min-width: 569px) and (max-width: 768px) {
  .form_wrapper.login,
  .form_wrapper.registration {
    width: 581px;
    padding-top: 40px;

    .form_wrapper__header {
      padding-bottom: 20px;
      padding-top: 30px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .inputs_container {
      height: auto;
      padding: 0 93px;

      .form-label {
        margin-bottom: 10px;

        .form-input {
          height: 42px;
          min-height: unset;
        }

        &.error {
          margin-bottom: 0;
        }
      }

      .inputs_container__name-wrapper {
        display: flex;

        .name-wrapper__name-field-wrapper {
          display: flex;
          flex-direction: column;
          flex-grow: 1;

          &:last-child {
            .form-label {
              margin-right: 0;
            }
          }
        }

        .form-label {
          flex-grow: 1;
          margin-right: 15px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .submit.auth-btn {
        height: auto;
        margin-bottom: 30px;
        margin-top: 10px;
        padding: 13px 25px;
        width: auto;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 30px;

        .sso-label {
          color: #000;
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 10px;
          text-align: center;
        }

        .sso-providers {
          display: flex;
          justify-content: center;
          list-style: none;

          .sso-provider {
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 5px;
            box-sizing: border-box;
            height: 42px;
            margin-right: 10px;
            width: 42px;

            &:last-child {
              margin-right: 0;
            }

            &.google {
              background-image: url('img/sso-google.png');
              border: solid 1px #d5dce0;
            }

            &.facebook {
              background-image: url('img/sso-facebook.png');
              background-color: #0055cc;              ;
            }

            &.amazon {
              background-image: url('img/sso-amazon.png');
              background-color: #ffaa00;
            }

            &.apple {
              background-image: url('img/sso-apple.png');
              background-color: #000;
            }
          }
        }
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 30px;
        text-align: center;

        .sign-in-link {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .proceed-text {
        margin-bottom: 20px;
        position: static;
      }
    }
  }

  html {
    min-height: 900px;
  }
}

@media all and (min-width: 569px) and (max-width: 768px) {
  .form_wrapper.login.login {
    width: 604px;
    padding-top: 80px;

    .form_wrapper__header {
      padding-top: 40px;
      padding-bottom: 20px;
    }

    .inputs_container {
      padding: 0 105px;

      .form-password {
        margin-top: 10px;
      }

      .submit.auth-btn {
        margin-bottom: 40px;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 40px;
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 50px;
      }
    }
  }

  html {
    min-height: 900px;
  }

  .show-banner {
    min-height: 1500px;

    .form_wrapper.login.login {
      display: flex;
      flex-direction: column;

      .banner-wrapper {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        width: 604px;
        border-top: 1px solid #d5dce0;

        .banner {
          flex-grow: 1;
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}

@media all and (min-width: 769px) and (max-width: 1024px) {
  .form_wrapper.registration-comp {
    width: 520px;
    padding: 80px 0;

    .form_wrapper__header {
      padding: 60px 20px 40px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .form_wrapper__confirmation {
      text-align: center;
      padding-bottom: 40px;
    }

    .form_wrapper__sign-in-button-wrapper {
      height: 42px;
      text-align: center;

      .sign-in-link {
        background-color: #0055cc;
        border-radius: 5px;
        color: #fff;
        display: inline-block;
        font-size: 14px;
        font-weight: 700;
        padding: 13px 25px;
        text-decoration: none;
      }
    }
  }
}

@media all and (min-width: 1025px) {
  .form_wrapper__footer {
    height: 50px;
    padding-bottom: 0;
    padding-top: 0;
    .terms {
      text-align: left;
      width: 100%;
      li {
        margin: 0 7px;
      }
    }
    .footer-right {
      .languages {
        .link {
          color: #0055cc;
        }
        display: flex;
        li {
          display: inline-block;
          .language-flag {
            display: none;
          }
        }
      }
      .selected-language {
        display: none;
      }
    }
  }
  .form_wrapper__footer.big-footer {
    grid-template-columns: 1.5fr 1.5fr 1.5fr;
  }
}

@media all and (min-width: 1024px) {
  .form_wrapper.two_factory_auth {
    width: 482px;
    min-height: 620px;
    padding-top: 86px;
  }
  .form_wrapper.two_factory_auth_request {
    width: 482px;
    min-height: 539px;
    padding-top: 30px;
  }
  .form_wrapper.with_error {
    width: 482px;
    height: 539px;
    padding-top: 36px;
  }
  .form_wrapper.bad_request {
    width: 482px;
    height: 539px;
    padding-top: 94px;
  }
  .form_wrapper.error {
    height: auto;
    min-height: 539px;
    padding-top: 94px;
  }
  .form_wrapper {
    .bad-request-title {
      font-size: 40px;
      padding-top: 43.7px;
      padding-bottom: 26px;
      padding-top: 40px;
    }
    .bad-request-text {
      margin: 0 35px;
      line-height: 1.43;
      padding-bottom: 21px;
    }
    .logo {
      width: 231px;
      height: 61.3px;
    }
    .inputs_container {
      padding: 0 72px;
    }
    .sign-up-text {
      padding-bottom: 28px;
    }
  }
  .form_wrapper__two_factory_auth {
    padding-top: 50px;
    padding-bottom: 17.5px;
  }
  .form_wrapper__footer {
    .notice-text {
      font-size: 13px;

      .link.link {
        font-size: 13px;
      }
    }

    .terms {
      text-align: left;
      width: 100%;
      li {
        margin: 0 7px;

        .link.link {
          font-size: 13px;
        }
      }
    }
  }
  .form_wrapper__footer.big-footer {
    grid-template-columns: 1.6fr 1.4fr 1.1fr;
  }

  html {
    min-height: 700px;
  }
}

@media all and (min-width: 769px) and (max-width: 1024px) {
  .form_wrapper.login,
  .form_wrapper.registration {
    width: 521px;
    padding-top: 20px;

    .form_wrapper__header {
      padding: 20px 20px 10px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .inputs_container {
      height: auto;
      padding: 0 63px;

      .form-label {
        margin-bottom: 10px;

        .form-input {
          height: 42px;
          min-height: unset;
        }

        &.error {
          margin-bottom: 0;
        }
      }

      .inputs_container__name-wrapper {
        display: flex;

        .name-wrapper__name-field-wrapper {
          display: flex;
          flex-direction: column;
          flex-grow: 1;

          &:last-child {
            .form-label {
              margin-right: 0;
            }
          }
        }

        .form-label {
          flex-grow: 1;
          margin-right: 15px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .submit.auth-btn {
        height: auto;
        margin-bottom: 20px;
        margin-top: 0px;
        padding: 13px 25px;
        width: auto;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 20px;

        .sso-label {
          color: #000;
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 10px;
          text-align: center;
        }

        .sso-providers {
          display: flex;
          justify-content: center;
          list-style: none;

          .sso-provider {
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 5px;
            box-sizing: border-box;
            height: 42px;
            margin-right: 10px;
            width: 42px;

            &:last-child {
              margin-right: 0;
            }

            &.google {
              background-image: url('img/sso-google.png');
              border: solid 1px #d5dce0;
            }

            &.facebook {
              background-image: url('img/sso-facebook.png');
              background-color: #0055cc;              ;
            }

            &.amazon {
              background-image: url('img/sso-amazon.png');
              background-color: #ffaa00;
            }

            &.apple {
              background-image: url('img/sso-apple.png');
              background-color: #000;
            }
          }
        }
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 20px;
        text-align: center;

        .sign-in-link {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .proceed-text {
        margin-bottom: 20px;
        position: static;
      }
    }
  }

  html {
    min-height: 800px;
  }
}

@media all and (min-width: 769px) and (max-width: 1024px) {
  .form_wrapper.login.login {
    width: 482px;
    padding-top: 30px;

    .form_wrapper__header {
      padding: 20px 20px 10px;
    }

    .inputs_container {
      padding: 0 43px;

      .submit.auth-btn {
        margin-bottom: 20px;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 20px;
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 25px;
      }
    }
  }

  html {
    min-height: 700px;
  }

  .show-banner {
    .form_wrapper.login.login {
      display: flex;
      flex-direction: row;
      padding: 0;
      width: auto;

      .login-wrapper {
        width: 482px;
        padding-top: 30px;
      }

      .banner-wrapper {
        border-bottom-right-radius: 10px;
        border-top-right-radius: 10px;
        overflow: hidden;
        width: 502px;
        border-left: 1px solid #d5dce0;

        .banner {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}

@media all and (min-width: 1920px) {
  .form_wrapper.two_factory_auth {
    padding-top: 161px;
    width: 673px;
    min-height: 722px;
  }
  .form_wrapper.two_factory_auth_request {
    padding-top: 161px;
    width: 673px;
    min-height: 722px;
  }
  .form_wrapper.with_error {
    padding-top: 133px;
    width: 673px;
    height: 722px;
  }
  .form_wrapper.bad_request {
    width: 697px;
    height: 722px;
    padding-top: 174px;
  }
  .form_wrapper.error {
    height: auto;
    min-height: 722px;
    padding-top: 174px;
  }
  .form_wrapper {
    .bad-request-text {
      margin: 0 82px;
      line-height: 1.43;
      padding: 30px 30px 31px 30px;
    }
    .bad-request-explanation {
      padding: 20px 110px;
    }
    .inputs_container {
      padding: 0 139px;
    }
  }
  .form_wrapper__footer.big-footer {
    grid-template-columns: 0.96fr 1.2fr 1.3fr;
  }
  .form_wrapper__footer {
    .notice-text {
      padding-bottom: 0;
      width: 100%;
    }
    .terms {
      text-align: left;
      width: 100%;
      li {
        margin: 0 28px;
      }
    }
    .footer-right {
      .phone.phone {
        font-size: 13px;
      }
      .languages {
        li {
          margin-left: 13px;
        }
      }
    }
  }
}


@media all and (min-width: 1024px) and (max-width: 1365px) {
  .form_wrapper {
    .bad-request-explanation {
      padding: 25px;
    }
    .alt_way_container {
      .alt_way_error {
        width: 110%;
        margin-top: 20px;
      }
      padding: 0 81px;
    }
    .auth-btn.back {
      margin: 0;
      margin-right: 15px;
    }
    .proceed-text {
      bottom: 20px;
    }
  }
  .form_wrapper__footer {
    .notice-text {
      width: 100%;
    }
  }
}

@media all and (min-width: 1025px) and (max-width: 1366px) {
  .form_wrapper.registration-comp {
    width: 654px;
    padding: 80px 0;

    .form_wrapper__header {
      padding: 60px 20px 40px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .form_wrapper__confirmation {
      text-align: center;
      padding-bottom: 40px;
    }

    .form_wrapper__sign-in-button-wrapper {
      height: 42px;
      text-align: center;

      .sign-in-link {
        background-color: #0055cc;
        border-radius: 5px;
        color: #fff;
        display: inline-block;
        font-size: 14px;
        font-weight: 700;
        padding: 13px 25px;
        text-decoration: none;
      }
    }
  }
}

@media all and (min-width: 1025px) and (max-width: 1366px) {
  .form_wrapper.login,
  .form_wrapper.registration {
    width: 653px;
    padding-top: 40px;

    .form_wrapper__header {
      padding: 30px 20px 20px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .inputs_container {
      height: auto;
      padding: 0 129px;

      .form-label {
        margin-bottom: 10px;

        .form-input {
          height: 42px;
          min-height: unset;
        }

        &.error {
          margin-bottom: 0;
        }
      }

      .inputs_container__name-wrapper {
        display: flex;

        .name-wrapper__name-field-wrapper {
          display: flex;
          flex-direction: column;
          flex-grow: 1;

          &:last-child {
            .form-label {
              margin-right: 0;
            }
          }
        }

        .form-label {
          flex-grow: 1;
          margin-right: 15px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .submit.auth-btn {
        height: auto;
        margin-bottom: 30px;
        margin-top: 10px;
        padding: 13px 25px;
        width: auto;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 30px;

        .sso-label {
          color: #000;
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 10px;
          text-align: center;
        }

        .sso-providers {
          display: flex;
          justify-content: center;
          list-style: none;

          .sso-provider {
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 5px;
            box-sizing: border-box;
            height: 42px;
            margin-right: 10px;
            width: 42px;

            &:last-child {
              margin-right: 0;
            }

            &.google {
              background-image: url('img/sso-google.png');
              border: solid 1px #d5dce0;
            }

            &.facebook {
              background-image: url('img/sso-facebook.png');
              background-color: #0055cc;              ;
            }

            &.amazon {
              background-image: url('img/sso-amazon.png');
              background-color: #ffaa00;
            }

            &.apple {
              background-image: url('img/sso-apple.png');
              background-color: #000;
            }
          }
        }
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 30px;
        text-align: center;

        .sign-in-link {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .proceed-text {
        margin-bottom: 20px;
        position: static;
      }
    }
  }

  html {
    min-height: 900px;
  }
}

@media all and (min-width: 1025px) and (max-width: 1366px) {
  .form_wrapper.login.login {
    width: 650px;
    padding-top: 80px;

    .form_wrapper__header {
      padding-top: 40px;
      padding-left: 20px;
      padding-right: 20px;
    }

    .inputs_container {
      padding: 0 125px;

      .form-password {
        margin-top: 10px;
      }

      .submit.auth-btn {
        margin-bottom: 40px;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 40px;
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 50px;
      }
    }
  }

  html {
    min-height: 900px;
  }

  .show-banner {
    .form_wrapper.login.login {
      display: flex;
      flex-direction: row;
      padding: 0;
      width: auto;

      .login-wrapper {
        width: 650px;
        padding-top: 80px;
      }

      .banner-wrapper {
        border-bottom-right-radius: 10px;
        border-top-right-radius: 10px;
        overflow: hidden;
        width: 654px;
        border-left: 1px solid #d5dce0;

        .banner {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}

@media all and (min-width: 320px) and (max-width: 768px) {
  .form_wrapper {
    .form_wrapper__two_factory_auth_padding {
      padding-bottom: 10.5px;
    }
  }
}
@media all and (min-width: 320px) and (max-width: 767px) and (max-height: 568px) {
  .form_wrapper {
    .alt_way_container {
      height: 53vh;
    }
  }
  .form_wrapper__footer {
    height: 18vh;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (-webkit-min-device-pixel-ratio: 2) {
  .form_wrapper {
    .proceed-text {
      bottom: 10px;
    }
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (-webkit-min-device-pixel-ratio: 2) {
  .form_wrapper {
    .proceed-text {
      bottom: 20px;
    }
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 812px) and (-webkit-min-device-pixel-ratio: 3) {
  .form_wrapper {
    .proceed-text {
      bottom: 60px;
    }
    .proceed-text-alt-way {
      bottom: 105px;
    }
    .proceed-text-error {
      bottom: 80px;
    }
    .proceed-text-two-factory {
      bottom: 100px;
    }
    .proceed-text-alternate {
      bottom: 120px;
    }
  }
}
@media all and (min-width: 320px) and (max-width: 767px) and (min-height: 736px) {
  .form_wrapper__footer {
    height: 16vh;
  }
}
@media all and (min-width: 320px) and (max-width: 767px) and (min-height: 812px) {
  .form_wrapper__footer {
    height: 16vh;
  }
}

@media all and (min-width: 1366px) {
  .text.error {
    font-size: 14px;
    padding: 20px;
  }
  .form_wrapper.two_factory_auth {
    width: 673px;
    min-height: 722px;
    padding-top: 161px;

    .alt_way_containe {
      margin-bottom: 100px;
    }

    .inputs_container {
      padding-bottom: 100px;
    }
  }
  .form_wrapper.two_factory_auth_request {
    width: 673px;
    min-height: 722px;
    padding-top: 161px;

    .alt_way_containe {
      margin-bottom: 100px;
    }

    .inputs_container {
      padding-bottom: 100px;
    }
  }
  .form_wrapper.with_error {
    width: 673px;
    height: 722px;
    padding-top: 133px;
  }
  .form_wrapper.bad_request {
    width: 597px;
    height: 622px;
    padding-top: 123px;
  }
  .form_wrapper {
    .bad-request-explanation {
      padding: 20px 70px;
    }
    .logo {
      width: 332px;
      height: 88px;
    }
    .inputs_container {
      padding: 0 139px;
    }
    .alt_way_container {
      .alt_way_error {
        margin-top: 18px;
      }
      padding: 0 139px;
    }
  }
  .form_wrapper__two_factory_auth {
    padding-top: 50px;
    padding-bottom: 21px;
  }
  .form_wrapper__footer.big-footer {
    grid-template-columns: 1.38fr 1.5fr 1.5fr;
    .notice-text {
      .link {
        font-size: 13px;
      }
    }
    > .terms {
      li {
        .link {
          font-size: 13px;
        }
      }
    }
  }
  .form_wrapper__footer {
    .notice-text {
      padding-bottom: 0;
      font-size: 13px;
      width: 100%;
    }
    .terms {
      text-align: left;
      width: 100%;
      li {
        margin: 0 14px;
      }
    }
    .footer-right {
      .phone {
        font-size: 13px;
      }
      .languages {
        .link {
          font-size: 13px;
        }
      }
    }
  }
}

@media all and (min-width: 1367px) {
  .form_wrapper.login,
  .form_wrapper.registration {
    width: 692px;
    padding-top: 40px;

    .form_wrapper__header {
      padding: 30px 20px 20px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .inputs_container {
      height: auto;
      padding: 0 149px;

      .form-label {
        margin-bottom: 10px;

        .form-input {
          height: 42px;
          min-height: unset;
        }

        &.error {
          margin-bottom: 0;
        }
      }

      .inputs_container__name-wrapper {
        display: flex;

        .name-wrapper__name-field-wrapper {
          display: flex;
          flex-direction: column;
          flex-grow: 1;

          &:last-child {
            .form-label {
              margin-right: 0;
            }
          }
        }

        .form-label {
          flex-grow: 1;
          margin-right: 15px;
        }
      }

      .submit.auth-btn {
        height: auto;
        margin-bottom: 30px;
        margin-top: 10px;
        padding: 13px 25px;
        width: auto;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 30px;

        .sso-label {
          color: #000;
          font-size: 18px;
          font-weight: 700;
          margin-bottom: 10px;
          text-align: center;
        }

        .sso-providers {
          display: flex;
          justify-content: center;
          list-style: none;

          .sso-provider {
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 5px;
            box-sizing: border-box;
            height: 42px;
            margin-right: 10px;
            width: 42px;

            &:last-child {
              margin-right: 0;
            }

            &.google {
              background-image: url('img/sso-google.png');
              border: solid 1px #d5dce0;
            }

            &.facebook {
              background-image: url('img/sso-facebook.png');
              background-color: #0055cc;              ;
            }

            &.amazon {
              background-image: url('img/sso-amazon.png');
              background-color: #ffaa00;
            }

            &.apple {
              background-image: url('img/sso-apple.png');
              background-color: #000;
            }
          }
        }
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 30px;
        text-align: center;

        .sign-in-link {
          font-size: 14px;
          font-weight: 500;
        }
      }

      .proceed-text {
        margin-bottom: 20px;
        position: static;
      }
    }
  }

  html {
    min-height: unset;
  }
}

@media all and (min-width: 1367px) {
  .form_wrapper.registration-comp {
    width: 692px;
    padding: 80px 0;

    .form_wrapper__header {
      padding: 60px 20px 40px;
    }

    .logo {
      width: 286px;
      height: 74px;
    }

    .form_wrapper__confirmation {
      text-align: center;
      padding-bottom: 40px;
    }

    .form_wrapper__sign-in-button-wrapper {
      height: 42px;
      text-align: center;

      .sign-in-link {
        background-color: #0055cc;
        border-radius: 5px;
        color: #fff;
        display: inline-block;
        font-size: 14px;
        font-weight: 700;
        padding: 13px 25px;
        text-decoration: none;
      }
    }
  }
}

@media all and (min-width: 1367px) {
  .form_wrapper.login.login {
    width: 697px;
    padding-top: 80px;

    .form_wrapper__header {
      padding-top: 40px;
      padding-left: 20px;
      padding-right: 20px;
    }

    .inputs_container {
      .form-password {
        margin-top: 10px;
      }

      .submit.auth-btn {
        margin-bottom: 40px;
      }

      .inputs_container__sso-wrapper {
        margin-bottom: 40px;
      }

      .sign-in-link-wrapper {
        color: #000;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 70px;
      }
    }
  }

  html {
    min-height: 900px;
  }

  .show-banner {
    .form_wrapper.login.login {
      display: flex;
      flex-direction: row;
      padding: 0;
      width: auto;

      .login-wrapper {
        width: 697px;
        padding-top: 80px;
      }

      .banner-wrapper {
        border-bottom-right-radius: 10px;
        border-top-right-radius: 10px;
        overflow: hidden;
        width: 674px;
        border-left: 1px solid #d5dce0;

        .banner {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}

html:not(.show-banner) {
  .banner-wrapper {
    display: none;
  }
}

.show-change-email-modal {
  .change-email-modal__wrapper {
    display: block;
  }
}

.change-email-modal__wrapper {
  display: none;

  .change-email-modal {
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 0 8px 0 rgba(21, 27, 38, 0.15);
    left: 50%;
    margin: 0 10px;
    max-width: 600px;
    position: absolute;
    top: 200px;
    transform: translateX(calc(-50% - 10px));
    width: calc(100% - 20px);
    z-index: 3;

    .change-email-modal__header {
      border-bottom: 1px solid #ebedf0;
      padding: 14px 20px;
      position: relative;

      .header__title {
        color: #000000;
        font-size: 16px;
        font-weight: 700;
      }

      .header__close {
        background-image: url('img/close.png');
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
        height: 24px;
        position: absolute;
        right: 10px;
        top: 10px;
        width: 24px;
      }
    }

    .change-email-modal__content {
      color: #666;
      font-size: 13px;
      padding: 20px;
    }

    .change-email-modal__footer {
      border-top: 1px solid #ebedf0;
      display: flex;
      justify-content: flex-end;
      padding: 10px 20px;

      .modal-btn {
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: block;
        font-size: 14px;
        font-weight: bold;
        margin-right: 10px;
        padding: 8px 20px;

        &:last-child {
          margin-right: 0;
        }
      }

      .modal-btn.primary {
        background-color: #0055cc;
        color: #ffffff;

        &:hover {
          opacity: 0.6;
          text-decoration: none;
        }
      }

      .modal-btn.secondary {
        background-color: #ffffff;
        border: solid 1px #d5dce0;
        color: #000000;
        transition: 0.2s;

        &:hover {
          border: solid 1px #0055cc;
          color: #0055cc;
          text-decoration: none;
        }
      }
    }
  }

  .change-email-modal__overlay {
    background-color: #000;
    bottom: 0;
    height: 100%;
    left: 0;
    opacity: 0.6;
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    z-index: 2;
  }
}

@media all and (min-width: 320px) and (max-width: 568px) {
  .change-email-modal__wrapper {
    .change-email-modal {
      top: 100px;

      .change-email-modal__header {
        padding: 14px 10px;
      }

      .change-email-modal__content {
        padding: 10px;
      }

      .change-email-modal__footer {
        flex-direction: column;
        padding: 10px;

        .modal-btn {
          margin-bottom: 10px;
          margin-right: 0;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
